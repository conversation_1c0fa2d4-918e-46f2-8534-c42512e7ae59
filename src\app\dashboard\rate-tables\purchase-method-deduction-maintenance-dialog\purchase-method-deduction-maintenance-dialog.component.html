<div class="dailog-title-bg">
  <div class="dailog-title">History<button class="dailog-close" [mat-dialog-close]><span>X</span></button>
  </div>
</div>

<div class="row" *ngIf="purchaseMethodDeductionGroup">
   
        
  <div class="col-md-6"> <div class="row">
                    <label class="col-sm-5">Purchase Method</label>
                    <span class="col-sm-7">{{purchaseMethodDeductionGroup[0].purchaseMethod}}</span>
                  </div> </div>

                  <div class="col-md-6"> <div class="row">
                    <label class="col-sm-5">Sales Territory</label>
                    <span class="col-sm-7">{{purchaseMethodDeductionGroup[0].salesTerritory}}</span>
                </div></div>
            <div class="col-md-12">
        <table class="my-table mat-table w-100 mt-3">
          <thead>
            <tr  class="mat-header-row">
              <th   class="mat-header-cell"  scope="col">Effective Start Date</th>
              <th  class="mat-header-cell"   scope="col">Effective End Date</th>
              <th  class="mat-header-cell"   scope="col">Purchase Method Deduction Rate</th>
            </tr>
          </thead>
          <tbody>
            <tr class="mat-row"  *ngFor="let tr of purchaseMethodDeductionGroup">
              <td data-td-head="Effective Start Date" class="mat-cell">{{tr.effectiveStartDate | date}}</td>
              <td data-td-head="Effective End Date" class="mat-cell">{{tr.effectiveEndDate | date}}</td>
              <!-- <td>{{tr.purchaseMethodDeductionRate | currency:'USD':true:'1.2-3'}}</td> --> 
              <!-- Dilip Com-1138 -->
              <td data-td-head="Purchase Method Deduction Rate" class="mat-cell">{{tr.purchaseMethodDeductionRate | currency:'USD':true:'1.4-4'}}</td>

            </tr>
          </tbody>
        </table>
      </div>
  </div>