#nav-toggle:focus {
  outline: none;
}

#nav-toggle {
  /* background-color: #6CCA98; */
}

.commissions-navbar {
  background-color: #3b3e47 !important;
  height: 50px; top:0;
  color: white; position: fixed; left:0; z-index: 9; right:0;
}
.trinity-logo-sidebar img{max-height:50px ;}
.header-link{font-size:12px; font-weight: 400;}
.spacer {
  flex: 1 1 auto;
}

.header-icon {
  padding: 0 5px;
  font-size: 18px;
}

.notification-drp{
  margin-left: 76%;  
  overflow-y: auto;
  max-height: 90vh;
}
.notification-width{
  width: 22rem;
  display: block !important;
}
.notifiy-numb{
  position: fixed;
  margin-left: 1.1rem;
  margin-bottom: 1.2rem;
  font-size: 10px;
}

.subtext {
  clear: both;
  font-size: 12px;
  color: #777;
  display: block;
}
.notification-margin{
  margin-right: 4px;
}
.low-notifi{
  margin-right: 7px;
}
.medium-notifi{
  margin-right: 14px;
}

.highcount-notifi{
  margin-right: 21px;
}
.guide-ico{
  height: 26px;
  width: 26px;
}
.toggle-color {
}
.menu-toggle{position: absolute; left:0; top:0; bottom: 0; background: #408bc0; border-radius: 0;} 
