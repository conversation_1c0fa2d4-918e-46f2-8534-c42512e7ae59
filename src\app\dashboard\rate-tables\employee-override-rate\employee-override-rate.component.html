<div class="page-title col-md-12">
  <h1>Employee Override Rates</h1>
  <div class="breadcrumbs">
    <a href="#">Home</a>/<span>Employee Override Rates</span>
  </div>
</div>

<div class="content">
  <div class="card">
    <div class="card-header-info">
      <h4 class="card-title no-hover-effect">Employee Override Rates</h4>
    </div>

    <div class="card-body">
      <div class="row">
        <div class="card-body">
          <div class="row">
            <div class="col-md-12">
              <div class="input-group float-right table-search">
                <input class="custom-input" type="text" id="searchTextId" [(ngModel)]="searchText" name="searchText" placeholder="Search" (input)="searchForItem()"/>
                <span class="input-group-icon"><i class="fas fa-search"></i></span>
              </div>
            </div>
          </div>

          <mat-table #table [dataSource]="dataSource" matSort>
            <ng-container matColumnDef="{{ column.id }}" *ngFor="let column of columnNames">
              <mat-header-cell *matHeaderCellDef mat-sort-header class="table-header"> {{ column.value }} </mat-header-cell>
              <mat-cell [attr.data-td-head]="column.value" *matCellDef="let element">{{ element[column.id] }}</mat-cell>
            </ng-container>
            <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
            <mat-row *matRowDef="let row; columns: displayedColumns" class="pointer table-content" (click)="rowClick(row)"></mat-row>
          </mat-table>

          <mat-paginator [pageSizeOptions]="[5, 10, 20, 50]"showFirstLastButtons></mat-paginator>
          <div>
            <a class="btn btn-primary float-right" *ngIf="!addRow" (click)="Add()"><i class="material-icons pointer">add_circle</i> Add</a>
            <a class="btn btn-primary float-right" *ngIf="addRow" (click)="addRow = !addRow"><i class="material-icons pointer">remove_circle</i> Hide</a>
            <a type="submit" class="btn btn-primary float-right" (click)="upload()">Upload</a>
            <label for="file-select" class="btn btn-primary float-right">Select File</label>
            <input type="file" id="file-select" style="display: none" (change)="handleFileInput($event)" value="" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"/>
          </div>
        </div>
      </div>

      <div class="card" *ngIf="addRow">
        <div class="card-header-info">
          <h4 class="card-title no-hover-effect">
            <i class="fas fa-plus"></i> Add Employee Override Rate
          </h4>
        </div>

        <div class="card-body">
          <div>
            <form [formGroup]="employeeOverrideFormAdd" (ngSubmit)="onAddSubmit()" class="w-100">
              <div class="row">
                <div class="form-group col-md-4">
                  <div class="row">
                    <label class="col-sm-5">Contact</label>
                    <div class="col-sm-7">
                      <input id="typeahead-prevent-manual-entry" type="text" class="custom-select" formControlName="contactId" [ngbTypeahead]="search" [inputFormatter]="formatter" 
                      [editable]="false" [resultTemplate]="rt" placeholder="Type to search"/>
                    </div>
                  </div>
                </div>
                <ng-template #rt let-r="result" let-t="id">
                  <div class="col-sm-12" style="width: 80%">
                    {{ r.contactName }}
                  </div>
                </ng-template>

                <div class="form-group col-md-4">
                  <div class="row">
                    <label class="col-sm-5">Sales Division</label>
                    <div class="col-sm-7">
                      <select class="custom-select" name="salesDivision_add_dropdown" formControlName="salesDivision" data-style="btn btn-link" id="salesDivision_add_dropdown">
                        <option *ngFor="let sd of this.salesDivisions"
                          [selected]="sd == employeeOverrideRateAdd[0].salesDivision" value="{{ sd }}"> {{ sd }} </option>
                      </select>
                    </div>
                  </div>
                </div>

                <div class="form-group col-md-4">
                  <div class="row">
                    <label class="col-sm-5">Sales Office</label>
                    <div class="col-sm-7">
                      <select class="custom-select" name="salesOffice_add_dropdown" formControlName="salesOffice" data-style="btn btn-link" id="salesOffice_add_dropdown">
                        <option *ngFor="let so of this.salesOffices"
                          [selected]="so === employeeOverrideRateAdd[0].salesOffice "value="{{ so }}"> {{ so }} </option>
                      </select>
                    </div>
                  </div>
                </div>

                <div class="form-group col-md-4">
                  <div class="row">
                    <label class="col-sm-5">Effective Start Date</label>
                    <div class="col-sm-7">
                      <div class="date-picker w-100">
                        <input #AddStartDatePicker type="date" name="start_date" id="start_date" class="custom-input" formControlName="effectiveStartDate" placeholder=""/>
                        <span *ngIf="AddStartDatePicker.value.length > 0" class="mat-icon cal-reset" (click)="clearAddStartDate(AddStartDatePicker)"><i class="far fa-calendar-times"></i></span>
                        <span *ngIf="AddStartDatePicker.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="form-group col-md-4">
                  <div class="row">
                    <label class="col-sm-5">Effective End Date</label>
                    <div class="col-sm-7">
                      <div class="date-picker w-100">
                        <input #AddEndDatePicker type="date" name="effectiveEndDate" id="effectiveEndDate" class="custom-input" formControlName="effectiveEndDate" placeholder=""/>
                        <span *ngIf="AddEndDatePicker.value.length > 0" class="mat-icon cal-reset" (click)="clearAddEndDate(AddEndDatePicker)"><i class="far fa-calendar-times"></i></span>
                        <span *ngIf="AddEndDatePicker.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="form-group col-md-4">
                  <div class="row">
                    <label class="col-sm-5">Rate</label>
                    <div class="col-sm-7">
                      <input class="custom-input" formControlName="rate" id="rate_add" name="rate_add" value="{{employeeOverrideRateAdd[0].rate | number : '1.2-2'}}"/>
                    </div>
                  </div>
                </div>

                <div class="form-group col-md-4">
                  <div class="row">
                    <label class="col-sm-5">Team</label>
                    <div class="col-sm-7">
                      <select class="custom-select" name="team" formControlName="team"
                        data-style="btn btn-link" id="team_add_dropdown">
                        <option *ngFor="let value of teamsData" [value]="value"> {{ value }} </option>
                      </select>
                    </div>
                  </div>
                </div>

                <div class="form-group col-md-4">
                  <div class="row">
                    <label class="col-sm-5" *ngIf="hideEmployeeTitle">Employee Title</label>
                    <div class="col-sm-7">
                      <select class="custom-select" name="employeeTitle_add_dropdown" formControlName="employeeTitle" data-style="btn btn-link" id="employeeTitle_add_dropdown">
                        <option *ngFor="let et of this.employeeTitles"
                          [selected]="et === employeeOverrideRateAdd[0].employeeTitle" value="{{ et }}">{{ et }}</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>

              <div class="row align-button-right">
                <button type="submit" class="btn btn-primary" [disabled]="employeeOverrideFormAdd.invalid"><i class="fas fa-plus"></i> Add Employee Override Rate</button>
              </div>
            </form>
          </div>
        </div>
      </div>

      <div class="card" *ngIf="editRow">
        <div class="card-header-info">
          <h4 class="card-title no-hover-effect">
            <i class="fas fa-plus"></i> Edit Employee Override Rate
          </h4>
        </div>
        <div class="card-body">
          <div>
            <form [formGroup]="employeeOverrideFormEdit" (ngSubmit)="onEditSubmit()" class="w-100">
              <div class="row">
                <div class="form-group col-md-4">
                  <div class="row">
                    <label class="col-sm-5">Contact Name</label>
                    <div class="col-sm-7">
                      <input class="custom-input" formControlName="contactName" name="contactId_edit" id="contactId_edit" readonly/>
                    </div>
                  </div>
                </div>

                <div class="form-group col-md-4">
                  <div class="row">
                    <label class="col-sm-5">Sales Division</label>
                    <div class="col-sm-7">
                      <select class="custom-select" name="salesDivision_edit_dropdown" formControlName="salesDivision" data-style="btn btn-link" id="salesDivision_edit_dropdown">
                        <option *ngFor="let sd of this.salesDivisions"
                          [selected]="sd == employeeOverrideRateEdit[0].salesDivision" value="{{ sd }}"> {{ sd }} </option>
                      </select>
                    </div>
                  </div>
                </div>
                <div class="form-group col-md-4">
                  <div class="row">
                    <label class="col-sm-5">Sales Office</label>
                    <div class="col-sm-7">
                      <select class="custom-select" name="salesOffice_edit_dropdown" formControlName="salesOffice" data-style="btn btn-link" id="salesOffice_edit_dropdown">
                        <option
                          *ngFor="let so of this.salesOffices"
                          [selected]="so === employeeOverrideRateEdit[0].salesOffice" value="{{ so }}"> {{ so }} </option>
                      </select>
                    </div>
                  </div>
                </div>

                <div class="form-group col-md-4">
                  <div class="row">
                    <label class="col-sm-5">Effective Start Date</label>
                    <div class="col-sm-7">
                      <div class="date-picker w-100">
                        <input #EditStartDatePicker type="date" name="effectiveStartDate" id="effectiveStartDate" class="custom-input" formControlName="effectiveStartDate" placeholder="" />
                        <span *ngIf="EditStartDatePicker.value.length > 0" class="mat-icon cal-reset" (click)="clearEditStartDate(EditStartDatePicker)"><i class="far fa-calendar-times"></i></span>
                        <span *ngIf="EditStartDatePicker.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="form-group col-md-4">
                  <div class="row">
                    <label class="col-sm-5">Effective End Date</label>
                    <div class="col-sm-7">
                      <div class="date-picker w-100">
                        <input #EditEndDatePicker type="date" name="effectiveEndDate" id="effectiveEndDate" class="custom-input" formControlName="effectiveEndDate" placeholder=""/>
                        <span *ngIf="EditEndDatePicker.value.length > 0" class="mat-icon cal-reset" (click)="clearEditEndDate(EditEndDatePicker)"><i class="far fa-calendar-times"></i></span>
                        <span *ngIf="EditEndDatePicker.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="form-group col-md-4">
                  <div class="row">
                    <label class="col-sm-5">Rate</label>
                    <div class="col-sm-7">
                      <input class="custom-input" name="rate_edit" id="rate_edit" formControlName="rate" value="{{employeeOverrideRateEdit[0].rate | number : '1.2-2'}}"/>
                    </div>
                  </div>
                </div>

                <div class="form-group col-md-4">
                  <div class="row">
                    <label class="col-sm-5">Team</label>
                    <div class="col-sm-7">
                      <input class="custom-input" formControlName="team" name="team_edit" id="team_edit"
                        value="{{employeeOverrideRateEdit[0].team}}" disabled />
                    </div>
                  </div>
                </div>

                <div class="form-group col-md-4">
                  <div class="row">
                    <label class="col-sm-5" *ngIf="hideEmployeeTitle">
                      Employee Title
                    </label>
                    <div class="col-sm-7">
                      <select class="custom-select" name="employeeTitle_edit_dropdown" formControlName="employeeTitle" data-style="btn btn-link" id="employeeTitle_edit_dropdown">
                        <option
                          *ngFor="let et of this.employeeTitles"
                          [selected]="et === employeeOverrideRateEdit[0].employeeTitle" value="{{ et }}"> {{ et }}
                        </option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
              <div class="row align-button-right">
                <button type="button" class="btn btn-primary" name="delete" (click)="deleteRate()">
                  <i class="fas fa-plus"></i> Delete Rate
                </button>
                <button type="submit" class="btn btn-primary" name="edit" [disabled]="employeeOverrideFormEdit.invalid">
                  <i class="fas fa-plus"></i> Update Employee Override Rate
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>