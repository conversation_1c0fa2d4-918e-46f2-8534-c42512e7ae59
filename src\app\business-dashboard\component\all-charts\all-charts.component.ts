import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { Chart } from 'angular-highcharts';
import { IdateRange } from '../../models/models';
import { CurrencyPipe, DatePipe } from '@angular/common';
import { ToastrService } from 'ngx-toastr';
import { ApiService } from 'src/app/services/api.service';
import * as Highcharts from 'highcharts';

interface CustomPoint extends Highcharts.Point {
  custom: any;
}
interface CustomSeries extends Highcharts.Series {
  color: any;
}

@Component({
  selector: 'app-all-charts',
  templateUrl: './all-charts.component.html',
  styleUrls: ['./all-charts.component.css']
})
export class AllChartsComponent implements OnInit {
  @ViewChild('globalChart') globalChart;
  commissionChartsData!: Chart;
  kiloWattChartsData!: Chart;
  pendingPaymentsChartsData!: Chart;
  commissionByDivisionChartsData!: Chart;
  traditionalCommissionByProductChartsData!: Chart;
  openJobsWithoutDisqualifier!: Chart;
  totalCommissionChartData!: Chart;
  totalCommissionPayments:string;
  totalKilowattsSold:string = "";
  pendingPaymentApproval:string;
  processedPayments:string;
  repsWithNoPlan:string = "";
  typeofPendingChartsData: any[]=[];
  commissionDivisionChartData: any[]=[];
  @Input() dateRange: IdateRange | null = null;
  @Input() tabNumber: number | null = null;
  previousDateRange: IdateRange | null = null;
  mapData!: any;
  showMap:boolean = false;
  enableChart:boolean =false;
  isEnableLoader:boolean = true;
  tabName:string = "";
  kilowattXaxisData:any;
  quaterListData:any;
  commissionProductChartData:any;
  constructor(private apiService: ApiService, private toastMsg: ToastrService,private datePipe: DatePipe,private currencyPipe: CurrencyPipe) { }

  ngOnInit() {
  }
  ngOnChanges(){
    if (this.tabNumber === 0) {
      if (this.dateRange) {
        if (this.previousDateRange === null || this.previousDateRange !== this.dateRange) {
          this.previousDateRange = this.dateRange;
          this.getChart();
        }       
      }
    }
  } 
  getChart(){
    this.enableChart = false;
    this.apiService.show();
    this.apiService.get(`BusinessDashboard/GetDashboardSummary?toDate=${this.dateRange.endDate}&fromDate=${this.dateRange.startDate}`).subscribe((res: any) => {
      this.showMap = true;
      this.commissionProductChartData  = res.paymentsByProductTypeChart;
      this.commissionProductChartData.forEach(s=>{
        s.custom = this.currencyPipe.transform(s.custom);
      })
      this.totalCommissionPayments = this.currencyPipe.transform(res.dashboardCount.totalCommissionPayments);
      this.totalKilowattsSold = res.dashboardCount.totalKilowattsSold;
      this.totalKilowattsSold = new Intl.NumberFormat().format(Number(this.totalKilowattsSold));
      this.pendingPaymentApproval = this.currencyPipe.transform(res.dashboardCount.pendingPaymentApproval);
      this.processedPayments = this.currencyPipe.transform(res.dashboardCount.processedPayments);
      this.repsWithNoPlan = res.dashboardCount.repsWithNoPlan;
      this.repsWithNoPlan = new Intl.NumberFormat().format(Number(this.repsWithNoPlan));
      this.typeofPendingChartsData = res.pendingPaymentChart;
      this.typeofPendingChartsData.forEach(s=>{
        s.custom = this.currencyPipe.transform(s.custom);
      })
      this.commissionDivisionChartData = res.paymentsDivisionChart;
      this.commissionDivisionChartData.forEach(s=>{
        s.custom = this.currencyPipe.transform(s.custom);
      })
      this.mapData= res.kilowattsSoldByStateChart;
      let quaterList = res.commissionsPaidByQuarterChart.quarters;
      this.quaterListData = res.commissionsPaidByQuarterChart.quarters;
      let totalAmont = res.commissionsPaidByQuarterChart.totalAmounts;
      let numberOfPayments = res.commissionsPaidByQuarterChart.numberOfPayments;
      this.kilowattXaxisData = res.kilowattsByDivisionChart.XAxis;
      let x_axis = res.kilowattsByDivisionChart.XAxis;
      let y_axis = res.kilowattsByDivisionChart.YAxis;
      this.kiloWattChart(x_axis,y_axis);
      this.commissionChartsData = this.setPieChart('Payments By Product Type',this.commissionProductChartData,"Count","Total Amount","Data of payments generated by product type  with payments in the status 'Created' or 'on Hold' or 'Approved' and payment modified on the date range");
      this.pendingPaymentsChartsData = this.setPieChart('Types of Pending Payment',this.typeofPendingChartsData,"Count","Total Amount","Data of payments by payment type name which is in the status 'Created' or 'on Hold' and payment modified on the date range");
      this.commissionByDivisionChartsData = this.setPieChart('Payments By Division',this.commissionDivisionChartData,"Count","Total Amount","Data of payments by sales division of the contact with payments in the status 'Created' or 'on Hold' or 'Approved' and payment modified on the date range");
      this.totalCommission(quaterList,totalAmont,numberOfPayments);
      this.apiService.hide();
      this.enableChart = true;
    }, (err: any) => {
      this.apiService.hide();
      this.isEnableLoader= false;
      this.toastMsg.error(err.message, "Server Error!");

    });
  }
  setPieChart(title:string,data:any,ylabel:string,customLabels:any,subtitle:string){
    let chart = new Chart({
      chart: {
        plotBackgroundColor: null,
        plotBorderWidth: null,
        plotShadow: false,
      },
      title: {
        text: title
      },
      subtitle:{
        text: subtitle,
      },
      lang: {
        thousandsSep: ','
      },
      tooltip: {  
        pointFormatter: function() {
          const point = this as CustomPoint;
          return `${ylabel}: <b>${point.y.toLocaleString("en-US")}</b> <br/> ${customLabels} : <b>${point.custom}</b>`;
        },
      },
      accessibility: {
        point: {
          valueSuffix: '%',
        },
      },
      legend: {
        maxHeight: 90,  
      },
      credits:{
        enabled: false
      },
      plotOptions: {
        pie: {
            allowPointSelect: true,
            innerSize: '50%',
            cursor: 'pointer',
            dataLabels: {
                enabled: true
            },
            showInLegend: true
        }
      },
      series: [
        {
          type: 'pie',
          name: ylabel,
          showInLegend: true,
          data:data
        }
      ]
    });
    return chart;
  }
  kiloWattChart(xAxis:string[],yAxis:any[]) {
    let chart = new Chart({
      chart: {
        type: 'column',
      },
      responsive: {
        rules: [{
          condition: {
            maxWidth: 300 // Adjust this value as needed
          },
          // Optional: Define options for small screens

        }]
      },
      title: {
        text: 'Total Kilowatts Sold by Division'
      },
      subtitle:{
        text: 'State wise data of Sales Division of the sales person and sum of system size kWDC sold with  actual install date on the date range [Sales- Direct, Sales- Traditional, Sales- Outreach, Sales- Inside]'
      },
      xAxis: {
        categories: xAxis,
        crosshair: true
      },
      yAxis: {
        min: 0,
        title: {
          text: 'Total Kilo Watts Sold'
        }
      },
      tooltip: {
        headerFormat: '<span style="font-size:10px">{point.key}</span><table>',
        footerFormat: '</table>',
        pointFormatter: function() {
          const point = this as CustomPoint;
          const series = this as unknown as CustomSeries;
          return `<tr><td><span style="color:${series.color};padding:0">${this.series.name}:</span></td><td><b>${point.y.toLocaleString("en-US")} KW</b></td></tr>`;
        },
        shared: true,
        useHTML: true
      },
      credits: {
        enabled: false
      },
      plotOptions: {
        column: {
          pointPadding: 0.2,
          borderWidth: 0
        }
      },
      series:yAxis
    })
    this.kiloWattChartsData = chart;
  }
  totalCommission(quaterList:string[],totalAmont:number[],numberOfPayments:number[]) {
    const extraData = numberOfPayments;
    let chart = new Chart({
      chart: {
        type: 'line'
      },
      subtitle: {
        text: "Data of payments of last 5 quarters with payments in the status 'Approved' and payment modified on the date range"
      },
      xAxis: {
        categories: quaterList,
      },
      yAxis: {
        title: {
          text: 'Commission Paid'
        }
      },
      title: {
        text: 'Total Commissions Paid by Quarter  ',
      },
      credits: {
        enabled: false
      },
      series: [{
        name: 'Commission Paid',
        type: 'line',
        data: totalAmont,        
        tooltip: {
          pointFormatter: function () {
            const index = this.index; // Index of the current point
            const extraValue = extraData[index];
            return ` ${this.series.name}: <b>${this.y.toLocaleString("en-US")}</b><br/>Number of Payments : <b>${extraValue.toLocaleString("en-US")}</b>`;
          }
        }
      }]

    });
    this.totalCommissionChartData = chart;
  }
  moveToSelectedTab(tabName: string) {    
    this.tabName = tabName;
    for (let i = 0; i < document.querySelectorAll('.mat-tab-label-content').length; i++) {
      if ((<HTMLElement>document.querySelectorAll('.mat-tab-label-content')[i]).innerText == tabName) {
        (<HTMLElement>document.querySelectorAll('.mat-tab-label')[i]).click();
      }
    }
  }

}
