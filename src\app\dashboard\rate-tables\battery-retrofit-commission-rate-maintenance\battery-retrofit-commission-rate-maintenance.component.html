<div class="page-title col-md-12 ">
    <h1> Battery Retrofit Commission Rates</h1>
    <div class="breadcrumbs"><a href="#">Home</a>/<span> Battery Retrofit Commission Rates</span>
    </div>
</div>

<div class="content">

    <div class="card">
      <div class="card-header-info">
        <h4 class="card-title no-hover-effect"><i class="fas fa-hand-holding-usd"></i>   Battery Retrofit Commission Rates Maintenance
        </h4>
      </div>
      <div class="card-body">
            <div class="row">
            <div class="col-md-12">
                <div class="input-group float-right table-search">
                <input class="custom-input" type="text" id="searchTextId" [(ngModel)]="searchText" name="searchText"
                    placeholder="Search" (input)="searchForItem()">
                <span class="input-group-icon">
                    <i class="fas fa-search"></i>
                </span>
                </div>
            </div>
            </div>
            <mat-table #table [dataSource]="dataSource" matSort>
            <ng-container matColumnDef="{{column.id}}" *ngFor="let column of columnNames">
                <mat-header-cell *matHeaderCellDef mat-sort-header class="table-header"> {{column.value}} </mat-header-cell>
                <mat-cell [attr.data-td-head]="column.value" *matCellDef="let element"> {{element[column.id]}} </mat-cell>
            </ng-container>
            <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>content
            <mat-row *matRowDef="let row; columns: displayedColumns;" class="pointer table-" (click)="rowClick(row)">
            </mat-row>
            </mat-table>
            <mat-paginator [pageSizeOptions]="[5, 10, 20, 50]" showFirstLastButtons></mat-paginator>
        
            <div>
                <a class="btn  btn-primary float-right" *ngIf="!addInd" (click)="addInd = !addInd"><i
                    class="material-icons pointer">add_circle</i> Add</a>
                <a class="btn  btn-primary float-right" *ngIf="addInd" (click)="addInd = !addInd"><i
                    class="material-icons pointer">remove_circle</i> Hide</a>
            </div>
        </div>
    </div>





    <div class="card" *ngIf="addInd">
        <div class="card-header-info">
          <h4 class="card-title no-hover-effect"><i class="fas fa-plus"></i>   Add Battery Retrofit Commission Rate </h4>
        </div>
        <div class="card-body">
          <div>
            <form [formGroup]="BatteryRetrofitCommissionForm" (ngSubmit)="onSubmit()" class="w-100">
              <div class="" >
                <div class="" >
                <div class="row">
                  <div class="form-group col-md-4">
                    <div class="row">
                      <label class="col-sm-5">State Code</label>
                      <div class="col-sm-7">
                        <select class="custom-select" name="state_code_dropdown" formControlName="stateCode"
                          data-style="btn btn-link" id="state_code_dropdown">
                          <option *ngFor="let st of dropdowns.stateCodes" value="{{st.stateCodeId}}">
                            {{st.stateCode1}}</option>
                        </select>
                      </div>
                    </div>
                  </div>
                  <div class="form-group col-md-4">
                    <div class="row">
                      <label class="col-sm-5">Battery Type</label>
                      <div class="col-sm-7">
                        <select class="custom-select" name="Battery_Type_dropdown" formControlName="batteryType"
                          data-style="btn btn-link" id="Battery_Type_dropdown">
                          <option *ngFor="let uc of dropdowns.BatteryType" value="{{uc.batteryTypeId}}">
                            {{uc.batteryTypeName}}</option>
                        </select>
                      </div>
                    </div>
                  </div>
                  <div class="form-group col-md-4">
                    <div class="row">
                      <label class="col-sm-5">Purchase Method</label>
                      <div class="col-sm-7">
                        <select class="custom-select" name="purchase_method_dropdown" formControlName="purchaseMethod"
                          data-style="btn btn-link" id="purchase_method_dropdown">
                          <option *ngFor="let pm of dropdowns.purchaseMethods" value="{{pm.purchaseMethodId}}">
                            {{pm.purchaseMethod1}}</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>
                  <div class="row">
                    <div class="form-group col-md-4">
                      <div class="row">
                        <label class="col-sm-5">Quantity</label>
                        <div class="col-md-7">
                          <input name="Quantity" formControlName="quantity" class="custom-input">
                          <div *ngIf="BatteryRetrofitCommissionForm.controls['Quantity'].errors && BatteryRetrofitCommissionForm.controls['Quantity'].errors">
                            <p style="color: red;">Quantity cannot be less 0 or negative</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="form-group col-md-4">
                      <div class="row">
                        <label class="col-sm-5">Effective Start Date</label>
                        <div class="col-sm-7 ">
                          <div class="date-picker w-100">
                            <input #StartDatePicker type="date" name="start_date" id="start_date" class="custom-input" formControlName="effectiveStartDate" placeholder="">
                            <span *ngIf="StartDatePicker.value.length > 0" class="mat-icon cal-reset" (click)="clearDate(StartDatePicker)"><i class="far fa-calendar-times"></i></span>
                            <span *ngIf="StartDatePicker.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span>
                            
                            <div *ngIf="BatteryRetrofitCommissionForm.errors && BatteryRetrofitCommissionForm.errors.maxDate">
                              <p style="color: red;">New Effecting Start Date should be greater than any previous start dates
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
      
                   <div class="row" *ngFor="let group of BatteryRetrofitCommissionRatesGroup; index as i">
                    <div class="gray-bg col-md-12 pt-3 mb-1">
                      <div class="row">
                        <div class="form-group col-md-4">
                          <div class="row">
                            <label class="col-sm-5">Battery Retrofit Commission Amount</label>
                            <div class="col-sm-7">
                              <input currencyMask [options]="{ allowNegative: false, align: 'left' }"
                                name="{{getControlName(group[1])}}" formControlName="{{getControlName(group[1])}}"
                                class="custom-input">
                            </div>
                          </div>
                        </div>
                        <div class="form-group col-md-4">
                          <div class="row">
                            <label class="col-sm-5">Battery Retrofit Sales Metric</label>
                            <div class="col-sm-7">
                              <input 
                                name="{{getControlName(group[0])}}" formControlName="{{getControlName(group[0])}}"
                                class="custom-input">
                            </div>
                          </div>
                        </div>
                        <a class="text-info"><i class="material-icons hover" (click)="removeFormRow(i)">delete</i></a>
                        <a class="text-info hover"><i *ngIf="i == BatteryRetrofitCommissionRatesGroup.length - 1"
                            class="material-icons" (click)="addFormRow()">add_box</i></a>
                      </div>
                    </div>
                  </div>
                  <div class="row align-button-right">
                    <button type="submit" class="btn btn-primary" [disabled]="BatteryRetrofitCommissionForm.invalid"><i
                        class="fas fa-plus"></i> Add Battery Retrofit Commission Rate </button>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>
         <div class="card" *ngIf="batteryRetrofitCommissionRateGruop">
          <div class="card">
            <div class="card-header-info">
              <h4 class="card-title"><i class="fas fa-history"></i> History</h4>
            </div>
            <div class="card-body">
              <div class="row">
                <hr style="width: 95%; border-color: #26c6da;" />
                <div class="col-md-12">  
                  <a class="text-info"><i class="material-icons float-right blue-icon"
                      (click)="batteryRetrofitCommissionRateGruop = null">cancel</i></a>
                </div>
                <div class="row">
                  <div class="col-md-2">
                    <div class="row">
                      <label class="col-sm-5" style="overflow: auto; white-space: nowrap">State Code</label>
                      <span class="col-sm-4" style="overflow: auto; white-space: nowrap">{{batteryRetrofitCommissionRateGruop[0].stateCode}}</span>
                    </div>
                  </div>
      
                  <div class="col-md-6">
                    <div class="row">
                      <label class="col-sm-5">Battery Type</label>
                      <span class="col-sm-7" style="float: left;  white-space: nowrap;">{{batteryRetrofitCommissionRateGruop[0].batteryType}}</span>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="row">
                      <label class="col-sm-5">Purchase Method</label>
                      <span class="col-sm-7">{{batteryRetrofitCommissionRateGruop[0].purchaseMethod}}</span>
                    </div>
                  </div>
                </div>
      
      
                <table class="my-table mat-table col-md-12 mt-3">
                  <thead>
                    <tr class="mat-header-row">
                      <th class="mat-header-cell" scope="col">Effective Start Date</th>
                      <th class="mat-header-cell" scope="col">Effective End Date</th>
                      <th scope="col">Battery Retrofit Sales Metric</th>
                    <th scope="col">Battery Retro fitCommission Amount</th>
                    </tr>
                  </thead>
                  <tbody>
                    <ng-container *ngFor="let tr of batteryRetrofitCommissionRateGruop">
                      <tr class="mat-row " (click)="groupClick(tr)">
                        <td class="mat-cell" data-td-head="Effective Start Date">{{tr[0].effectiveStartDate | date}}</td>
                        <td class="mat-cell" data-td-head="Effective End Date">{{tr[0].effectiveEndDate | date}}</td>
                      </tr>
                      <tr style="background-color: #FFF;"
                        *ngIf="batteryRetrofitCommissionRateSelectedGroup && tr[0].effectiveStartDate == batteryRetrofitCommissionRateSelectedGroup[0].effectiveStartDate">
                        <td colspan="2" class="custom-table p-3">
                          <table class="my-table mat-table col-md-12">
                            <thead>
                              <tr class="mat-header-row">
                                <th class="mat-header-cell"> State Code</th>
                              </tr>
                            </thead>
                            <tbody>
                              <tr class="mat-row " *ngFor="let tr of batteryRetrofitCommissionRateSelectedGroup">
                                <td class="mat-cell" data-td-head=" State Code"> {{tr.stateCode}}</td>
                             </tr>
                          </table>
      
                        </td>
                      </tr>
                    </ng-container>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>  
</div>














