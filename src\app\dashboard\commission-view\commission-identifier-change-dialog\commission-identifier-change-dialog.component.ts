import { Component, OnInit, Inject, ViewChild } from '@angular/core';
import { ICommissionIdentifierChangeCapture } from 'src/app/model/commission-identifier-change-capture.model';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { ApiService } from 'src/app/services/api.service';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-commission-identifier-change-dialog',
  templateUrl: './commission-identifier-change-dialog.component.html',
  styleUrls: ['./commission-identifier-change-dialog.component.css']
})
export class CommissionIdentifierChangeDialogComponent implements OnInit {
  elements: MatTableDataSource<ICommissionIdentifierChangeCapture> = new MatTableDataSource();
  displayColumns: string[] = ["applicationMetadataDisplayName","oldValue","newValue"];

  constructor(public dialogRef: MatDialogRef<CommissionIdentifierChangeDialogComponent>, @Inject(MAT_DIALOG_DATA) public data: ICommissionIdentifierChangeDialogData, private apiService: ApiService, private toastMsg: ToastrService) { }

  ngOnInit() {
  }

}

export interface ICommissionIdentifierChangeDialogData {
  changes: MatTableDataSource<ICommissionIdentifierChangeCapture>
}