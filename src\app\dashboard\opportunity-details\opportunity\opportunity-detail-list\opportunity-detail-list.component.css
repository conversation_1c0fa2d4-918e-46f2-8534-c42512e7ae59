.opportunity-table {
    max-width: 100%;
}

.opportunity-row {
    display: inline-flex;
    width: 100%;
    height: 100%;
    justify-content: flex-start;
}

.opportunity-list-row {
    width: 100%;
}

.opportunity-row-item {
    width: 50%;
    font-size: 12px;
    border-bottom: 1px solid #aaaaaa;
    white-space: nowrap;
}

.opportunity-row-title {
    font-weight: bold;
}

.opportunity-grid-tile {
}

.opportunity-grid-list {

}

.opportunity-list-header {
    color: white;
    width: 200px;
    margin-bottom: 2%;
}

.opportunity-list-container {
 
    margin-bottom: 0.5%;
    margin-top: 0.5%;
}

p.opportunity-row-item.opportunity-row-item {
    margin: 0;
}
.chk-bx{
    padding-left: 1px;
}
.lbl-chk{
    padding-right: 0;
    padding-left: 2px;
}
.lbl-chk-last{
    padding-right: 0;
    padding-left: 2px;
    margin-left: -5%;
}
.lbl2-chk{
    padding: 0;
    padding-left: 15px;
}