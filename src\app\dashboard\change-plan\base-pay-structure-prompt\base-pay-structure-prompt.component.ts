import { Component, OnInit, Input, Output, EventEmitter, SimpleChanges } from '@angular/core';
import { IRule } from 'src/app/model/rule.model';
import { IRulePrompt } from 'src/app/model/rule-prompt.model';
import { UntypedFormGroup } from '@angular/forms';
import { IBasePayStructureForm } from 'src/app/model/base-pay-structure.model';
import { IRuleItem } from 'src/app/model/rule-item.model';
import { ApiService } from 'src/app/services/api.service';
import { ToastrService } from 'ngx-toastr';
import { IBasePayStructure } from 'src/app/model/base-pay-structure.model';
import { IPayStreamItem } from 'src/app/model/base-pay-structure.model';
import { IRuleInput } from 'src/app/model/rule-input.model';

@Component({
  selector: 'app-base-pay-structure-prompt',
  templateUrl: './base-pay-structure-prompt.component.html',
  styleUrls: ['./base-pay-structure-prompt.component.css']
})
export class BasePayStructurePromptComponent implements OnInit {
  @Input() rule: IRule;
  @Output() rulePrompt: EventEmitter<IRulePrompt> = new EventEmitter<IRulePrompt>();
  promptFormGroup: UntypedFormGroup;
  basePayStructureForm: IBasePayStructureForm;
  ruleItems: IRuleItem[] = [];

  constructor(private apiService: ApiService, private toastMsg: ToastrService) { }

  ngOnInit() {

  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.rule) {
      // console.log(changes.rule.currentValue);
      if (changes.rule.currentValue.ruleTypeName == "Base Pay Structure") {
        this.ruleItems = [];
        this.getBasePayStructureForm();
        // console.log(changes.rule.currentValue.promptValues[changes.rule.currentValue.ruleId].ruleItems);
        if (changes.rule.currentValue.promptValues) {
          this.ruleItems = changes.rule.currentValue.promptValues[changes.rule.currentValue.ruleId].ruleItems;
        }
      }
    }
  }

  getBasePayStructureForm(): void {
    this.apiService.get(`BasePayStructures/${this.rule.ruleId}`)
      .subscribe(data => {
        if (data.statusCode === "201" && data.result) {
          // console.log("Base Pay Structure Result:", data.result);
          this.basePayStructureForm = <IBasePayStructureForm>{
            ruleId: data.result.ruleId,
            ruleName: data.result.ruleName,
            ruleTypeId: data.result.ruleTypeId,
            description: data.result.description,
            promptAssignPlan: data.result.promptAssignPlan,
            numberOfStages: data.result.numberOfStages,
            basePayStructures: data.result.basePayStructures.map(bps => {
              return <IBasePayStructure>{
                basePayStructureId: bps.basePayStructureId,
                basePayStructureName: bps.basePayStructureName,
                numberOfPayments: bps.numberOfPayments,
                startDate: bps.startDate,
                endDate: bps.endDate,
                payStream: bps.payStream.map(payStreamItem => {
                  return <IPayStreamItem>{
                    paymentNumber: payStreamItem.paymentNumber,
                    percentage: payStreamItem.percentage,
                    stage: payStreamItem.stage,
                    payBasedOn: payStreamItem.payBasedOn,
                    paymentTypeId: payStreamItem.paymentTypeId,
                    daysInAdvance: payStreamItem.daysInAdvance
                  }
                })
              }
            })
          }

          // console.log(this.basePayStructureForm);

          this.basePayStructureForm.basePayStructures.forEach(bps => {
            // console.log("Base Pay Structure Id", bps.basePayStructureId);

            this.addRuleItem(bps.basePayStructureId);
            this.addRuleItemInput(bps.basePayStructureId, "Start_Date");
          })

          // console.log("Rule Items", this.ruleItems);
        }
      }, (err: any) => {
        this.toastMsg.error(err.message, "Server Error!");
      });
  }

  addRuleItem(itemId: number) {
    if (this.ruleItems.find(x => x.itemId == itemId) == null) {
      this.ruleItems.push(<IRuleItem>{
        itemId: itemId,
        ruleItemInputs: []
      });
    }
  }

  addRuleItemInput(itemId: number, columnName: string) {
    if (itemId) {
      var item = this.ruleItems.find(item => item.itemId == itemId);
      if (item && item.ruleItemInputs.find(x => x.columnName == columnName) == null) {
        this.ruleItems.find(item => item.itemId == itemId).ruleItemInputs.push(<IRuleInput>{
          columnName: columnName,
          columnValue: null
        });
      }
    }
  }

  onChange(itemId: number, columnName: string, columnValue: any) {
    // console.log("Column Name", columnName);
    // console.log("Column Value", columnValue);
    // console.log("Item ID", itemId);

    if (itemId) {
      this.ruleItems.filter(item => item.itemId == itemId)[0].ruleItemInputs.filter(input => input.columnName == columnName)[0].columnValue = columnValue;
    }

    // console.log("Rule Items", this.ruleItems);

    this.rulePrompt.emit(<IRulePrompt>{
      commissionRuleId: this.rule.ruleId,
      ruleItems: this.ruleItems
    })
  }

  getRuleItemValue(itemId, columnName) {
    return this.ruleItems.find(x => x.itemId == itemId).ruleItemInputs.find(x => x.columnName == columnName).columnValue;
  }

}
