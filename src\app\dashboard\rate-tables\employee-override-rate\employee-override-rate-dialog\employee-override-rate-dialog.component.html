<p>The following submitted rates had errors</p>

  <mat-table [dataSource]="errors" matSort>
    <ng-container matColumnDef="reason">
      <th mat-header-cell *matHeaderCellDef> Reason </th>
      <td mat-cell *matCellDef="let element"> {{element.reason}} </td>
    </ng-container>

    <ng-container matColumnDef="paycomId">
      <th mat-header-cell *matHeaderCellDef> Paycom Id </th>
      <td mat-cell *matCellDef="let element"> {{element.rate.paycomId}} </td>
    </ng-container>

    <ng-container matColumnDef="employeeTitle">
      <th mat-header-cell *matHeaderCellDef> Employee Title </th>
      <td mat-cell *matCellDef="let element"> {{element.rate.employeeTitle}} </td>
    </ng-container>

    <ng-container matColumnDef="salesDivision">
      <th mat-header-cell *matHeaderCellDef> Sales Division </th>
      <td mat-cell *matCellDef="let element"> {{element.rate.salesDivision}} </td>
    </ng-container>

    <ng-container matColumnDef="salesOffice">
      <th mat-header-cell *matHeaderCellDef> Sales Office </th>
      <td mat-cell *matCellDef="let element"> {{element.rate.salesOffice}} </td>
    </ng-container>

    <ng-container matColumnDef="effectiveStartDate">
      <th mat-header-cell *matHeaderCellDef> Effective Start Date </th>
      <td mat-cell *matCellDef="let element"> {{element.rate.effectiveStartDate | date: 'MM-dd-yyyy'}} </td>
    </ng-container>

    <ng-container matColumnDef="effectiveEndDate">
      <th mat-header-cell *matHeaderCellDef> Effective End Date </th>
      <td mat-cell *matCellDef="let element"> {{element.rate.effectiveEndDate | date: 'MM-dd-yyyy'}} </td>
    </ng-container>


    <tr mat-header-row *matHeaderRowDef="columnsToDisplay"></tr>
    <tr mat-row *matRowDef="let myRowData; columns: columnsToDisplay"></tr>

  </mat-table>


