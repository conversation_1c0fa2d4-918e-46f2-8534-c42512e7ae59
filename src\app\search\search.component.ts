import { Component, OnInit, On<PERSON><PERSON>roy, ViewEncapsulation } from "@angular/core";
import { ApiService } from "../services/api.service";
import { ToastrService } from "ngx-toastr";
import { Router } from "@angular/router";
import { ISearchResult, ISearchValue } from '../model/search-result.model';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import * as _ from 'lodash';
import { UntypedFormControl } from '@angular/forms';
import { SharedSidebarService } from '../shared/sidebar-icon';
declare var $: any;

@Component({
  selector: "app-search",
  templateUrl: "./search.component.html",
  styleUrls: ["./search.component.css"],
  encapsulation: ViewEncapsulation.None
})
export class SearchComponent implements OnInit, OnDestroy {
  keyword: string = "";
  searchValues: ISearchValue[] = [];
  page: number = 1;
  ruleTypeId: number;
  dropdownList = [];
  selectedItems = []
  dropdownSettings:IDropdownSettings = {};
  filterArray:any[] = [];
  multiselectControl = new UntypedFormControl([]);

  constructor(private toastMsg: ToastrService, public apiService: ApiService, private router: Router, private sharedSidebarService: SharedSidebarService) {
  }

  clear() {
  }

  ngOnInit() {
    const urlString = window.location.href;
    const queryIndex = urlString.indexOf("?");
    const hashPart = urlString.slice(queryIndex + 1);
    const queryPart = hashPart.split("#")[0];
    const queryParams = new URLSearchParams(queryPart);
    const viewBaseFormula = queryParams.get("viewBaseFormula");
    const viewContact = queryParams.get("viewContact");
    const viewOpportunity = queryParams.get("viewOpportunity");
    const viewPlan = queryParams.get("viewPlan");
    const viewRule = queryParams.get("viewRule");

    let selectedItm = [];
    if (JSON.parse(viewBaseFormula)) {
      selectedItm.push({ item_id: 1, item_text: 'BaseFormula' })
    }
    if (JSON.parse(viewContact)) {
      selectedItm.push({ item_id: 2, item_text: 'Contact' })
    }
    if (JSON.parse(viewOpportunity)) {
      selectedItm.push({ item_id: 3, item_text: 'Opportunity' })
    }
    if (JSON.parse(viewPlan)) {
      selectedItm.push({ item_id: 4, item_text: 'Plan' })
    }
    if (JSON.parse(viewRule)) {
      selectedItm.push({ item_id: 5, item_text: 'Rule' })
    }
    if (JSON.parse(viewBaseFormula) && JSON.parse(viewPlan) && JSON.parse(viewContact) && JSON.parse(viewOpportunity) && JSON.parse(viewRule)) {
      selectedItm = [
        { item_id: 0, item_text: 'All' }
      ];
    }
    if (selectedItm.length == 0) {
      selectedItm = [
        { item_id: 0, item_text: 'All' }
      ];
    }
    this.selectedItems = selectedItm;


    this.dropdownList = [
      { item_id: 0, item_text: 'All' },
      { item_id: 1, item_text: 'BaseFormula' },
      { item_id: 2, item_text: 'Contact' },
      { item_id: 3, item_text: 'Opportunity' },
      { item_id: 4, item_text: 'Plan' },
      { item_id: 5, item_text: 'Rule' }
    ];
    
    this.filterArray = this.selectedItems;
    this.multiselectControl.patchValue(this.selectedItems);    

    this.dropdownSettings = {
      singleSelection: false,
      idField: 'item_id',
      textField: 'item_text',
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
      enableCheckAll: false,
      itemsShowLimit: 1,
      allowSearchFilter: false
    };
  }



  onItemSelect(item: any) {
    if (item.item_text == 'All') {
      this.multiselectControl.patchValue([item]);
      this.filterArray = [item]
    } else {
      this.filterArray = this.filterArray.filter(obj => obj.item_text !== 'All');
      this.filterArray.push(item);
      this.multiselectControl.patchValue(this.filterArray);
      if (this.filterArray.length == 5) {
        this.filterArray = [{ item_id: 0, item_text: 'All' }]
        this.multiselectControl.patchValue(this.filterArray);
      }
    }
  }
  onSelectAll(items: any) {
    this.filterArray = items;
  }
  onDeSelect(item) {
    if (item.item_text == 'All') {
      this.multiselectControl.patchValue([item]);
      this.filterArray = [item];
    } else if (this.filterArray.length == 1) {
      this.multiselectControl.patchValue([item]);
      this.filterArray = [item];
    }
    else {
      this.filterArray = this.filterArray.filter(obj => obj.item_id !== item.item_id);
    }
  }

  viewRule(ruleId: number) {
    this.router.navigate(["ui/commissions/viewRule", ruleId]);
  }

  viewBasePayStructure(ruleId: number) {
    this.router.navigate(["ui/commissions/viewBasePayStructure", ruleId]);
  }

  viewPaymentBook(ruleId: number) {
    this.router.navigate(["ui/commissions/viewPaymentBook", ruleId]);
  }

  viewPlan(planId: any) {
    this.router.navigate(["ui/commissions/viewPlan", planId]);
  }

  viewBaseFormula(formulaId: number) {
    this.router.navigate(["ui/commissions/viewBaseFormula", formulaId]);
  }

  viewOpportunity(oppId: number) {
    this.router.navigate(["ui/commissions/opportunitydetails", oppId]);
  }

  viewContact(contactId: number) {
    this.router.navigate(["ui/commissions/salesrep", contactId]);
  }

  searchValue(keyword: string) {
    if (keyword && keyword.length > 2) {
      const searchpayLoad = {
        keyword: keyword,
        viewRule: this.apiService.checkPermission('ViewRule'),
        viewOpportunity: this.apiService.checkPermission('ViewOpportunityDetail'),
        viewContact: this.apiService.checkPermission('ViewSalesRepDetail'),
        viewPlan: this.apiService.checkPermission('ViewPlan'),
        viewBaseFormula: this.apiService.checkPermission('ViewBaseFormula')
      };
      if (!keyword) {
        this.toastMsg.error("Please enter the value to search");
      } else {
        this.apiService.post("SearchContent/", searchpayLoad).subscribe(
          data => {
            if (data.statusCode === "200" || data.statusCode === "201") {
              var values = data.result.map(x => { return <ISearchResult>x });

              values = _.groupBy(values, 'objectType');

              this.searchValues = Object.keys(values).map(k => {
                return <ISearchValue>{
                  type: k,
                  values: values[k]
                };
              })

              if (this.keyword == undefined) {
                this.keyword = "";
              }
            } else {
              this.toastMsg.error("Server", "Error!");
            }
          },
          (err: any) => {
            this.toastMsg.error(err.message, "Error!");
          }
        );
      }
    }
  }

  onClick(item: ISearchResult): void {
    this.sharedSidebarService.updateSidebarToggle("close");
    this.sharedSidebarService.updateMenuToggle("open");

    switch (item.objectType) {
      case "CommissionRule":
        switch (item.ruleType) {
          case "Base Pay Structure":
            this.viewBasePayStructure(item.objectId);
            break;

          case "Payment Book":
            this.viewPaymentBook(item.objectId);
            break;

          default:
            this.viewRule(item.objectId);
            break;
        }
        break;

      case "Opportunity":
        this.viewOpportunity(item.objectId);
        break;

      case "Plan":
        this.viewPlan(item.objectId);
        break;

      case "Formula":
        this.viewBaseFormula(item.objectId);
        break;

      case "Contact":
        this.viewContact(item.objectId);
        break;

      default:
        break;
    }
  }

  ngOnDestroy() {
    localStorage.setItem('opportunity', '/ui/commissions/search');
  }
  search(keyword: string) {    
    let queryParams;    
    if (this.filterArray.length == 1 && this.filterArray[0].item_text == 'All') {
      
      queryParams = {
        viewBaseFormula: true,
        viewContact: true,
        viewOpportunity: true,
        viewPlan: true,
        viewRule: true,
      };
      this.router.navigate(["ui/commissions/search", keyword], { queryParams });
    } else {
      const namesToCheck = ['BaseFormula', 'Contact', 'Opportunity', 'Plan', 'Rule'];
      const resultObject: any = {};
      namesToCheck.forEach(name => {
        resultObject[name] = this.filterArray.some(obj => obj.item_text === name);
      });
      console.log(resultObject);
      queryParams = {
        viewBaseFormula: resultObject.BaseFormula,
        viewContact: resultObject.Contact,
        viewOpportunity: resultObject.Opportunity,
        viewPlan: resultObject.Plan,
        viewRule: resultObject.Rule,
      };      
      this.router.navigate(["ui/commissions/search", keyword], { queryParams });
    }
  }
}