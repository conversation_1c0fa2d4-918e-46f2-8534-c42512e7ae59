import { Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ApiService } from 'src/app/services/api.service';
import { ToastrService } from 'ngx-toastr';
import { IContactPaymentBook, IPaymentBookTransaction } from 'src/app/model/pay-book.model';
import { MatLegacyPaginator } from '@angular/material/legacy-paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import {  PaymentContact, PaymentPayload } from 'src/app/model/payment.model';
import { environment } from "src/environments/environment";
import { HttpClient, HttpParams } from '@angular/common/http';
import * as FileSaver from 'file-saver';

@Component({
  selector: 'app-payment-book-dashboard',
  templateUrl: './payment-book-dashboard.component.html',
  styleUrls: ['./payment-book-dashboard.component.css']
})
export class PaymentBookDashboardComponent implements OnInit {
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatLegacyPaginator, {static: true}) paginator: MatLegacyPaginator;
  contactId: number;
  roleName;
  searchText: string = "";
  contactPaymentBook: IContactPaymentBook = <IContactPaymentBook>{};
  transactions: MatTableDataSource<IPaymentBookTransaction> = new MatTableDataSource();
  // pageSizeOptions: number[] = [5, 10, 25, 100];
  pageSizeOptions: number[] = [5, 10]; // dilip
  columns: string[] = ["selected", "dateProcessed", "opportunityName", "commissionTransactionTypeName", "paymentTypeName", "credit", "debit","transactionName", "paymentNote", "paymentReversalNote"];
  withdrawalAmount:string = '';
  withdrawalName:string = '';
  paymentReversalNotes:string = '';
  AllowUsers: boolean = false; 
  isCheckboxDisabled: boolean = false;
  constructor(private activatedRoute: ActivatedRoute, public apiService: ApiService, private router: Router, private toastMsg: ToastrService, private http: HttpClient) {
    // this.contactId = this.activatedRoute.snapshot.params.contact_id;
  }

  ngOnInit() {
    // dilip UAM issues
    if (!this.apiService.checkPermission('ContactPaymentBook')) {
      this.apiService.goBack();
        this.toastMsg.error("Insufficient Permissions. Please make sure your account can access this information.", "Permissions");
    }
    this.roleName = localStorage.getItem('role');
    this.activatedRoute.params.subscribe(params => {
      this.contactId = params.contact_id;

      this.getContactPaymentBook();
      this.getPaymentBookTransactions();
    });
  }

  getContactPaymentBook() {
    if (this.contactId) {
      this.apiService.get(`PaymentBook/GetContactPaymentBook/${this.contactId}`)
        .subscribe(data => {
          if (data && data.result) {
            this.contactPaymentBook = <IContactPaymentBook>data.result;
            this.applyFilter(this.searchText);
            // console.log("Contact Payment Book", this.contactPaymentBook);
          }
        }, err => {
          this.toastMsg.error(err.message, "Error!");
        })
    }
  }
  
  applyFilter(input: any): any {
    var filterValue: string;
    if (typeof input === "string") {
      filterValue = input;
    } else {
      filterValue = (input.target as HTMLInputElement).value;
    }
    this.transactions.filter = filterValue.trim().toLowerCase();
  }

  getPaymentBookTransactions() {
    if (this.contactId) {
      this.apiService.get(`PaymentBookTransactions/${this.contactId}`)
        .subscribe(data => {
          if (data && data.result) {
            var trans = data.result.map((trans: IPaymentBookTransaction) => {
              return trans;
            });

            this.transactions = new MatTableDataSource(trans);  
            this.transactions.paginator = this.paginator;
            this.transactions.sort = this.sort; 
          }
        }, err => {
          this.toastMsg.error(err.message, "Error!");
        })
    }
  }

  getCurrentBalance() {
    if (this.transactions && this.transactions.data.length > 0) {
      var credits = this.transactions.data.filter(x => x.debitCredit == "C").map(x => {return x.amount});
      if (credits && credits.length > 0) {
        var totalCredit = credits.reduce((a,b) => a + b); 
      } else {
        var totalCredit = 0;
      }

      var debits = this.transactions.data.filter(x => x.debitCredit == "D").map(x => {return x.amount})
      if (debits && debits.length > 0){
        var totalDebit = debits.reduce((a,b) => a + b); 
      } else {
        var totalDebit = 0;
      }

      return totalCredit - totalDebit;
    }
  }

  checkSelected() {    
    return (this.AllowUsers  &&  this.withdrawalAmount != '');
  }


  submitPaymentBookWithdrawalsPayBook() {    
    let name:string = "payBook -"+ this.withdrawalName ; 
    let WithdrawalAmount:number = parseFloat (this.withdrawalAmount);    
    let contacts: PaymentContact[] = [{ contactId: this.contactId }]    
    let paymentPayload:PaymentPayload = {withdrawalName: name, paymentContact: contacts, allowUsersOverride: false, withdrawalAmount: WithdrawalAmount, paymentWithdrawalOverrides: []};
    this.http.post(`${environment.apiBaseUrl}Payments/SubmitPaymentBookWithdrawal`, paymentPayload, { responseType: 'blob' })
      .subscribe(data => {
        this.toastMsg.success("Successfully submitted payments.");
        this.downLoadFile(data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;");
        this.getContactPaymentBook();
        this.getPaymentBookTransactions();
        this.AllowUsers = false;
        this.withdrawalAmount = '';
        this.withdrawalName = '';
      }, err => {
        this.toastMsg.error(`${err} - Error processing payment`, "Error!");
      });
  }

    /**
   * Method is use to download file.
   * @param data - Array Buffer data
   * @param type - type of the document.
   */
     downLoadFile(data: any, type: string) {
      let blob = new Blob([data], { type: type });
      let date: Date = new Date();
  
      FileSaver.saveAs(blob, `Pre_Extracted_${date}.xlsx`);
     }

     
     downLoadTxnFile(data: any, type: string) {
      let blob = new Blob([data], { type: type });
      let date: Date = new Date();
  
      FileSaver.saveAs(blob, `Payment_Book_Transaction_${date}.xlsx`);
     }
  
  downLoadTransactionFile() {
    this.http.get(`${environment.apiBaseUrl}PaymentBookTransactions/GetPaymentBookTransactionsExcel/${this.contactId}`, { responseType: 'blob' })
      .subscribe(data => {
        this.downLoadTxnFile(data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      });
  }

  onSelectionChange() {
    let balances = this.transactions.filteredData.filter(bal => bal.selected);
    // console.log("balances = > 167  "+ JSON.stringify(balances));
  }

  checkReverseSelected() {
    return (this.transactions && this.transactions.data.filter(bal => bal.selected).length > 0);
  }

  checkboxDisabled(row) {
    
    if ((this.roleName == 'Finance Admin With UAM' && row.commissionTransactionTypeName == "Payment Book Withdrawal" && row.reversedPaymentBookTransactionId == 0) ||
      row.commissionTransactionTypeName == "Payment Approval" && row.paymentStatus == "Approved") {
      this.isCheckboxDisabled = false;
      return false;
    }
    if (row.commissionTransactionTypeName == "Payment Approval" && row.paymentStatus == "Approved") {
      this.isCheckboxDisabled = false;
      return false;
    }
    this.isCheckboxDisabled = true;
    return true;
  }

  ReversePayment() {
    let pbts = this.transactions.filteredData.filter(bal => bal.selected);
    let reversalPayload = JSON.parse(JSON.stringify(pbts));
    reversalPayload.forEach(obj => obj.paymentReversalNote = this.paymentReversalNotes);
    console.log(reversalPayload)
   this.apiService.post("Payments/ReversePaymentBookTransactions", reversalPayload)
    .subscribe(data => {
      this.toastMsg.success("Successfully reverted selected payment transactions");
      this.getContactPaymentBook();
      this.getPaymentBookTransactions();
    }, err => {
      this.toastMsg.error(err.message, "Error!");
    })
  }

}


