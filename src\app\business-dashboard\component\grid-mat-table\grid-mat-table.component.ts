import { Component, EventEmitter, Input, OnInit,Output,ViewChild } from '@angular/core';
import { MatLegacyTableDataSource, MatLegacyTableModule } from '@angular/material/legacy-table';
import { GridFilterComponent } from '../grid-filter/grid-filter.component';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatLegacyPaginator, MatLegacyPaginatorModule } from '@angular/material/legacy-paginator';
import { MatLegacyDialog } from '@angular/material/legacy-dialog';
import { TableFilterPipe } from 'src/app/pipe/table-filter.pipe';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';


@Component({
  selector: 'app-grid-mat-table',
  standalone: true,
  imports: [CommonModule,FormsModule,MatLegacyTableModule,MatSortModule,MatLegacyPaginatorModule,MatIconModule,RouterModule],
  templateUrl: './grid-mat-table.component.html',
  styleUrls: ['./grid-mat-table.component.css']
})
export class GridMatTableComponent implements OnInit {
  @Input() gridData:any;
  @Input() columnData:any;
  @Input() displayColumnData:any;
  @Input() dateFields:any;
  @Input() tableWidth:string;
  @Input() isScrollWidth:boolean = true;
  @Input() isSearchAvailable:boolean = false;
  @Output() rowClick = new EventEmitter<any>();
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatLegacyPaginator, {static: true}) paginator: MatLegacyPaginator;
  dataSource:any;
  columnNames:any;
  displayedColumns:any;
  dateFieldData:any;
  filterData:any[]=[];
  filterResult:any[]=[];
  searchText: string = "";
  constructor( private dialog: MatLegacyDialog,private pipe: TableFilterPipe) { }

  ngOnInit() {
  }
  ngOnChanges() {
    this.getDataSource();
  }
  getDataSource(){
    if(this.gridData){
      this.dataSource = new MatLegacyTableDataSource(this.gridData);
      this.columnNames = this.columnData;
      this.displayedColumns = this.displayColumnData;
      this.dateFieldData = this.dateFields;
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
    }
  }
  onSortChange(event:any){
    let dateField = this.dateFieldData.filter(s=>s.id === event.active).map(c=> c.id).toString();
    if(dateField){
      this.dataSource.sortingDataAccessor = (item, property) => {
        switch (property) {
          case dateField:
            return new Date(item[dateField]).toISOString();
          default:
            return item[property];
        }
      };
      this.dataSource.sortingFn = (a: any, b: any, active: string, direction: string) => {
        if (active === dateField) {
          const dateA = new Date(a);
          const dateB = new Date(b);
          if (direction === 'asc') {
            return dateA.getTime() - dateB.getTime();
          } else {
            return dateB.getTime() - dateA.getTime();
          }
        } else {
          return this.dataSource.sortingDataAccessor(a, active) > this.dataSource.sortingDataAccessor(b, active) ? 1 : -1;
        }
      };
    }
    
    
  }
  onClickFilterIcon(event:MouseEvent,selectedColumnId:string,selectedColumnList:any){
    const iconElement: HTMLElement = event.target as HTMLElement;
    const iconPosition = iconElement.getBoundingClientRect();
    const availableWidth = window.innerWidth - 250 - 10;
    const popupLeft = Math.max(availableWidth, 10);
    let posLeft = 0;
    let posTop = 0;
    posTop = iconPosition.top + 30;
    posLeft = iconPosition.left > popupLeft ? popupLeft: iconPosition.left;
    let dialogRef = this.dialog.open(GridFilterComponent, {
      width: '250px',
      data: {data1: this.filterData,data2: selectedColumnId,data3:selectedColumnList},
      position: {top:posTop.toString()+"px", left: posLeft.toString()+"px"},
      backdropClass: 'dialog-bg-trans'  
    });
    dialogRef.afterClosed().subscribe(result => {
      if(result.filterValue){
        this.filterData.push({columnId:result.columnId,filterValue:result.filterValue,filterOperator:result.filterOperator})
      }
      else{
        this.filterData = this.filterData.filter(s=>s.columnId!= selectedColumnId);
      }
      this.filterData.forEach((c, i) => {
        var data = this.filterData.filter(s => s.columnId == c.columnId)
        if (data.length > 1)
           this.filterData.splice(i, 1)
      })
      this.fetchFilterTable();
      this.dataSource = new MatLegacyTableDataSource(this.filterResult);
      this.dataSource.sort = this.sort;
      this.dataSource.paginator = this.paginator;
    });
  }
  fetchFilterTable(){
    if(this.filterData.length > 0){
      this.filterResult =[];
      this.filterResult = this.gridData;
      this.filterData.forEach((s,i)=>{
        this.filterResult = this.filterResult.filter((item:any) =>{
          const columnValue = item[s.columnId] ? item[s.columnId].toString().trim().toLowerCase():'';
          if(s.filterOperator =='equals'){
            return columnValue === s.filterValue.toLowerCase();
          }
          if(s.filterOperator =='notequals'){
            return columnValue != s.filterValue.toLowerCase();
          }
          if(s.filterOperator =='contains'){
            return columnValue.includes(s.filterValue.toLowerCase());
          }
          if(s.filterOperator =='notcontains'){
            return !columnValue.includes(s.filterValue.toLowerCase());
          }
          return true
        });
      })
    }
    else{
      this.filterResult = this.gridData
    }
  }
  onEnableIcons(columnId:string){
    return this.filterData.some(s=>s.columnId.includes(columnId))
  }
  onClearFilter(){
    this.filterData =[];
    this.getDataSource();
  }
  searchForItem(): void {
    let filteredResults: Element[] = [];
    if (this.searchText == '') {
      this.dataSource = new MatLegacyTableDataSource(this.gridData);
      this.dataSource.sort = this.sort;
      this.dataSource.paginator = this.paginator;
    } else {
      filteredResults = this.pipe.transform(this.gridData, this.searchText);
      this.dataSource = new MatLegacyTableDataSource(filteredResults);
      this.dataSource.sort = this.sort;
      this.dataSource.paginator = this.paginator;
    }
  }
  onRowClick(event){
    this.rowClick.emit(event);
  }
}
