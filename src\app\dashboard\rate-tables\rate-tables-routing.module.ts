import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AuthGuard } from '../../guards/auth.guard';
import { RateTablesComponent } from './rate-tables.component';
import { TerritoryRateMaintenanceComponent } from './territory-rate-maintenance/territory-rate-maintenance.component';
import { FinancePartnerDeductionMaintenanceComponent } from './finance-partner-deduction-maintenance/finance-partner-deduction-maintenance.component';
import { ModuleDeductionMaintenanceComponent } from './module-deduction-maintenance/module-deduction-maintenance.component';
import { InverterDeductionMaintenanceComponent } from './inverter-deduction-maintenance/inverter-deduction-maintenance.component';
import { InstallationTypeDeductionMaintenanceComponent } from './installation-type-deduction-maintenance/installation-type-deduction-maintenance.component';
import { PurchaseMethodDeductionMaintenanceComponent } from './purchase-method-deduction-maintenance/purchase-method-deduction-maintenance.component';
import { ProductPurchaseMethodDeductionMaintenanceComponent } from './product-purchase-method-deduction-maintenance/product-purchase-method-deduction-maintenance.component';
import { PermitDeductionMaintenanceComponent } from './permit-deduction-maintenance/permit-deduction-maintenance.component';
import { PpaBonusRateMaintenanceComponent } from './ppa-bonus-rate-maintenance/ppa-bonus-rate-maintenance.component';
import { BatteryCommissionRateMaintenanceComponent } from './Battery_Commission_Rate-maintenance/Battery_Commission_Rate-maintenance.component';
import { OutReachConfigurationComponent } from './out-reach-configuration-maintenance/out-reach-configuration-maintenance.component';
import {TeiredOverageComponent} from './teired-overage/teired-overage-maintenance.component';
import { PpwBonusRateMaintenanceComponent } from './ppw-bonus-rate-maintenance/ppw-bonus-rate-maintenance.component';
import { EmployeeOverrideRateComponent } from './employee-override-rate/employee-override-rate.component';
import { EmployeeOverrideRoofRateComponent } from './employee-override-roof-rate/employee-override-roof-rate.component';
import { EmployeeOverrideRateTierComponent } from './employee-override-rate-tier/employee-override-rate-tier.component';
import { EmployeeRoleComponent } from './employee-role/employee-role.component';
// import { EmployeeOverrideRateDialogComponent } from './employee-override-rate/employee-override-rate-dialog/employee-override-rate-dialog.component';
// import { EmployeeOverrideRoofRateDialogComponent } from './employee-override-roof-rate/employee-override-roof-rate-dialog/employee-override-roof-rate-dialog.component';

import { BatteryRetrofitCommissionRateMaintenanceComponent } from './battery-retrofit-commission-rate-maintenance/battery-retrofit-commission-rate-maintenance.component';
import { PPABonusFlatRateMaintenanceComponent } from './ppa-bonus-flat-rate-maintenance/ppa-bonus-flat-rate-maintenance.component';
import { BatteryPurchaseMethodDeductionMaintetanceComponent } from './battery-purchase-method-deduction-maintetance/battery-purchase-method-deduction-maintetance.component';
import { RoofCommissionRatesComponent } from './roof-commission-rates/roof-commission-rates.component';
import { RoleRateComponent } from './role-rate/role-rate.component';
import { TieredAmountComponent } from './tiered-amount/tiered-amount.component';
import { BatteryEmployeeRoleComponent } from './battery-employee-role/battery-employee-role.component';
import { RoofingMonthlyOverrideComponent } from './roofing-monthly-override/roofing-monthly-override.component';
import { SplitCommissionRatesComponent } from './split-commission-rates/split-commission-rates.component';
const routes: Routes = [
  { path: '', redirectTo: 'list', pathMatch: 'full'},
  { path: 'list', component: RateTablesComponent, canActivate:[AuthGuard] },
  { path: 'territoryrate', component: TerritoryRateMaintenanceComponent, canActivate:[AuthGuard] },
  { path: 'financepartnerdeduction', component: FinancePartnerDeductionMaintenanceComponent, canActivate:[AuthGuard] },
  { path: 'modulededuction', component: ModuleDeductionMaintenanceComponent, canActivate:[AuthGuard] },
  { path: 'inverterdeduction', component: InverterDeductionMaintenanceComponent, canActivate:[AuthGuard] },
  { path: 'installationtypededuction', component: InstallationTypeDeductionMaintenanceComponent, canActivate:[AuthGuard] },
  { path: 'purchasemethoddeduction', component: PurchaseMethodDeductionMaintenanceComponent, canActivate:[AuthGuard] },
  { path: 'productpurchasemethoddeduction', component: ProductPurchaseMethodDeductionMaintenanceComponent, canActivate:[AuthGuard] },
  { path: 'batterypurchasemethoddeduction', component: BatteryPurchaseMethodDeductionMaintetanceComponent, canActivate:[AuthGuard] },
  { path: 'permitdeduction', component: PermitDeductionMaintenanceComponent, canActivate:[AuthGuard] },
  { path: 'ppabonusrate', component: PpaBonusRateMaintenanceComponent, canActivate: [AuthGuard] },
  { path: 'ppabonusflatrate', component: PPABonusFlatRateMaintenanceComponent, canActivate: [AuthGuard] },
  { path: 'BatteryCommissionRate', component:BatteryCommissionRateMaintenanceComponent, canActivate: [AuthGuard] },
  { path: 'ppwbonusrate', component: PpwBonusRateMaintenanceComponent, canActivate: [AuthGuard] },
  { path: 'tieredoverage', component: TeiredOverageComponent, canActivate:[AuthGuard] },
  { path: 'outreachconfig', component: OutReachConfigurationComponent, canActivate:[AuthGuard] },
  { path: 'employeeoverriderates', component: EmployeeOverrideRateComponent },
  { path: 'employeeoverrideroofrates', component: EmployeeOverrideRoofRateComponent },
  { path: 'employeeoverrideratestier', component: EmployeeOverrideRateTierComponent},
  { path: 'employeerole', component: EmployeeRoleComponent},
  // { path: 'employeeoverrideratedialog', component: EmployeeOverrideRateDialogComponent},
  // { path: 'employeeoverrideroofratedialog', component: EmployeeOverrideRoofRateDialogComponent},
  { path: 'batteryretrofitcommissionsrate', component: BatteryRetrofitCommissionRateMaintenanceComponent},
  { path: 'roofcommissionrate', component: RoofCommissionRatesComponent},
  { path: 'rolerate', component: RoleRateComponent},
  { path: 'tieredamount', component: TieredAmountComponent},
  { path: 'batteryemplyeerole', component: BatteryEmployeeRoleComponent},
  { path: 'roofingmonthlyoverride', component: RoofingMonthlyOverrideComponent},
  { path: 'splitcommissionrates', component: SplitCommissionRatesComponent},
]; 
@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class RateTablesRoutingModule { }