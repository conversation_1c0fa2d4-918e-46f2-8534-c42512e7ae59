<div class="page-title col-md-12 ">
  <h1> Lead Source Qualifiers</h1>
  <div class="breadcrumbs">
    <a href="#">Home</a>/<span>Lead Source Qualifiers</span>
  </div>
</div>
<div class="content">

  <div class="card">
    <div class="card-header-info">
      <h4 class="card-title no-hover-effect">Lead Source Qualifiers</h4>
    </div>
    <div class="card-body">
      <div class="row">
        <div class="col-md-12">
          <div class="float-right">
            <button class="btn btn-primary" (click)="filter = !filter">
              <i class="material-icons">filter_list</i>
              Filter
            </button>
          </div>
          <div class="form-group  col-sm-3 input-group  float-right pr-0 mr-2">
            <input class="custom-input" type="text" id="searchTextId" [(ngModel)]="searchText"
              (keyup)="applyFilter($event)" name="searchText" placeholder="Search">
            <span class="input-group-icon">
              <i class="fas fa-search"></i>
            </span>
          </div>
        </div>
      </div>
      <ng-container *ngIf="filter">
        <div class="gray-bg row">
          <div class="col-md-12 pt-3 pb-3 gray-bg">
            <div class="row filter-row">

              <div class="form-group col-md-3">
                <label class="bmd-label-floating">Lead Source Modifier Type</label>
                <select class="custom-select" [(ngModel)]="selectedLeadSourceModifierTypeId"
                  (change)="onChangeFilter()">
                  <option [value]="0">--NO FILTER--</option>
                  <option *ngFor="let st of dropdowns.modifierTypes" value="{{st.leadSourceCommissionModifierTypeId}}">
                    {{st.leadSourceCommissionModifierTypeName}}
                  </option>
                </select>
              </div>

              <div class="form-group col-md-3">
                <label class="bmd-label-floating">Lead Source</label>
                <select class="custom-select" [(ngModel)]="selectedLeadSourceId" (change)="onChangeFilter()">
                  <option [value]="0">--NO FILTER--</option>
                  <option *ngFor="let uc of dropdowns.leadSources" value="{{uc.leadSourceId}}">
                    {{uc.leadSourceName}}
                  </option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </ng-container>
      <table mat-table [dataSource]="leadSourceCommissionsModifiers" matSort class="my-table mt-3" style="width: 100%">
        <ng-container matColumnDef="selected">
          <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Select </th>
          <td mat-cell *matCellDef="let element">
            <section class="checkbox-section">
              <mat-checkbox [(ngModel)]="element.selected" (change)="onSelectionChange()">
              </mat-checkbox>
            </section>
          </td>
        </ng-container>

        <ng-container matColumnDef="modifierType">
          <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header>
            Lead Source Modifier
            Type
          </th>
          <td data-td-head="Lead Source Modifier Type" mat-cell *matCellDef="let element">
            {{element.modifierType}}
          </td>
        </ng-container>

        <ng-container matColumnDef="leadSource">
          <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Lead Source </th>
          <td data-td-head=" Lead Source" mat-cell *matCellDef="let element">
            {{element.leadSource}}
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="leadSourceCommissionModifiers"></tr>
        <tr mat-row *matRowDef="let row; columns leadSourceCommissionModifiers;"></tr>
      </table>

      <mat-paginator [pageSize]="pageSize" [pageSizeOptions]="pageSizeOptions">
      </mat-paginator>

      <div>
      </div>


      <div *ngIf="apiService.checkPermission('CreateRateTables')">
        <button class="btn btn-danger" (click)="deleteSelected()" [disabled]="!checkSelected()">
          <i class="material-icons">clear</i>Delete ({{getNumberSelected()}})
          Selected
        </button>
        <a class="btn btn-primary float-right" (click)="addInd = !addInd" *ngIf="!addInd">
          <i class="material-icons pointer">add_circle</i> Add
        </a>
        <a class="btn btn-primary float-right" (click)="addInd = !addInd" *ngIf="addInd">
          <i class="material-icons pointer">remove_circle</i> Hide
        </a>

      </div>
    </div>
  </div>

  <div class="card" *ngIf="addInd">
    <div class="card-header-info">
      <h4 class="card-title no-hover-effect">Add Lead Source Qualifiers </h4>
    </div>
    <div class="card-body">

      <form [formGroup]="leadSourceForm" (ngSubmit)="onSubmit()" class="w-100">
        <div class="row">
          <div class="form-group col-md-4">
            <div class="row">
              <label class="col-sm-5">Lead Source </label>
              <div class="col-sm-7">
                <select class="custom-select" name="utility_company_dropdown" formControlName="leadSource"
                  data-style="btn btn-link" id="utility_company_dropdown">
                  <option *ngFor="let uc of dropdowns.leadSources" value="{{uc.leadSourceId}}">
                    {{uc.leadSourceName}}
                  </option>
                </select>
              </div>
            </div>
          </div>
          <div class="form-group col-md-4">
            <div class="row">
              <label class="col-sm-5">Lead Source Modifier Type</label>
              <div class="col-sm-7">
                <select class="custom-select" name="modifier_type_dropdown" formControlName="leadSourceModifierType"
                  data-style="btn btn-link" id="modifier_type_dropdown">
                  <option *ngFor="let st of dropdowns.modifierTypes" value="{{st.leadSourceCommissionModifierTypeId}}">
                    {{st.leadSourceCommissionModifierTypeName}}
                  </option>
                </select>
              </div>
            </div>
          </div>
          <div class="col-md-12 text-right">
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-plus"></i> Add Lead
              source
            </button>
          </div>

        </div>
      </form>


    </div>
  </div>