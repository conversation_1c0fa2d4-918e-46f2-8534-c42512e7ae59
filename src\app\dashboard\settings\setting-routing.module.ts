import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AuthGuard } from 'src/app/guards/auth.guard';
import { NotificationSettingsComponent } from './components/notification-settings/notification-settings.component';


const routes: Routes = [
  { path: '', redirectTo: 'notification', pathMatch: 'full'},
  { path: 'notification', component: NotificationSettingsComponent, canActivate:[AuthGuard] },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class SettingRoutingModule { }
