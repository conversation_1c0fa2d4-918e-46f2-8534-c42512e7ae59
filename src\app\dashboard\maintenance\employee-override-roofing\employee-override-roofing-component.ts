import { Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, FormGroup, Validators } from "@angular/forms";
import { ApiService } from "../../../services/api.service";
import { ToastrService } from 'ngx-toastr';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { DataSource } from '@angular/cdk/table';

import { TableFilterPipe } from '../../../pipe/table-filter.pipe';
import { DatePipe, PercentPipe, CurrencyPipe } from '@angular/common';
import { MatLegacyDialog } from '@angular/material/legacy-dialog';
import { HttpParams, HttpClient } from '@angular/common/http';
import { ApiResponse } from '../../../services/api.response';
import { environment } from '../../../../environments/environment';
import { IEmployeeOverrideRoofing } from '../../../model/employee-override-roofing';
import * as FileSaver from 'file-saver';

@Component({
    selector: 'app-employee-override-roofing',
    templateUrl: './employee-override-roofing-component.html',
    styleUrls: ['./employee-override-roofing-component.css']
})
export class EmployeeOverrideRoofingComponent implements OnInit {
    selectedYear: number;
    quarters = [{
        id: "Q1",
        value: 1
        },
        {
        id: "Q2",
        value: 2
        }, 
        {
        id: "Q3",
        value: 3
        },
        {
        id: "Q4",
        value: 4
        }];
    selectedQuarter: number;
    constructor(public apiService: ApiService, private toastMsg: ToastrService, private formBuilder: UntypedFormBuilder, private pipe: TableFilterPipe, private http: HttpClient,
        private datePipe: DatePipe, private percentPipe: PercentPipe, private currencyPipe: CurrencyPipe, private dialog: MatLegacyDialog) {
    }

    ngOnInit() {
        if (!this.apiService.checkPermission('ViewRateTables')) {
            this.apiService.goBack();
            this.toastMsg.error("Insufficient Permissions. Please make sure your account can access this information.", "Permissions");
        }
    }

    
  onChangeQuarter(quarter: any) {
    this.selectedQuarter = quarter.target.value;
  }

    onSubmit() {
        if (!(this.selectedYear > 2000 && this.selectedYear < 2099)) {
            this.toastMsg.error('Please enter valid year number between 2000 and 2099');
            return;
        }

        if(this.selectedQuarter === undefined){
            this.toastMsg.error('Please select a Quarter from the dropdown.');
            return;
        }

        var body = {
            Quarter: this.selectedQuarter, 
            Year: this.selectedYear
        }

        this.apiService.post('EmployeeOverrideRoofing', body)
            .subscribe(data => {
                this.toastMsg.success('Employee override roofing snapshot taken successfully. Please check dynamic report to see the snapshot.');
                }, (err: any) => {
                this.toastMsg.error(err.message, 'Server Error!');
            });
    }
}
