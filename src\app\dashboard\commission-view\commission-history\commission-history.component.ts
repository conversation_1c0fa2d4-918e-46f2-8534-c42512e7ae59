import { Component, OnInit, ViewChild, Input } from '@angular/core';
import { MatLegacyPaginator } from '@angular/material/legacy-paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { ICommissionHistory } from 'src/app/model/commission-history.model';
import { ApiService } from 'src/app/services/api.service';
import { ToastrService } from 'ngx-toastr';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-commission-history',
  templateUrl: './commission-history.component.html',
  styleUrls: ['./commission-history.component.css']
})
export class CommissionHistoryComponent implements OnInit {
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatLegacyPaginator, {static: true}) paginator: MatLegacyPaginator;
  @Input() commissionId: number;
  showCommissionHistory: boolean = false;
  pageSizeOptions: number[] = [5, 10, 25, 100];
  commissionHistoryElements: MatTableDataSource<ICommissionHistory> = new MatTableDataSource();
  commissionHistoryColumns: string[] = ["userModifiedTimestamp", "commissionAmount", "commissionFinalized", "commissionOverridden", "commissionNote"];


  constructor(public apiService: ApiService, private toastMsg: ToastrService, private http: HttpClient) { }

  ngOnInit() {
    this.getCommissionHistory(this.commissionId);
  }

  getCommissionHistory(commissionId: number) {
    this.http.get<ICommissionHistory[]>(`${environment.apiBaseUrl}CommissionHistory/${commissionId}`)
      .subscribe(data => {
        if (data) {
          this.commissionHistoryElements = new MatTableDataSource(data);
          this.commissionHistoryElements.sort = this.sort;
          this.commissionHistoryElements.paginator = this.paginator;
        }
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      });
  }

}
