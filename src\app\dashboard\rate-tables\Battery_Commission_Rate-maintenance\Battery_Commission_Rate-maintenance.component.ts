import { Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators, UntypedFormControl, AbstractControl } from "@angular/forms";
import { ApiService } from "../../../services/api.service";
import { ToastrService } from 'ngx-toastr';
import { groupBy } from '../../../shared/group-by';
import { getControlName } from '../../../shared/get-control-name';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { DataSource } from '@angular/cdk/table';
import { MatLegacyPaginator } from '@angular/material/legacy-paginator';
import { TableFilterPipe } from '../../../pipe/table-filter.pipe';
import { DatePipe, PercentPipe, CurrencyPipe } from '@angular/common';
// import { StringMap } from '@angular/compiler/src/compiler_facade_interface';
import { MatLegacyDialog } from '@angular/material/legacy-dialog';
import { BatteryRateMaintenanceDialogComponent } from '../Battery_Commission_Rate-maintenance-dialog/Battery_Commission_Rate-maintenance-dialog.component';  // check this Add dialog here
import { maxPurchaseMethodDeductionDate } from 'src/app/shared/validators';

@Component({
  selector: 'app-Battery_Commission_Rate-maintenance',
  templateUrl: './Battery_Commission_Rate-maintenance.html',
  styleUrls: ['./Battery_Commission_Rate.component-maintenance.css']
})
export class BatteryCommissionRateMaintenanceComponent implements OnInit {
  allBatteryCommissionRates: any;  
  activeBatteryCommissionRates: any;   
  batteryCommissionRateGruop: any;  
  batteryCommissionRateSelectedGroup: any;   
  dropdowns: any;
  BatteryTypeName :"BatteryTypeName";
  PurchaseMethod1 :"PurchaseMethod1";
  StateCode1 :"StateCode1";
  BatteryCommissionForm: UntypedFormGroup;
  BatteryCommissionRatesGroup: AbstractControl[][];  
  addInd: boolean = false;
  stateCodeDefault: number = 1;
  BatteryTypeDefault: number = 1;
  purchaseMethodDefault: number = 1;
  FlatFeeAmountDefault: number = 1;
  Quantity: number = 1;
  p: number = 1;
  searchText: string = "";
  public date: Date;
  originalDataSource;
  dataSource;
  displayedColumns = [];
  isReloading :boolean = false ;
  tableArr: Element[] = [];
  batteryCommissionRate1; 
  isBatteryCommissionRateSelected : boolean = false;

  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatLegacyPaginator, { static: true }) paginator: MatLegacyPaginator;

  columnNames = [{
    id: "stateCode",
    value: "State Code"

  }, {
    id: "BatteryType",
    value: "Battery Type"
  },
  {
    id: "purchaseMethod",
    value: "Purchase Method"
  },  
  {
    id: "FlatFeeAmount",
    value: "Flat Fee Amount"   
  },  
  {
    id: "Quantity",
    value: "Quantity"
  },  
  {
    id: "effectiveStartDate",
    value: "Effective Start Date"
  },
  {
    id: "effectiveEndDate",
    value: "Effective End Date"
  }];

  constructor(public apiService: ApiService, private toastMsg: ToastrService, private formBuilder: UntypedFormBuilder, private pipe: TableFilterPipe,
    private datePipe: DatePipe, private percentPipe: PercentPipe, private currencyPipe: CurrencyPipe, private dialog: MatLegacyDialog) {
      this.getallBatteryCommissionRates(); 
      this.getactiveBatteryCommissionRates();
  }

  // check ViewRateTables
  ngOnInit() {
    if (!this.apiService.checkPermission('ViewRateTables')) {  
      // this.router.navigate(['/ui/dashboard'])
      this.apiService.goBack();
      this.toastMsg.error("Insufficient Permissions. Please make sure your account can access this information.", "Permissions");
    }
    this.BatteryCommissionForm = this.formBuilder.group({
      stateCode: [this.stateCodeDefault, [Validators.required]],
      BatteryType: [this.BatteryTypeDefault, [Validators.required]],
      purchaseMethod: [this.purchaseMethodDefault, [Validators.required]],
      Quantity: [this.Quantity, [Validators.required]],
      FlatFeeAmount: [this.FlatFeeAmountDefault, [Validators.required]],
      effectiveStartDate: ['', [Validators.required]],
      BatterySalesMetric: [0, [Validators.required]],
      BatteryCommissionAmount: [0, [Validators.required]],
    });
    this.getallBatteryCommissionRates(); 
    this.getactiveBatteryCommissionRates();
    this.getDropdowns(); 

    this.BatteryCommissionRatesGroup = [[this.BatteryCommissionForm.controls.BatterySalesMetric, this.BatteryCommissionForm.controls.BatteryCommissionAmount]];

    this.onChanges();
  }

  onChanges() {
    this.BatteryCommissionForm.valueChanges.subscribe(val => {
      // console.log("this.BatteryCommissionForm.errors  = > "+this.BatteryCommissionForm.errors);
    });
  }

  clearDate(date: HTMLInputElement) {
    date.value = "";
    this.date = null;
    this.BatteryCommissionForm.controls.effectiveStartDate.setValue('');
    event.stopPropagation();
  }

  onSubmit() {
    if (!this.BatteryCommissionForm.invalid) {
      var groupArr = [];
      this.BatteryCommissionRatesGroup.forEach(x => {
        groupArr.push(
          {
            "BatterySalesMetric": x[0].value,
            "BatteryCommissionAmount": x[1].value
          }
        );
      });

      // console.log(groupArr);
      
      var body = {
        stateCodeId: this.BatteryCommissionForm.controls.stateCode.value,
        BatteryTypeId: this.BatteryCommissionForm.controls.BatteryType.value,
        purchaseMethodId: this.BatteryCommissionForm.controls.purchaseMethod.value,
        Quantity: this.BatteryCommissionForm.controls.Quantity.value,
        FlatFeeAmount: this.BatteryCommissionForm.controls.FlatFeeAmount.value,
        effectiveStartDate: this.BatteryCommissionForm.controls.effectiveStartDate.value,
        BatteryCommissionRatesGroup: groupArr
      }

      this.apiService.post('BatteryCommissionRateMaintenance', body)
        .subscribe(data => { 
          this.toastMsg.success('Battery commission and rate has been updated successfully.');
          this.isReloading = true ;
          this.getallBatteryCommissionRates(); 
          this.getactiveBatteryCommissionRates();
          this.addInd = !this.addInd;
        }, 
        (err: any) => {
          if (err?.err?.message)
            this.toastMsg.error(err.message, "Server Error!");
          if (typeof err === "string") {
            let isJsonObject = false;            
            let parsedErr: any;
            try {
              parsedErr = JSON.parse(err);
              isJsonObject =parsedErr && typeof parsedErr === "object" &&!Array.isArray(parsedErr);
            } catch (e) {
              isJsonObject = false;
            }
            if (isJsonObject) {
              this.toastMsg.error(parsedErr.result, "Error!");
            } else this.toastMsg.error(err, "Error!");
          }
          if (err?.result) {
            this.toastMsg.error(err.result, "Error!");
          }
        }
      );
    }
  }

  getallBatteryCommissionRates() {
    this.apiService.get('BatteryCommissionRateMaintenance/allBatteryCommissionRates')
      .subscribe(data => {
        this.allBatteryCommissionRates = data;
        if (this.batteryCommissionRateGruop) {
          this.getbatteryCommissionRateGruop(this.batteryCommissionRateGruop[0][0]);
          this.batteryCommissionRateSelectedGroup = this.batteryCommissionRateGruop;
        }
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }

  getactiveBatteryCommissionRates() {
    this.apiService.get('BatteryCommissionRateMaintenance/activeBatteryCommissionRates')
      .subscribe(data => {
        this.activeBatteryCommissionRates = data;
        this.displayedColumns = this.columnNames.map(x => x.id);
        this.createTable();
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }

  getDropdowns() {  
    this.apiService.get('BatteryCommissionRateMaintenance/dropdowns')
      .subscribe(data => {
        this.dropdowns = data;
        this.getallBatteryCommissionRates();
        this.getactiveBatteryCommissionRates();
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }
  getbatteryCommissionRateGruop(batteryCommissionRate: any) {
    var batteryCommissionRates = this.allBatteryCommissionRates.filter(x => x.BatteryTypeId === batteryCommissionRate.BatteryTypeId && x.purchaseMethodId === batteryCommissionRate.purchaseMethodId && x.stateCodeId === batteryCommissionRate.stateCodeId );
    batteryCommissionRates = Object.values(groupBy(batteryCommissionRates, 'effectiveStartDate'));
    this.batteryCommissionRateGruop = batteryCommissionRates;
    this.batteryCommissionRateSelectedGroup = null;
  }

  get BatterySalesMetric() { return this.BatteryCommissionForm.get('BatterySalesMetric'); }

  get BatteryCommissionAmount() { return this.BatteryCommissionForm.get('BatteryCommissionAmount'); }

  rowClick(batteryCommissionRate: any) {
    var batteryCommissionRate = this.allBatteryCommissionRates.filter(x => x.batteryTypeId === batteryCommissionRate.BatteryTypeId && x.purchaseMethodId === batteryCommissionRate.purchaseMethodId && x.stateCodeId === batteryCommissionRate.stateCodeId && x.quantity === batteryCommissionRate.Quantity);
    this.batteryCommissionRate1 = batteryCommissionRate;
    batteryCommissionRate = Object.values(groupBy(batteryCommissionRate, 'effectiveStartDate'));
    this.isBatteryCommissionRateSelected = true;
    var last = this.batteryCommissionRate1.length - 1;
    this.BatteryCommissionForm.controls.stateCode.setValue(this.batteryCommissionRate1[last].stateCodeId);
    this.BatteryCommissionForm.controls.BatteryType.setValue(this.batteryCommissionRate1[last].batteryTypeId);
    this.BatteryCommissionForm.controls.purchaseMethod.setValue(this.batteryCommissionRate1[last].purchaseMethodId);
    this.BatteryCommissionForm.controls.Quantity.setValue(this.batteryCommissionRate1[last].quantity);
    this.BatteryCommissionForm.controls.FlatFeeAmount.setValue(this.batteryCommissionRate1[last].flatFeeAmount);

    const dialogRef = this.dialog.open(BatteryRateMaintenanceDialogComponent, {
      width: '80%', data: { batteryCommissionRate }
    });
    dialogRef.afterClosed().subscribe(result => {
      // console.log(result);
    });
  }


Add(){
  this.addInd = !this.addInd;
  this.batteryCommissionRate1 = this.tableArr;
  this.isReloading = true ;
  if(!this.isReloading)
  {
   this.BatteryCommissionForm.setValidators([maxPurchaseMethodDeductionDate(this.allBatteryCommissionRates)]);
  } else {
    this.BatteryCommissionForm.clearValidators();
  }
  this.BatteryCommissionForm.controls.stateCode.setValue(this.batteryCommissionRate1[0].stateCodeId);
  this.BatteryCommissionForm.controls.BatteryType.setValue(this.batteryCommissionRate1[0].batteryTypeId);
  this.BatteryCommissionForm.controls.purchaseMethod.setValue(this.batteryCommissionRate1[0].purchaseMethodId);
  this.BatteryCommissionForm.controls.Quantity.setValue(this.batteryCommissionRate1[0].quantity);
  this.BatteryCommissionForm.controls.FlatFeeAmount.setValue(this.batteryCommissionRate1[0].flatFeeAmount);
}

  groupClick(group: any) {
    this.batteryCommissionRateSelectedGroup = this.batteryCommissionRateGruop[0].filter(x=>x.batteryCommissionMappingId === group);
  }

  addFormRow() {
    this.BatteryCommissionForm.addControl(`BatterySalesMetric${this.BatteryCommissionRatesGroup.length}`, new UntypedFormControl(0, []));
    this.BatteryCommissionForm.addControl(`BatteryCommissionAmount${this.BatteryCommissionRatesGroup.length}`, new UntypedFormControl(0, []));
    var c1 = this.BatteryCommissionForm.get(`BatterySalesMetric${this.BatteryCommissionRatesGroup.length}`);
    var c2 = this.BatteryCommissionForm.get(`BatteryCommissionAmount${this.BatteryCommissionRatesGroup.length}`);
    c1.setValidators([Validators.required]);
    c2.setValidators([Validators.required]);
    this.BatteryCommissionRatesGroup.push([c1, c2]);
  }

  removeFormRow(index: number) {
    if (this.BatteryCommissionRatesGroup.length == 1) return;
    this.BatteryCommissionRatesGroup[index].slice(0).forEach(x => {
      this.BatteryCommissionForm.removeControl(getControlName(x));
    });
    this.BatteryCommissionRatesGroup.splice(index, 1);
  }

  getControlName(control: AbstractControl) {
    var controlName =getControlName(control);
    return controlName ;
  }

  createTable() {
    let tableArr: Element[] = [];
    for (let i: number = 0; i <= this.activeBatteryCommissionRates.length - 1; i++) {
      let currentRow = this.activeBatteryCommissionRates[i];
      if(i==0)
      {
        this.tableArr[0] =this.activeBatteryCommissionRates[0];
      }
      tableArr.push({
        effectiveEndDate: this.datePipe.transform(currentRow.effectiveEndDate), effectiveStartDate: this.datePipe.transform(currentRow.effectiveStartDate),
        BatteryType: currentRow.batteryTypeName, BatteryTypeId: currentRow.batteryTypeId, BatterySalesMetric: this.currencyPipe.transform(currentRow.BatterySalesMetric),
        BatteryCommissionAmount: this.currencyPipe.transform(currentRow.BatteryCommissionAmount), purchaseMethod: currentRow.purchaseMethod1, purchaseMethodId: currentRow.purchaseMethodId,
         stateCode: currentRow.stateCode1, stateCodeId: currentRow.stateCodeId, FlatFeeAmount : this.currencyPipe.transform(currentRow.flatFeeAmount),
         Quantity: currentRow.quantity
      });
    }
    this.dataSource = new MatTableDataSource(tableArr);
    this.originalDataSource = tableArr;
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }
  searchForItem(): void {
    let filteredResults: Element[] = [];
    if (this.searchText == '') {
      this.dataSource = new MatTableDataSource(this.originalDataSource);
      this.dataSource.sort = this.sort;
      this.dataSource.paginator = this.paginator;
    } else {
      filteredResults = this.pipe.transform(this.originalDataSource, this.searchText);
      this.dataSource = new MatTableDataSource(filteredResults);
      this.dataSource.sort = this.sort;
      this.dataSource.paginator = this.paginator;
    }
  }
}

export interface Element {
  effectiveEndDate: string,
  effectiveStartDate: string,
  BatteryType: string,
  BatteryTypeId: number,
  BatterySalesMetric: string,
  BatteryCommissionAmount: string,
  purchaseMethod: string,
  purchaseMethodId: number,
  Quantity: number,
  stateCode: string,
  FlatFeeAmount : string,
  stateCodeId: number,
}
