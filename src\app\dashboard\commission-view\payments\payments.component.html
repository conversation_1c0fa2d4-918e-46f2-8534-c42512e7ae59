<!-- Payments -->
<div class="slide-toggle-btn tab-light-bg mb-3">
    <button [className]="showPayments ? 'tab-toggle tab-expanded' : 'tab-toggle tab-collapsed'" mat-flat-button
        color="primary" (click)="showPayments = !showPayments"><i class="fas fa-dollar-sign"></i> Payments<i
            class="material-icons tab-icons">{{showPayments ? 'remove_circle_outline' : 'add_circle_outline'}}</i></button>
    <div [hidden]="!showPayments" class="toggle-container">
        <table mat-table [dataSource]="paymentElements" matSort class="my-table w-100">
            <ng-container matColumnDef="paymentTypeName">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> Payment Type </th>
                <td data-td-head="Payment Type" mat-cell *matCellDef="let element"> {{element.paymentTypeName}} </td>
            </ng-container>

            <ng-container matColumnDef="commissionTypeName">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> Commission Type </th>
                <td data-td-head="Commission Type" mat-cell *matCellDef="let element"> {{element.commissionTypeName}}
                </td>
            </ng-container>

            <ng-container matColumnDef="amount">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> Amount </th>
                <td data-td-head="Amount" mat-cell *matCellDef="let element"> {{element.amount | currency}} </td>
            </ng-container>

            <ng-container matColumnDef="paymentStatusName">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> Payment Status </th>
                <td data-td-head="Payment Status" mat-cell *matCellDef="let element"> {{element.paymentStatusName}}
                </td>
            </ng-container>

            <ng-container matColumnDef="paymentDueDate">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> Payment Due Date </th>
                <td data-td-head="Payment Due Date" mat-cell *matCellDef="let element">
                    {{element.paymentDueDate | date}} </td>
            </ng-container>

            <ng-container matColumnDef="paymentNote">
                <th class="mat-column-25" mat-header-cell *matHeaderCellDef mat-sort-header> Payment Note </th>
                <td data-td-head="Payment Note" mat-cell *matCellDef="let element"> 
                    {{element.paymentNote}} 
                    <button class="btn btn-primary payment-note-edit" (click)="openPaymentNoteModel(element.paymentId,element.paymentNote)"><i class="fa fa-edit"></i></button>
                </td>
            </ng-container>

            <ng-container matColumnDef="modifiedUser">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> Modified User</th>
                <td data-td-head="Modified User" mat-cell *matCellDef="let element"> {{element.modifiedUser}}
                </td>
            </ng-container>

            <ng-container matColumnDef="paymentHistory">
                <th mat-header-cell *matHeaderCellDef> Payment History </th>
                <td data-td-head="Payment History" mat-cell *matCellDef="let element"> <a class="hover" (click)="getPaymentHistory(element.paymentId)"><mat-icon>open_in_new</mat-icon></a></td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="paymentColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: paymentColumns;"></tr>
        </table>
        <mat-paginator [pageSizeOptions]="pageSizeOptions">
        </mat-paginator>
    </div>
</div>