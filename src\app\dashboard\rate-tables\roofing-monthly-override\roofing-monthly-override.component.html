<div class="page-title col-md-12">
    <h1>Roofing Monthly Override</h1>
    <div class="breadcrumbs">
      <a href="#">Home</a>/<span>Roofing Monthly Override</span>
    </div>
  </div>
  
  <div class="content">
    <div class="card">
      <div class="card-header-info">
        <h4 class="card-title no-hover-effect">Roofing Monthly Override</h4>
      </div>
        <div class="row">
          <div class="col-md-12">
            <div class="card-body">
                <app-grid-mat-table [gridData]="originalDataSource"  
                  [columnData]="columnNames" 
                  [displayColumnData]="displayedColumns"
                  [dateFields]="dateColumns"
                  [isSearchAvailable]="true"
                  [tableWidth]="'2500px'"
                  (rowClick)="rowClick($event)">
                </app-grid-mat-table>
                <div>
                  <a class="btn btn-primary float-right" *ngIf="!addRow" (click)="onAdd()"><i
                      class="material-icons pointer">add_circle</i> Add</a>
                  <a class="btn btn-primary float-right" *ngIf="addRow" (click)="addRow = !addRow"><i
                      class="material-icons pointer">remove_circle</i> Hide</a>
                </div>
            </div>
          </div>
        </div>
      <div class="card" *ngIf="addRow">
        <div class="card-header-info">
          <h4 class="card-title no-hover-effect">
            <i class="fas fa-plus"></i> Add Roofing Override Contact
          </h4>
        </div>
  
        <div class="card-body">
          <div>
            <form [formGroup]="roofingMonthlyForm" (ngSubmit)="onSubmit()" class="w-100">
                <div class="row">
                  <div class="form-group col-md-4">
                    <div class="row">
                      <label class="col-sm-5">Contact</label>
                      <div class="col-sm-7">
                        <input id="typeahead-prevent-manual-entry" type="text" class="custom-select" 
                          formControlName="contactId" 
                           [ngbTypeahead]="search" [inputFormatter]="formatter"
                          [editable]="false" [resultTemplate]="rt" placeholder="Type to search" />
                      </div>
                    </div>
                  </div>
                  <ng-template #rt let-r="result" let-t="id">
                    <div class="col-sm-12" style="width: 80%">
                      {{ r.contactName }}
                    </div>
                  </ng-template>
    
                  <div class="form-group col-md-4">
                    <div class="row">
                      <label class="col-sm-5">Sales Division</label>
                      <div class="col-sm-7">
                        <select class="custom-select" name="salesDivision_add_dropdown" formControlName="salesDivision"
                          data-style="btn btn-link" id="salesDivision_add_dropdown">
                          <option *ngFor="let sd of salesDivisions" value="{{sd}}"> {{sd}}</option>
                        </select>
                      </div>
                    </div>
                  </div>
                  <div class="form-group col-md-4">
                    <div class="row">
                      <label class="col-sm-5">Sales Office</label>
                      <div class="col-sm-7">
                        <select class="custom-select" name="salesOffice_add_dropdown" formControlName="salesOffice"
                          data-style="btn btn-link" id="salesOffice_add_dropdown">
                          <option *ngFor="let so of salesOffices" value="{{so}}"> {{so}}</option>
                        </select>
                      </div>
                    </div>
                  </div>
                  <div class="form-group col-md-4" >
                    <div class="row">
                      <label class="col-sm-5">Product Type</label>
                      <div class="col-sm-7">
                        <ng-multiselect-dropdown [placeholder]="'Search'" [settings]="dropdownSettings" [data]="productTypeList"
                        formControlName="productType">
                        </ng-multiselect-dropdown>
                      </div>
                    </div>
                  </div>
                  <div class="form-group col-md-4">
                    <div class="row">
                      <label class="col-sm-5">Type</label>
                      <div class="col-sm-7">
                        <select class="custom-select" name="roofInstalls" formControlName="roofRateTypeId"
                          data-style="btn btn-link" id="roofRate">
                          <option *ngFor="let rr of roofRateData" [value]="rr.roofRateTypeId"> {{ rr.roofRateTypeName }} </option>
                        </select>
                      </div>
                    </div>
                  </div>
                  <div class="form-group col-md-4">
                    <div class="row">
                      <label class="col-sm-5">Team</label>
                      <div class="col-sm-7">
                        <select class="custom-select" name="team" formControlName="team"
                          data-style="btn btn-link" id="team">
                          <option *ngFor="let t of teamsData" [value]="t"> {{ t }} </option>
                        </select>
                      </div>
                    </div>
                  </div>
                  <div class="form-group col-md-4">
                    <div class="row">
                      <label class="col-sm-5">Rate Amount</label>
                      <div class="col-sm-7">
                        <input type="number" name="amount" formControlName="rateAmount" min="0" class="custom-input">
                      </div>
                    </div>
                  </div>
                  <div class="form-group col-md-4">
                    <div class="row">
                      <mat-radio-group formControlName="netInd" (click)="$event.stopPropagation()">
                        <mat-radio-button class="mr-2" value="0">Gross</mat-radio-button>
                        <mat-radio-button class="mr-2" value="1">Net</mat-radio-button>													
                        </mat-radio-group>
                    </div>
                  </div>
                  <div class="form-group col-md-4">
                    <div class="row">
                      <label class="col-sm-5">Exclude Minimum Commission</label>
                      <div class="col-sm-7">
                        <input type="checkbox" class="form-check-input" formControlName="excludeMinCommission" >
                      </div>
                    </div>
                  </div>
                  <div class="form-group col-md-4" *ngIf="roofingMonthlyForm.controls['netInd'].value ==='1'">
                    <div class="row">
                      <label class="col-sm-5">Net Value</label>
                      <div class="col-sm-7">
                        <input type="number" name="netValue" formControlName="netValue" min="0" class="custom-input">
                      </div>
                    </div>
                  </div>
                  <div class="form-group col-md-4">
                    <div class="row">
                      <label class="col-sm-5">Roof Install Range</label>
                      <div class="col-sm-7">
                        <select class="custom-select" name="roofInstalls" formControlName="roofInstallRange"
                          data-style="btn btn-link" id="roofInstalls">
                          <option *ngFor="let rd of roofInstallData" [value]="rd.roofInstallRangeId"> {{ rd.installRangeName }} </option>
                        </select>
                      </div>
                    </div>
                  </div>
                  <div class="form-group col-md-4">
                    <div class="row">
                      <label class="col-sm-5">Effective Start Date</label>
                      <div class="col-sm-7">
                        <div class="date-picker w-100">
                          <input #AddStartDatePicker type="date" name="start_date" id="start_date" class="custom-input"
                            formControlName="effectiveStartDate" placeholder="" />
                          <span *ngIf="AddStartDatePicker.value.length > 0" class="mat-icon cal-reset"
                            (click)="clearAddStartDate(AddStartDatePicker)"><i class="far fa-calendar-times"></i></span>
                          <span *ngIf="AddStartDatePicker.value.length <= 0" class="mat-icon cal-open"><i
                              class="far fa-calendar-alt"></i></span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              <div class="row align-button-right">
                <button type="submit" class="btn btn-primary" [disabled]="roofingMonthlyForm.invalid"><i
                    class="fas fa-plus"></i> Add Roofing Override Contact</button>
              </div>
            </form>
          </div>
        </div>
      </div>
      <div class="card" *ngIf="editRow">
        <div class="card-header-info">
          <h4 class="card-title no-hover-effect">
            <i class="fas fa-plus"></i> Edit Roofing Override Contact
          </h4>
        </div>
  
        <div class="card-body">
          <div>
            <form [formGroup]="roofingMonthlyEditForm" (ngSubmit)="onEditSubmit()" class="w-100">
                <div class="row">
                  <div class="form-group col-md-4">
                    <div class="row">
                      <label class="col-sm-5">Contact</label>
                      <div class="col-sm-7">
                        <input type="text" class="custom-input"  formControlName="contactName" />
                      </div>
                    </div>
                  </div>
                  <div class="form-group col-md-4">
                    <div class="row">
                      <label class="col-sm-5">Sales Division</label>
                      <div class="col-sm-7">
                        <input type="text" class="custom-input" formControlName="salesDivision" />
                      </div>
                    </div>
                  </div>

                  <div class="form-group col-md-4">
                    <div class="row">
                      <label class="col-sm-5">Sales Office</label>
                      <div class="col-sm-7">
                        <input type="text" class="custom-input" formControlName="salesOffice" />
                      </div>
                    </div>
                  </div>
                  <div class="form-group col-md-4" >
                    <div class="row">
                      <label class="col-sm-5">Product Type</label>
                      <div class="col-sm-7">
                        <ng-multiselect-dropdown [placeholder]="'Search'" disabled="true" [settings]="dropdownSettings" [data]="productTypeList"
                        formControlName="productType">
                        </ng-multiselect-dropdown>
                      </div>
                    </div>
                  </div>
                  <div class="form-group col-md-4">
                    <div class="row">
                      <label class="col-sm-5">Type</label>
                      <div class="col-sm-7">
                        <input type="text" formControlName="roofRateTypeId" class="custom-input" >
                      </div>
                    </div>
                  </div>
                  <div class="form-group col-md-4">
                    <div class="row">
                      <label class="col-sm-5">Team</label>
                      <div class="col-sm-7">
                        <select class="custom-select" name="team" formControlName="team"
                        data-style="btn btn-link" id="team" disabled>
                        <option *ngFor="let t of teamsData" [value]="t"> {{ t }} </option>
                      </select>
                      </div>
                    </div>
                  </div>
                  <div class="form-group col-md-4">
                    <div class="row">
                      <label class="col-sm-5">Rate Amount</label>
                      <div class="col-sm-7">
                        <input type="number" formControlName="rateAmount" class="custom-input" >
                      </div>
                    </div>
                  </div>
                  <div class="form-group col-md-4">
                    <div class="row">
                      <mat-radio-group formControlName="netInd">
                        <mat-radio-button class="mr-2" value="0">Gross</mat-radio-button>
                        <mat-radio-button class="mr-2" value="1">Net</mat-radio-button>													
                        </mat-radio-group>
                    </div>
                  </div>
                  <div class="form-group col-md-4">
                    <div class="row">
                      <label class="col-sm-5">Exclude Minimum Commission</label>
                      <div class="col-sm-7">
                        <input type="checkbox" class="form-check-input" formControlName="excludeMinCommission" id="excludeMinCommission" name="excludeMinCommission" disabled>
                      </div>
                    </div>
                  </div>
                  <div class="form-group col-md-4" *ngIf="roofingMonthlyEditForm.controls['netInd'].value ==='1'">
                    <div class="row">
                      <label class="col-sm-5">Net Value</label>
                      <div class="col-sm-7">
                        <input type="number" formControlName="netValue" class="custom-input" >
                      </div>
                    </div>
                  </div>
                  <div class="form-group col-md-4">
                    <div class="row">
                      <label class="col-sm-5">Roof Install Range</label>
                      <div class="col-sm-7">
                        <input type="text" formControlName="roofInstallRange" class="custom-input" >
                      </div>
                    </div>
                  </div>
                  <div class="form-group col-md-4">
                    <div class="row">
                      <label class="col-sm-5">Effective Start Date</label>
                      <div class="col-sm-7">
                        <input type="text" formControlName="effectiveStartDate" class="custom-input" >
                      </div>
                    </div>
                  </div>
    
                  <div class="form-group col-md-4">
                    <div class="row">
                      <label class="col-sm-5">Effective End Date</label>
                      <div class="col-sm-7">
                        <div class="date-picker w-100" *ngIf="!disableDateField">
                          <input #AddEditEndDatePicker type="date" name="effectiveEndDate_edit" id="effectiveEndDate_edit"
                            class="custom-input" formControlName="effectiveEndDate" [min]="roofingMonthlyEditForm.controls.effectiveStartDate.value" placeholder="" />
                          <span *ngIf="AddEditEndDatePicker.value.length > 0" class="mat-icon cal-reset"
                            (click)="clearEditEndDate(AddEditEndDatePicker)"><i class="far fa-calendar-times"></i></span>
                          <span *ngIf="AddEditEndDatePicker.value.length <= 0" class="mat-icon cal-open"><i
                              class="far fa-calendar-alt"></i></span>
                        </div>
                        <input type="text" formControlName="effectiveEndDate" class="custom-input" *ngIf="disableDateField">
                      </div>
                    </div>
                  </div>
                </div>
              <div class="row align-button-right">
                <button type="submit" class="btn btn-primary" *ngIf="!disableDateField"><i
                    class="fas fa-plus"></i> Update Roofing Override Contact</button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>