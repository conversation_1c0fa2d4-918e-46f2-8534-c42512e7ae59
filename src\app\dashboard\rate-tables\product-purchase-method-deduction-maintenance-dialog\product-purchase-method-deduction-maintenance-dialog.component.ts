import { Component, OnInit, Inject } from '@angular/core';
import { MatLegacyDialogRef, MAT_LEGACY_DIALOG_DATA } from '@angular/material/legacy-dialog';
import { ApiService } from '../../../services/api.service';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-product-purchase-method-deduction-maintenance-dialog',
  templateUrl: './product-purchase-method-deduction-maintenance-dialog.component.html',
  styleUrls: ['./product-purchase-method-deduction-maintenance-dialog.component.css']
})
export class ProductPurchaseMethodDeductionMaintenanceDialogComponent implements OnInit {
  purchaseMethodDeductionGroup: Element[] = [];
  constructor(public dialogRef: MatLegacyDialogRef<ProductPurchaseMethodDeductionMaintenanceDialogComponent>,
    private apiService: ApiService, private toastMsg: ToastrService, @Inject(MAT_LEGACY_DIALOG_DATA) public data: any) { }


  ngOnInit() {
    this.purchaseMethodDeductionGroup = this.data.filteredPurchaseMethodDeduction;
    this.purchaseMethodDeductionGroup.sort((a, b) => {
      return <any>new Date(b.effectiveStartDate) - <any>new Date(a.effectiveStartDate);
    });
  }

}

export interface Element {
  effectiveStartDate: string,
  effectiveEndDate: string,
  purchaseMethodDeductionRate: string,
  salesTerritory: string,
  purchaseMethod: string
}


