<div class="page-title col-md-12 ">
  <h1>Dashboard</h1>

</div>

<div class="content">


  <div class="row">

    <div class="col-md-4">
      <div class="card bg-purple-dark info-card hover" (click)='moveToSelectedTab("Reps With Plans")'>
        <div class="card-body">
          <div class="row">
            <div class="col-8 ">
              <div class="h4 mt-0">{{infoBox.blueInfo.name}}</div>
              <div class="text-uppercase">
                <h1>{{infoBox.blueInfo.number}}</h1>
              </div>
            </div>
            <div class="col-4 text-right info-icon"><i class="fas fa-user-tie"></i></div>

          </div>

        </div>
      </div>

    </div>
    <div class="col-md-4">
      <div class="card bg-green-dark info-card hover" (click)='moveToSelectedTab("Reps Without Plans")'>
        <div class="card-body">
          <div class="row">
            <div class="col-8 ">
              <div class="h4 mt-0">{{infoBox.greenInfo.name}}</div>
              <div class="text-uppercase">
                <h1>{{infoBox.greenInfo.number}}</h1>
              </div>
            </div>
            <div class="col-4 text-right info-icon"><i class="fas fa-user"></i></div>

          </div>

        </div>
      </div>

    </div>
    <div class="col-md-4">
      <div class="card bg-gray-dark info-card hover" (click)='moveToSelectedTab("List of Plans")'>
        <div class="card-body">
          <div class="row">
            <div class="col-8 ">
              <div class="h4 mt-0">{{infoBox.greyInfo.name}}</div>
              <div class="text-uppercase">
                <h1>{{infoBox.greyInfo.number}}</h1>
              </div>
            </div>
            <div class="col-4 text-right info-icon"><i class="fas fa-user-alt"></i></div>

          </div>

        </div>
      </div>

    </div>
  </div>
  <div class="row">
    <div class="col-md-12 mt-2">
      <div class="card" style='min-height:400px'>
        <mat-tab-group animationDuration="2000ms">
          <mat-tab label="Reps With Plans">
            <div class="w-100 text-right">
              <button class="btn btn-primary" (click)="exportTable('Reps With Plans')"><i class="fas fa-download"></i> Download</button>
            </div>
            <mat-table #table1 [dataSource]="dataSourceRepsWithPlans" matSort>
              <ng-container matColumnDef="{{column.id}}" *ngFor="let column of columnNames">
                <mat-header-cell *matHeaderCellDef mat-sort-header class="table-header"> {{column.value}}
                </mat-header-cell>
                <mat-cell [attr.data-td-head]="column.value" *matCellDef="let element">
                  <ng-container *ngIf="column.value == 'Contact Name'; else paybook">
                    <a [routerLink]="['/ui/commissions/salesrep', element.Contact_Id]">{{element[column.id]}}</a>
                  </ng-container>
                  <ng-template #paybook>
                    <ng-container *ngIf="column.value == 'Payment Book'; else noRepsWithPlans">
                      <a [routerLink]="['/ui/commissions/paybook', element.Contact_Id]">
                        <mat-icon>account_balance_wallet</mat-icon>
                      </a>
                    </ng-container>
                  </ng-template>
                  <ng-template #noRepsWithPlans>
                    <ng-container *ngIf="column.value == 'Plan Name'; else noRepsWithPlans2">
                      <a
                        [routerLink]="['/ui/commissions/salesrepconfiguration', element.Contact_Id]">{{element[column.id]}}</a>
                    </ng-container>
                  </ng-template>
                  <ng-template #noRepsWithPlans2>
                    {{element[column.id]}}
                  </ng-template>
                </mat-cell>
              </ng-container>
              <mat-header-row *matHeaderRowDef="displayedColumnsRepsWithPlans"></mat-header-row>
              <mat-row *matRowDef="let row; columns: displayedColumnsRepsWithPlans;" class="pointer table-content"
                ></mat-row>
            </mat-table>
            <mat-paginator #paginator1 [pageSizeOptions]="[5, 10, 20, 50]" showFirstLastButtons></mat-paginator>
          </mat-tab>
          <mat-tab label="Reps Without Plans">
            <div class="w-100 text-right">
              <button class="btn btn-primary" (click)="exportTable('Reps Without Plans')"><i class="fas fa-download"></i> Download</button>
            </div>
            <mat-table #table2 [dataSource]="dataSourceRepsWithoutPlans" matSort #sort2="matSort">
              <ng-container matColumnDef="{{column.id}}" *ngFor="let column of columnNamesAlt">
                <mat-header-cell *matHeaderCellDef mat-sort-header class="table-header"> {{column.value}}
                </mat-header-cell>
                <mat-cell [attr.data-td-head]="column.value" *matCellDef="let element">
                  <ng-container *ngIf="column.value == 'Contact Name'; else paybook">
                    <a [routerLink]="['/ui/commissions/salesrep', element.Contact_Id]">{{element[column.id]}}</a>
                  </ng-container>
                  <ng-template #paybook>
                    <ng-container *ngIf="column.value == 'Payment Book'; else noRepsWithoutPlans">
                      <a [routerLink]="['/ui/commissions/paybook', element.Contact_Id]">
                        <mat-icon>account_balance_wallet</mat-icon>
                      </a>
                    </ng-container>
                  </ng-template>
                  <ng-template #noRepsWithoutPlans>
                    {{element[column.id]}}
                  </ng-template>
                </mat-cell>
              </ng-container>
              <mat-header-row *matHeaderRowDef="displayedColumnsRepsWithoutPlans"></mat-header-row>
              <mat-row *matRowDef="let row; columns: displayedColumnsRepsWithoutPlans;" class="pointer table-content"
                ></mat-row>
            </mat-table>
            <mat-paginator #paginator2 [pageSizeOptions]="[5, 10, 20, 50]" showFirstLastButtons></mat-paginator>
          </mat-tab>
          <mat-tab label="List of Plans">
            <div class="w-100 text-right">
              <button class="btn btn-primary" (click)="exportTable('List of Plans')"><i class="fas fa-download"></i> Download</button>
            </div>
            <mat-table #table3 [dataSource]="dataSourcePlans" matSort #sort3="matSort">
              <ng-container matColumnDef="{{column.id}}" *ngFor="let column of columnNamesPlans">
                <mat-header-cell *matHeaderCellDef mat-sort-header class="table-header"> {{column.value}}
                </mat-header-cell>
                <mat-cell [attr.data-td-head]="column.value" *matCellDef="let element">
                  <ng-container *ngIf="column.value == 'Plan Name'; else noPlans">
                    <a [routerLink]="['/ui/commissions/viewPlan', element.Plan_Header_Id]">{{element[column.id]}}</a>
                  </ng-container>
                  <ng-template #noPlans>
                    {{element[column.id]}}
                  </ng-template>
                </mat-cell>
              </ng-container>
              <mat-header-row *matHeaderRowDef="displayedColumnsPlans"></mat-header-row>
              <mat-row *matRowDef="let row; columns: displayedColumnsPlans;" class="pointer table-content"
                ></mat-row>
            </mat-table>
            <mat-paginator #paginator3 [pageSizeOptions]="[5, 10, 20, 50]" showFirstLastButtons></mat-paginator>
          </mat-tab>

        </mat-tab-group>
      </div>
    </div>
  </div>
</div>