<div class="dailog-title-bg">
  <div class="dailog-title"><i class="fas fa-history"></i> History<button class="dailog-close" [mat-dialog-close]><span>X</span></button>
  </div>
</div>
  <div class="row" *ngIf="permitDeductionGroup">
 
    <div class="col-md-6"> <div class="row">
                    <label class="col-sm-5">Sales Territory</label> 
                    <span class="col-sm-7">{{permitDeductionGroup[0].salesTerritory}}</span>
    </div> </div>
    <div class="col-md-6"> <div class="row">
                  
                    <label class="col-sm-5">Finance Partner</label>
                    <span class="col-sm-7">{{permitDeductionGroup[0].financePartner}}</span>
                </div></div>
                    <div class="col-md-6"> <div class="row">
                    <label class="col-sm-5">Purchase Method</label>
                    <span class="col-sm-7">{{permitDeductionGroup[0].purchaseMethod}}</span>
                  </div></div>
                  <div class="col-md-6"> <div class="row">
                    <label class="col-sm-5">Minimum PPW</label>
                    <span class="col-sm-7">{{permitDeductionGroup[0].minimumPpw}}</span>
                  </div></div>
                  <div class="col-md-6"> <div class="row">
                    <label class="col-sm-5">Utility Company</label>
                    <span class="col-sm-7">{{permitDeductionGroup[0].utilityCompany}}</span>
                  </div></div>
                <div class="col-md-12">
        <table class="my-table mat-table w-100 mt-3">
          <thead>
            <tr class="mat-header-row">
              <th class="mat-header-cell"  scope="col">Effective Start Date</th>
              <th class="mat-header-cell"  scope="col">Effective End Date</th>
              <th class="mat-header-cell"  scope="col">Permit Deduction Rate</th>
            </tr>
          </thead>
          <tbody>
            <tr  class="mat-row"  *ngFor="let tr of permitDeductionGroup">
              <td data-td-head="Effective Start Date" class="mat-cell">{{tr.effectiveStartDate | date}}</td>
              <td data-td-head="Effective End Date" class="mat-cell">{{tr.effectiveEndDate | date}}</td>
              <td data-td-head="Permit Deduction Rate" class="mat-cell">{{tr.permitDeductionRate | currency:'USD':true:'1.2-3'}}</td>
            </tr>
          </tbody>
        </table>
      </div>
  </div>