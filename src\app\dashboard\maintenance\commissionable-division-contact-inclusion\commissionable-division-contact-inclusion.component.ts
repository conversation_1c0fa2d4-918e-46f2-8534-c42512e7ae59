import { DatePipe } from '@angular/common';
import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { ToastrService } from 'ngx-toastr';
import { ApiService } from 'src/app/services/api.service';
import { MatLegacyPaginator } from '@angular/material/legacy-paginator';
import { MatSort } from '@angular/material/sort';
import { TableFilterPipe } from 'src/app/pipe/table-filter.pipe';
import { IContact } from 'src/app/model/one-time-payment.model';
import { Observable, OperatorFunction } from 'rxjs';
import { debounceTime, distinctUntilChanged, filter, map } from 'rxjs/operators';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';

@Component({
  selector: 'app-commissionable-division-contact-inclusion',
  templateUrl: './commissionable-division-contact-inclusion.component.html',
  styleUrls: ['./commissionable-division-contact-inclusion.component.css']
})
export class CommissionableDivisionContactInclusionComponent implements OnInit {
inclusions: CommissionableDivisionContactInclusions[];
originalDataSource: any;
dataSource: any;
searchText: string = "";
displayedColumns = [];
tableArr: CommissionableDivisionContactInclusions[] = [];
addRow: boolean = false;
contacts: IContact[] = [];
form: UntypedFormGroup;
@ViewChild(MatSort, { static: true }) sort: MatSort;
@ViewChild(MatLegacyPaginator, { static: true }) paginator: MatLegacyPaginator;
formatter = (c: IContact) => c.contactName;
search: OperatorFunction<string, readonly { contactId, contactName }[]> = (text$: Observable<string>) => text$.pipe(
  debounceTime(0),
  distinctUntilChanged(),
  filter(term => term.length >= 2),
  map(term => this.contacts.filter(c => new RegExp(term, 'mi').test(c.contactName)).slice(0, 10))
)
columnNames = [{
  id: "contactName",
  value: "Contact Name"
},
{
id: "contactId",
value: "Contact Id"

}, {
id: "salesDivision",
value: "Sales Division"
},
{
id: "userCreatedTimestamp",
value: "Created Date"
},
{
id: "userCreatedId",
value: "Created By"
}];

  constructor(private formBuilder: UntypedFormBuilder, public apiService: ApiService, private toastMsg: ToastrService, private datePipe: DatePipe, private pipe: TableFilterPipe) {}

  ngOnInit(): void {
    this.form = this.formBuilder.group({
      contactId: ["", [Validators.required]],
    })
    this.getInclusions();
  }

  getInclusions() {
    this.apiService.get('GetData/CommissionableDivisionContactInclusions')
    .subscribe(data => {
      console.log(data)
      if (data && data.result) {
        this.inclusions = data.result.inclusions;
        this.contacts = data.result.contacts && data.result.contacts.length > 0 ? data.result.contacts.map(type => { return <IContact>{ contactId: type.id, contactName: type.name } }) : [];
        this.createTable();
      }
    }, err => {
      this.toastMsg.error(err.message, "Error!");
    })
  }

  submit() {
    this.apiService.post(`GetData/AddCommissionableDivisionContactInclusion/${this.form.controls.contactId.value.contactId}`, null)
          .subscribe(data => {
            if (data && data.result) {
              this.toastMsg.success('Contact successfully submitted');
              this.getInclusions();
              this.addRow = !this.addRow;
            }
            else {
              this.toastMsg.warning('Issue detected with submission, please try again');
            }
          }, (err: any) => {
            this.toastMsg.error(err.message, 'Server Error!');
          });
  }

  createTable() {
    console.log(this.inclusions)
    let tableArr: CommissionableDivisionContactInclusions[] = [];
    for(let i:number = 0; i <= this.inclusions.length - 1; i++) {
      let currentRow = this.inclusions[i];
      if(i==0)
      {
        this.tableArr[0] =this.inclusions[0];
      }
      tableArr.push({commissionableDivisionContactInclusionId: currentRow.commissionableDivisionContactInclusionId, contactId: currentRow.contactId, contactName: currentRow.contactName, salesDivision: currentRow.salesDivision,
        userCreatedTimestamp: this.datePipe.transform(currentRow.userCreatedTimestamp), userCreatedId: currentRow.userCreatedId});
    }
    this.displayedColumns = this.columnNames.map(x => x.id);
    this.dataSource = new MatTableDataSource(tableArr);
    this.originalDataSource = tableArr;
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }

  searchForItem():void {
    let filteredResults: CommissionableDivisionContactInclusions[] = [];
    if (this.searchText == '') {
      this.dataSource = new MatTableDataSource(this.originalDataSource);
      this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
    } else {
      filteredResults = this.pipe.transform(this.originalDataSource, this.searchText);
      this.dataSource = new MatTableDataSource(filteredResults);
      this.dataSource.sort = this.sort;
      this.dataSource.paginator = this.paginator;
    }
  }

  rowClick(row: any) {
  }

}

export interface CommissionableDivisionContactInclusions{
  commissionableDivisionContactInclusionId: number;
  contactId: number;
  contactName: string;
  salesDivision: string;
  userCreatedTimestamp: string;
  userCreatedId: string;
}