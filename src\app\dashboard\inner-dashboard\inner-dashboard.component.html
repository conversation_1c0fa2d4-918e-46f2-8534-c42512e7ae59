<div class="container-fluid pt-4">
    <div class="row">
        <div class="col-md-4 mb-3">
            <div class="card light-blue-card info-card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-8">
                            <div class="h4 mt-0">{{infoBox.blueInfo.name}}</div>
                            <div class="text-uppercase">
                                <h1>{{infoBox.blueInfo.number}}</h1>
                            </div>
                        </div>
                        <div class="col-4 text-right info-card">
                            <i class="material-icons info-icon">people_alt</i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="card light-green-card info-card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-8">
                            <div class="h4 mt-0">{{infoBox.greenInfo.name}}</div>
                            <div class="text-uppercase">
                                <h1>{{infoBox.greenInfo.number}}</h1>
                            </div>
                        </div>
                        <div class="col-4 text-right info-icon">
                            <i class="material-icons info-icon">format_list_bulleted</i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="card light-grey-card info-card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-8">
                            <div class="h4 mt-0">{{infoBox.greyInfo.name}}</div>
                            <div class="test-uppercase">
                                <h1>{{infoBox.greyInfo.number}}</h1>
                            </div>
                        </div>
                        <div class="col-4 text-rught info-icon">
                            <i class="material-icons info-icon">payment</i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12 mt-2">
            <div class="card">
<mat-tab-group animationDuration="2000ms">
  <mat-tab label="Processed Payments">
    <mat-table #table [dataSource]="dataSource" matSort>
        <ng-container matColumnDef="{{column.id}}" *ngFor="let column of columnNames">
          <mat-header-cell *matHeaderCellDef mat-sort-header class="table-header"> {{column.value}} </mat-header-cell>
          <mat-cell *matCellDef="let element"> {{element[column.id]}} </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns;" class="pointer table-content" (click)= "rowClick(row)"></mat-row>
      </mat-table>
      <mat-paginator [pageSizeOptions]="[5, 10, 20, 50]" showFirstLastButtons></mat-paginator>
  </mat-tab>
  <mat-tab label="Pending Payments">
    <mat-table #table [dataSource]="dataSourcePending" matSort>
        <ng-container matColumnDef="{{column.id}}" *ngFor="let column of columnNames">
          <mat-header-cell *matHeaderCellDef mat-sort-header class="table-header"> {{column.value}} </mat-header-cell>
          <mat-cell *matCellDef="let element"> {{element[column.id]}} </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="displayedColumnsPending"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumnsPending;" class="pointer table-content" (click)="rowClick(row)"></mat-row>
      </mat-table>
      <mat-paginator [pageSizeOptions]="[5, 10, 20, 50]" showFirstLastButtons></mat-paginator>
  </mat-tab>
  <mat-tab label="Pending Reclaims">
    <mat-table #table [dataSource]="dataSourceReclaim" matSort>
        <ng-container matColumnDef="{{column.id}}" *ngFor="let column of columnNames">
          <mat-header-cell *matHeaderCellDef mat-sort-header class="table-header"> {{column.value}} </mat-header-cell>
          <mat-cell *matCellDef="let element"> {{element[column.id]}} </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="displayedColumnsReclaim"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumnsReclaim;" class="pointer table-content" (click)= "rowClick(row)"></mat-row>
      </mat-table>
      <mat-paginator [pageSizeOptions]="[5, 10, 20, 50]" showFirstLastButtons></mat-paginator>
  </mat-tab>
  <mat-tab label="Active Sales Reps">
    <mat-table #table [dataSource]="dataSourceSales" matSort>
        <ng-container matColumnDef="{{column.id}}" *ngFor="let column of columnNamesAlt">
          <mat-header-cell *matHeaderCellDef mat-sort-header class="table-header"> {{column.value}} </mat-header-cell>
          <mat-cell *matCellDef="let element"> {{element[column.id]}} </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="displayedColumnsSales"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumnsSales;" class="pointer table-content" (click)= "rowClick(row)"></mat-row>
      </mat-table>
      <mat-paginator [pageSizeOptions]="[5, 10, 20, 50]" showFirstLastButtons></mat-paginator>
  </mat-tab>
</mat-tab-group>
            </div>
        </div>
    </div>
</div>