import { Component, OnInit } from '@angular/core';
import { ApiService } from "../../../services/api.service";
import { Router, ActivatedRoute } from "@angular/router";
import { ToastrService } from 'ngx-toastr';
declare var $: any;

@Component({
  selector: 'app-version-formula',
  templateUrl: './version-formula.component.html',
  styleUrls: ['./version-formula.component.css']
})
export class VersionFormulaComponent implements OnInit {

  formulaId: number;
  versionNo: number;
  formulaObj: any
  cloneFormula: any;
  loadPage: boolean = false
  constructor(public apiService: ApiService, private router: Router, private activatedRoute: ActivatedRoute, private toastMsg: ToastrService) {
  }

  ngOnInit() {
    if (!this.apiService.checkPermission('CloneRule')) {
      this.apiService.goBack();
      this.toastMsg.error("Insufficient Permissions. Please make sure your account can access this information.", "Permissions");
    }
    this.activatedRoute.params.subscribe(params => {
      this.formulaId = parseInt(params.formula_id);
      this.versionNo = parseInt(params.version_no);

      if (localStorage.getItem('LastRuleDetails')) {
        this.formulaObj = JSON.parse(localStorage.getItem('LastRuleDetails'));
        if (this.formulaObj.rule_id != this.formulaId) {
          this.apiService.get('baseFormula/' + this.formulaId + '/' + this.versionNo)
            .subscribe(data => {
              if (data.statusCode === "200" || data.statusCode === "201") {
                this.loadPage = true;
                localStorage.setItem('LastRuleDetails', JSON.stringify(data.result))
                this.formulaObj = JSON.parse(localStorage.getItem('LastRuleDetails'));
                this.formulaObj.description = this.formulaObj.description;
                this.cloneFormula = this.formulaObj;
                this.cloneFormula.newVersion = true;
              }
              else {
                this.toastMsg.error("Server", 'Error!')
              }
            }, (err: any) => {
              this.toastMsg.error(err.message, 'Error!')
            });
        } else {
          this.formulaObj.description = this.formulaObj.description;
          this.cloneFormula = this.formulaObj;
          this.cloneFormula.newVersion = true;
          this.loadPage = true;
        }
      }
      })
  }
}
