<div class="card" *ngIf="viewRuleType">
  <div class="card-header-info p-3">
    <div class="row">
      <div class="col-md-12">
        <div class="row">
          <div class="col-md-12  pl-3 pr-3" *ngIf="contactPlanId && isInclusion == false">
            <div class="form-group  pl-4">
              <input class="form-check-input" type="checkbox" id="activeInd" [(ngModel)]="activeInd"
                [checked]="activeInd" [disabled]="false">
              <label class="form-check-label ">Exclude {{ruleName}}?</label>
            </div>
          </div>
        </div>
      </div>

    </div>
    <div class="row justify-content-end" *ngIf="viewRuleType && isInclusion == false">
      <button class="btn btn-primary" [disabled]="activeInd == existingActiveInd" (click)="SaveActiveInd()"><i
          class="fas fa-save"></i> Save</button>
    </div>
  </div>
  <div *ngIf="isInclusion">
      <p style="text-align: center;">No rule data to display currently</p>
  </div>
</div>