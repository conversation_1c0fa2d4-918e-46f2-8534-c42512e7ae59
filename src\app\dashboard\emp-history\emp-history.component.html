<div class="logo main-head" >
	<a [routerLink] = "['/']" class="simple-text logo-normal"><img src="./assets/images/Trinity.png" width="100px" alt=""></a>
</div>
<app-go-back></app-go-back>
	<div class=" w-100">
		<div class="content mt-0 py-0">
			<div class="container-fluid">

				<div class="row">
					<div class="card">
						<div class="card-header card-header-info">
							<div class="row">
								<div class="col-md-4 mt-3">
									<h4>Employee history</h4>	
								</div>

							</div>
						</div>
						<div class="card-body">
							<div class="col-md text-right">
							<a href="" class="text-info"> Employee History Help
								<i class="material-icons" style="font-size:25px">help</i>
							</a>

					 	</div>
							<div class="row mt-4">
								<div class="col">
									<table class="table">
										<tbody>
											<tr>
												
												<th scope="col" >Employee History Name</th>
												<th scope="col" >Title</th>
												<th scope="col" >Division</th>
												<th scope="col" >Solar Pro</th>
												<!-- <th scope="col" >Sales Office</th>
												<th scope="col" >Effective Start Date</th>
												<th scope="col" >Commissioned on Watts Sold Amount</th>
												<th scope="col" >Demo Base Rate</th>
												<th scope="col" >Appointment Commission</th> -->
												<th scope="col">Action</th>
											</tr>

											<tr *ngFor="let contact of contactsList">
												
												<!-- <td><a class="nav-link" [routerLink] = "['/ui/commissions/emphistorydetails']">{{contact.contactId}}</a></td> -->
												<td>{{contact.contactId}}</td>
												<td>{{contact.contactName}}</td>
												<td>{{contact.salesDivision}}</td>
												<td>{{contact.solarPro}}</td>
												<td><a [routerLink]="['/ui/commissions/salesrep', contact.contactId]">View</a></td>
											</tr>
											
										</tbody>
									</table>
								</div>
							</div>
							<div class="col-md text-right">
								<button type="submit" class="btn btn-round">new</button>  

							</div>
						</div>
					</div> 
				</div>
			</div>
		</div>
		
	</div>