import { Component, OnInit, Input } from '@angular/core';
import {UntypedFormBuilder, UntypedFormGroup, Validators} from "@angular/forms";
import {Router} from "@angular/router";
import {ApiService} from "../../../services/api.service";
import { ToastrService } from 'ngx-toastr';
import { User } from '../../../model/user.model';
import { MustMatch } from '../../../services/must-match.service'

@Component({
  selector: 'app-add-user',
  templateUrl: './add-user.component.html',
  styleUrls: ['./add-user.component.css']
})
export class AddUserComponent implements OnInit {

  submit_btn: string = "Create User"
  userForm: UntypedFormGroup;
  user: User;
  default_pass: string = ""
  rolesList: any = []

  usersList: any = [];
  emailAddress: string = "";
  constructor(private formBuilder: UntypedFormBuilder, 
              private router: Router, 
              public apiService: ApiService, 
              private toastMsg: ToastrService) {
    this.user = new User();

  }
  @Input() editUser: any = {}
  ngOnInit() {
    //console.log("Edit Data", this.editUser)
    if(!this.apiService.checkPermission('CreateNewUser')){
      this.router.navigate(['notfound'])
    }
    if(Object.keys(this.editUser).length > 0 && this.editUser.type == 'Edit'){
      this.submit_btn = "Edit User"
      this.emailAddress = this.editUser.email;
      if (this.emailAddress != "") {
        var s = this.emailAddress.split("@");
        if (s.length > 1) {
          this.emailAddress = s[0];
        }
      }
      this.user.name = this.editUser.userName?this.editUser.userName: ""
      this.user.email = this.emailAddress ? this.emailAddress: ""
      this.user.role = this.editUser.role?this.editUser.roleID: ""
      this.user.status = this.editUser.status ? this.editUser.status : ""
      this.user.recruiter = this.editUser.recruiter ? this.editUser.recruiter : "0"
      this.default_pass = "123456"
    }
    this.initializeForm();
    this.getRolesList();
    this.getUsersList();
  }

  /**
   * Initialize form with default values
   */
  initializeForm(){
    this.userForm = this.formBuilder.group({
      name: [this.user.name, Validators.required],
      email: [this.user.email, Validators.compose([Validators.required])],
      role: [this.user.role, Validators.required],
      status: [this.user.status, Validators.required],
      recruiter : [this.user.recruiter]
    });
  }

  /**
   * Role List
   */
  getRolesList(){
      this.apiService.get('commissions/getRoles')
      .subscribe(data => {
        //console.log("Get Roles", data)
        if(data.statusCode === "201" && data.result) {
          this.rolesList = data.result;
          //this.user.role = this.editUser.role?this.editUser.roleID: ""
          this.userForm.controls.role.setValue(this.editUser.roleID)
        }else {
          this.toastMsg.error(data.message, "Error!")
        }
      },(err: any) => {
        // console.log(err)
        this.toastMsg.error(err.message, "Error!")
      });
  }

  /**
   * Users List
   */
  getUsersList() {
    this.apiService.ssoGet('UserDetails/GetUserDetails?roleid=1')
      .subscribe(data => {
        //console.log("Get Users", data)
        if (data.statusCode === "201" && data.result) {
          this.usersList = data.result;
        } else {
          this.toastMsg.error(data.message, "Error!")
        }
      }, (err: any) => {
        // console.log(err)
        this.toastMsg.error(err.message, "Error!")
      });
  }

  /**
   * User Create & Update
   * Check Edit User Type
   */
  onSubmit() {
    //console.log(this.userForm)
    this.emailAddress = this.userForm.controls.email.value;
    var s = this.emailAddress.split("@");
    if (s.length > 1) {
      this.emailAddress = s[0];
    }
    this.emailAddress = this.emailAddress + "@trinity-solar.com"
    if (this.userForm.invalid) {
      return;
    }
    
    var recruiter = 0
    if (this.userForm.controls.recruiter.value != null) {
      recruiter = this.userForm.controls.recruiter.value;
    } 
    
    const userPayload = {
      UserName: this.userForm.controls.name.value,
      email: this.emailAddress,
      password: this.default_pass,
      RoleID: this.userForm.controls.role.value,
      Status: this.userForm.controls.status.value,
      Recruiter: recruiter
    }
    
    if(Object.keys(this.editUser).length > 0 && this.editUser.type == 'Edit'){
      userPayload["empId"] = this.editUser.empId
      delete userPayload["password"]
      //console.log("New User", userPayload)
      this.apiService.ssoPut('UserDetails', userPayload)
      .subscribe(data => {
        if(data.statusCode === "201") {
          this.toastMsg.success("User updated successfully.", "Success!");
          this.router.navigate(['ui/commissions/usermanagement/users']);
        }else {
          this.toastMsg.error(data.message, "Error!")
        }
      },(err: any) => {
        // console.log(err)
        this.toastMsg.error(err.message, "Error!")
      });
    }else{
      //console.log("New User", userPayload)
      this.apiService.ssoPost('UserDetails', userPayload)
      .subscribe(data => {
        if(data.statusCode === "201") {
          this.toastMsg.success("User created successfully.", "Success!");
          this.router.navigate(['ui/commissions/usermanagement/users']);
        }else {
          this.toastMsg.error(data.message, "Error!")
        }
      },(err: any) => {
        // console.log(err)
        this.toastMsg.error(err.message, "Error!")
      });
    }
    
  }


}
