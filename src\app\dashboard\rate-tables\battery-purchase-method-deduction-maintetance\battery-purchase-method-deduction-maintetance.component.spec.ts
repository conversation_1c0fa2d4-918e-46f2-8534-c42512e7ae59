import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { BatteryPurchaseMethodDeductionMaintetanceComponent } from './battery-purchase-method-deduction-maintetance.component';

describe('BatteryPurchaseMethodDeductionMaintetanceComponent', () => {
  let component: BatteryPurchaseMethodDeductionMaintetanceComponent;
  let fixture: ComponentFixture<BatteryPurchaseMethodDeductionMaintetanceComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ BatteryPurchaseMethodDeductionMaintetanceComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(BatteryPurchaseMethodDeductionMaintetanceComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
