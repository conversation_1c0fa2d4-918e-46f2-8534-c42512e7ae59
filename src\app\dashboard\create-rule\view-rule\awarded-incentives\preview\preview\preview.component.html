<div class="row">
    <label class="col-sm-1" style="margin-top:3px">Attempt</label>
    <div class="col-sm-3">
      <select name="preview-attempt-selection" class="custom-select" style="width: 75%" id="preview-attempt-selection_id" [(ngModel)]="selectedPreview" (change)="onPreviewAttemptChange()">
        <option *ngFor="let attempt of previewAttempts" [value]="attempt" [selected]="type == selectedPreview">
          Attempt {{attempt}}
        </option>
      </select>
    </div>
    <label class="col-sm-1">Executed By:</label>
    <div class="col-sm-2">
        <input type="text" class="custom-input" [(ngModel)]="attemptExecutedBy" disabled>
    </div>
    <label class="col-sm-1">Executed On:</label>
    <div class="col-sm-2">
        <input type="text" class="custom-input" [(ngModel)]="attemptExecutedAt" disabled>
    </div>
</div>
<table mat-table [dataSource]="awardedIncentivesDataPreview" matSort class="my-table w-100" >
    <ng-container matColumnDef="contactId">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Contact Id </th>
        <td data-td-head="Contact Id" mat-cell *matCellDef="let element"> {{element.contactId}} </td>
    </ng-container>

    <ng-container matColumnDef="ruleName">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Rule </th>
        <td data-td-head="Rule" mat-cell *matCellDef="let element"> {{element.ruleName}} </td>
    </ng-container>

    <ng-container matColumnDef="contactName">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Employee </th>
        <td data-td-head="Employee" mat-cell *matCellDef="let element"> <a [routerLink]="['/ui/commissions/salesrep', element.contactId]"
                >{{element.contactName}}</a> </td>
    </ng-container>

    <ng-container matColumnDef="salesDivision">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Division </th>
        <td data-td-head="Division" mat-cell *matCellDef="let element"> {{element.salesDivision}} </td>
    </ng-container>
    <ng-container matColumnDef="employementStatus">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Employment Status </th>
        <td data-td-head="Employment Status" mat-cell *matCellDef="let element"> {{element.employementStatus}} </td>
    </ng-container>

    <ng-container matColumnDef="stepName">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Step </th>
        <td data-td-head="Step"  mat-cell *matCellDef="let element"> {{element.stepName}} </td>
    </ng-container>

    <ng-container matColumnDef="actionValue">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Amount Due </th>
        <td data-td-head="Action"  mat-cell *matCellDef="let element"> {{element.actionValue | currency}} </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="previewColumns"></tr>
    <tr mat-row *matRowDef="let row; columns previewColumns;" (click)="onClick(row);" class="hover"></tr>
</table>
<mat-paginator #awardedPaginator [pageSizeOptions]="pageSizeOptions" [pageSize]="pageSize" (page)="pageEvent = $event;">
</mat-paginator>