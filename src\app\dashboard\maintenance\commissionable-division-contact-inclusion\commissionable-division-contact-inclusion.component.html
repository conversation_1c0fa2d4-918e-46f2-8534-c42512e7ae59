<div class="page-title col-md-12">
    <h1>Commissionable Division Contact Inclusions</h1>
    <div class="breadcrumbs">
      <a href="#">Home</a>/<span>Commissionable Division Contact Inclusions</span>
    </div>
</div>
  
<div class="content">
    <div class="card">
      <div class="card-header-info">
        <h4 class="card-title no-hover-effect">Commissionable Division Contact Inclusions</h4>
      </div>
  
      <div class="card-body">
          <div class="row">
            <div class="card-body">
              <div class="row">
                <div class="col-md-12">
                  <div class="input-group float-right table-search">
                    <input class="custom-input" type="text" id="searchTextId" [(ngModel)]="searchText" name="searchText" placeholder="Search" (input)="searchForItem()"/>
                    <span class="input-group-icon"><i class="fas fa-search"></i></span>
                  </div>
                </div>
              </div>
    
              <mat-table #table [dataSource]="dataSource" matSort>
                <ng-container matColumnDef="{{ column.id }}" *ngFor="let column of columnNames">
                  <mat-header-cell *matHeaderCellDef mat-sort-header class="table-header"> {{ column.value }} </mat-header-cell>
                  <mat-cell [attr.data-td-head]="column.value" *matCellDef="let element">{{ element[column.id] }}</mat-cell>
                </ng-container>
                <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
                <mat-row *matRowDef="let row; columns: displayedColumns" class="pointer table-content" (click)="rowClick(row)"></mat-row>
              </mat-table>
    
              <mat-paginator [pageSizeOptions]="[25, 50, 100]"showFirstLastButtons></mat-paginator>
              <div>
                <a class="btn btn-primary float-right" *ngIf="!addRow" (click)="addRow = !addRow"><i class="material-icons pointer">add_circle</i> Add</a>
                <a class="btn btn-primary float-right" *ngIf="addRow" (click)="addRow = !addRow"><i class="material-icons pointer">remove_circle</i> Hide</a>
              </div>
            </div>
          </div>

          <div class="card" *ngIf="addRow">
          <div class="card-header-info">
            <h4 class="card-title no-hover-effect">
              <i class="fas fa-plus"></i> Add Contact
            </h4>
          </div>

          <div class="card-body">
            <div>
              <form [formGroup]="form" (ngSubmit)="submit()" class="w-100">
                <div class="row">
                  <div class="form-group col-md-4">
                    <div class="row">
                      <label class="col-sm-5">Contact</label>
                      <div class="col-sm-7">
                        <input id="typeahead-prevent-manual-entry" type="text" class="custom-select" formControlName="contactId" [ngbTypeahead]="search" [inputFormatter]="formatter" 
                        [editable]="false" [resultTemplate]="rt" placeholder="Type to search"/>
                      </div>
                    </div>
                  </div>
                  <ng-template #rt let-r="result" let-t="id">
                    <div class="col-sm-12" style="width: 80%">
                      {{ r.contactName }}
                    </div>
                  </ng-template>
                </div>
                <div class="row align-button-right">
                  <button type="submit" class="btn btn-primary" [disabled]="form.invalid"><i class="fas fa-plus"></i> Add Contact</button>
                </div>
              </form>
            </div>
          </div>


        </div>
      </div>
    </div>
</div>