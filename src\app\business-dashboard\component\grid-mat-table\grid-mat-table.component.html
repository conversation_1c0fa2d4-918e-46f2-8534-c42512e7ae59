    <div class="row" *ngIf="isSearchAvailable">
      <div class="col-md-12">
        <div class="input-group float-right table-search">
          <input class="custom-input" type="text" id="searchTextId" autocomplete="off" [(ngModel)]="searchText" name="searchText"
            placeholder="Search" (input)="searchForItem()">
          <span class="input-group-icon">
            <i class="fas fa-search"></i>
          </span>
        </div>
      </div>
    </div>
    <div class="row justify-content-end">
      <div class="mt-2 mr-1">
        <img src="./assets/img/clear-filter-icon.svg" class="custom-filter" *ngIf="filterData &&filterData.length > 0" title="Clear Filters" alt="" (click)="onClearFilter()">
      </div>
    </div>
    <div [ngClass]="{'overflow-scroll': isScrollWidth}">
     
      <mat-table [ngStyle]="tableWidth && {'width': tableWidth}" [dataSource]="dataSource" matSort (matSortChange)="onSortChange($event)">
        <ng-container matColumnDef="{{column.id}}"  *ngFor="let column of columnNames;let i= index">
          <mat-header-cell *matHeaderCellDef>
            <th mat-sort-header *ngIf="column.value !=='Exclude Minimum Commission' else excludeCommission" title="{{column.value}}">{{column.value}}</th>
            <ng-template #excludeCommission>
              <th mat-sort-header title="{{column.value}}">Exclude...</th>
            </ng-template>
              <th class="cursor-pointer pt-2">
                <mat-icon class="custom-filter" *ngIf="!onEnableIcons(column.id)" (click)="onClickFilterIcon($event,column.id,column)">menu</mat-icon>
                <mat-icon class="custom-filter" *ngIf="onEnableIcons(column.id)" (click)="onClickFilterIcon($event,column.id,column)">filter_alt</mat-icon>
              </th>
          </mat-header-cell>
          <mat-cell [attr.data-td-head]="column.value" *matCellDef="let element">
            <span *ngIf="column?.routeUrl">
              <!-- ;else dateColumns -->
              <a class="search-link" [routerLink]="[column?.routeUrl, element[column?.routeId]]">{{element[column.id]}}</a>
            </span>
            <span *ngIf="!column?.routeUrl">{{element[column.id]}} </span>
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>content
        <mat-row *matRowDef="let row; columns: displayedColumns" class="pointer table-content"
              (click)="onRowClick(row)"></mat-row>
      </mat-table>
    </div>
    <div class="text-center p-2" *ngIf="gridData && gridData.length === 0 && filterData &&filterData.length ==0">
      <h5>No Data Found</h5>
    </div>
    <div class="text-center p-2" *ngIf="(filterData && filterData.length > 0) && (filterResult &&  filterResult.length == 0)">
      <h5>No Data Found</h5>
    </div>
    <mat-paginator [pageSizeOptions]="[5,10,20,50]" showFirstLastButtons></mat-paginator>
  