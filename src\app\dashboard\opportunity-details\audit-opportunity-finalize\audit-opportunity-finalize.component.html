<div class="card">
    <div class="card-header-info">
      <h4 class="card-title">Opportunity Finalization Audit</h4>
    </div>
    <div class="card-body">
      <table mat-table  #table [dataSource]="dataSource" matSort class="w-100 my-table">
        <ng-container matColumnDef="opportunityId">
            <th mat-header-cell  *matHeaderCellDef mat-sort-header> Opportunity ID </th>
            <td  data-td-head="Opportunity ID"  mat-cell  *matCellDef="let element" class="no-hover-effect">{{element.opportunityId}}</td> 
        </ng-container>
          
        <ng-container matColumnDef="finalized">
            <th mat-header-cell  *matHeaderCellDef mat-sort-header> Finalized </th>
            <td  data-td-head="Finalized"  mat-cell  *matCellDef="let element" class="no-hover-effect">{{element.finalized}}</td> 
        </ng-container>

        <ng-container matColumnDef="reasonForChange">
          <th mat-header-cell  *matHeaderCellDef mat-sort-header> Reason For Change </th>
          <td  data-td-head="Reason For Change"  mat-cell  *matCellDef="let element" class="no-hover-effect">{{element.reasonForChange}}</td> 
        </ng-container>

        <ng-container matColumnDef="userCreatedTimestamp">
          <th mat-header-cell  *matHeaderCellDef mat-sort-header> User Created Timestamp </th>
          <td  data-td-head="User Created Timestamp"  mat-cell  *matCellDef="let element" class="no-hover-effect">{{element.userCreatedTimestamp | timezoneDate}}</td> 
        </ng-container>

        <ng-container matColumnDef="userCreatedId">
          <th mat-header-cell  *matHeaderCellDef mat-sort-header> User Created ID </th>
          <td  data-td-head="User Created ID"  mat-cell  *matCellDef="let element" class="no-hover-effect">{{element.userCreatedId}}</td> 
        </ng-container>
        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row  *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>
      <mat-paginator [pageSizeOptions]="[5, 10, 20, 50]" showFirstLastButtons></mat-paginator>
    </div>
  </div>

  