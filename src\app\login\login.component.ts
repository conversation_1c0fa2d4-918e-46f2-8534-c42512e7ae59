import { Component, OnInit } from '@angular/core';
import {UntypedFormBuilder, UntypedFormGroup, Validators} from "@angular/forms";
import {Router} from "@angular/router";
import {ApiService} from "../services/api.service";
import { ToastrService } from 'ngx-toastr';



@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css']
})
export class LoginComponent implements OnInit {

  loginForm: UntypedFormGroup;
  invalidLogin: boolean = false;
  // routesObject: any = require('../model/capabilities.json');


  constructor(private formBuilder: UntypedFormBuilder, private router: Router, private apiService: ApiService, private toastMsg: ToastrService) {
    
  }

  ngOnInit() {
    
    if(this.apiService.isLoggedIn()){
      this.router.navigate(['ui/dashboard'])
    }
    this.loginForm = this.formBuilder.group({
      email: ['', Validators.compose([Validators.required, Validators.email])],
      password: ['', Validators.required]
    });
  }

  /**
   * Login post request
   */
  onSubmit() {
    console.log("ONSUBMIT");
    if (this.loginForm.invalid) {
      this.invalidLogin = true
      return;
    }
    this.invalidLogin = false
    const loginPayload = {
      email: this.loginForm.controls.email.value,
      password: this.loginForm.controls.password.value
    }
    console.log("LOGGING IN");
    this.apiService.ssoPost('Authentication/Login', loginPayload)
    .subscribe(data => {
      if(data.statusCode === "200" && data.result && data.result.user) {
        localStorage.setItem('listOfApplications', JSON.stringify(data.result.listOfApplications));
        localStorage.setItem('token', data.result.token);
        localStorage.setItem('currentUser', JSON.stringify(data.result.user));
        this.getUserCapabilities(data.result.user.empId)
        //this.router.navigate(['ui/dashboard']);
      }else {
        this.invalidLogin = true;
      }
    },(err: any) => {
      this.invalidLogin = true;
      // console.log(err)
    });
  }

  /**
   * Create Salt and store in session
   * @param capabilities 
   */
  parseCapabilityData(capabilities: any){
    
    let finalCapbilities = []
    if(capabilities.length > 0){
      capabilities.filter(obj => finalCapbilities.push(obj.name))
    }
    let encrypt = this.apiService.encryptData(finalCapbilities)
    localStorage.setItem("permissions", encrypt)
    this.router.navigate(['ui/dashboard']);
  }

  getUserCapabilities(empId: any){
    if(empId){
      this.apiService.ssoGet('UserDetails/'+empId)
      .subscribe(data => {
        if(data.statusCode == "201" && data.result && data.result[0].capabilities) {
          this.parseCapabilityData(data.result[0].capabilities)
          localStorage.setItem("cid", data.result[0].contactId?data.result[0].contactId:null)
          localStorage.setItem("role", data.result[0].roleName?data.result[0].roleName:null)
        }else {
          this.toastMsg.error('No capabilities for this user', 'Error!')
        }
      },(err: any) => {
        // console.log(err)
        this.toastMsg.error(err.message, 'Error!')
      });
    }else{
      this.toastMsg.error('Server Error', 'Error!')
    }
    
  }

}
