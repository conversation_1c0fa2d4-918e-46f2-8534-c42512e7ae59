export interface ICommission {
    commissionId: number;
    contactId: number;
    planName: string;
    ruleName: string;
    ruleId: number;
    paidTo: string;
    rateOnWattSold: number;
    totalReductions: number;
    commissionAmount: number;
    commissionablePPW: number;
    commissionWatts: number;
    solarProDeduction: number;
    opportunityId: number;
    opportunityName: string;
    opportunityFinalized: boolean;
    salesDivision: string;
    salesOffice: string;
    actualInstallDate: Date;
    dateContractSigned: Date;
    commissionFinalized: boolean;
    commissionOverridden: boolean;
    createdDate: Date;
    salesTerritory: string;
    utilityCompany: string;
    financePartner: string;
    purchaseMethod: string;
    stage: string;
    leadSource: string;
    iosTrack: string;
    systemSizeKWdc: string;
    lastRunDate: Date;
    commissionNote: string;
    batteryContractDate?: Date;
    batteryInstallDate?: Date;
    batteryPurchaseMethod?: string;
    inverterType?: string;
    moduleType?: string;
    roofingadders?: number;
    roofingContractDate?: Date;    
    roofingInstallDate?: Date;
    roofingPurchaseMethod?: string;
    roofingsaleamount?: number;
    roofSquaresIncludingwaste?: number;
    viewCategory?:string;  
    batterySaleAmount?:number;
    batteryType?:string;
    batteryQuantity?:number;
    batteryAmount?:number;
    batteryinstallcompletedate?:Date;
    batteryPTODate?:Date;
    batteryAdderAmount?:number;
    batteryContractID?:string;
    batteryFinancePartner?:string;
}

export class Commission {
    commissionId: number;
    contactId: number;
    planName: string;
    ruleName: string;
    ruleId: number;
    paidTo: string;
    rateOnWattSold: number;
    totalReductions: number;
    commissionAmount: number;
    commissionablePPW: number;
    commissionWatts: number;
    solarProDeduction: number;
    opportunityId: number;
    opportunityName: string;
    salesDivision: string;
    salesOffice: string;
    actualInstallDate: Date;
    dateContractSigned: Date;
    commissionFinalized: boolean;
    commissionOverridden: boolean;
    createdDate: Date;
    salesTerritory: string;
    utilityCompany: string;
    financePartner: string;
    purchaseMethod: string;
    stage: string;
    leadSource: string;
    iosTrack: string;
    systemSizeKWdc: string;
    lastRunDate: Date;
    commissionNote: string;
}
