import { Component, OnInit, ViewChild } from '@angular/core';
import { ApiService } from "../../../services/api.service";
import { ToastrService } from 'ngx-toastr';
import { DatePipe, PercentPipe, CurrencyPipe } from '@angular/common';
import { MatTableDataSource } from '@angular/material/table';
import { groupBy } from '../../../shared/group-by';
import { MatLegacyPaginator } from '@angular/material/legacy-paginator';
import { MatSort } from '@angular/material/sort';
import { MatLegacyDialog } from '@angular/material/legacy-dialog';
import { UntypedFormBuilder, UntypedFormGroup, Validators, UntypedFormControl, AbstractControl } from "@angular/forms";
import { getControlName } from '../../../shared/get-control-name';
import { BatteryRetrofitCommissionRateMaintenanceDialogComponent } from '../battery-retrofit-commission-rate-maintenance-dialog/battery-retrofit-commission-rate-maintenance-dialog.component';
import { TableFilterPipe } from '../../../pipe/table-filter.pipe';

@Component({
  selector: 'app-battery-retrofit-commission-rate-maintenance',
  templateUrl: './battery-retrofit-commission-rate-maintenance.component.html',
  styleUrls: ['./battery-retrofit-commission-rate-maintenance.component.css']
})
export class BatteryRetrofitCommissionRateMaintenanceComponent implements OnInit {
  columnNames = [{
    id: "stateCode",
    value: "State Code"

  }, {
    id: "batteryTypeName",
    value: "Battery Type"
  },
  {
    id: "purchaseMethod",
    value: "Purchase Method"
  },  
  {
    id: "quantity",
    value: "Quantity"
  },  
  {
    id: "effectiveStartDate",
    value: "Effective Start Date"
  },
  {
    id: "effectiveEndDate",
    value: "Effective End Date"
  }];
  displayedColumns = [];
  addInd: boolean = false;
  activeBatteryRetrofitCommissionRates: any;
  allBatteryRetrofitCommissionRates: any;
  dataSource: any;
  originalDataSource;
  BatteryRetrofitCommissionForm: UntypedFormGroup;
  dropdowns: any;
  Quantity: number = 1;
  stateCodeDefault: number = 1;
  BatteryTypeDefault: number = 1;
  purchaseMethodDefault: number = 1;
  BatteryRetrofitCommissionRatesGroup: AbstractControl[][];
  batteryRetrofitCommissionRateSelectedGroup: any;
  batteryRetrofitCommissionRateGruop: any;
  searchText: string = "";

  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatLegacyPaginator, { static: true }) paginator: MatLegacyPaginator;
  constructor(public apiService: ApiService, private toastMsg: ToastrService, private formBuilder: UntypedFormBuilder, private pipe: TableFilterPipe,
    private datePipe: DatePipe, private percentPipe: PercentPipe, private currencyPipe: CurrencyPipe, private dialog: MatLegacyDialog) { }

  ngOnInit() {
    this.BatteryRetrofitCommissionForm = this.formBuilder.group({
      stateCode: [this.stateCodeDefault, [Validators.required]],
      batteryType: [this.BatteryTypeDefault, [Validators.required]],
      purchaseMethod: [this.purchaseMethodDefault, [Validators.required]],
      quantity: [this.Quantity, [Validators.required]],
      effectiveStartDate: ['', [Validators.required]],
      batteryRetrofitSalesMetric: [0, [Validators.required]],
      batteryRetrofitCommissionAmount: [0, [Validators.required]]
    });
    this.getAllBatteryRetrofitCommissionRates();
    this.getActiveBatteryRetrofitCommissionRates();
    this.getDropdowns();
    this.BatteryRetrofitCommissionRatesGroup = [[this.BatteryRetrofitCommissionForm.controls.batteryRetrofitSalesMetric, this.BatteryRetrofitCommissionForm.controls.batteryRetrofitCommissionAmount]];
  }

  getActiveBatteryRetrofitCommissionRates() {
    this.apiService.get('BatteryRetrofitCommissionRateMaintenance')
      .subscribe(data => {
        this.activeBatteryRetrofitCommissionRates = data;
        console.log(data);
        this.displayedColumns = this.columnNames.map(x => x.id);
        this.createTable();
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }

  getAllBatteryRetrofitCommissionRates() {
    this.apiService.get('BatteryRetrofitCommissionRateMaintenance/GetAllRetrofitRates')
      .subscribe(data => {
        this.allBatteryRetrofitCommissionRates = data;
        if (this.batteryRetrofitCommissionRateGruop) {
          this.getbatteryRetrofitCommissionRateGruop(this.batteryRetrofitCommissionRateGruop[0][0]);
          this.batteryRetrofitCommissionRateSelectedGroup = this.batteryRetrofitCommissionRateGruop;
        }
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }



  getDropdowns() {  
    this.apiService.get('BatteryRetrofitCommissionRateMaintenance/dropdowns')
      .subscribe(data => {
        console.log(data)
        this.dropdowns = data;
        this.getAllBatteryRetrofitCommissionRates();
        this.getActiveBatteryRetrofitCommissionRates();
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }
  
  createTable() {
    let tableArr: Element[] = [];
    for (let i: number = 0; i <= this.activeBatteryRetrofitCommissionRates.length - 1; i++) {
      let currentRow = this.activeBatteryRetrofitCommissionRates[i];
      tableArr.push({
        batteryRetrofitCommissionMappingId: currentRow.batteryRetrofitCommissionMappingId, effectiveEndDate: this.datePipe.transform(currentRow.effectiveEndDate), effectiveStartDate: this.datePipe.transform(currentRow.effectiveStartDate),
        batteryTypeName: currentRow.batteryTypeName, batteryTypeId: currentRow.batteryTypeId, batteryRetrofitSalesMetric: this.currencyPipe.transform(currentRow.batteryRetrofitSalesMetric),
        batteryRetrofitCommissionAmount: this.currencyPipe.transform(currentRow.batteryRetrofitCommissionAmount), purchaseMethod: currentRow.purchaseMethod, purchaseMethodId: currentRow.purchaseMethodId,
         stateCode: currentRow.stateCode, stateCodeId: currentRow.stateCodeId,
         quantity: currentRow.quantity
      });
    }
    this.dataSource = new MatTableDataSource(tableArr);
    this.originalDataSource = tableArr;
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }

  clearDate(date: HTMLInputElement) {
    date.value = "";
    //this.date = null;
    this.BatteryRetrofitCommissionForm.controls.effectiveStartDate.setValue('');
    event.stopPropagation();
  }

  addFormRow() {
    this.BatteryRetrofitCommissionForm.addControl(`batteryRetrofitSalesMetric${this.BatteryRetrofitCommissionRatesGroup.length}`, new UntypedFormControl(0, []));
    this.BatteryRetrofitCommissionForm.addControl(`batteryRetrofitCommissionAmount${this.BatteryRetrofitCommissionRatesGroup.length}`, new UntypedFormControl(0, []));
    var c1 = this.BatteryRetrofitCommissionForm.get(`batteryRetrofitSalesMetric${this.BatteryRetrofitCommissionRatesGroup.length}`);
    var c2 = this.BatteryRetrofitCommissionForm.get(`batteryRetrofitCommissionAmount${this.BatteryRetrofitCommissionRatesGroup.length}`);
    c1.setValidators([Validators.required]);
    c2.setValidators([Validators.required]);
    this.BatteryRetrofitCommissionRatesGroup.push([c1, c2]);
  }

  removeFormRow(index: number) {
    if (this.BatteryRetrofitCommissionRatesGroup.length == 1) return;
    this.BatteryRetrofitCommissionRatesGroup[index].slice(0).forEach(x => {
      this.BatteryRetrofitCommissionForm.removeControl(getControlName(x));
    });
    this.BatteryRetrofitCommissionRatesGroup.splice(index, 1);
  }

  getControlName(control: AbstractControl) {
    var controlName = getControlName(control);
    return controlName;
  }

  onSubmit() {
    if (!this.BatteryRetrofitCommissionForm.invalid) {
      var groupArr = [];
      this.BatteryRetrofitCommissionRatesGroup.forEach(x => {
        groupArr.push(
          {
            "batteryRetrofitSalesMetric": x[0].value,
            "batteryRetrofitCommissionAmount": x[1].value
          }
        );
      });

      // console.log(groupArr);
      
      var body = {
        stateCodeId: this.BatteryRetrofitCommissionForm.controls.stateCode.value,
        batteryTypeId: this.BatteryRetrofitCommissionForm.controls.batteryType.value,
        purchaseMethodId: this.BatteryRetrofitCommissionForm.controls.purchaseMethod.value,
        quantity: this.BatteryRetrofitCommissionForm.controls.quantity.value,
        effectiveStartDate: this.BatteryRetrofitCommissionForm.controls.effectiveStartDate.value,
        batteryRetrofitCommissionRatesGroup: groupArr
      }
      console.log(body);

      this.apiService.post('BatteryRetrofitCommissionRateMaintenance', body)
        .subscribe(data => { 
          this.toastMsg.success('Battery commission and rate has been updated successfully.');
          this.getAllBatteryRetrofitCommissionRates();
          this.getActiveBatteryRetrofitCommissionRates(); 
          this.addInd = !this.addInd;
        }, 
        (err: any) => {
          if (err?.err?.message)
            this.toastMsg.error(err.message, "Server Error!");
          if (typeof err === "string") {
            let isJsonObject = false;            
            let parsedErr: any;
            try {
              parsedErr = JSON.parse(err);
              isJsonObject =parsedErr && typeof parsedErr === "object" &&!Array.isArray(parsedErr);
            } catch (e) {
              isJsonObject = false;
            }
            if (isJsonObject) {
              this.toastMsg.error(parsedErr.result, "Error!");
            } else this.toastMsg.error(err, "Error!");
          }
          if (err?.result) {
            this.toastMsg.error(err.result, "Error!");
          }
        }
      );
    }
  }

  rowClick(batteryRetrofitCommissionRate: any) {
    var batteryRetrofitCommissionRate = this.allBatteryRetrofitCommissionRates.filter(x => x.batteryTypeId === batteryRetrofitCommissionRate.batteryTypeId && x.purchaseMethodId === batteryRetrofitCommissionRate.purchaseMethodId && x.stateCodeId === batteryRetrofitCommissionRate.stateCodeId && x.quantity === batteryRetrofitCommissionRate.quantity);
    batteryRetrofitCommissionRate = Object.values(groupBy(batteryRetrofitCommissionRate, 'effectiveStartDate'));

    const dialogRef = this.dialog.open(BatteryRetrofitCommissionRateMaintenanceDialogComponent, {
      width: '80%', data: { batteryRetrofitCommissionRate }
    });
    dialogRef.afterClosed().subscribe(result => {
      // console.log(result);
    });
  }

  getbatteryRetrofitCommissionRateGruop(batteryRetrofitCommissionRate: any) {
    var batteryRetrofitCommissionRates = this.allBatteryRetrofitCommissionRates.filter(x => x.batteryTypeId === batteryRetrofitCommissionRate.batteryTypeId && x.purchaseMethodId === batteryRetrofitCommissionRate.purchaseMethodId && x.stateCodeId === batteryRetrofitCommissionRate.stateCodeId);
    batteryRetrofitCommissionRates = Object.values(groupBy(batteryRetrofitCommissionRates, 'effectiveStartDate'));
    this.batteryRetrofitCommissionRateGruop = batteryRetrofitCommissionRates;
    this.batteryRetrofitCommissionRateSelectedGroup = null;
  }

  groupClick(group: any) {
    this.batteryRetrofitCommissionRateSelectedGroup = this.batteryRetrofitCommissionRateGruop[0].filter(x => x.batteryRetrofitCommissionMappingId === group);
  }

  searchForItem(): void {
    let filteredResults: Element[] = [];
    if (this.searchText == '') {
      this.dataSource = new MatTableDataSource(this.originalDataSource);
      this.dataSource.sort = this.sort;
      this.dataSource.paginator = this.paginator;
    } else {
      filteredResults = this.pipe.transform(this.originalDataSource, this.searchText);
      this.dataSource = new MatTableDataSource(filteredResults);
      this.dataSource.sort = this.sort;
      this.dataSource.paginator = this.paginator;
    }

  }
}

export interface Element {
  batteryRetrofitCommissionMappingId: number,
  batteryTypeId: number,
  batteryTypeName: string,
  purchaseMethodId: number,
  purchaseMethod: string,
  stateCodeId: number,
  stateCode: string,
  quantity: number,
  effectiveStartDate: string,
  effectiveEndDate: string,
  batteryRetrofitSalesMetric: string,
  batteryRetrofitCommissionAmount: string
}
