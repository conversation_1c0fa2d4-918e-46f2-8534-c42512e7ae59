<div class="card">
  <div class="card-header card-header-info">
    <h4 class="card-title">Prompts</h4>
  </div>
  <div class="card-body">
    <div class="row" *ngIf="rule.promptAssignPlan">
      <div class="col-md-6">
        <div class="row">
          <label class="col-5">Start Date </label>
          <div class="col-7">
            <div class=" input-group date-picker">
              <input #datepickerInputStart type="date" id="start-date" class="custom-input" (change)="onChange()" [(ngModel)]="startDate">
              <span *ngIf="datepickerInputStart.value.length > 0" class="mat-icon cal-reset"  (click)="this.startDate = null; onChange();"><i class="far fa-calendar-times"></i></span> 
              <span *ngIf="datepickerInputStart.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span> 
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="row">
          <label class="col-5">End Date </label>
          <div class="col-7">
            <div class=" input-group date-picker">
              <input #datepickerInputEnd type="date" id="end-date" class="custom-input" (change)="onChange()" [(ngModel)]="endDate">
              <span *ngIf="datepickerInputEnd.value.length > 0" class="mat-icon cal-reset"  (click)="this.endDate = null; onChange();"><i class="far fa-calendar-times"></i></span> 
              <span *ngIf="datepickerInputEnd.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span> 
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="row mt-2" *ngIf="extraPrompts && extraPrompts.length > 0">
      <div class="form-group col-md-6" *ngFor="let prompt of extraPrompts">
        <div class="row">
          <label class="col-5" for="{{prompt.columnName}}">{{prompt.displayName}}</label>
          <div class="col-7">
            <input type="number" name="{{prompt.columnName}}" class="custom-input" (change)="onChange()"
              [(ngModel)]="prompt.value">
          </div>
        </div>
      </div>
    </div>
  </div>
</div>