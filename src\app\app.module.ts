import { BrowserModule } from '@angular/platform-browser';
import { NgModule, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AppRoutingModule } from './app-routing.module';
import { DashboardModule } from './dashboard/dashboard.module';
import { AppComponent } from './app.component';
// import { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';
import { HttpClientModule, HttpClient, HTTP_INTERCEPTORS } from '@angular/common/http';
import { ApiService } from './services/api.service';
import { TokenInterceptor } from './services/interceptor';
import { ErrorInterceptor } from './services/error.interceptor';
import { HeaderComponent } from './header/header.component';
import { ForgotpasswordComponent } from './forgotpassword/forgotpassword.component';
import { ResetpasswordComponent } from './resetpassword/resetpassword.component';
import { LogoutComponent } from './logout/logout.component';
import { AuthGuard } from './guards/auth.guard';
import { FooterComponent } from './footer/footer.component';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ToastrModule } from 'ngx-toastr';
import { NotfoundComponent } from './notfound/notfound.component';
import { LoaderComponent } from './loader/loader.component';
import { MatLegacyProgressSpinnerModule } from '@angular/material/legacy-progress-spinner';
import { MatLegacyAutocompleteModule } from '@angular/material/legacy-autocomplete';
import { MatLegacyInputModule  } from '@angular/material/legacy-input';
import { MatLegacyPaginatorModule } from '@angular/material/legacy-paginator';
import { MatLegacySelectModule } from '@angular/material/legacy-select';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatLegacyTooltipModule } from '@angular/material/legacy-tooltip';
import { CommonModule, DatePipe, DecimalPipe } from '@angular/common';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { MainPipeModule } from './pipe/main-pipe.module';
import { SharedModule } from './shared-module/shared.module';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatLegacyButtonModule } from '@angular/material/legacy-button';
import { MatLegacyDialogModule } from '@angular/material/legacy-dialog';
import { SidenavService } from './services/sidenav.service';
import { SidebarComponent } from './sidebar/sidebar.component';
import { SearchComponent } from './search/search.component';
// import { NgxPaginationModule } from 'ngx-pagination';

import { environment } from 'src/environments/environment';
import { MsalModule, MsalInterceptor, MSAL_INSTANCE, MsalService, MsalInterceptorConfiguration, MSAL_INTERCEPTOR_CONFIG, MsalBroadcastService, MsalGuard, MSAL_GUARD_CONFIG, MsalRedirectComponent } from '@azure/msal-angular';
import { MsalUserService } from './services/msaluser.service';

import { SharedSidebarService } from './shared/sidebar-icon';
import { NewNavigationComponent } from './new-navigation/new-navigation.component';
import { LayoutModule } from '@angular/cdk/layout';
import { MatIconModule } from '@angular/material/icon';
import { MatLegacyListModule } from '@angular/material/legacy-list';
import { UnauthorizedComponent } from './unauthorized/unauthorized.component';
import { ExportService } from './services/export.service';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { NotificationDialogComponent } from './notification-dialog/notification-dialog.component';
import { TimeagoModule } from 'ngx-timeago';
// import { InfiniteScrollModule } from 'ngx-infinite-scroll';
import { IPublicClientApplication, InteractionType, PublicClientApplication,BrowserCacheLocation } from '@azure/msal-browser';
import { MSALGuardConfigFactory, MSALInstanceFactory, MSALInterceptorConfigFactory } from './services/msal.interceptor';
import { ActivatedRoute, NavigationEnd, Router, RouterEvent } from '@angular/router';

@NgModule({
    declarations: [
        AppComponent,
        HeaderComponent,
        ForgotpasswordComponent,
        ResetpasswordComponent,
        LogoutComponent,
        FooterComponent,
        NotfoundComponent,
        LoaderComponent,
        SidebarComponent,
        SearchComponent,
        NewNavigationComponent,
        UnauthorizedComponent,
        NotificationDialogComponent
    ],
    imports: [
        BrowserModule,
        CommonModule,
        MatLegacyProgressSpinnerModule,
        MatLegacyTooltipModule,
        MatLegacyDialogModule,
        FormsModule,
        ReactiveFormsModule.withConfig({callSetDisabledState: 'whenDisabledForLegacyCode'}),
        AppRoutingModule,
        DashboardModule,
        HttpClientModule,
        DragDropModule,
        BrowserAnimationsModule,
        ToastrModule.forRoot({
            timeOut: 3000,
            positionClass: 'toast-top-full-width',
            preventDuplicates: true,
            maxOpened: 1
        }),
        MsalModule,
        MainPipeModule,
        NgMultiSelectDropDownModule.forRoot(),
        SharedModule,
        MatSidenavModule,
        MatLegacyButtonModule,
        MatLegacyPaginatorModule,
        //NgxPaginationModule,
        MatLegacySelectModule,
        MatLegacyInputModule,
        MatLegacyAutocompleteModule,
        MatToolbarModule,
        LayoutModule,
        MatIconModule,
        MatLegacyListModule,
        TimeagoModule.forRoot(),
        //InfiniteScrollModule
    ],
    exports: [
        MatLegacyTooltipModule
    ],
    providers: [
        {
          provide: HTTP_INTERCEPTORS,
          useClass: MsalInterceptor,
          multi: true
        },
        {
            provide:MSAL_INSTANCE,
            useFactory:MSALInstanceFactory
        },
        {
          provide: MSAL_GUARD_CONFIG,
          useFactory: MSALGuardConfigFactory
        },
        { 
          provide: MSAL_INTERCEPTOR_CONFIG, 
          useFactory: MSALInterceptorConfigFactory 
        },
        { provide: HTTP_INTERCEPTORS, useClass: TokenInterceptor, multi: true },
        { provide: HTTP_INTERCEPTORS, useClass: ErrorInterceptor, multi: true },
        {
          provide: HTTP_INTERCEPTORS,
          useClass: MsalInterceptor,
          multi: true
        },
        ApiService,
        AuthGuard,
        DatePipe,
        DecimalPipe,
        SidenavService,
        HttpClient,
        MsalUserService,
        MsalGuard,
        MsalService,
        MsalBroadcastService,
        SharedSidebarService,
        ExportService
    ],
    schemas:[CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
    bootstrap: [AppComponent, MsalRedirectComponent]
})
export class AppModule { }
