<div class="page-title col-md-12 ">
    <h1>Purchase Method Deduction Maintenance(Battery)</h1>
    <div class="breadcrumbs"><a href="#">Home</a>/<span>Purchase Method Deduction Maintenance(Battery)</span>
    </div>
  </div>
  
  <div class="content">
  
    <div class="card">
      <div class="card-header-info">
        <h4 class="card-title no-hover-effect"><i class="fas fa-shopping-cart"></i>Purchase Method Deductions(Battery)</h4>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-12">
  
            <div class="input-group float-right table-search">
              <input class="custom-input" type="text" id="searchTextId" [(ngModel)]="searchText" name="searchText"
                placeholder="Search" (input)="searchForItem()">
  
              <span class="input-group-icon">
                <i class="fas fa-search"></i>
              </span>
            </div>
  
          </div>
  
  
        </div>
        <mat-table #table [dataSource]="dataSource" matSort>
          <ng-container matColumnDef="{{column.id}}" *ngFor="let column of columnNames">
            <mat-header-cell *matHeaderCellDef mat-sort-header class="table-header"> {{column.value}} </mat-header-cell>
            <mat-cell *matCellDef="let element"> {{element[column.id]}} </mat-cell>
          </ng-container>
          <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
          <mat-row *matRowDef="let row; columns: displayedColumns;" class="pointer table-content" (click)="rowClick(row)">
          </mat-row>
        </mat-table>
        <mat-paginator [pageSizeOptions]="[5, 10, 20, 50]" showFirstLastButtons></mat-paginator>
        <div *ngIf="apiService.checkPermission('CreateRateTables')">
          <a class="btn  btn-primary float-right" *ngIf="!addInd" (click)="Add()"><i
              class="material-icons pointer">add_circle</i> Add</a>
          <a class="btn  btn-primary float-right" *ngIf="addInd" (click)="addInd = !addInd"><i
              class="material-icons pointer">remove_circle</i> Hide</a>
        </div>
      </div>
    </div>
    <div class="card" *ngIf="addInd">
      <div class="card-header-info">
        <h4 class="card-title no-hover-effect"><i class="fas fa-plus"></i> Add Purchase Method Deduction</h4>
      </div>
      <div class="card-body">
        <div>
          <form [formGroup]="purchaseMethodDeductionForm" (ngSubmit)="onSubmit()" class="w-100">
            <div class="row" *ngIf="activePurchaseMethodDeductions">
              <div class="form-group col-md-6">
                <div class="row">
                  <label class="col-sm-5">Purchase Method</label>
                  <div class="col-sm-7">
                    <select class="custom-select" name="purchase_method_dropdown" formControlName="purchaseMethod"
                      data-style="btn btn-link" id="purchase_method_dropdown">
                      <option *ngFor="let fp of dropdowns.purchaseMethods"  
                       [selected]="fp.purchaseMethodId == purchaseMethodDeductionRate1[0].purchaseMethodId"  value="{{fp.purchaseMethodId}}">
                        {{fp.purchaseMethod1}}</option>
                    </select>
                  </div>
                </div>
              </div>
              <div class="form-group col-md-6">
                <div class="row">
                  <label class="col-sm-5">Sales Territory</label>
                  <div class="col-sm-7">
                    <select class="custom-select" name="sales_territory_dropdown" formControlName="salesTerritory"
                      data-style="btn btn-link" id="sales_territory_dropdown">
                      <option *ngFor="let fp of dropdowns.salesTerritories"
                      [selected]="fp.salesTerritoryId == purchaseMethodDeductionRate1[0].salesTerritoryId" value="{{fp.salesTerritoryId}}">
                        {{fp.salesTerritory1}}</option>
                    </select>
                  </div>
                </div>
              </div>
  
              <div class="form-group col-md-6">
                <div class="row">
                  <label class="col-sm-5">Effective Start Date</label>
                  <div class="col-sm-7 "> <div class="date-picker w-100">
                     <input #StartDatePicker type="date" name="start_date" id="start_date" class="custom-input"
                      formControlName="effectiveStartDate" placeholder=""> 
                      <span *ngIf="StartDatePicker.value.length > 0" class="mat-icon cal-reset"  (click)="clearDate(StartDatePicker)"><i class="far fa-calendar-times"></i></span> 
                      <span *ngIf="StartDatePicker.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span>   
                    
                    <div *ngIf="purchaseMethodDeductionForm.errors && purchaseMethodDeductionForm.errors.maxDate">
                      <p style="color: red;">New Effecting Start Date should be greater than any previous start dates</p>
                    </div>
                  </div>
                </div>
                </div>
              </div>
              <div class="form-group col-md-6">
                <div class="row">
                  <label class="col-sm-5">Purchase Method Deduction Rate</label>
                  <div class="col-sm-7">
                    <input currencyMask [options]="{ allowNegative: false, align: 'left', precision: 4 }"
                    [value]="purchaseMethodDeductionRate1[0].purchaseMethodDeductionRate"   name="purchaseMethodDeductionRate" formControlName="purchaseMethodDeductionRate"
                      class="custom-input">
                    <div *ngIf="purchaseMethodDeductionRate.errors && purchaseMethodDeductionRate.errors.max">
                      <p style="color: red;">Purchase Method Deduction Rate cannot be higher than $20.00</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="align-button-right">
              <button type="submit" class="btn btn-primary" [disabled]="purchaseMethodDeductionForm.invalid"><i
                  class="fas fa-plus"></i> Add Purchase
                Method Deduction</button>
            </div>
          </form>
        </div>
      </div>
    </div>
    <div class="card" *ngIf="purchaseMethodDeductionGroup">
      <div class="card-header-info">
        <h4 class="card-title"><i class="fas fa-history"></i> History</h4>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="w-100">
            <a class="text-info"><i class="material-icons float-right blue-icon"
                (click)="purchaseMethodDeductionGroup = null">cancel</i></a>
          </div>
  
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-6">
                <div class="row">
                  <label class="col-sm-5">Purchase Method</label>
                  <span class="col-sm-7">{{purchaseMethodDeductionGroup[0].purchaseMethod}}</span>
                </div>
              </div>
  
              <div class="col-md-6">
                <div class="row">
                  <label class="col-sm-5">Sales Territory</label>
                  <span class="col-sm-7">{{purchaseMethodDeductionGroup[0].salesTerritory}}</span>
                </div>
              </div>
            </div>
  
          </div>
          <div class="col-md-12 mt-3">
            <table class="my-table mat-table w-100">
              <thead>
                <tr class="mat-header-row">
                  <th class="mat-header-cell" scope="col">Effective Start Date</th>
                  <th class="mat-header-cell" scope="col">Effective End Date</th>
                  <th class="mat-header-cell" scope="col">Purchase Method Deduction Rate</th>
                  <th class="mat-header-cell" scope="col">Sales Territory</th>
                </tr>
              </thead>
              <tbody>
                <tr class="mat-row " *ngFor="let tr of purchaseMethodDeductionGroup">
                  <td class="mat-cell" data-td-head="Effective Start Date">{{tr.effectiveStartDate | date}}</td>
                  <td class="mat-cell" data-td-head="Effective End Date">{{tr.effectiveEndDate | date}}</td>
                  <td class="mat-cell" data-td-head="Purchase Method Deduction Rate">
                    {{tr.purchaseMethodDeductionRate | currency:'USD':true:'1.4-4'}}</td>
                  <td class="mat-cell" data-td-head="Sales Territory">{{tr.salesTerritory}}</td>
  
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>