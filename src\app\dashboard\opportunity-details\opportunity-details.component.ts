import { Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ApiService } from 'src/app/services/api.service';
import { CommissionComponent } from './commission/commission.component';
import { ToastrService } from 'ngx-toastr';
import { OneTimePaymentComponent } from './onetimepayment/onetimepayment.component';
import { AuditOpportunityFinalizeComponent } from './audit-opportunity-finalize/audit-opportunity-finalize.component';

@Component({
  selector: 'app-opportunity-details',
  templateUrl: './opportunity-details.component.html',
  styleUrls: ['./opportunity-details.component.css']
})
export class OpportunityDetailsComponent implements OnInit {
  @ViewChild(CommissionComponent) private commissionComponent: CommissionComponent;
  @ViewChild(OneTimePaymentComponent) private oneTimePaymentComponent: OneTimePaymentComponent;
  @ViewChild(AuditOpportunityFinalizeComponent) private auditOpportunityFinalizeComponent: AuditOpportunityFinalizeComponent;
  opportunityId: number;
  opportunityUrl=localStorage.getItem('opportunity');

  constructor(private activatedRoute: ActivatedRoute, public apiService: ApiService, private router: Router, private toastMsg: ToastrService) {
    // this.opportunityId = this.activatedRoute.snapshot.params.opportunity_id
    // console.log(this.opportunityUrl);
    this.router.routeReuseStrategy.shouldReuseRoute = function () {
      return false;
    };
  }

  ngOnInit() {
    this.activatedRoute.params.subscribe(params => {
      this.opportunityId = params.opportunity_id;
    
      // if(!this.apiService.checkPermission('ViewOpportunityDetail')){
      //   this.router.navigate(['notfound'])
      // } 
     // Dilip - UAM issue
      if (!this.apiService.checkPermission('ViewOpportunityDetail')) {        
        this.apiService.goBack();
        this.toastMsg.error("Insufficient Permissions. Please make sure your account can access this information.", "Permissions");
      }
    })
  }

  onUpdate() {
    this.commissionComponent.getCommissions(this.opportunityId);
  }

}
