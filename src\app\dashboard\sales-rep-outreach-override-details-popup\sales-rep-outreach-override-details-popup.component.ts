import { Component, Inject, OnInit, ViewChild, ChangeDetectorRef } from '@angular/core';
import { Router, ActivatedRoute } from "@angular/router";
import { ApiService } from "../../services/api.service";
import { ToastrService } from "ngx-toastr";
import { MAT_LEGACY_DIALOG_DATA } from '@angular/material/legacy-dialog';
import { MatLegacyPaginator } from '@angular/material/legacy-paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { IOutreachOverrideDetails } from '../../model/IOutreachOverrideDetails';
import { CurrencyPipe } from '@angular/common';
import { TableFilterPipe } from 'src/app/pipe/table-filter.pipe';

@Component({ 
  selector: 'app-sales-rep-outreach-override-details-popup',
  templateUrl: './sales-rep-outreach-override-details-popup.component.html',
  styleUrls: ['./sales-rep-outreach-override-details-popup.component.css']
})
export class SalesRepOutreachOverrideDetailsPopupComponent implements OnInit {
 outreachOverrideDetails: Array<IOutreachOverrideDetails>;
 @ViewChild(MatSort, { static: true }) sort: MatSort;
 @ViewChild(MatLegacyPaginator, {static: true}) paginator: MatLegacyPaginator;
 totalAmount: any;
 originalTotalAmount: any;
 employeeName: string;
 dataSource;
 originalDataSource;
 tableArr: IOutreachOverrideDetails[] = [];
 displayedColumns = [];
 columnNames = [{
  id: "monthYear",
  value: "Month-Year"
 },
 {
  id: "paycomId",
  value: "Paycom Id"
 },
 {
  id: "employeeTitle",
  value: "Employee Title"
 },
 {
  id: "salesDivision",
  value: "Sales Division"
 },
 {
  id: "salesOffice",
  value: "Sales Office"
 },
 {
  id: "rate",
  value: "Rate"
 },
 {
  id: "opportunityCount",
  value: "Opportunity Count"
 },
 {
  id: "totalAmount",
  value: "Total Amount"
 }];
 searchText: string = "";
 monthFilter: any;

  constructor(
    private currencyPipe: CurrencyPipe,
    private pipe: TableFilterPipe,
    @Inject(MAT_LEGACY_DIALOG_DATA) public data: any
    ) 
    {
      this.outreachOverrideDetails = data.outreachOverrides;
    }
 
  ngOnInit() {
    this.displayedColumns = this.columnNames.map(x => x.id);
    this.employeeName = this.outreachOverrideDetails[0].employeeName;
    this.createTable();
    
  }

  createTable() {
    let tableArr: any[] = [];
    var total = 0;
    for (let i: number = 0; i <= this.outreachOverrideDetails.length - 1; i++) {
      let currentRow = this.outreachOverrideDetails[i];
      if(i==0)
      {
        this.tableArr[0] =this.outreachOverrideDetails[0];
      }
      total+= Number(currentRow.totalAmount);
      tableArr.push({
        monthYear: currentRow.monthYear, paycomId: currentRow.paycomId, employeeTitle: currentRow.employeeTitle, salesDivision: currentRow.salesDivision,
        salesOffice: currentRow.salesOffice, rate: currentRow.rate, opportunityCount: currentRow.opportunityCount,
        totalAmount: this.currencyPipe.transform(currentRow.totalAmount)
      });
    }
    this.totalAmount = this.currencyPipe.transform(total);
    this.originalTotalAmount = this.currencyPipe.transform(total);
    this.dataSource = new MatTableDataSource(tableArr);
    this.originalDataSource = tableArr;
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }

  searchForItem(event: any): void {
    var total = 0;
    this.clearDate(event)
    let filteredResults: any[] = [];
    if (this.searchText == '') {
      this.dataSource = new MatTableDataSource(this.originalDataSource);
      this.totalAmount = this.originalTotalAmount;
      this.dataSource.sort = this.sort;
      this.dataSource.paginator = this.paginator;
    } else {
      filteredResults = this.pipe.transform(this.originalDataSource, this.searchText);
      this.dataSource = new MatTableDataSource(filteredResults);
      filteredResults.forEach(x => {total+= Number(x.totalAmount.replace("$", "").replace(",", ""))});
      this.totalAmount = this.currencyPipe.transform(total);
      this.dataSource.sort = this.sort;
      this.dataSource.paginator = this.paginator;
    }
  }

  onMonthChange(date: any): void {
    var total = 0;
    this.searchText = "";
    const dateOptions: Intl.DateTimeFormatOptions = {
      month: 'short'
    };
    var m = new Date('January 1, 1901, 12:00:00');
    m.setMonth(Number(date.toString().substring(date.toString().length-2))-1)
    var search = m.toLocaleString('en-US', dateOptions);
    search += "-" + date.toString().substring(0, 4)
    console.log(search)
    let filteredResults: any[] = [];
    if (!search) {
      this.dataSource = new MatTableDataSource(this.originalDataSource);
      this.totalAmount = this.originalTotalAmount;
      this.dataSource.sort = this.sort;
      this.dataSource.paginator = this.paginator;
    } else {
      filteredResults = this.pipe.transform(this.originalDataSource, search);
      filteredResults.forEach(x => {total+= Number(x.totalAmount.replace("$", "").replace(",", ""))});
      this.totalAmount = this.currencyPipe.transform(total);
      this.dataSource = new MatTableDataSource(filteredResults);
      this.dataSource.sort = this.sort;
      this.dataSource.paginator = this.paginator;
    }
  }

  clearDate(date: HTMLInputElement) {
    date.value = "";   
    this.dataSource = new MatTableDataSource(this.originalDataSource);
    this.totalAmount = this.originalTotalAmount;
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
    event.stopPropagation();
  }

}
