.custom-table {
 
    width: 100%;
    background: #efefef;
}

.custom-thead {
    font-size: 1.2em;
    font-weight: bolder;
    background-color: #F3F3F3
}

.custom-tr {
    display: flex;
    justify-content: space-evenly;
    width: 100%;
    border-top: 1px solid lightgrey;
}

.custom-td {
    text-align: left;
    width: 100%;
    padding: 2px 12px;
}
.pointer {
    cursor: pointer;
}

.table-header {
    font-weight: 800;
    color:#5f5f5f;
    /* align-content: flex-start; */
}

mat-header-cell {
    display:flex;
    justify-content:flex-start;
   }

.e-grid { 
    border-color: #fafafa;
  } 

  .e-grid .e-headercelldiv {
    font-size: 15px;
  }

  .e-grid .e-headercell {
    font-size: x-large;
  }

  .e-grid.e-default .e-headercell, .e-grid.e-default .e-detailheadercell {
    border-color: black;
    border-bottom-width: 2px;
  }

  .e-grid .e-altrow { 
    background-color: #fafafa;
  } 

  .e-grid .e-gridheader {
    background-color: #fff;
    border-bottom-color: #e0e0e0;
    border-top-color: white;
  }

  .e-pager .e-pagercontainer {
    background-color: #fff;
    border-color: #e0e0e0;
    float: right;
  }