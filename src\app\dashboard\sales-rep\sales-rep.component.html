<ng-template #rt let-r="result" let-t="id">

  <div class="col-sm-12" style="width: 80%;">
  {{ r.contactName}}
  </div>
</ng-template>


<div class="page-title col-md-12 ">
  <h1 class="">Contact Details</h1>
  <div class="breadcrumbs"><a href="#">Home</a>/<span>Contact Details</span>
  </div>
</div>

<div class="content">
  <!--<app-go-back></app-go-back>-->

  <!-- Contact Details -->
  <div class="card">
    <div class="card-header-info">
      <h4 class="card-title no-hover-effect">Contact Details</h4>
    </div>
    <div class="card-body">
      <div class="row" *ngIf="contactsList">
        <div class="col-md-6">
          <div class="row">
            <div class="col-5"><label>Name</label></div>
            <div class="col-7">{{contactsList.contactName}}</div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="row">
            <div class="col-5"><label>Legal Name</label></div>
            <div class="col-7">{{contactsList.contactLegalName}}</div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="row">
            <div class="col-5"><label>Division</label> </div>
            <div class="col-7">{{contactsList.salesDivision}}</div>
          </div>
        </div>

        <div class="col-md-6">
          <div class="row">
            <div class="col-5"><label>Phone</label> </div>
            <div class="col-7"> {{contactsList.contactPhone}}</div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="row">
            <div class="col-5"><label>Email</label> </div>
            <div class="col-7">{{contactsList.contactEmail}}</div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="row">
            <div class="col-5"><label>Start Date</label> </div>
            <div class="col-7">{{contactsList.joining_date | date}}</div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="row">
            <div class="col-5"><label>Employment Status</label> </div>
            <div class="col-7">{{contactsList.employmentStatus}}</div>
          </div>
        </div>

        <div class="col-md-6">
          <div class="row">
            <div class="col-5"><label>Solar Pro</label> </div>
            <div class="col-7">{{contactsList.solarPro ? 'True': 'False'}}</div>
          </div>
        </div>

        <div class="col-md-6">
          <div class="row">
            <div class="col-5"><label>Team</label> </div>
            <div class="col-7">{{contactsList.team}}</div>
          </div>
        </div>

        <div class="col-md-6">
          <div class="row">
            <div class="col-5"><label>Roof Pro</label> </div>
            <div class="col-7">{{contactsList.roofPro ? 'True': 'False'}}</div>
          </div>
        </div>

        <div class="col-md-6">
          <div class="row">
            <div class="col-5"><label>Stryker</label> </div>
            <div class="col-7">{{contactsList.stryker ? 'True': 'False'}}</div>
          </div>
        </div>

      </div>



      <!--

          <div class="table-responsive-sm ">
            <table class="table">
              <tbody>
                <tr>
                  <th class="no-hover-effect">Name</th>
                  <th class="no-hover-effect">Legal Name</th>
                  <th class="no-hover-effect">Sales Division</th>
                  <th class="no-hover-effect">Phone</th>
                  <th class="no-hover-effect">Email</th>
                  <th class="no-hover-effect">Start Date</th>
                  <th class="no-hover-effect">Employment Status</th>
                </tr>
                <tr *ngIf="contactsList">
                  <td class="no-hover-effect"> </td>
                  <td class="no-hover-effect">{{contactsList.contactLegalName}}</td>
                  <td class="no-hover-effect">{{contactsList.salesDivision}}</td>
                  <td class="no-hover-effect">{{contactsList.contactPhone}}</td>
                  <td class="no-hover-effect">{{contactsList.contactEmail}}</td>
                  <td class="no-hover-effect">{{contactsList.joining_date | date}}</td>
                  <td class="no-hover-effect">{{contactsList.employmentStatus}}</td>
                </tr>
              </tbody>
            </table>
          </div> -->
      <div class=" text-right mt-3 ">
        <a [routerLink]="['/ui/commissions/paybook', contact_id]" *ngIf="contactPaymentBook">
          <button class="btn btn-primary" type="button"><i class="fa fa-file-invoice-dollar"></i> Payment Book</button>
        </a>

        <button class="btn btn-primary" (click)="calculateCommissions()" *ngIf="checkCanCalcCommissions()">
          <i class="fa fa-calculator"></i> Calculate Commissions</button>

        <a [routerLink]="['/ui/commissions/salesrepdashboard', contact_id]" *ngIf="checkCanViewSalesRepDashboard()">
          <button class="btn btn-primary"><i class="fas fa-chart-line"></i> Rep Dashboard</button>
        </a>
      </div>
    </div>
  </div>

  <!-- Current Rate Plan -->
  <div class="card" *ngIf="apiService.checkPermission('ViewSalesRepPayPlan')">
    <div class="card-header-info">
      <h4 class="card-title no-hover-effect">Current Pay Plan</h4>
    </div>
    <div class="card-body pt-4">
      <div class="row">
        <div class="col-md-4">

          <div *ngIf="apiService.checkPermission('ViewPaymentBooks')">
            <label class="bmd-label-floating"> Pay Plan :</label>
            <!-- <span *ngIf="plan"><a
                [routerLink]="['/ui/commissions/salesrepconfiguration/'+contact_id]">{{plan.planName}}</a>
            </span> -->
            <span *ngIf="plan"><a [routerLink]="['/ui/commissions/salesrepconfiguration/'+contact_id +'/' + contact_plan_id + '/' + plan_id]">{{plan.planName}}</a>
            </span>
          </div>

        </div>
        <div class="col-md-4">

          <label class="bmd-label-floating">Start Date :</label>
          <span *ngIf="plan" class="no-hover-effect">{{plan.startDate | date}}</span>

        </div>
        <div class="col-md-4">
          <div class="row">
            <label class="bmd-label-floating">End Date: </label>
            <span *ngIf="plan" style="width: 200px; margin: 10px;">
              <ng-container *ngIf="!plan.endDate; else showEndDate">
                <ng-container *ngIf="checkAssignPlanCapability()">

                  <div class="input-group date-picker">
                    <input #datepickerInput type="date" name="new_plan_end_date" id="new_plan_end_date"
                      class="custom-input" [(ngModel)]="newPlanEndDate" placeholder=""
                      min="{{getMinEndDate() | date: 'yyyy-MM-dd'}}">

                    <span *ngIf="datepickerInput.value.length > 0" class="mat-icon cal-reset"
                      (click)="this.newPlanEndDate = null; "><i class="far fa-calendar-times"></i></span>
                    <span *ngIf="datepickerInput.value.length <= 0" class="mat-icon cal-open"><i
                        class="far fa-calendar-alt"></i></span>
                  </div>

                </ng-container>
              </ng-container>
              <ng-template #showEndDate>
                {{plan.endDate }}
              </ng-template>
            </span>
          </div>

        </div>
        <div class="col-md-4">

          <label class="bmd-label-floating">Action:</label>
          <span *ngIf="!plan"><a [routerLink]="['/ui/commissions/planassign/'+contact_id]"
              *ngIf="apiService.checkPermission('AssignPlan')">Assign Plan</a>
          </span>
          <span *ngIf="plan && plan.planId"><a [routerLink]="['/ui/commissions/planassign/'+contact_id]"
              *ngIf="apiService.checkPermission('AssignPlan')">Change Plan</a></span>
        </div>
      </div>
      <div class="col-md-12 text-right" *ngIf="newPlanEndDate">
        <div class="row w-100 text-right justify-content-end">
          <div class="alert alert-danger w-25" *ngIf="!checkNewPlanEndDate()" style="margin-bottom: 0;">
            <!-- Effecting Start Date should be less than Effective End Date // Dilip 06/08/2020 COM-940-->
            Plan End Date cannot come before Plan Start Date

          </div>
          <button class="btn btn-primary" (click)="updatePlanEndDate()" [disabled]="!checkNewPlanEndDate()"><i
              class="fas fa-save"></i> SAVE</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Contact Plan History -->
  <div class="card" *ngIf="apiService.checkPermission('ViewPlanHistory')">
    <div class="card-header-info">
      <h4 class="card-title no-hover-effect">Plan History</h4>
    </div>
    <div class="card-body">
      <table mat-table [dataSource]="contactPlanHistory" matSort class=" w-100 my-table">
        <ng-container matColumnDef="planName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header class="mat-column-20"> Plan Name </th>
          <td data-td-head="Plan Name" mat-cell *matCellDef="let element">
            <a (click)="setUrl()"
              [routerLink]="['/ui/commissions/salesrepconfiguration/'+ element.contactId +'/' + element.contactPlanId + '/' + element.planHeaderId]">{{element.planName}}</a>
          </td>
        </ng-container>

        <ng-container matColumnDef="contactPlanStartDate">
          <th mat-header-cell *matHeaderCellDef mat-sort-header class="mat-column-20"> Start Date </th>
          <td data-td-head="Start Date" mat-cell *matCellDef="let element"> {{element.contactPlanStartDate | date}}
          </td>
        </ng-container>

        <ng-container matColumnDef="contactPlanEndDate">
          <th mat-header-cell *matHeaderCellDef mat-sort-header class="mat-column-20"> End Date </th>
          <td data-td-head="End Date" mat-cell *matCellDef="let element"> {{element.contactPlanEndDate | date}} </td>
        </ng-container>

        <ng-container matColumnDef="contactPlanComments">
          <th mat-header-cell *matHeaderCellDef mat-sort-header class="mat-column-20"> Comments </th>
          <td data-td-head="Comments" mat-cell *matCellDef="let element"> {{element.contactPlanComments}} </td>
        </ng-container>

        <ng-container matColumnDef="userCreatedTimestamp">
          <th mat-header-cell *matHeaderCellDef mat-sort-header class="mat-column-20"> Date Created </th>
          <td data-td-head="User Created Timestamp" mat-cell *matCellDef="let element"> {{element.userCreatedTimestamp | datezone}} </td>
        </ng-container>

        <ng-container matColumnDef="userCreatedId">
          <th mat-header-cell *matHeaderCellDef mat-sort-header class="mat-column-20"> Contact </th>
          <td data-td-head="User Created ID" mat-cell *matCellDef="let element">{{element.userCreatedId}}</td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="contactPlanHistoryColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: contactPlanHistoryColumns;"></tr>
      </table>
      <mat-paginator [pageSizeOptions]="pageSizeOptions" style="margin-top: 2%;">
      </mat-paginator>
    </div>
  </div>
  <!-- Contact Rates And Overrides -->
  <div class="card" *ngIf="apiService.checkPermission('ViewSalesRepOverrides')">
    <div class="card-header-info">
      <h4 class="card-title no-hover-effect">Contact Rates and Overrides</h4>
    </div>
    <div class="card-body">
      <div class="text-right">
        <mat-checkbox class="example-margin" [(ngModel)]="contactRatesAndOverridesShowInactive" (change)="getRateAndExceptionTypes()">Show Inactive</mat-checkbox>
      </div>
      <!-- Rate and Exceptions -->
      <table mat-table [dataSource]="selectedRateAndExceptions" matSort class="mt-1 w-100 my-table">
        <ng-container matColumnDef="contactRateAndExceptionTypeName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Rate/Override Type </th>
          <td data-td-head="Start Date" mat-cell *matCellDef="let element"> {{element.contactRateAndExceptionTypeName}}
          </td>
        </ng-container>

        <ng-container matColumnDef="effectiveStartDate">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Start Date </th>
          <td data-td-head="Start Date" mat-cell *matCellDef="let element"> {{element.effectiveStartDate | date}} </td>
        </ng-container>

        <ng-container matColumnDef="effectiveEndDate">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> End Date </th>
          <!-- <td data-td-head="End Date" mat-cell *matCellDef="let element"> {{element.effectiveEndDate | date}} </td> -->
          <td data-td-head="End Date" mat-cell *matCellDef="let element" class="pointer table-content">
            <ng-container *ngIf="element.effectiveEndDate == null">
              <a (click)="rowClick($event)" style="cursor:pointer">Set effective end date</a>
            </ng-container>
            <ng-container *ngIf="element.effectiveEndDate">
              {{element.effectiveEndDate | date}}
            </ng-container>
          </td>


        </ng-container>

        <!-- <ng-container [ngSwitch]="selectedRateAndExceptionType.inputType"> -->
        <ng-container matColumnDef="value">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Value </th>
          <td data-td-head="Amount" mat-cell *matCellDef="let element">
            <ng-container *ngIf="element.rateOrExceptionAmount != null; else numberCheck">
              {{element.value | currency}}
            </ng-container>
            <ng-template #numberCheck>
              <ng-container *ngIf="element.rateOrExceptionNumber != null; else percentageValue">
                {{element.value}}
              </ng-container>
            </ng-template>
            <ng-template #percentageValue>
              {{element.value | number:'1.2-2'}}%
            </ng-template>
          </td>
        </ng-container>
        <!-- </ng-container> -->

        <ng-container matColumnDef="salesTerritory">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Sales Territory </th>
          <td data-td-head="Sales Territory" mat-cell *matCellDef="let element"> {{element.salesTerritory}} </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="rateAndExceptionColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: rateAndExceptionColumns;" class="pointer table-content"
          (click)="rowClick(row)"></tr>
      </table>
      <mat-paginator [pageSizeOptions]="pageSizeOptions" style="margin-top: 2%;">
      </mat-paginator>

      <div *ngIf="apiService.checkPermission('NewSalesRepOverride')" class="text-right">
        <a class="btn btn-primary" (click)="addClick()" *ngIf="!addInd"><i class="far fa-plus-square"></i> Add
        </a>
        <a class="btn btn-primary" *ngIf="addInd" (click)="hideClick()"><i class="far fa-trash-alt"></i> Hide</a>
      </div>

      <div *ngIf="addInd || updateRateAndException" class="col-md-12">
        <ng-container *ngIf="rateAndExceptionTypes">
          <label class="mr-3 mt-2 float-left ">Rate/Override Type</label>
          <select class="custom-select col-md-3 hover" name="rateAndExceptionType" id="rateAndExceptionType"
            [(ngModel)]="contactRateAndExceptionTypeId" data-style="btn btn-link" (change)="onTypeChange($event);"
            [disabled]="updateRateAndException">
            <option *ngFor="let re of rateAndExceptionTypes" [value]="re.contactRateAndExceptionTypeId">
              {{re.contactRateAndExceptionTypeName}}</option>
          </select>
        </ng-container>
        <form [formGroup]="rateAndExceptionFormGroup" (ngSubmit)="confirm()">
          <div class="row mt-3">
            <div [ngClass]="selectedRateAndExceptionType.contactRateAndExceptionTypeId == this.commissionOnWattsSoldTypeId ? 'col-md-3' : 'col-md-4'">
              <label>Effective Start Date</label>
              <div class="input-group date-picker">
                <input #datepickerEffectiveStartDate type="date" name="start_date" id="start_date" class="custom-input"
                  formControlName="effectiveStartDate" placeholder=""
                  min="{{getMinRateStartDate() | date: 'yyyy-MM-dd'}}">
                <span *ngIf="datepickerEffectiveStartDate.value.length > 0" class="mat-icon cal-reset"
                  (click)="clearDateStart(datepickerEffectiveStartDate)"><i class="far fa-calendar-times"></i></span>
                <span *ngIf="datepickerEffectiveStartDate.value.length <= 0" class="mat-icon cal-open"><i
                    class="far fa-calendar-alt"></i></span>

                <!-- <span class="input-group-icon">
                  <i class="far fa-calendar-alt"></i>
                </span> -->
              </div>
              <div class="alert alert-danger"
                *ngIf="rateAndExceptionFormGroup.errors && rateAndExceptionFormGroup.errors.maxDate && effectiveStartDate.value != null">

                <!-- Effecting Start Date should be greater than any previous start dates // Dilip 06/08/2020 COM-940-->
                Effective Start Date should be after the previously created End Date

              </div>
              <div class="alert alert-danger"
                *ngIf="rateAndExceptionFormGroup.errors && rateAndExceptionFormGroup.errors.dateLessThanDate && effectiveStartDate.value != null">
                <!-- Effecting Start Date should be less than Effective End Date // Dilip 06/08/2020 COM-940-->
                Effective Start Date should be prior to the Effective End Date

              </div>
            </div>
            <div [ngClass]="selectedRateAndExceptionType.contactRateAndExceptionTypeId == this.commissionOnWattsSoldTypeId ? 'col-md-3' : 'col-md-4'">
              <label>Effective End Date</label>
              <div class="input-group date-picker">
                <input #datepickerEffectiveEndDate type="date" name="end_date" id="end_date" class="custom-input"
                  formControlName="effectiveEndDate" placeholder="">
                <span *ngIf="datepickerEffectiveEndDate.value.length > 0" class="mat-icon cal-reset"
                  (click)="clearDateEnd(datepickerEffectiveEndDate)"><i class="far fa-calendar-times"></i></span>
                <span *ngIf="datepickerEffectiveEndDate.value.length <= 0" class="mat-icon cal-open"><i
                    class="far fa-calendar-alt"></i></span>

              </div>
            </div>
            <div [ngClass]="selectedRateAndExceptionType.contactRateAndExceptionTypeId == this.commissionOnWattsSoldTypeId ? 'col-md-3' : 'col-md-4'" [ngSwitch]="selectedRateAndExceptionType.inputType">
              <label>{{selectedRateAndExceptionType.inputType}}</label>

              <ng-container *ngIf="selectedRateAndExceptionType.hasControlTable else elseBlock">
                <!-- AMOUNT -->
                <select *ngSwitchCase="'Amount'" class="custom-select" name="amount" id="amount"
                  data-style="btn btn-link" formControlName="amount">
                  <option *ngFor="let row of dropdowns[selectedRateAndExceptionType.contactRateAndExceptionTypeId].rows"
                    [value]="row.value">
                    {{row.displayName | currency}}</option>
                </select>
                <!-- PERCENTAGE -->
                <select *ngSwitchCase="'Percentage'" class="custom-select" name="percentage" id="percentage"
                  data-style="btn btn-link" formControlName="percentage">
                  <option *ngFor="let row of dropdowns[selectedRateAndExceptionType.contactRateAndExceptionTypeId].rows"
                    [value]="row.value">
                    {{row.displayName}}%</option>
                </select>
                <!-- NUMBER -->
                <select *ngSwitchCase="'Number'" class="custom-select" name="number" id="number"
                  data-style="btn btn-link" formControlName="number">
                  <option *ngFor="let row of dropdowns[selectedRateAndExceptionType.contactRateAndExceptionTypeId].rows"
                    [value]="row.value">
                    {{row.displayName}}</option>
                </select>
              </ng-container>

              <ng-template #elseBlock>
                <!-- AMOUNT -->
                <input *ngSwitchCase="'Amount'" currencyMask
                  [options]="{ allowNegative: false, align: 'left', precision: 2 }" name="amount"
                  formControlName="amount" class="custom-input">
                <!-- PERCENTAGE -->
                <input *ngSwitchCase="'Percentage'" currencyMask
                  [options]="{ allowNegative: false, align: 'left', prefix: '', suffix: '%', precision: 2 }"
                  name="percentage" formControlName="percentage" class="custom-input">
                <!-- NUMBER -->
                <input *ngSwitchCase="'Number'" currencyMask
                  [options]="{ allowNegative: false, align: 'left', prefix: '', precision: 0 }" name="number"
                  formControlName="number" class="custom-input">
              </ng-template>
            </div>
            <div class="col-md-3">
              <ng-container *ngIf="selectedRateAndExceptionType.contactRateAndExceptionTypeId == this.commissionOnWattsSoldTypeId">
                <label>Sales Territory</label>
                <select class="custom-select" name="salesTerritory" id="salesTerritory"
                data-style="btn btn-link" formControlName="salesTerritory" (change)="onTerritoryChange()">
                  <option *ngFor="let territory of salesTerritories" [value]="territory.salesTerritoryId">
                    {{territory.salesTerritory1}}
                  </option>
                </select>
            </ng-container>
            </div>
          </div>
          <div class="w-100 text-right">
            <button type="submit" class="btn btn-primary" [disabled]="rateAndExceptionFormGroup.invalid"><i
                class="fas fa-check"></i> SUBMIT</button>
            <!-- <p class="col-md-10" style="color: red; text-align: right; margin: 0; padding: 0;"
                    *ngIf="checkStartDate() && !rateAndExceptionFormGroup.errors">* The last Effective End Date will be
                    changed to the day before the selected Start Date</p> -->
          </div>
        </form>

      </div>
    </div>
  </div>
  <div class="card">
    <div class="card-header-info">
      <h4 class="card-title no-hover-effect">Contact Rate Incentive Modifiers</h4>
    </div>
    <div class="card-body">
      <table mat-table [dataSource]="contactRateIncentiveModifiers" matSort class="w-100 my-table">
        <ng-container matColumnDef="commissionRuleName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Commission Rule </th>
          <td data-td-head="Commission Rule" mat-cell *matCellDef="let element"> {{element.commissionRuleName}} </td>
        </ng-container>

        
        <ng-container matColumnDef="amountEligible">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Amount Eligible </th>
          <td data-td-head="Amount Eligible " mat-cell *matCellDef="let element"> 
            <span *ngIf="element.amountEligible;else amtEligible">{{element.amountEligible | currency}}</span>
            <ng-template #amtEligible>
                $0.00
            </ng-template>
          </td>
        </ng-container>
        <ng-container matColumnDef="amountGained">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Amount Gained </th>
          <td data-td-head="Amount Gained " mat-cell *matCellDef="let element"> {{element.amountGained | currency}}
          </td>
        </ng-container>

        <ng-container matColumnDef="percentGained">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Rate Gained </th>
          <td data-td-head="Rate Gained" mat-cell *matCellDef="let element"> {{element.percentGained / 100 |
            percent:'1.0-3'}}
          </td>
        </ng-container>

        <ng-container matColumnDef="effectiveStartDate">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Start Date </th>
          <td data-td-head="Start Date" mat-cell *matCellDef="let element"> {{element.effectiveStartDate | date}} </td>
        </ng-container>

        <ng-container matColumnDef="effectiveEndDate">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> End Date </th>
          <td data-td-head="End Date" mat-cell *matCellDef="let element"> {{element.effectiveEndDate | date}} </td>
        </ng-container>

        <ng-container matColumnDef="activeInd">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Active </th>
          <td data-td-head="Active" mat-cell *matCellDef="let element">
            <mat-checkbox [(ngModel)]="element.activeInd" [disabled]="true"></mat-checkbox>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="columns"></tr>
        <tr mat-row *matRowDef="let row; columns: columns;"></tr>
      </table>
      <mat-paginator [pageSizeOptions]="pageSizeOptions" style="margin-top: 2%;">
      </mat-paginator>
    </div>
  </div>

  <!-- Commission Rule Trigger Opportunities -->
  <ng-container
    *ngIf="opportunities && commissionRuleTriggers && apiService.checkPermission('ViewSalesRepRecentOpportunities')">
    <ng-container *ngFor="let ruleTrigger of commissionRuleTriggers">
      <div class="card"
        [hidden]="!opportunities[ruleTrigger.commissionRuleTriggerId] || opportunities[ruleTrigger.commissionRuleTriggerId].filteredData.length == 0">
        <div class="card-header-info">
          <h4 class="card-title no-hover-effect">Recent {{ruleTrigger.commissionRuleTriggerName}} Opportunities</h4>
        </div>
        <div class="card-body">
          <div class="table-responsive-sm ">
            <table mat-table [dataSource]="opportunities[ruleTrigger.commissionRuleTriggerId]" matSort
              id="opps-{{ruleTrigger.commissionRuleTriggerId}}-sort" class="w-100 my-table">
              <ng-container matColumnDef="opportunityName">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> Opportunity Name </th>
                <td data-td-head="Opportunity Name" mat-cell *matCellDef="let element">
                  <ng-container *ngIf="checkCanViewOpp(); else noOppLink">
                    <a (click)="setUrl()"
                      [routerLink]="['/ui/commissions/opportunitydetails', element.opportunityId]">{{element.opportunityName}}</a>
                  </ng-container>
                  <ng-template #noOppLink>
                    <span>{{element.opportunityName}}</span>
                  </ng-template>
                </td>
              </ng-container>

              <ng-container matColumnDef="salesTerritory">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> Sales Territory </th>
                <td data-td-head="Sales Territory" mat-cell *matCellDef="let element"> {{element.salesTerritory}} </td>
              </ng-container>

              <ng-container matColumnDef="stage">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> Stage </th>
                <td data-td-head="Stage" mat-cell *matCellDef="let element"> {{element.stage}} </td>
              </ng-container>

              <ng-container matColumnDef="dateContractSigned">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> Contract Signed Date </th>
                <td data-td-head="Contract Signed Date" mat-cell *matCellDef="let element">
                  {{element.dateContractSigned | date}} </td>
              </ng-container>

              <ng-container matColumnDef="actualInstallDate">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> Actual Install Start Date </th>
                <td data-td-head="Actual Install Start Date" mat-cell *matCellDef="let element">
                  {{element.actualInstallDate | date}} </td>
              </ng-container>

              <ng-container matColumnDef="opportunityFinalized">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> Opportunity Finalized </th>
                <td data-td-head="Actual Install Start Date" mat-cell *matCellDef="let element">
                  <span class="col-7">
                    <mat-checkbox [ngModel]="element.opportunityFinalized" [disabled]="true"></mat-checkbox>
                  </span>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="opportunityColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: opportunityColumns;"></tr>
            </table>
            <mat-paginator id="opps-{{ruleTrigger.commissionRuleTriggerId}}-paginator"
              [pageSizeOptions]="pageSizeOptions" style="margin-top: 2%;">
            </mat-paginator>
          </div>
        </div>
      </div>
    </ng-container>
  </ng-container>

  <!-- Recent Commissions -->
  <div class="card" *ngIf="apiService.checkPermission('ViewSalesRepRecentCommissions')">
    <div class="card-header-info">
      <h4 class="card-title no-hover-effect">Recent Commissions</h4>
    </div>
    <div class="card-body">
      <div class="table-responsive-sm ">
        <table mat-table [dataSource]="recentCommissionsList" matSort class=" w-100 my-table">
          <ng-container matColumnDef="commissionId">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Commission ID </th>
            <td data-td-head="Commission ID" mat-cell *matCellDef="let element">
              <ng-container *ngIf="element.opportunityName; else noLink">
                <a (click)="setUrl()"
                  [routerLink]="['/ui/commissions/commission', element.commissionId]">{{element.commissionId}}</a>
              </ng-container>
              <ng-template #noLink>
                <!-- <ng-container *ngIf="checkCanViewComm(); else noLinkPrivilege"> -->
                <span matTooltip="Cannot view Commission due to Rule Type">{{element.commissionId}}</span>
                <!-- </ng-container>
                    <ng-template #noLinkPrivilege>
                      <span>{{element.commissionId}}</span>
                    </ng-template> -->
              </ng-template>
            </td>
          </ng-container>

          <ng-container matColumnDef="ruleName">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Rule </th>
            <td data-td-head="Opportunity Name" mat-cell *matCellDef="let element">
              <ng-container *ngIf="checkCanViewRule(); else noRuleLink">
                <a (click)="setUrl()"
                  [routerLink]="['/ui/commissions/viewRule', element.ruleId]">{{element.ruleName}}</a>
              </ng-container>
              <ng-template #noRuleLink>
                <span>{{element.ruleName}}</span>
              </ng-template>
            </td>
          </ng-container>

          <ng-container matColumnDef="opportunityName">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Opportunity Name </th>
            <td data-td-head="Opportunity Name" mat-cell *matCellDef="let element">
              <ng-container *ngIf="checkCanViewOpp(); else noOppLink">
                <a (click)="setUrl()"
                  [routerLink]="['/ui/commissions/opportunitydetails', element.opportunityId]">{{element.opportunityName}}</a>
              </ng-container>
              <ng-template #noOppLink>
                <span>{{element.opportunityName}}</span>
              </ng-template>
            </td>
          </ng-container>

          <ng-container matColumnDef="commissionAmount">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Total Amount </th>
            <td data-td-head="Total Amount" mat-cell *matCellDef="let element"> {{element.commissionAmount | currency}}
            </td>
          </ng-container>

          <ng-container matColumnDef="createdDate">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Created Date </th>
            <td data-td-head="Created Date" mat-cell *matCellDef="let element"> {{element.createdDate | datezone}}</td>
          </ng-container>

          <ng-container matColumnDef="opportunityFinalized">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Opportunity Finalized </th>
            <td data-td-head="Actual Install Start Date" mat-cell *matCellDef="let element">
              <span class="col-7">
                <mat-checkbox [ngModel]="element.opportunityFinalized" [disabled]="true"></mat-checkbox>
              </span>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="recentCommissionColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: recentCommissionColumns;"></tr>
        </table>
        <mat-paginator [pageSizeOptions]="pageSizeOptions" style="margin-top: 2%;">
        </mat-paginator>
      </div>
    </div>
  </div>



  <!-- One Time Payment -->
  <div class="card" *ngIf="apiService.checkPermission('ViewSalesRepRecentCommissions')">
    <div class="card-header-info">
      <h4 class="card-title no-hover-effect">One Time Payment</h4>
    </div>
    <div class="card-body">
      <form [formGroup]="oneTimePaymentFormGroup" (ngSubmit)="onSubmitOneTimePayment()" class="w-100">
        <div class="row" *ngIf="commissionRuleTypes">
          <div class="form-group col-md-4">
            <div class="row">
              <label class="col-sm-5">Commission Rule</label>
              <div class="col-sm-7">
                <select class="custom-select" [(ngModel)]="commissionRuleTypeId" formControlName="commissionRuleTypeId">
                  <option [value]="0">--SELECT--</option>
                  <option *ngFor="let type of commissionRuleTypes" [value]="type.commissionRuleTypeId">
                    {{type.commissionRuleTypeName}}
                  </option>
                </select>
              </div>
            </div>
          </div>
          <div class="form-group col-md-4">
            <div class="row">
              <label class="col-sm-5">Amount</label>
              <div class="col-sm-7">
                <input currencyMask [options]="{ allowNegative: true, align: 'left', precision: 2 }" name="amount"
                  formControlName="amount" class="custom-input" autocomplete="new-amount">
                <div *ngIf="amount.errors && amount.errors.max">
                  <p style="color: red;">Amount should be non-zero value.</p>
                </div>
              </div>
            </div>
          </div>
          <!-- <div class="form-group col-md-4">
            <div class="row">
              <label class="col-sm-5">Payment for Contact</label>
              <div class="col-sm-7">
                <select class="custom-select" [(ngModel)]="contactId" formControlName="contactId">
                  <option [value]="0">--SELECT--</option>
                  <option *ngFor="let c of contacts" [value]="c.contactId">
                    {{c.contactName}}
                  </option>
                </select>
              </div>
            </div>
          </div> -->

          <div class="form-group col-md-4">
            <div class="row">
              <label class="col-sm-5">Contact</label>
              <div class="col-sm-7">
                <input id="typeahead-prevent-manual-entry" type="text" class="custom-select"
                  formControlName="contactId" [ngbTypeahead]="search" [inputFormatter]="formatter"
                  [editable]='false' [resultTemplate]="rt" placeholder="Type to search" autocomplete="new-contact"/>
              </div>
            </div>
          </div>

          <div class="form-group col-md-4">
            <div class="row">
              <label class="col-sm-5">Notes</label>
              <div class="col-sm-7">
                <input name="notes" formControlName="notes" class="custom-input" placeholder="One Time Payment Notes" autocomplete="new-notes">
              </div>
            </div>
          </div>

        </div>
        <div class="row">
          <div class="col-md-12 align-button-right">
            <button type="submit" class="btn btn-primary" [disabled]="oneTimePaymentFormGroup.invalid"><i
                class="fas fa-check"></i>
              Pay</button>
          </div>
        </div>
    </form>
    </div>

  </div>


  <!-- One time payment withdrawals -->
  <div class="card" *ngIf="apiService.checkPermission('ViewSalesRepRecentCommissions')">
    <div class="card-header-info">
      <h4 class="card-title no-hover-effect">One time payments</h4>
    </div>
    <div class="card-body">
      <div class="table-responsive-sm ">
        <table mat-table [dataSource]="payments" matSort class="my-table mt-3" style="width: 100%">
          <ng-container matColumnDef="contactName">
            <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Contact </th>
            <td data-td-head="Plan Name" mat-cell *matCellDef="let element">
              <a
                [routerLink]="['/ui/commissions/salesrepconfiguration/'+ element.contactId ]">{{element.contactName}}</a>

            </td>
          </ng-container>

          <ng-container matColumnDef="opportunityName">
            <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Opportunity </th>
            <td data-td-head="Opportunity" mat-cell *matCellDef="let element" class="hover-approval"><a
               [routerLink]="['/ui/commissions/opportunitydetails/'+ element.opportunityId]" >{{element.opportunityName}}</a> </td>
          </ng-container>

          <ng-container matColumnDef="paymentTypeName">
            <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Payment Type </th>
            <td data-td-head=" Payment Type" mat-cell *matCellDef="let element"> {{element.paymentTypeName}} </td>
          </ng-container>

          <ng-container matColumnDef="commissionTypeName">
            <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Commission Type </th>
            <td data-td-head="Commission Type" mat-cell *matCellDef="let element"> {{element.commissionTypeName}} </td>
          </ng-container>

          <ng-container matColumnDef="commissionRuleName">
            <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Commission Rule </th>
            <td data-td-head="Commission Rule" mat-cell *matCellDef="let element"> {{element.commissionRuleName}} </td>
          </ng-container>

          <ng-container matColumnDef="amount">
            <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Amount </th>
            <td data-td-head="Amount" mat-cell *matCellDef="let element"> {{element.amount | currency}} </td>
          </ng-container>

          <ng-container matColumnDef="paymentStatusName">
            <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Status </th>
            <td data-td-head="Status" mat-cell *matCellDef="let element"> {{element.paymentStatus.paymentStatusName}} </td>
          </ng-container>

          <ng-container matColumnDef="processedDate">
            <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Date Created/Processed </th>
            <td data-td-head="Payment Due Date" mat-cell *matCellDef="let element"> {{element.processedDate ?
              (element.processedDate | date) : ""}} </td>
          </ng-container>

          <ng-container matColumnDef="paymentDueDate">
            <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Payment Due Date </th>
            <td data-td-head="Payment Due Date" mat-cell *matCellDef="let element"> {{element.paymentDueDate | date}} </td>
          </ng-container>

          <ng-container matColumnDef="commissionForContact">
            <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header>Commission For Contact </th>
            <td data-td-head="Commission For Contact" mat-cell *matCellDef="let element"> {{element.commissionForContact}} </td>
          </ng-container>

          <ng-container matColumnDef="PaymentNote">
            <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header>Payment Notes </th>
            <td data-td-head="Payment Notes" mat-cell *matCellDef="let element"> {{element.paymentNote}} </td>
          </ng-container>

          <ng-container matColumnDef="PaymentReversalNote">
            <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header>Payment Reversal Notes </th>
            <td data-td-head="Payment Notes" mat-cell *matCellDef="let element"> {{element.paymentReversalNote}} </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="paymentColumns"></tr>
          <tr mat-row *matRowDef="let row; columns paymentColumns;"></tr>
        </table>
        <mat-paginator [pageSize]="pageSize" [pageSizeOptions]="pageSizeOptions">
        </mat-paginator>

      </div>
    </div>
  </div>


</div>
