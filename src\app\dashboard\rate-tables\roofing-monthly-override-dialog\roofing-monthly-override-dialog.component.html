<div class="dailog-title-bg">
    <div class="dailog-title"><i class="fas fa-history"></i> History<button class="dailog-close"
        [mat-dialog-close]><span>X</span></button>
    </div>
  </div>
  <div class="row" *ngIf="monthlyOverrideGroup">
    <div class="col-md-6">
      <div class="row">
        <label class="col-sm-5">Contact Name</label>
        <span class="col-sm-7">{{monthlyOverrideGroup[0][0].contactName}}</span>
      </div>
    </div>
    <div class="col-md-6">
      <div class="row">
        <label class="col-sm-5">Sales Division</label>
        <span class="col-sm-5">{{monthlyOverrideGroup[0][0].salesDivision}}</span>
      </div>
    </div>
    <div class="col-md-6">
      <div class="row">
        <label class="col-sm-5">Sales Office</label>
        <span class="col-sm-7">{{monthlyOverrideGroup[0][0].salesOffice}}</span>
      </div>
    </div>
    <div class="col-md-6">
      <div class="row">
        <label class="col-sm-5">Rate Amount</label>
        <span class="col-sm-7">{{monthlyOverrideGroup[0][0].rateAmount}}</span>
      </div>
    </div>
    <div class="col-md-12">
      <table class="my-table mat-table w-100 mt-3">
        <thead>
          <tr class="mat-header-row">
            <th class="mat-header-cell" scope="col">Effective Start Date</th>
            <th class="mat-header-cell" scope="col">Effective End Date</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let tr of monthlyOverrideGroup">
            <tr class="mat-row" (click)="groupClick(tr)">
              <td data-td-head="Effective Start Date" class="mat-cell">{{tr[0].effectiveStartDate | date}}</td>
              <td data-td-head="Effective End Date" class="mat-cell">{{tr[0].effectiveEndDate | date}}</td>
            </tr>
            <td colspan="2" style="background-color: #FFF;"
              *ngIf="monthlyOverrideSelectedGroup && tr[0].effectiveStartDate == monthlyOverrideSelectedGroup[0].effectiveStartDate">
  
              <table class="my-table mat-table w-100 mt-3">
                <thead>
                  <tr class="mat-header-row">
                    <th class="mat-header-cell" scope="col">Contact Name</th>
                    <th class="mat-header-cell" scope="col">Paycom Id</th>
                    <th class="mat-header-cell" scope="col">Sales Division</th>
                    <th class="mat-header-cell" scope="col">Sales Office</th>
                    <th class="mat-header-cell" scope="col">Product Type</th>
                    <th class="mat-header-cell" scope="col">Type</th>
                    <th class="mat-header-cell" scope="col">Exclude Minimum Commission</th>
                    <th class="mat-header-cell" scope="col">Rate Amount</th>
                    <th class="mat-header-cell" scope="col">Net/Gross</th>
                    <th class="mat-header-cell" scope="col">Net Value</th>
                    <th class="mat-header-cell" scope="col">Roof Install Range Name</th>
                  </tr>
                </thead>
                <tbody>
                  <tr class="mat-row" *ngFor="let tr of monthlyOverrideSelectedGroup">
                    <td data-td-head="Contact Name" class="mat-cell"> {{tr.contactName}}</td>
                    <td data-td-head="Paycom ID" class="mat-cell"> {{tr.paycomId }}</td>
                    <td data-td-head="Sales Division" class="mat-cell"> {{tr.salesDivision }}</td>
                    <td data-td-head="Sales Office" class="mat-cell"> {{tr.salesOffice }}</td>
                    <td data-td-head="Product Type" class="mat-cell">{{productTypeList}} </td>
                    <td data-td-head="Roof Rate" class="mat-cell"> {{tr.roofRateTypeName }}</td>
                    <td data-td-head="Exclude Min Commission" class="mat-cell"> {{tr.excludeMinCommission }}</td>
                    <td data-td-head="Rate Amount" class="mat-cell"> {{tr.rateAmount }}</td>
                    <td data-td-head="Net/Gross" class="mat-cell">{{tr.netInd == true ? 'Net':'Gross'}} </td>
                    <td data-td-head="Net Value" class="mat-cell"> {{tr.netValue }}</td>
                    <td data-td-head="Roof Install Range Name" class="mat-cell"> {{tr.roofInstallRangeName }}</td>
                  </tr>
                </tbody>
              </table>
            </td>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>