<div class="page-title col-md-12 ">
  <h1 class="">Sales Rep Configuration</h1>
  <div class="breadcrumbs"><a href="#">Home</a>/<a [routerLink]="['/ui/commissions/salesrep', contactId]">Contact
      Details</a>/<span>Sales Rep Configuration</span>
  </div>
</div>


<div class="content">
  <div class="row">
    <div class="col-md">
      <form>
        <div class="card">


          <div *ngIf="contactsDetails">
            <div class="card-header-info">
              <h4 class="card-title no-hover-effect"><i class="fas fa-user"></i> {{contactsDetails.contactName}}</h4>
            </div>
            <div class="card-body pt-4">
              <div class="row">
                <div class="col-md-4">
                  <div class="row">
                    <label class="col-5">Joining Date</label>
                    <div class="col-7">{{contactsDetails.joining_date | date}}</div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="row">
                    <label class="col-5">Sales Division</label>
                    <div class="col-7">{{contactsDetails.salesDivision}}</div>
                  </div>
                </div>
                <div class="col-md-4" *ngIf="planDetails">
                  <div class="row">
                    <label class="col-5">Pay Plan </label>
                    <div class="col-7">{{planDetails.planName}}</div>
                  </div>
                </div>
                <div class="col-md-4" *ngIf="planDetails">
                  <div class="row">

                    <label class="col-5">Start Date</label>
                    <div class="col-7">{{contactPlan.contactPlanStartDate | date}}</div>

                  </div>
                </div>
                <div class="col-md-4" *ngIf="planDetails">
                  <div class="row">

                    <label class="col-5">End Date</label>
                    <div class="col-7">{{contactPlan.contactPlanEndDate | date}}</div>

                  </div>
                </div>
              </div>
            </div>



            <div class="col-md-12">
              <div class="row">
                <div class="col-md-4 mt-3">
                  <div class="gray-bg pt-3 pb-3">
                    <ul id="tree1" class="custom-tree">
                      <li *ngFor="let item of basicPlan;let i = index" id={{i}} class="hover"><i
                          class="fas fa-chevron-circle-right"></i> <a id={{i}} class="hover">{{item.parent}}</a>
                        <ul>
                          <li *ngFor="let childItem of item.child;let j =index" id={{i}}
                            (click)="onChildClick(item, childItem)" class="hover"><i class="fas fa-chevron-right"></i>
                            <a class=child{{j}}>{{childItem.ruleName}}{{childItem.basePayRuleName ? " - " + childItem.basePayRuleName : ""}}</a>
                          </li>
                        </ul>
                      </li>
                    </ul>
                  </div>
                </div>
                <ng-container *ngIf="!noRule;">
                  <!-- else ruleless -->
                  <ng-container *ngIf="showPreview && ruleId && ruleType == 'Payment Book'">
                    <app-show-payment-book class="col-md-8 mt-3" [paymentBookId]="ruleId" [payPlanId]="planId" [contactPlanId]="contactPlanId"
                      [contactId]="contactId" (updated)="onUpdated($event)">
                    </app-show-payment-book>
                  </ng-container>
                  <ng-container *ngIf="showPreview && ruleId && ruleType == 'Base Pay Structure'">
                    <app-show-base-pay-structure class="col-md-8 mt-3" [ruleId]="ruleId" [basePayRuleId]="basePayRuleId"
                      [contactPlanId]="contactPlanId">
                    </app-show-base-pay-structure>
                  </ng-container>
                  <ng-container
                    *ngIf="showPreview && ruleId && ruleType == 'Employee Incentive' || ruleType == 'Base Pay' || ruleType == 'Bonus' || ruleType == 'Rate Incentive Goal' || ruleType == 'Bonus Incentive Goal' || ruleType == 'Contact Plan Inclusions'">
                    <app-show-rule class="col-md-8 mt-3" [viewRuleId]="ruleId" [contactPlanId]="contactPlanId" [contactId]="contactId" [inclusionRuleSelected]="inclusionRuleSelected" [exclusionWithInclusion]="exclusionWithInclusion">
                    </app-show-rule>
                  </ng-container>
                </ng-container>
                <ng-container *ngIf="noRule">
                  <app-show-rule-type class="col-md-8 mt-3" [contactPlanId]="contactPlanId" [ruleName]="ruleName">
                  </app-show-rule-type>
                </ng-container>
                <!-- <ng-template #ruleless>
                  <app-show-rule-type class="col-md-8 mt-3">
                  </app-show-rule-type>
                  <span>There are no current rules of this type.</span>
                </ng-template> -->
              </div>
            </div>
            <div class="card-footer align-button-right">
              <ng-container *ngIf="apiService.checkPermission('AssignPlan')">
                <div>
                  <button type="button" class="btn btn-primary salesrep-config-button"><a class="salesrep-config-link"
                      [routerLink]="['/ui/commissions/planassign', contactId]"><i class="fas fa-edit"></i> Change
                      Plan</a></button>
                  <button class="btn btn-primary" (click)="addPlanInclusion()"><i class="fas fa-folder"></i> ADD Plan
                    INCLUSION
                  </button>
                <button class="btn btn-primary" (click)="addEmployeeIncentive()"><i class="fas fa-hand-holding-usd"></i> ADD EMPLOYEE
                  INCENTIVE
                </button>
                </div>
              </ng-container>
            </div>

          </div>
        </div>
      </form>
    </div>
  </div>
</div>