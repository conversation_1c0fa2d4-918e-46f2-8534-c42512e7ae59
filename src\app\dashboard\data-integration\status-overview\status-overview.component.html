<div class="card">
  <div class="card-header-info">
    <h4 class="card-title">Status Overview</h4>
  </div>
  <div class="card-body">
    <div class="row"><div class="col-md-12 text-right">
    <button class="btn btn-primary" (click)="refresh();"><i class="fas fa-sync-alt"></i> Refresh</button>
  </div></div>
    <table class="my-table mt-3 w-100 mat-table">
      <thead >
        <tr class="mat-header-row">
          <th class="mat-header-cell">Job Type</th>
          <th class="mat-header-cell">Start Time</th>
          <th class="mat-header-cell">End Time</th>
          <th class="mat-header-cell">Status</th>
        </tr>
      </thead>
      <tbody>
        <ng-container *ngIf="overview">
          <ng-container *ngFor="let job of jobs | paginate: { itemsPerPage: 10, currentPage: p }">
            <tr class="mat-row" (click)="onRowClick(job.diJobId)">
              <td data-td-head="Job Type" class="mat-cell">{{job.diJobTypeName}}</td>
              <td data-td-head="Start Time" class="mat-cell">{{ job.jobStartTimestamp ? (job.jobStartTimestamp.toString() | timezoneDate) : "" }}</td>
              <td  data-td-head="Job Type"  class="mat-cell">{{job.jobEndTimestamp ? (job.jobEndTimestamp.toString() | timezoneDate) : ""}}</td>
              <td  data-td-head="End Time"  class="mat-cell">{{job.jobStatus}}</td>
            </tr>
            <td colspan="5" class="gray-bg"
              *ngIf="selectedJobId && overview && overview[selectedJobId] && job.diJobId == selectedJobId">
              <div class="gray-bg p-3">
                  <table class="my-table  w-100 mat-table">
<thead>
<tr class="mat-header-row">
  <th class="mat-header-cell">Extract Component</th>
  <th class="mat-header-cell">Start Time</th>
  <th class="mat-header-cell">End Time</th>
  <th class="mat-header-cell"> Status</th>
  <th class="mat-header-cell">Row Count</th>
</tr>

</thead>
<tbody>
  <tr  class="mat-row" *ngFor="let comp of overview[selectedJobId]">
<td class="mat-cell"> {{comp.diExtractComponentName}}</td>
<td class="mat-cell">      {{comp.jobComponentStartTimestamp | timezoneDate}}</td>
<td class="mat-cell"> {{comp.jobComponentEndTimestamp | timezoneDate}}</td>
<td class="mat-cell">  {{comp.jobComponentStatus}}</td>
<td class="mat-cell">     {{comp.batchCount}}</td>
  </tr>


</tbody>
                  </table>

 
              </div>
            </td>
          </ng-container>
        </ng-container>
      </tbody>
    </table>
    <!-- <table class="table table-striped table-borderless">
      <thead class="thead-light">
        <tr>
          <th>Job ID</th>
          <th>Extract Component</th>
          <th>Start Time</th>
          <th>End Time</th>
          <th>Status</th>
          <th>Batch Count</th>
        </tr>
      </thead>
      <tbody>
        <ng-container *ngIf="overview">
          <ng-container *ngFor="let o of overview | paginate: { itemsPerPage: 10, currentPage: p }">
            <tr>
              <td>{{o.diJobId}}</td>
              <td>{{o.diExtractComponentName}}</td>
              <td>{{o.jobComponentStartTimestamp | date:'medium'}}</td>
              <td>{{o.jobComponentEndTimestamp | date:'medium'}}</td>
              <td>{{o.jobComponentStatus}}</td>
              <td>{{o.batchCount}}</td>
            </tr>
          </ng-container>
        </ng-container>
      </tbody>
    </table> -->
    <pagination-controls class="float-right" (pageChange)="p = $event"></pagination-controls>
  </div>
</div>