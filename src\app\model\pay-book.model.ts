export interface IPaymentBook {
    pbId: number;
    ruleId: number;
    ruleName: string;
    ruleType: number;
    ruleDescription: string;
    pbType: string;
    overdrawLimit: number;
    weeklyPay: number;
}

export interface IContactPaymentBook {
    contactName: string;
    paymentBookTypeId: number;
    paymentBookTypeName: string;
    weeklyPay: number;
    overdrawLimit: number;
}

export interface IPaymentBookTransaction {
    selected?: boolean;
    dateProcessed: Date;
    opportunityId: number;
    opportunityName: string;
    paymentTypeName: string;
    commissionTransactionTypeName: string;
    debitCredit: string;
    amount: number;
    commissionId: number;
    paymentId: number;
    paymentNote: string;
    paymentReversalNote: string;
    paymentTransactionId: number;
    paymentStatus: string;
    contactId: number;
    contactPaymentBookId: number;
}

// colMyOpportunities: string[] = ["opportunityName", "demoDate", "dateContractSigned", "actualInstallDate", "stage", "systemSize", "appointmentConfirmed"];

export interface IMyOpportinity {
    opportunityName: string;
    demoDate: string;
    dateContractSigned: string;
    actualInstallDate: string;
    stage: string;
    systemSize: string;
    appointmentConfirmed: string;
    trinitySalespersonId: string;
    leadGeneratorId: string;
    sdrInsideSalesId: string;
    salesSuccessRepresentativeId: string;
    accountExecutiveId: string;
    batterySalespersonId : string;
    roofingSalespersonId : string ;
}


export interface IMyOppoEnabledColumns {
    ColumnName: string;
    Enabled: boolean;
}