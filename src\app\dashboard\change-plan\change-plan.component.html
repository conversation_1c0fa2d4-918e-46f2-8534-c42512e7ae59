<div class="page-title col-md-12 ">
	<h1>Assign Plan</h1>
	<div class="breadcrumbs"><a href="#">Home</a>/<ng-container *ngIf="checkCanViewContactDetails()"><a
				[routerLink]="['/ui/commissions/salesrep', contactId]">Contact
				Details</a>/</ng-container><span>Assign Plan</span>
	</div>
</div>
<div class="content">
	<form>
		<div class="card">
			<div class="card-header card-header-info">
				<h4 class="card-title no-hover-effect"><i class="fas fa-user"></i>
					{{contactsDetails != null ? contactsDetails.contactName : ""}}</h4>
			</div>
			<div class="card-body">
				<div class="col-md-12" *ngIf="contactsDetails">


					<div class="row">

						<div class="col-md-4">
							<div class="row">
								<label class="col-5">Join Date</label>
								<div class="col-7">{{contactsDetails.joining_date | date}}
								</div>
							</div>
						</div>
						<div class="col-md-4">
							<div class="row">
								<label class="col-5">Sales Division</label>
								<div class="col-7">Sales - Direct</div>
							</div>
						</div>
						<div class="col-md-4">
							<div class="row">
								<label class="col-5">Solar Pro</label>

								<!-- <td class="no-hover-effect">{{ contactsDetails.solarPro }}</td> -->
								<!-- Dilip 06/22/2020 COM-1023 -->
								<div class="col-7">
									<mat-checkbox [checked]="contactsDetails != undefined && contactsDetails.solarPro"
										[disabled]="true"></mat-checkbox>
								</div>
							</div>
						</div>

						<div class="col-md-4">
							<div class="row">
								<label class="col-5">Existing Plan</label>
								<div class="col-7">{{planDetails != undefined ? planDetails.planName : ""}}</div>
							</div>
						</div>
						<div class="col-md-4">
							<div class="row">
								<label class="col-5">Start Date :</label>
								<div class="col-7">{{planDetails != undefined ? (planDetails.startDate | date) : ""}}
								</div>
							</div>
						</div>
						<div class="col-md-4">
							<div class="row">
								<label class="col-5">End Date :</label>
								<div class="col-7">{{planDetails != undefined ? (planDetails.endDate | date) : ""}}
								</div>

							</div>
						</div>


					</div>

				</div>
			</div>
		</div>
		<div class="card">
			<div class="card-header card-header-info">
				<h4 class="card-title no-hover-effect"><i class="fas fa-file-signature"></i> Assign Plan</h4>
			</div>
			<div class="card-body">


				<!--<fieldset>
											<legend *ngIf="contactsDetails" class="no-hover-effect">
												{{contactsDetails.contactName}}</legend>
										</fieldset> -->
				<div class="form-group row">

					<div class="col-md-6">
						<div class="row">
							<!-- <div class="dropdown-menu" aria-labelledby="planType">
														<ng-container *ngIf="plansList">
															<a class="dropdown-item" href="javascript:void(0);" *ngFor="let plan of plansList">{{plan.planName}}</a>
														</ng-container>
													</div> -->
							<label class="col-5 ">Select Plan </label>
							<div class="col-7">
								<select name="plans_list" class="custom-select hover" (change)="changePlan($event);"
									style="margin: auto;" id="plans_list">
									<option value="">Select Plan</option>
									<ng-container *ngIf="plansList">
										<option *ngFor="let plan of plansList" value="{{plan.planId}}">{{plan.planName}}
										</option>
									</ng-container>
								</select></div>
						</div>
					</div>
					<div class="col-md-6">
						<div class="row">

							<label class="col-5">Start Date : </label>
							<div class="col-7">							
								<div class="input-group date-picker">
									<input #datepickerInput type="date" name="planstartday" id="planstartday" class="custom-input"
										[ngModelOptions]="{standalone :true}" [(ngModel)]="planstartday" placeholder=" ">
									<span *ngIf="datepickerInput.value.length > 0" class="mat-icon cal-reset"
										(click)="this.planstartday = null; "><i class="far fa-calendar-times"></i></span>
									<span *ngIf="datepickerInput.value.length <= 0" class="mat-icon cal-open"><i
											class="far fa-calendar-alt"></i></span>
								</div>							
							</div>
						</div>
					</div>
					
					<!-- <div class="col-md-6">
						<div class="row">
							<label class="col-5">Continue Last Plan</label>
							<div class="col-7">
								<mat-checkbox [(ngModel)]="continueLastPlan" name="continueLastPlan"></mat-checkbox>
							</div>
						</div>
					</div> -->

					<!-- <div class="form-group">
												<label class="bmd-label-floating ">End Date : </label>
													<input type="text" name="enddate" class="form-control">
											</div> -->
					<div class="mt-2 w-100">&nbsp;</div>
					<div class="col-md-12 m-auto gray-bg mt-3 pt-2 pb-3">
						<h4 class="pb-2">
							<!--Plan Name:--> {{planName}}</h4>

						<!--
																	<h4 class="no-hover-effect">Plan Details</h4>-->

						<!-- <ul id="tree1">
																					<li *ngFor="let item of basicPlan;let i = index"
																						id={{i}}
																						class="no-hover-effect">
																						{{item.parent}}
																						<ul>
																							<li *ngFor="let childItem of item.child;let j =index"
																								id={{i}}
																								class="no-hover-effect">
																								{{childItem.ruleName}}
																							</li>
																						</ul>
																					</li>
																				</ul> -->
						<div class="row">
							<div class="col-md-3" *ngFor="let item of basicPlan; let i = index">
								<div class="parent" id={{i}}>
									<h6>{{item.parent}}</h6>
									<ng-container *ngFor="let childItem of item.child; let j = index">
										<mat-checkbox *ngIf="childItem && childItem.ruleName" [checked]="true" [disabled]="checkExcludedRuleInclusion(childItem)"
											(change)="includeRule(childItem);">
											{{childItem.ruleName}}
										</mat-checkbox>
									</ng-container>
								</div>
							</div>
						</div>







					</div>
					<!-- Display Addons -->
					<app-addons [addOns]="addOns" [basePayRules]="getCurrentBasePayRules()" class="col-md-12" (addOnsOutput)="onAddOnsChange($event)"
						[hidden]="!allRules || allRules.length == 0">
					</app-addons>

					<!-- <div class="card col-md-6" *ngIf="prompts && prompts.length > 0">
									<div class="card-body">
										<ng-container *ngFor="let prompt of prompts">
											<ng-container
												*ngIf="prompt.ruleTypeName == 'Base Pay Structure'; else elseBlock">
												<app-base-pay-structure-prompt [rule]="prompt"
													(rulePrompt)="onRulePromptChange($event)">
												</app-base-pay-structure-prompt>
											</ng-container>
											<ng-template #elseBlock>
												<ng-container
													*ngIf="prompt && prompt.ruleTypeName == 'Employee Incentive'">
													<app-employee-incentive-prompt [rule]="prompt"
														(rulePrompt)="onRulePromptChange($event)">
													</app-employee-incentive-prompt>
												</ng-container>
											</ng-template>
										</ng-container>
									</div>
								</div> -->
					<div class="text-right w-100 mt-3" [hidden]="!allRules || allRules.length == 0">
						<button class="btn btn-primary" (click)="addPlanInclusion()"><i class="fas fa-folder"></i> ADD Plan
							INCLUSION
						</button>
						<button class="btn btn-primary" (click)="addEmployeeIncentive()"><i
								class="fas fa-hand-holding-usd"></i> ADD EMPLOYEE INCENTIVE
						</button>
						<button class="btn btn-primary" (click)="addBasePayStructure()"
							[disabled]="!checkBasePay()"><i
								class="fas fa-dollar-sign"></i> ADD BASE PAY
							STRUCTURE
						</button>
						<button class="btn btn-primary" (click)="addPaymentBook()" [disabled]="paymentBookAssigned()"><i
								class="fas fa-file-invoice-dollar"></i> ADD PAYMENT BOOK </button>
								
						<button class="btn btn-primary" (click)="addPaymentBookSchedule()"><i
								class="fas fa-file-invoice-dollar"></i> ADD PAYMENT BOOK SCHEDULE</button>
					</div>

				</div>
			</div>
		</div>
		<div class="text-sm-right mt-3">
			<button type="button" class="btn btn-primary" (click)="cancel()"><i class="fas fa-times"></i>
				Cancel</button>
			<button type="button" class="btn btn-primary" (click)="submitPlanAssign();"
				[disabled]="!paymentBookAssigned() || !checkNewPlanStartDate() || (checkBasePay()) || checkUniqueBasePayMappings()"><i
					class="fas fa-save"></i>
				Save</button>
		</div>
	</form>
</div>