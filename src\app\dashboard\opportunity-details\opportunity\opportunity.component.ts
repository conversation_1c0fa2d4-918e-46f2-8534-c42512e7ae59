import { Component, OnInit, Input, SimpleChanges, Output, EventEmitter } from '@angular/core';
import { ApiService } from '../../../services/api.service';
import { ToastrService } from 'ngx-toastr';
import { IOpportunity, SlopeType } from 'src/app/model/opportunity.model';
import { IOpportunityDetailRow } from 'src/app/model/opportunity-detail-row.model';
import { DatePipe, CurrencyPipe } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { ApiResponse } from 'src/app/services/api.response';
import { DatezonePipe } from 'src/app/pipe/datezone.pipe';
import { timer, Observable, Subscription, finalize } from 'rxjs';
import { TimezoneDatePipe } from 'src/app/pipe/timezone-date.pipe';
import { environment } from "../../../../environments/environment"

@Component({
  selector: 'app-opportunity',
  templateUrl: './opportunity.component.html',
  styleUrls: ['./opportunity.component.css']
})
export class OpportunityComponent implements OnInit {
  @Input() oppId: number;
  @Output() updated: EventEmitter<boolean> = new EventEmitter<boolean>();
  opportunity: IOpportunity;
  errors: any = ["Name is required", "Email is required", "Data is required"]
  opportunityDetailsList: IOpportunityDetailRow[];
  systemInfoList: IOpportunityDetailRow[];
  trinityContactList: IOpportunityDetailRow[];
  importantDatesList: IOpportunityDetailRow[];
  financeInfoList: IOpportunityDetailRow[];
  roofInfoList: IOpportunityDetailRow[];
  batteryInfoList: IOpportunityDetailRow[];
  finalizationDate:string;
  slopeType: SlopeType = {};
  rrInfoList: IOpportunityDetailRow[];
  enableEdit: boolean = false;
  updatedOpportunityValuesForm: {[key: string]: any} = {};
  isSave: boolean = false;
  appId=environment.applicationId;

  constructor(private apiService: ApiService, private toastMsg: ToastrService, private datePipe: DatePipe, private currencyPipe: CurrencyPipe, private http: HttpClient,private datezone:DatezonePipe,private timezoneDate:TimezoneDatePipe) { }

  ngOnInit() {

  }

  ngOnChanges(changes: SimpleChanges) {
    for (let propName in changes) {
      let chng = changes[propName];
      let oppId = chng.currentValue;
      this.getOppDetails(oppId);
      this.checkFinalization(oppId)
    }
  }

  getOppDetails(oppId: number) {
  if (this.isSave) {
    this.apiService.ensureShow();
  }
  this.apiService.get(`Opportunities/${oppId}`).pipe(
    finalize(() => {
      // if (this.isSave) { // Only hide if we showed
      this.isSave = false;
        this.apiService.ensureHide();
      // }
    })
  )
  .subscribe(
    (data: any) => {
      this.opportunity = <IOpportunity>data[0];
      this.isSave = false;
      if(this.opportunity && this.opportunity.opportunityFinalized){
        this.checkFinalization(oppId);
      }
      this.prepareLists();
    },
    (err: any) => {if (this.isSave) { // Only hide if we showed
      this.isSave = false;
        this.apiService.ensureHide();
      }
      this.toastMsg.error(err.message, "Server Error!");
    }
  );
}

  checkOpportunityCommission(oppId: number, isEvaluateCommission: boolean) {
    let payload = {
      "PlanExecutionJobTypeName": "Runtime_Automation",
      "Plans": [],
      "SalesDivision": "",
      "Opportunities": [oppId],
      "Contacts": []
    }

    this.http.post(`${this.apiService.workflowBaseUrl}Workflow/RunPlanExecutionV2/${isEvaluateCommission}`, payload)
      .subscribe((data: any) => {
        // console.log("Opprt", oppId, data)
        if (data && data.statusCode == '201' && data.result && data.result.errors.length > 0) {
          this.toastMsg.warning(data.result.errors.join(" & "), "Server Error!");
        } else {
          this.toastMsg.success("Commission successfully Recalculated!", "Success!");
          this.updated.emit(true);          
            this.getOppDetails(oppId);
            
          // }
        }
      }, (err: any) => {
        this.toastMsg.error(err.message, "Server Error!");
      });
  }

  checkFinalization(oppId){
    if(oppId){
      this.apiService.get(`Opportunities/OpportunityFinalizeTimestamp/${oppId}`)
      .subscribe(data => {
        this.finalizationDate = data.result;
        this.finalizationDate = this.timezoneDate.transform(this.finalizationDate);
        this.finalizationDate = this.datePipe.transform(this.finalizationDate, 'yyyy MMM dd , hh:mm:ss a');
      }, (err: any) => {
        this.toastMsg.error(err.message, "Server Error!");
      });      
    }

  }

  unfinalizeOpportunity(oppId: number) {
    this.apiService.post(`Opportunities/Unfinalize/${oppId}`, {})
      .subscribe(data => {
        if (data && data.result) {
          this.opportunity = <IOpportunity>data.result;
          this.getOppDetails(oppId);
        }
      }, (err: any) => {
        this.toastMsg.error(err.message, "Error!");
      })
  }

  prepareLists() {
    this.opportunityDetailsList = [
      { key: "Opportunity Name", value: this.opportunity.opportunityName, link: false },
      { key: "Lead Source", value: this.opportunity.leadSource, link: false },
      { key: "SFDC Opp ID", value: this.opportunity.salesForceId, link: true, route:'https://trinity-solar.lightning.force.com/lightning/r/Opportunity/'+this.opportunity.salesForceId+'/view'},
      { key: "Project Status", value: this.opportunity.projectStatus, link: false },
      { key: "Event Name", value: this.opportunity.eventName, link: false },
      { key: "Permit Payer", value: this.opportunity.permitPayer, link: false },
      { key: "Sales Territory", value: this.opportunity.salesTerritory, link: false },
      { key: "Utility Company", value: this.opportunity.utilityCompany, link: false },
      { key: "Stage", value: this.opportunity.stage, link: false },
      { key: "Stage Status", value: this.opportunity.stageStatus, link: false },
      { key: "Opportunity Type", value: this.opportunity.opportunityType, link: false },
      { key: "Appointment Territory", value: this.opportunity.appointmentTerritory, link: false },
      { key: "Direct Sales Office", value: this.opportunity.directSalesOffice, link: false },
      { key: "Territory Sales Office", value: this.opportunity.territorySalesOffice, link: false },
      { key: "County", value: this.opportunity.county, link: false },
      { key: "Lead Generator Office", value: this.opportunity.leadGeneratorOffice, link: false },
      { key: "IOS Track", value: this.opportunity.iosTrack, link: false },
      { key: "Appointment Completed", value: this.opportunity.appointmentCompleted, link: false },
      { key: "Inside Sales Campaign", value: this.opportunity.insideSalesCampaignName,editValue: this.opportunity.insideSalesCampaignId, link: false },
      { key: "Appointment Confirmed", value: this.opportunity.appointmentConfirmed, link: false },
      { key: "Confirmation Formula", value: this.opportunity.confirmationFormula, link: false },
      { key: "Monthly Demo Number", value: this.opportunity.monthlyDemoNumber, link: false },
      { key: "Opportunity Finalized", value: this.opportunity.opportunityFinalized, link: false },
      { key: "Sales Ops Role", value: this.opportunity.salesOpsRole, link: false },
    ];

    this.systemInfoList = [
      { key: "System Size kWdc", value: this.opportunity.systemSizeKWdc, link: false },
      { key: "Module Type", value: this.opportunity.moduleType, link: false },
      { key: "Inverter Type", value: this.opportunity.inverterType, link: false },
      { key: "Inverter Type 2", value: this.opportunity.inverter2Type, link: false },
      { key: "Inverter Type 3", value: this.opportunity.inverter3Type, link: false },
      { key: "Inverter Type 4", value: this.opportunity.inverter4Type, link: false },
      { key: "Installation Type", value: this.opportunity.installationType, link: false }
    ];
    this.trinityContactList = [
      { key: "Trinity Salesperson", value: this.opportunity.trinitySalespersonName, link: true, route: `/ui/commissions/salesrep/${this.opportunity.trinitySalespersonId}`, editValue: this.opportunity.trinitySalespersonId},
      { key: "Lead Generator", value: this.opportunity.leadGeneratorName, link: true, route: `/ui/commissions/salesrep/${this.opportunity.leadGeneratorId}`, editValue: this.opportunity.leadGeneratorId },
      { key: "Secondary Salesperson", value: this.opportunity.secondarySalesPersonName, link: true, route: `/ui/commissions/salesrep/${this.opportunity.secondarySalesPersonId}`, editValue: this.opportunity.secondarySalesPersonId },
      { key: "SDR Inside Sales", value: this.opportunity.sdrInsideSalesName, link: true, route: `/ui/commissions/salesrep/${this.opportunity.sdrInsideSalesId}`, editValue: this.opportunity.sdrInsideSalesId },
      { key: "Secondary Salesperson Role", value: this.opportunity.secondarySalespersonRole, link: false },
      { key: "Sales Success Representative", value: this.opportunity.salesSuccessRepresentativeName, link: true, route: `/ui/commissions/salesrep/${this.opportunity.salesSuccessRepresentativeId}`, editValue: this.opportunity.salesSuccessRepresentativeId },
      { key: "Previous Trinity Salesperson", value: this.opportunity.previousTrinitySalesperson, link: false },
      { key: "Account Executive", value: this.opportunity.accountExecutiveName, link: true, route: `/ui/commissions/salesrep/${this.opportunity.accountExecutiveId}`, editValue: this.opportunity.accountExecutiveId },
      { key: "Battery Salesperson", value: this.opportunity.batterySalesPersonName, link: true, route: `/ui/commissions/salesrep/${this.opportunity.batterySalesPersonId}`, editValue: this.opportunity.batterySalesPersonId },
      { key: "Roofing Salesperson", value: this.opportunity.roofingSalesPersonName, link: true, route: `/ui/commissions/salesrep/${this.opportunity.roofingSalesPersonId}`, editValue: this.opportunity.roofingSalesPersonId },
      { key: "Sales Ops", value: this.opportunity.salesOpsContactName, link: true, route: `/ui/commissions/salesrep/${this.opportunity.salesOpsId}`, editValue: this.opportunity.salesOpsId },
      { key: "R&R Salesperson", value: this.opportunity.rrSalespersonName, link: true, route: `/ui/commissions/salesrep/${this.opportunity.rrSalespersonId}`, editValue: this.opportunity.rrSalespersonId },
      { key: "BR Lead Generator", value: this.opportunity.brLeadGeneratorName, link: true,route: `/ui/commissions/salesrep/${this.opportunity.brLeadGeneratorId}`, editValue: this.opportunity.brLeadGeneratorId },
    ];

    this.importantDatesList = [
      { key: "Date Of First Appointment", value: this.datePipe.transform(this.opportunity.dateOfFirstAppointment), link: false, editValue:this.datePipe.transform(this.opportunity.dateOfFirstAppointment, 'yyyy-MM-dd'),isDate:true },
      { key: "Date Contract Signed", value: this.datePipe.transform(this.opportunity.dateContractSigned), link: false,editValue:this.datePipe.transform(this.opportunity.dateContractSigned, 'yyyy-MM-dd'),isDate:true },
      { key: "Actual Install Date", value: this.datePipe.transform(this.opportunity.actualInstallDate), link: false,editValue:this.datePipe.transform(this.opportunity.actualInstallDate, 'yyyy-MM-dd'),isDate:true },
      { key: "Date LOI Signed", value: this.datePipe.transform(this.opportunity.dateLoiSigned), link: false,editValue:this.datePipe.transform(this.opportunity.dateLoiSigned, 'yyyy-MM-dd'),isDate:true },
      { key: "Win Back Date", value: this.datePipe.transform(this.opportunity.winBackDate), link: false,editValue:this.datePipe.transform(this.opportunity.winBackDate, 'yyyy-MM-dd'),isDate:true },
      { key: "Actual Install Complete Date", value: this.datePipe.transform(this.opportunity.actualInstallCompleteDate), link: false,editValue:this.datePipe.transform(this.opportunity.actualInstallCompleteDate, 'yyyy-MM-dd'),isDate:true },
      { key: "Scheduled Install Date", value: this.datePipe.transform(this.opportunity.scheduledInstallDate), link: false,editValue:this.datePipe.transform(this.opportunity.scheduledInstallDate, 'yyyy-MM-dd'),isDate:true },
      { key: "Demo Date", value: this.opportunity.demoDate ? this.datezone.transform(String(this.opportunity.demoDate)) : null, link: false,editValue:this.datePipe.transform(this.opportunity.demoDate, 'yyyy-MM-dd'),isDate:true },
      { key: "Opportunity Created Date", value: this.datezone.transform(String(this.opportunity.opportunityCreatedDate)), link: false,editValue:this.datePipe.transform(this.opportunity.opportunityCreatedDate, 'yyyy-MM-dd'),isDate:true },
      { key: "Date Quoted", value: this.datePipe.transform(this.opportunity.dateQuoted), link: false,editValue:this.datePipe.transform(this.opportunity.dateQuoted, 'yyyy-MM-dd') },
      { key: "PTO Date", value: this.datePipe.transform(this.opportunity.ptoDate), link: false,editValue:this.datePipe.transform(this.opportunity.ptoDate, 'yyyy-MM-dd'),isDate:true }
    ];

    this.financeInfoList = [
      { key: "Opportunity Amount", value: this.currencyPipe.transform(this.opportunity.opportunityAmount), link: false },
      { key: "PPA Rate", value: this.opportunity.ppaRate, link: false },
      { key: "PPA Rate Escalator", value: this.opportunity.ppaRateEscalator, link: false },
      { key: "Price Per Watt", value: this.currencyPipe.transform(this.opportunity.pricePerWatt), link: false },
      { key: "Partner", value: this.opportunity.partner, link: false },
      { key: "Purchase Method", value: this.opportunity.purchaseMethod, link: false },
      { key: "Sales Chargeback Amount", value: this.currencyPipe.transform(this.opportunity.salesChargeBackAmount), link: false },
      { key: "Amount For Reduction", value: this.currencyPipe.transform(this.opportunity.amountForReduction), link: false },
      { key: "Permit Amount", value: this.currencyPipe.transform(this.opportunity.permitAmount), link: false },
      { key: "Rebate Amount", value: this.currencyPipe.transform(this.opportunity.contractRebateAmount ), link: false }, 
      { key: "Deposit Amount", value: this.currencyPipe.transform(this.opportunity.contractDepositAmount), link: false }, 
    ];

    this.roofInfoList = [
      { key: "Roofing Install Date", value: this.datePipe.transform(this.opportunity.roofingInstallDate), link: false,isDate:true },
      { key: "Roofing Contract Signed Date", value: this.datePipe.transform(this.opportunity.roofingContractDate), link: false,editValue:this.datePipe.transform(this.opportunity.roofingContractDate, 'yyyy-MM-dd'),isDate:true }, 
      { key: "Roofing Purchase Method", value: this.opportunity.roofingPurchaseMethod, link: false },
      { key: "Roof Squares (including waste)", value: this.opportunity.roofSquares, link: false },
      { key: "Roofing Sale Amount", value: this.currencyPipe.transform(this.opportunity.roofingSaleAmount), link: false },      
      { key: "Roof Sales Chargeback Amount", value: this.currencyPipe.transform(this.opportunity.roofSalesChargebackAmount), link: false },      
      { key: "Roofing Adders", value: this.currencyPipe.transform(this.opportunity.roofAdderAmount), link: false },      
      { key: "Roofing Manufacturer", value: this.opportunity.roofingManufacturer, link: false },
      { key: "Roofing Finance Partner", value: this.opportunity.roofingFinancePartnerName, link: false },
      { key: "Roofing Installation Stage", value: this.opportunity.roofingInstallationStage, link: false },          
      { key: "Roofing Contract Id", value: this.opportunity.roofingContractId, link: false },   
      { key: "Roof Date Quoted", value: this.datePipe.transform(this.opportunity.roofDateQuoted), link: false,editValue:this.datePipe.transform(this.opportunity.roofDateQuoted, 'yyyy-MM-dd') }
    ];

    this.slopeType = {lowSlope:this.opportunity.lowSlope,normalSlope:this.opportunity.normalSlope,steepSlope:this.opportunity.steepSlope};

    this.batteryInfoList = [
      { key: "Battery Install Date", value: this.datePipe.transform(this.opportunity.batteryInstallDate), link: false,editValue:this.datePipe.transform(this.opportunity.batteryInstallDate, 'yyyy-MM-dd'),isDate:true },
      { key: "Battery Contract Date", value: this.datePipe.transform(this.opportunity.batteryContractDate), link: false,editValue:this.datePipe.transform(this.opportunity.batteryContractDate, 'yyyy-MM-dd'),isDate:true }, 
      { key: "Battery Purchase Method", value: this.opportunity.batteryPurchaseMethod, link: false },
      { key: "Battery Amount", value: this.currencyPipe.transform(this.opportunity.batteryAmount), link: false },
      { key: "Battery Sale Amount", value: this.currencyPipe.transform(this.opportunity.batterySaleAmount), link: false },      
      { key: "Battery Sales Chargeback Amount", value: this.currencyPipe.transform(this.opportunity.batterySalesChargebackAmount), link: false },      
      { key: "Battery Type", value: this.opportunity.batteryType, link: false },      
      { key: "Battery Quantity", value: this.opportunity.batteryQuantity, link: false },      
      { key: "Battery Adder Amount", value: this.currencyPipe.transform(this.opportunity.batteryAdderAmount), link: false },
      { key: "Battery Finance Partner", value: this.opportunity.batteryFinancePartner, link: false },
      { key: "Battery Contract ID", value: this.opportunity.batteryContractID, link: false },          
      { key: "Battery PTO Date", value: this.datePipe.transform(this.opportunity.batteryPTODate), link: false,editValue:this.datePipe.transform(this.opportunity.batteryPTODate, 'yyyy-MM-dd'),isDate:true },   
      { key: "Battery install complete date", value: this.datePipe.transform(this.opportunity.batteryinstallcompletedate), link: false,editValue:this.datePipe.transform(this.opportunity.batteryinstallcompletedate, 'yyyy-MM-dd'),isDate:true }, 
      { key: "Battery Date Quoted", value: this.datePipe.transform(this.opportunity.batteryDateQuoted), link: false,editValue:this.datePipe.transform(this.opportunity.batteryDateQuoted, 'yyyy-MM-dd') }, 
      { key: "Components Needed", value: this.opportunity.componentsNeeded, link: false }  
    ]

    this.rrInfoList = [
      { key: "R&R Installation Stage", value: this.opportunity.rrInstallationStage, link: false},
      { key: "R&R System Size", value: this.opportunity.rrSystemSize, link: false},
      { key: "R&R Amount", value: this.currencyPipe.transform(this.opportunity.rrAmount), link: false},
      { key: "R&R Chargeback Amount", value: this.currencyPipe.transform(this.opportunity.rrChargebackAmount), link: false},
      { key: "R&R Contract Id", value: this.opportunity.rrContractId, link: false},
      { key: "R&R Contract Date", value: this.datePipe.transform(this.opportunity.rrContractDate), link: false,editValue:this.datePipe.transform(this.opportunity.rrContractDate, 'yyyy-MM-dd'),isDate:true},
      { key: "R&R Purchase Method", value: this.opportunity.rrPurchaseMethod, link: false},
      { key: "Reinstall Date", value: this.datePipe.transform(this.opportunity.reinstallDate), link: false,editValue:this.datePipe.transform(this.opportunity.reinstallDate, 'yyyy-MM-dd'),isDate:true},
      { key: "Amount Details", value: this.opportunity.amountDetails, link: false}
    ]

    if (this.opportunity.flip) {
      if (this.opportunity.nonflipSolarContractDate) {
        this.importantDatesList.push({key: "Flip - Original Solar Contract Date", value: this.datePipe.transform(this.opportunity.nonflipSolarContractDate), link: false});
      }
      if (this.opportunity.nonflipRoofContractDate) {
        this.roofInfoList.push({key: "Flip - Original Roof Contract Date", value: this.datePipe.transform(this.opportunity.nonflipRoofContractDate), link: false});
      }
      if (this.opportunity.nonflipBatteryContractDate) {
        this.batteryInfoList.push({key: "Flip - Original Battery Contract Date", value: this.datePipe.transform(this.opportunity.nonflipBatteryContractDate), link: false});
      }
    }    

  }

  checkCanViewUnfinalize(): boolean {
    if (this.apiService.checkPermission('UnfinalizeOpportunityDetails')) {
      return true;
    } else {
      return false;
    }
  }

  checkCanViewCalcReCalcCommission(): boolean {
    if (this.apiService.checkPermission('CalcRecalcCommission') && this.opportunity != undefined && !this.opportunity.opportunityFinalized) {
      return true;
    } else {
      return false;
    }
  }

  checkCanViewEvaluateCommission(): boolean {
    if (this.apiService.checkPermission('CalcRecalcCommission') && this.opportunity != undefined) {
      return true;
    } else {
      return false;
    }
  }
  onOpportunitySave(){
    this.isSave = true;
    const payloadData = {
      updates: this.updatedOpportunityValuesForm
    };
    this.apiService.patch(`Opportunities/${this.opportunity.opportunityId}`, payloadData)
      .subscribe(data => {
        this.getOppDetails(this.opportunity.opportunityId); 
        this.updatedOpportunityValuesForm = {};       
        this.enableEdit = false;
      }, (err: any) => {
        this.updatedOpportunityValuesForm = {};       
        this.enableEdit = false;        
        this.toastMsg.error(err.message, "Error!");
      })
  }
}
