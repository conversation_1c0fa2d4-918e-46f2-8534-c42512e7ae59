<div class="page-title col-md-12 ">
  <h1> Base Formula Versions</h1>
  <div class="breadcrumbs">
    <a href="#">Home</a>/<span>Base Formula Versions</span>
  </div>
</div>
<div class="content">

  <div class="card">
    <div class="card-header-info">
      <h4 class="card-title no-hover-effect">Base Formula Versions</h4>
    </div>
    <div class="card-body">
     
      <table mat-table [dataSource]="formulaVersionsDataSource" matSort class="my-table mt-3" style="width: 100%">

        <ng-container matColumnDef="selected">
      <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Activate </th>
      <td mat-cell *matCellDef="let element">
       
         <section class="checkbox-section">
           <mat-radio-group [(ngModel)]="selectedVersion">
             <mat-radio-button [value]="element.versionNumber"></mat-radio-button>
           </mat-radio-group>
        </section>
        </td>
        </ng-container>

        <ng-container matColumnDef="versionNumber">
          <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Version </th>
          <td data-td-head="Version Number" mat-cell *matCellDef="let element">
            <a style="cursor:pointer" (click)="viewRuleVersion(element.versionNumber)">{{element.versionNumber}}</a>
          </td>
        </ng-container>

        <ng-container matColumnDef="formulaName">
          <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Formula Name </th>
          <td data-td-head=" Formula Name" mat-cell *matCellDef="let element">
            {{element.formulaName}}
          </td>
        </ng-container>

        <ng-container matColumnDef="createdBy">
          <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header>Created By </th>
          <td data-td-head="Created By" mat-cell *matCellDef="let element">
            {{element.createdBy}}
          </td>
        </ng-container>

        <ng-container matColumnDef="createdDate">
          <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header>Created Date </th>
          <td data-td-head="Created Date" mat-cell *matCellDef="let element">
            {{element.createdDate | date}}
          </td>
        </ng-container>

        <ng-container matColumnDef="active">
          <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header>Status </th>
          <td data-td-head="active" mat-cell *matCellDef="let element">
            <span class="badge badge-success" *ngIf="element.active == true">Active</span>
            <span class="badge badge-danger" *ngIf="element.active == false">Inactive</span>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="formulaVersionCols"></tr>
        <tr mat-row *matRowDef="let row; columns formulaVersionCols;"></tr>
      </table>

      <mat-paginator [pageSize]="pageSize" [pageSizeOptions]="pageSizeOptions">
      </mat-paginator>


      <div *ngIf="apiService.checkPermission('CreateRateTables')">
        <button class="btn btn-primary" (click)="setSelectedVersion()" [disabled]="selectedVersion == 0">
          <i class="material-icons">check</i>Activate Selected Version
        </button>

      </div>

      <div>


      </div>
    </div>
  </div>
