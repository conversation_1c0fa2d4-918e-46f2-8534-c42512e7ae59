<div class="page-title col-md-12 ">
    <h1> Home Depot FM Outreach</h1>
    <div class="breadcrumbs">
      <a href="#">Home</a>/<span>Home Depot FM Outreach Snapshot</span>
    </div>
  </div>
  <div class="content">
  
    <div class="card">
      <div class="card-header-info">
        <h4 class="card-title no-hover-effect">Home Depot FM Outreach Snapshot</h4>
      </div>
      <div class="card-body">
        <ng-container>
          <div class="gray-bg row">
            <div class="col-md-12 pt-3 pb-3 gray-bg">
              <div class="row filter-row">
                
                <div class="form-group col-md-3">
                  <label class="bmd-label-floating">Enter Year</label>
                  <input type="number" class="custom-input" [(ngModel)]="selectedYear">
                </div>
                <div class="form-group col-md-3">
                  <label class="bmd-label-floating">Enter Month</label>
                  <input type="number" class="custom-input" [(ngModel)]="selectedMonth">
                </div>
              </div>
  
              <div class="row filter-row">
                <div class="form-group col-md-12">
              <label class="bmd-label-floating">Details</label> <br>
              This generates the monthly home depot fm outreach snapshot that is used to award monthly home depot incentive.
              </div>
              </div>
  
            </div>
          </div>
          <div class="row">
            <div class="col-md-12">
              <div class="float-right ">
                <button class="btn btn-primary" (click)="onSubmit()">
                  <i class="material-icons">filter_list</i>
                  Run Home Depot FM Outreach Snapshot
                </button>
              </div>
            </div>
          </div>
        </ng-container>
        <div>
  
  
        </div>
      </div>
    </div>
  
   
    </div>
  