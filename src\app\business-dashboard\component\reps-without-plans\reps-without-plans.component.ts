import { DatePipe } from '@angular/common';
import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { ApiService } from 'src/app/services/api.service';
import { MatTableDataSource } from '@angular/material/table';
import { Chart } from 'angular-highcharts';
import { IdateRange } from '../../models/models';

interface CustomPoint extends Highcharts.Point {
  custom: any;
}
@Component({
  selector: 'app-reps-without-plans',
  templateUrl: './reps-without-plans.component.html',
  styleUrls: ['./reps-without-plans.component.css']
})
export class RepsWithoutPlansComponent implements OnInit {
  getRepsWithoutPlans: any;
  displayedColumnsRepsWithoutPlans = [];
  dataSourceRepsWithoutPlans;
  originalDataSource: any;
  @Input() dateRange: IdateRange | null = null;
  repsWithoutPlansChart: Chart;
  repsWithoutPlansChartData: any;
  @Input() tabNumber: number | null = null;
  previousDateRange: IdateRange | null = null;
  


  dateColumns:any;
  columnNamesAlt = [{
    id: "Contact_Name",
    value: "Contact Name",
    routeUrl:"/ui/commissions/salesrep",
    routeId:"Contact_Id"
  },
  {
    id: "Paycom_Employee_Number",
    value: "Paycom/Referral No."
  }, 
  {
    id: "Sales_Office",
    value: "Sales Office"
  },
  {
    id: "Sales_Division",
    value: "Sales Division"
  },
  {
  id: "Start_Date",
  value: "Start Date",
  dataType:'Date'
  },
  {
    id: "End_Date",
    value: "End Date",
    dataType:'Date'
  },
  {
    id: "Contact_Phone",
    value: "Contact Phone"
  },
  {
    id: "Contact_Email",
    value: "Contact Email"
  }];
  columnNamesPlans = [{
    id: "Plan_Name",
    value: "Plan Name"
  }];

  constructor(public apiService: ApiService, private toastMsg: ToastrService,private datePipe: DatePipe,) { }

  ngOnInit() {
  }
  ngOnChanges(){
    if (this.tabNumber === 2) {
      if (this.dateRange) {
        if (this.previousDateRange === null || this.previousDateRange !== this.dateRange) {
          this.previousDateRange = this.dateRange;
          this.getRepsWithoutPlansData()
        }       
      }
    }
  }

  getRepsWithoutPlansData(){
    this.apiService.get('BusinessDashboard/RepsWithOutPlan')
      .subscribe((data:any) => {        
        this.getRepsWithoutPlans = data.repsWithOutPlanList.result;
        this.displayedColumnsRepsWithoutPlans = this.columnNamesAlt.map(x => x.id);
        this.dateColumns = this.columnNamesAlt.filter(s => s.dataType =='Date');
        this.createTableRepsWithoutPlans();
        this.repsWithoutPlansChartData = data.salesDivisionCount;
        this.getrepsWithoutPlansChartData();
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }
  createTableRepsWithoutPlans() {
    let tableArr: ElementWithoutPlans[] = [];
    for (let i: number = 0; i <= this.getRepsWithoutPlans.length - 1; i++) {
      let currentRow = this.getRepsWithoutPlans[i];
      tableArr.push({
        Contact_Id: currentRow.contact_Id, Contact_Name: currentRow.contact_Name,Paycom_Employee_Number:currentRow.paycom_Employee_Number, Sales_Office: currentRow.sales_Office, Sales_Division: currentRow.sales_Division,
        Start_Date: this.datePipe.transform(currentRow.start_Date), End_Date: this.datePipe.transform(currentRow.end_Date), Contact_Phone: currentRow.contact_Phone, Contact_Email: currentRow.contact_Email
      });
    }
    this.dataSourceRepsWithoutPlans = new MatTableDataSource(tableArr);
    this.originalDataSource = tableArr;
  }

  getrepsWithoutPlansChartData() {
    let chart = new Chart({
      chart: {
        plotBackgroundColor: null,
        plotBorderWidth: null,
        plotShadow: false,
      },
      title: {
        text: 'Reps by Sales Division'
      },
      tooltip: {
        pointFormatter: function() {
          const point = this as CustomPoint;
          return `Reps: <b>${point.y.toLocaleString("en-US")}`;
        },
      },
      accessibility: {
        point: {
          valueSuffix: '%',
        },
      },
      credits: {
        enabled: false
      },
      legend: {
        maxHeight: 90,  
      },
      plotOptions: {
        pie: {
            allowPointSelect: true,
            innerSize: '50%',
            cursor: 'pointer',
            dataLabels: {
                enabled: true,
            },
            showInLegend: true
        },
      },
      series: [
        {
          type: 'pie',
          name: 'Reps',
          showInLegend: true,
          data: this.repsWithoutPlansChartData
        }
      ]
    });
    this.repsWithoutPlansChart = chart;
  }
}

export interface ElementWithoutPlans {
  Contact_Id: string,
  Contact_Name: string,
  Paycom_Employee_Number:string,
  Sales_Office: string,
  Sales_Division: string,
  Start_Date: string,
  End_Date: string,
  Contact_Phone: string,
  Contact_Email: string
}
