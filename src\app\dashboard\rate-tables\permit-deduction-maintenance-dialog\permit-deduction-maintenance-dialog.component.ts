import { Component, OnInit, Inject } from '@angular/core';
import { MatLegacyDialogRef, MAT_LEGACY_DIALOG_DATA } from '@angular/material/legacy-dialog';
import { ApiService } from '../../../services/api.service';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-permit-deduction-maintenance-dialog',
  templateUrl: './permit-deduction-maintenance-dialog.component.html',
  styleUrls: ['./permit-deduction-maintenance-dialog.component.css']
})
export class PermitDeductionMaintenanceDialogComponent implements OnInit {
  permitDeductionGroup: Element[] = [];

  constructor(public dialogRef: MatLegacyDialogRef<PermitDeductionMaintenanceDialogComponent>,
    private apiService: ApiService, private toastMsg: ToastrService, @Inject(MAT_LEGACY_DIALOG_DATA) public data: any) { }


  ngOnInit() {
  
  this.permitDeductionGroup = this.data.permitDeductions;
  this.permitDeductionGroup.sort((a, b) => {
    return <any>new Date(b.effectiveStartDate) - <any>new Date(a.effectiveStartDate);
  });
  }

}

export interface Element {
  effectiveStartDate: string,
  effectiveEndDate: string,
  permitDeductionRate: string,
  utilityCompany: string,
  minimumPpw: string,
  salesTerritory: string,
  financePartner: string,
  purchaseMethod: string
}


