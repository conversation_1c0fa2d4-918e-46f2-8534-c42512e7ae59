import { Component, OnInit, Input, SimpleChanges } from '@angular/core';
import { ApiService } from "../../../services/api.service";
import { ToastrService } from 'ngx-toastr';
import { IBasePayStructure, IContactBasePayStructure, IContactBasePayStructureForm, IContactBasePayStructureUpdate } from '../../../model/base-pay-structure.model';
import { IPaymentType } from 'src/app/model/payment-type.model';
import { IRule } from 'src/app/model/rule.model';
import { IPayStreamItem } from 'src/app/model/base-pay-structure.model';
import { IBasePayStructureForm } from 'src/app/model/base-pay-structure.model';
import { HttpClient } from '@angular/common/http';
import { environment } from "../../../../environments/environment"
import { ApiResponse } from 'src/app/services/api.response';
import { IPaymentDueDateMapping } from 'src/app/model/payment-due-date-mapping.model';

@Component({
  selector: 'app-show-base-pay-structure',
  templateUrl: './show-base-pay-structure.component.html',
  styleUrls: ['./show-base-pay-structure.component.css']
})
export class ShowBasePayStructureComponent implements OnInit {
  @Input() ruleId: number;
  @Input() basePayRuleId: number;
  @Input() contactPlanId: number;
  rulesForm: any = {};
  payStreamList: any[] = [];
  ruleTypes: any[] = [];
  paymentTypes: IPaymentType[];
  basePayStructureRules: IRule[];
  basePayStructureForm: IContactBasePayStructureForm = <IContactBasePayStructureForm>{};
  edit: boolean = false;
  baseUrl: string = `${environment.apiBaseUrl}BasePayStructures`;
  paymentDueDateMappings: IPaymentDueDateMapping[];

  constructor(public apiService: ApiService, private http: HttpClient, private toastMsg: ToastrService) {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes.contactPlanId || changes.basePayRuleId || changes.ruleId) {
      this.getPaymentDueDateMappings();
    }
  }

  ngOnInit() {
    this.getPaymentTypes();
  }

  onEditChange() {
    this.edit = !this.edit;

    if (this.edit) {
      // console.log("Base Pay Structure Form", this.basePayStructureForm);
      // this.basePayStructureForm.contactBasePayStructures.forEach(cbps => {
      //   cbps.startDate = null;
      // })
    } else {
      this.getBasePayStructureForm();
    }
  }

  changeVal(input: any, val: any): any {
    var filterValue: string;
    if (typeof input === "string") {
      filterValue = input;
    } else {
      filterValue = (input.target as HTMLInputElement).value;
    }
    
    this.basePayStructureForm.contactBasePayStructures.forEach((cbps: IContactBasePayStructure, i) => {
      if (this.basePayStructureForm.contactBasePayStructures[i] != null) {
        let cbps = this.basePayStructureForm.contactBasePayStructures[i];

        cbps.payStream.forEach((ps: IPayStreamItem, j) => {
          if (ps.basePayStreamId == val.basePayStreamId) {
            ps.daysInAdvance = parseInt(filterValue);
          }
        });
      }
    });
  }

  changeDueDateVal(input: any, val: any): any {
    var filterValue: string;
    if (typeof input === "string") {
      filterValue = input;
    } else {
      filterValue = (input.target as HTMLInputElement).value;
    }
    
    this.basePayStructureForm.contactBasePayStructures.forEach((cbps: IContactBasePayStructure, i) => {
      if (this.basePayStructureForm.contactBasePayStructures[i] != null) {
        let cbps = this.basePayStructureForm.contactBasePayStructures[i];

        cbps.payStream.forEach((ps: IPayStreamItem, j) => {
          if (ps.basePayStreamId == val.basePayStreamId) {
            ps.paymentDueDateMappingId = parseInt(filterValue);
          }
        });
      }
    });
  }

  onSubmitChange() {
    let nullDates = this.basePayStructureForm.contactBasePayStructures.filter(cbps => {
      return cbps.startDate == null || cbps.startDate == undefined || cbps.startDate.toString() == "";
    });

    if (nullDates.length > 0) {
      this.toastMsg.warning("All Start Dates must be entered");
      return;
    }

    let error = false;

    this.basePayStructureForm.contactBasePayStructures.forEach((cbps: IContactBasePayStructure, i) => {
      if (this.basePayStructureForm.contactBasePayStructures[i + 1] != null) {
        let next = this.basePayStructureForm.contactBasePayStructures[i + 1];
        let startDate = next.startDate;
        
        if (cbps.startDate >= startDate) {
          error = true;
        }
      } 
    });

    if (error) {
      this.toastMsg.warning("Start Dates are not in a proper order");
      return;
    }

    let formData = <IContactBasePayStructureUpdate>{
      contactPlanId: this.contactPlanId,
      contactBasePayStructures: this.basePayStructureForm.contactBasePayStructures
    }

    this.http.post<ApiResponse>(`${this.baseUrl}/UpdateContactBasePayStructure`, formData)
      .subscribe(data => {
        this.toastMsg.success("Contact Base Pay Structure successfully updated!");
        this.edit = !this.edit;
        this.getBasePayStructureForm();
      }, (err: any) => {
        this.toastMsg.error("Server Error", "Error");
      });
  }

  getPaymentTypes() {
    this.http.get<ApiResponse>(`${this.baseUrl}/GetPaymentTypes`)
      .subscribe(data => {
        if (data && data.result) {
          this.paymentTypes = data.result.map(x => {
            return <IPaymentType>{
              paymentTypeId: x.paymentTypeId,
              paymentTypeName: x.paymentTypeName
            }
          });
        }
      }, (err: any) => {
        this.toastMsg.error(err.message, "Server Error!");
      });
  }

  getBasePayStructureForm() {
    this.http.get<ApiResponse>(`${this.baseUrl}/ContactBasePayStructures/${this.contactPlanId}/${this.ruleId}/${this.basePayRuleId}`)
      .subscribe(data => {
        if (data && data.result) {
          localStorage.setItem('LastRuleDetails', JSON.stringify(data));

          this.basePayStructureForm = <IContactBasePayStructureForm>{
            ruleId: data.result.ruleId,
            ruleName: data.result.ruleName,
            ruleTypeId: data.result.ruleTypeId,
            description: data.result.description,
            numberOfStages: data.result.numberOfStages,
            basePayRuleId: data.result.basePayRuleId,
            basePayRuleName: data.result.basePayRuleName,
            contactBasePayStructures: data.result.contactBasePayStructures.map(cbps => {
              return <IContactBasePayStructure>{
                contactBasePayStructureId: cbps.contactBasePayStructureId,
                contactBasePayStructureName: cbps.contactBasePayStructureName,
                contactPlanId: cbps.contactPlanId,
                ruleId: cbps.ruleId,
                numberOfPayments: cbps.numberOfPayments,
                startDate: cbps.startDate,
                endDate: cbps.endDate,
                payStream: cbps.payStream.map(payStreamItem => {
                  return <IPayStreamItem>{
                    paymentNumber: payStreamItem.paymentNumber,
                    percentage: payStreamItem.percentage,
                    stage: payStreamItem.stage,
                    payBasedOn: payStreamItem.payBasedOn,
                    paymentTypeId: payStreamItem.paymentTypeId,
                    daysInAdvance: payStreamItem.daysInAdvance,
                    paymentDueDateMappingId: payStreamItem.paymentDueDateMappingId,
                    paymentDueDateMapping: this.getPaymentDateMapping(payStreamItem.paymentDueDateMappingId),
                    basePayStreamId:  payStreamItem.basePayStreamId,
                  }
                })
              }
            })
          }

           console.log("Base Pay Structure Form", this.basePayStructureForm);
        }
      }, (err: any) => {
        this.toastMsg.error(err.message, "Server Error!");
      });
  }

  getPaymentDateMapping(paymentDueDateMappingId: number) {
    if(this.paymentDueDateMappings && this.paymentDueDateMappings.length > 0){
      let dateDateMapping = this.paymentDueDateMappings.filter(mapping => mapping.paymentDueDateMappingId == paymentDueDateMappingId)[0];
      return dateDateMapping.paymentDueDateMappingName;
    }
  }

  getPaymentType(paymentTypeId: number) {
    if (!this.paymentTypes) return <IPaymentType>{};
    let paymentType = this.paymentTypes.filter(type => type.paymentTypeId == paymentTypeId)[0];

    paymentType = paymentType || <IPaymentType>{};

    return paymentType;
  }

  getPaymentDueDateMappings() {
    this.apiService.get('GetData/PaymentDueDateMappings')
      .subscribe(data => {
        if (data && data.statusCode === "201" && data.result) {
          this.paymentDueDateMappings = data.result.map(x => { return <IPaymentDueDateMapping>x });
          this.getBasePayStructureForm();
          // console.log("Payment Due Date Mappings", this.paymentDueDateMappings);
        }
      }, err => {
        this.toastMsg.error(err.message, "Server Error!");
      })
  }

}
