import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { CreateRuleComponent } from '../create-rule/create-rule.component'
import { AuthGuard } from '../../guards/auth.guard';
import { ViewRuleComponent } from './view-rule/view-rule.component';
import { ViewPlanComponent } from './view-plan/view-plan.component';
import { ViewBasePayStructureComponent } from './view-base-pay-structure/view-base-pay-structure.component';
import { ViewPayBookComponent } from './view-pay-book/view-pay-book.component';
import { ViewBaseFormulaComponent } from './view-base-formula/view-base-formula.component';
import { CloneRuleComponent } from './clone-rule/clone-rule.component'
import { VersionRuleComponent } from './version-rule/version-rule.component'
import { RuleVersionsComponent } from './rule-versions/rule-versions-component'
import { VersionFormulaComponent } from './version-formula/version-formula.component';
import { FormulaVersionsComponent } from './formula-versions/formula-versions-component';

const routes: Routes = [
  { path: '', redirectTo: 'create', pathMatch: 'full' },
  { path: 'create', component: CreateRuleComponent, canActivate: [AuthGuard] },
  { path: 'baseformula', component: CreateRuleComponent, canActivate: [AuthGuard] },
  { path: 'commissions/viewRule/:id', component: ViewRuleComponent, canActivate: [AuthGuard] },
  { path: 'commissions/viewRule/:id/:versionNo', component: ViewRuleComponent, canActivate: [AuthGuard] },
  { path: 'commissions/viewPlan/:id', component: ViewPlanComponent, canActivate: [AuthGuard] },
  { path: 'commissions/viewBasePayStructure/:id', component: ViewBasePayStructureComponent, canActivate: [AuthGuard] },
  { path: 'commissions/viewPaymentBook/:id', component: ViewPayBookComponent, canActivate: [AuthGuard] },
  { path: 'commissions/viewBaseFormula/:id', component: ViewBaseFormulaComponent, canActivate: [AuthGuard] },
  { path: 'commissions/viewBaseFormula/:id/:versionNo', component: ViewBaseFormulaComponent, canActivate: [AuthGuard] },
  { path: 'clone-rule/:rule_id/:version_no', component: CloneRuleComponent, canActivate: [AuthGuard] },
  { path: 'version-rule/:rule_id/:version_no', component: VersionRuleComponent, canActivate: [AuthGuard] },
  { path: 'version-baseformula/:formula_id/:version_no', component: VersionFormulaComponent, canActivate: [AuthGuard] },
  { path: 'rule-versions/:rule_id', component: RuleVersionsComponent, canActivate: [AuthGuard] },
  { path: 'baseformula-versions/:formula_id', component: FormulaVersionsComponent, canActivate: [AuthGuard] },
  { path: 'clone-baseformula/:rule_id/:version_no', component: CloneRuleComponent, canActivate: [AuthGuard] },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class CreateRuleRoutingModule { }
