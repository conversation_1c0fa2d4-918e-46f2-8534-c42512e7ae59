import { Component, OnInit, Inject } from '@angular/core';
import { MatLegacyDialogRef, MAT_LEGACY_DIALOG_DATA } from '@angular/material/legacy-dialog';

@Component({
  selector: 'app-roof-commission-rate-dialog',
  templateUrl: './roof-commission-rate-dialog.component.html',
  styleUrls: ['./roof-commission-rate-dialog.component.css']
})
export class RoofCommissionRateDialogComponent implements OnInit {
  roofCommissionRateGroup: Element[] = [];
  roofCommissionSelectedGroup: any;
  
  constructor(public dialogRef: MatLegacyDialogRef<RoofCommissionRateDialogComponent>, @Inject(MAT_LEGACY_DIALOG_DATA) public data: any) { }

  ngOnInit() {
    this.roofCommissionRateGroup = this.data.commissionRate; 
    this.roofCommissionRateGroup.sort((a, b) => {
      return <any>new Date(b[0].effectiveStartDate) - <any>new Date(a[0].effectiveStartDate);
    });
  }

  groupClick(group: any) {
    this.roofCommissionSelectedGroup = group;
  } 

}
