import { Component, OnInit, ViewChild } from '@angular/core';
import { ApiService } from 'src/app/services/api.service';
import { ToastrService } from 'ngx-toastr';
import { Router, ActivatedRoute } from '@angular/router';
import { ICommission } from 'src/app/model/commission.model';
import { IPayment } from 'src/app/model/payment.model';
import { Location } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { ApiResponse } from 'src/app/services/api.response';
// import { createOutput } from '@angular/compiler/src/core';
import { CommissionFilters } from './service/filters.service';
import { ICommissionHistory } from 'src/app/model/commission-history.model';
import { MatLegacyDialog } from '@angular/material/legacy-dialog';
import { MatTableDataSource } from '@angular/material/table';
import { MatSort } from '@angular/material/sort';
import { CommissionPaymentsComponent } from './payments/payments.component';
import { CommissionHistoryComponent } from './commission-history/commission-history.component';
import { CommissionAuditComponent } from './audit-commission-finalize/audit-commission-finalize.component';
import { ICommissionIdentifierChangeCapture } from 'src/app/model/commission-identifier-change-capture.model';
import { CommissionIdentifierChangeDialogComponent } from './commission-identifier-change-dialog/commission-identifier-change-dialog.component';
import { CommissionDataComponent } from './commission-data/commission-data.component';

@Component({
  selector: 'app-commission-view',
  templateUrl: './commission-view.component.html',
  styleUrls: ['./commission-view.component.css']
})
export class CommissionViewComponent implements OnInit {
  @ViewChild(CommissionPaymentsComponent) commissionPaymentsComponent: CommissionPaymentsComponent;
  @ViewChild(CommissionHistoryComponent) commissionHistoryComponent: CommissionHistoryComponent;
  @ViewChild(CommissionDataComponent) commissionDataComponent: CommissionDataComponent;
  @ViewChild(CommissionAuditComponent) commissionAuditComponent: CommissionAuditComponent;
  commissionId: number;
  commission: ICommission = <ICommission>{};
  p: number = 1;
  newCommissionAmount: number;
  override: boolean = false;
  commissionRuleInfo: any;
  calculatedCommissionAmount: number = 0
  commissionRuleName: string = ''
  baseformulasList = []
  baseformulasInformation = [];
  baseFormulaBreakdown = [];
  baseFormulaConditions = [];
  metadata = [];
  filteredMetadata = [];
  setConditionName: string = '';
  overwriteCommissionNote: string = '';
  commissionIdentifierChanges: MatTableDataSource<ICommissionIdentifierChangeCapture> = new MatTableDataSource([]);
  stepConditions = [];

  constructor(private router: Router, public apiService: ApiService, private toastMsg: ToastrService, private activatedRoute: ActivatedRoute,
    private location: Location, private http: HttpClient, private commissionFilters: CommissionFilters, private dialog: MatLegacyDialog) {
      this.router.routeReuseStrategy.shouldReuseRoute = function () {
        return false;
      };
    // this.commissionId = this.activatedRoute.snapshot.params.commission_id;
  }

  ngOnInit() {
    this.activatedRoute.params.subscribe(params => {
      this.commissionId = params.commission_id;

      if (!this.apiService.checkPermission('ViewCommissions')) {
        // this.router.navigate(['/ui/dashboard'])
        this.apiService.goBack();
        this.toastMsg.error("Insufficient Permissions. Please make sure your account can access this information.", "Permissions");
      }
      this.getCommission(this.commissionId);
      this.getCommissionRule(this.commissionId);
      this.getCommissionIdentifierChanges(this.commissionId);
    })
  }

  getCommission(commissionId: number) {
    this.http.get<ApiResponse>(`${environment.apiBaseUrl}Commissions/${commissionId}/${false}`)
      .subscribe(data => {
        if (data && data.result) {
          this.commission = <ICommission>data.result;
          console.log('commission', this.commission);
          this.newCommissionAmount = this.commission.commissionAmount;
          this.overwriteCommissionNote = this.commission.commissionNote;
        }
      }, err => {
        this.toastMsg.error("No Preview Available", "Error!");
      })
  }

  overrideCommission() {
    if (this.overwriteCommissionNote.length > 1) {
      //this.toastMsg.warning(`${this.overwriteCommissionNote}`, "success");
      if (this.commissionId && this.newCommissionAmount >= 0) {
        let body = {
          commissionId: this.commissionId,
          newCommissionAmount: this.newCommissionAmount,
          commissionNote: this.overwriteCommissionNote
        }
        this.apiService.post('Commissions/Override', body)
          .subscribe(data => {
            this.toastMsg.success("Commission Successfully Overridden!");
            this.override = false;
            this.getCommission(this.commissionId);
            this.commissionPaymentsComponent.getPayments(this.commissionId);
            this.commissionHistoryComponent.getCommissionHistory(this.commissionId);
          }, err => {
            this.toastMsg.error(err.message, "Error!");
          });
      }
    } else {
      this.toastMsg.warning("Please provide a commission overwrite note", "Empty Overwrite Note");
    }
  }

  unfinalizeCommission(commissionId: number) {
    if (this.commissionId) {
      this.apiService.get(`Commissions/Unfinalize/${commissionId}`)
        .subscribe(data => {
          if (data && data.result) {
            this.toastMsg.success("Successfully unfinalized commission");
            this.getCommission(commissionId);
          }
        }, err => {
          this.toastMsg.error(err.message, "Error!");
        })
    }
  }

  checkCanUnfinalize() {
    return this.apiService.checkPermission('UnfinalizeCommission');
  }

  checkCanOverride() {
    return this.apiService.checkPermission('OverrideTotalCommission');
  }

  onBackClick() {
    this.location.back();
  }

  onOverrideClick() {
    this.override = !this.override;
  }

  onOverrideConfirm() {
    this.overrideCommission();
  }

  onOverrideCancel() {
    this.override = !this.override;
    this.newCommissionAmount = this.commission.commissionAmount;
  }

  /**
   * Get Commission Rule Infomation
   * @param commissionId
   */
  getCommissionRule(commissionId: any) {
    if (commissionId) {
      //http://52.186.122.15:5002/api//api/CommissionAuto/235522
      this.apiService.get('CommissionJson/' + commissionId)   //'CommissionJson/' + commissionId
        .subscribe(data => {
          console.log(data);
          if (data && data.result) {
            //this.toastMsg.success("Successfully unfinalized commission");
            this.commissionRuleInfo = data.result.rules;
            this.calculatedCommissionAmount = data.result.rules.commissionAmount
            this.commissionRuleName = data.result.rules.ruleName
            this.baseformulasList = this.commissionFilters.filterBaseForumulas(data.result.rules.baseFormulas, data.result.rules.baseFormulas);
            this.parseBaseFormulas(this.commissionRuleInfo.steps, this.baseformulasList);
            this.metadata = this.commissionFilters.filterMetadata(this.metadata, this.metadata, this.baseformulasList);


          }
        }, err => {
          this.toastMsg.error("No Preview Available", "Error!");
        })
    }
  }

  getCommissionIdentifierChanges(commissionId: number) {
    if (commissionId) {
      this.apiService.get(`Commissions/IdentifierChanges/${commissionId}`)
        .subscribe(data => {
          if (data && data.result) {
            var changes = data.result.map(c => { return <ICommissionIdentifierChangeCapture>c });

            this.commissionIdentifierChanges = new MatTableDataSource(changes);
          }
        })
    }
  }

  openChangesDialog() {
    if (this.commissionIdentifierChanges) {
      this.dialog.open(CommissionIdentifierChangeDialogComponent, {
        width: '60%',
        data: { changes: this.commissionIdentifierChanges }
      })
    }
  }

  parseBaseFormulas(allSteps: any, baseformulalist: any) {
    // console.log('steps', allSteps);
    if (allSteps && allSteps.length > 0) {
      allSteps.filter(step => {
        if (step.baseFormulas && step.baseFormulas.length > 0) {
          step.baseFormulas.filter(bChildCon => {
            this.baseformulasInformation.push(bChildCon);
            this.baseformulasList.push({ "displayName": bChildCon.baseFormulaName, "commissionAmount": bChildCon.commissionAmount })
            if (bChildCon.steps && bChildCon.steps.length > 0) {
              this.parseBaseFormulas(bChildCon.steps, null)
            }
          })
        }
        if (step.actionIdentifierInformation && step.actionIdentifierInformation.length > 0) {
          step.actionIdentifierInformation.filter(identifier => {
            this.metadata.push({ "displayName": identifier.displayName, "value": identifier.value });
          })
        }
        if (step.conditions && step.conditions.length > 0) {
          step.conditions.filter(con => {
            if (con.baseFormulas && con.baseFormulas.length > 0) {
              con.baseFormulas.filter(childCon => {
                this.baseformulasInformation.push(childCon);
                this.baseformulasList.push({ "displayName": childCon.baseFormulaName, "commissionAmount": childCon.commissionAmount })
                if (childCon.steps && childCon.steps.length > 0) {
                  this.parseBaseFormulas(childCon.steps, null)
                }
              })
            }
            else {
              this.metadata.push({ "displayName": con.leftString, "value": con.leftValue });
              this.metadata.push({ "displayName": con.rightString, "value": con.rightValue });
            }
          })
        }
      })
    }
    if (baseformulalist && baseformulalist.length > 0) {
      baseformulalist.filter(baseformula => {
        if (baseformula.steps && baseformula.steps.length > 0) {
          baseformula.steps.filter(step => {
            if (step.conditions && step.conditions.length > 0) {
              step.conditions.filter(condition => {
                this.metadata.push({ "displayName": condition.leftString, "value": condition.leftValue });
                this.metadata.push({ "displayName": condition.rightString, "value": condition.rightValue });
              })
            }
            if (step.actionIdentifierInformation && step.actionIdentifierInformation.length > 0) {
              step.actionIdentifierInformation.filter(action => {
                this.metadata.push({ "displayName": action.displayName, "value": action.value });
              })
            }
          })
        }
      })
    }
  }

  findBaseFormula(name: string) {
    this.baseFormulaBreakdown = [];
    this.baseFormulaConditions = [];
    for (let i: number = 0; i < this.baseformulasList.length; i++) {
      if (this.baseformulasList[i].baseFormulaName === name) {
        this.baseFormulaBreakdown.push(this.baseformulasList[i]);
      }
    }
    this.baseFormulaBreakdown = this.commissionFilters.filterBaseFormulaBreakdown(this.baseFormulaBreakdown, this.baseFormulaBreakdown);
    console.log(this.baseFormulaBreakdown);
  }

  setConditions(ruleStep) {
    this.stepConditions = [];
    this.stepConditions.push(ruleStep);
  }
}
