<div class="page-title col-md-12 ">
  <h1> Employee Override</h1>
  <div class="breadcrumbs">
    <a href="#">Home</a>/<span>Employee Override Roofing Snapshot</span>
  </div>
</div>
<div class="content">

  <div class="card">
    <div class="card-header-info">
      <h4 class="card-title no-hover-effect">Employee Override Roofing Snapshot</h4>
    </div>
    <div class="card-body">
      <ng-container>
        <div class="gray-bg row">
          <div class="col-md-12 pt-3 pb-3 gray-bg">
            <div class="row filter-row">
              <div class="form-group col-md-3">
                <label class="bmd-label-floating">Enter Year</label>
                <input type="number" class="custom-input" [(ngModel)]="selectedYear">
              </div>
              <div class="form-group col-md-3">
                <label class="bmd-label-floating">Enter Quarter</label>
                <select (change)="onChangeQuarter($event)" class="custom-select" id="selectQuarter">
                  <option [value]="">
                    Select Quarter
                  </option>
                  <option *ngFor="let quarter of quarters" [value]="quarter.value">
                    {{quarter.id}}
                  </option>
                </select>
              </div>
            </div>

            <div class="row filter-row">
              <div class="form-group col-md-12">
            <label class="bmd-label-floating">Details</label> <br>
            This generates the monthly employee override roofing snapshot that is used to award monthly employee override roofing incentive.
            </div>
            </div>

          </div>
        </div>
        <div class="row">
          <div class="col-md-12">
            <div class="float-right ">
              <button class="btn btn-primary" (click)="onSubmit()">
                <i class="material-icons">filter_list</i>
                Run Employee Override Roofing Snapshot
              </button>
            </div>
          </div>
        </div>
      </ng-container>
      <div>


      </div>
    </div>
  </div>

 
  </div>
