// This file can be replaced during build by using the `fileReplacements` array.
// `ng build --prod` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

// export const environment = {
//   production: false,
//   // ssoBaseUrl: 'https://trinitywebapisso20190618011520.azurewebsites.net/api/',
//   // apiBaseUrl: 'https://trinitywebapicommissions20190620020514.azurewebsites.net/api/'
//   ssoBaseUrl: 'http://localhost:5002/api/',
//   apiBaseUrl: 'http://localhost:5002/api/',
//   workflowBaseUrl: 'http://localhost:5004/api/',
//   // ssoBaseUrl: 'http://localhost:5001/api/',
//   secretKey: "M@%$#*!21@$&#%$*#61"
// };
export const environment = {
  production: true,
  ssoBaseUrl: 'https://devopsiis.trinity-solar.com:5002/api/',
  apiBaseUrl: 'https://devopsiis.trinity-solar.com:5002/api/',
  workflowBaseUrl: 'https://devopsiis.trinity-solar.com:5004/api/',
  secretKey: "TEST-M@%$#*!21@$&#%$*#61",
  
  // baseUrl:'https://devopsiis.trinity-solar.com:58980/',
  baseUrl:'https://devopsiis.trinity-solar.com:5002/',
  scopeUri: ['api://f7d0a0c9-be05-4a05-ade8-f2ecae4dd8ad/AuthApp'],
  tenantId: 'f1006ee5-f888-4308-92ea-fcaebe1c0b5e',
  uiClienId: 'f7279b00-3063-48e1-a48b-2b350ec27380',
  redirectUrl: 'http://localhost:4200'
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/dist/zone-error';  // Included with Angular CLI.
