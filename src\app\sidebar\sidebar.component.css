.commissions-container {
  /*  width: 100vw;
    height: 100vh;  */
}

.commissions-drawer {
    width: 260px;
  /*  box-shadow: 0 16px 38px -12px rgba(0, 0, 0, 0.56), 0 4px 25px 0px rgba(0, 0, 0, 0.12), 0 8px 10px -5px rgba(0, 0, 0, 0.2); */
    border: none; position: fixed;
}

.commissions-nav-button {
    box-shadow: 0 4px 20px 0px rgba(0, 0, 0, 0.14), 0 7px 10px -5px rgba(0, 188, 212, 0.4);
    /* background-color: #369DD6; */
    /* background: linear-gradient(60deg, #26c6da, #00acc1);  */
    background-color: #369DD6;
    color: white !important;
    width: 100%;
    display: flex !important;
    justify-content: left !important;
    align-items: center;
    align-content: center;
    padding: 3px 10px;
    flex-direction: row;
}

.button-container {
    display: inline-flex;
    justify-content: left;
    align-items: center;
}

p.custom-font-weight {
    margin: 0;
    color: white !important;
}

i.custom-font-weight {
    margin: 0;
    color: white !important;
}

i.material-icons {
    color: rgba(0,0,0,.54) !important;
    height: fit-content; font-size: 20px; line-height: 1.2em;
}
i.material-icons.close{
    color:#FFF!important;
    height: fit-content;
    font-size: 1.3rem; text-shadow: none; opacity: 1; line-height: 50px;
}
.top-close {
    padding-top: 5px;
    height: 20px;
    /* background: linear-gradient(60deg, #26c6da, #00acc1); */
    background-color: #369DD6;
    display: flex;
    justify-content: flex-end;
}
.trinity-logo-sidebar {
    margin-right: auto;
    margin-left: auto;
}
.top-close-button {
    background-color: transparent;
    border: none;
    font-size: 1rem;
    position:absolute; top:0; right:0;
}
.top-close-button:hover {
    cursor: pointer;
    color: #369DD6;
    background-color: #308dc0;
    transition: .3s;
}

.main-head{
    /* padding: 4px 0px 0px 75px;
    padding-top: 5px; */
    height: 50px; position: relative;
    /* background: linear-gradient(60deg, #26c6da, #00acc1); */
    background-color: #616060;
    display: flex;
    justify-content: center; padding-right: 50px;
}
.trinity-logo-sidebar img{max-height:50px;}
.nav-list-container {
    display: flex;
    flex-direction: column;
}

.nav-list {
    padding: 0; margin-top:30px;
}

ul {
    list-style-type: none;
}

a:visited {
    color: inherit;
}

.commissions-nav-item {
 font-weight: 400; font-size: 14px;
}

.menu-toggle{position: absolute; text-align: center; right:0; top:0; bottom: 0; width:50px; background: #408bc0; border-radius: 0;} 
.menu-toggle .close{float:none;}
.commissions-nav-link {
      padding: 5px 15px; width:100%; display: block;
    justify-content: left; color:#333;
}
.mat-drawer.mat-drawer-side{width:70px;}
.toggler:hover {
    cursor: pointer;
}
.rate-table-row {
    display: flex;
    flex-direction: row;
    justify-content: left;
    align-items: center;
}
.sub-rates {
    /* margin-left: 20px; */
    background-color: #f9f8f8;
}
.sub-rate-table-row {
    color: #5f5f5f;
    word-wrap: none;
    display: flex;
    flex-direction: row;
    justify-content: left;
    align-items: center;
    padding: 5px 10px 5px 50px;
    border-radius: 4px; font-size: 13px;;
}

p.custom-font-weight {
    height: fit-content;
}

.sub-rate-table-row:hover {
    background-color: #e7e7e7;
    color: #369DD6;
    cursor: pointer;
}
.custom-font-weight {
    white-space:nowrap;
}
.icon-spacer {
    margin-right: 10px;
}