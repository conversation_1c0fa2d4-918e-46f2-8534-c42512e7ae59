import { HttpClient } from '@angular/common/http';
import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { IContact } from 'src/app/model/one-time-payment.model';
import { ApiService } from 'src/app/services/api.service';
import { environment } from 'src/environments/environment';
import * as FileSaver from 'file-saver';
import { DatePipe } from '@angular/common';
import { MatLegacyPaginatorModule } from '@angular/material/legacy-paginator';

@Component({
  selector: 'app-prepaid-payments-dashboard',
  templateUrl: './prepaid-payments-dashboard.component.html',
  styleUrls: ['./prepaid-payments-dashboard.component.css']
})
export class PrepaidPaymentsDashboardComponent implements OnInit {
  @ViewChild(MatLegacyPaginatorModule, { static: true }) paginator: MatLegacyPaginatorModule;
  Math = Math;
  paymentData: any;
  paymentDataSource: any;
  salesOffices: string[] = [];
  selectedOffices: string[] = [];
  displayedColumns = [];
  salesDivisions: string[] = ["Sales- Traditional", "Sales- Direct", "Sales- Inside"];
  selectedDivision: string[] = [];
  employmentStatuses: string[] = ["Active", "Not Active"];
  selectedEmploymentStatuses: string[] = [];
  teams: string[] = ["Roofing", "RRR Roofing", "Non-Roofing"];
  selectedTeams: string[] = [];
  salesTerritories: string[] = [];
  selectedTerritories: string[] = [];
  groupingOptions: string[] = ["Contact_Legal_Name", "Sales_Division", "Sales_Office", "Sales_Territory", "Employment_Status", "Team"];
  selectedGroupingOptions: string[] = [];
  selectedReport: string = "prepaid";
  pageSize: number = 20;
  pageNumber: number = 1;
  totalCount: number;
  contacts: IContact[] = [];
  selectedContacts: IContact[] = [];
  officeDisabled: boolean = false;
  territoryDisabled: boolean = false;
  sortBy: string = "";
  selectedDateFilter: string = "Install Date";
  dateFiltersDisabled: boolean = false;
  totalAmount: number = 0;
  totalBalance: number = 0;
  totalOpenPayments: number = 0;
  totalOpenPaymentReclaims: number = 0;
  sortDirection: string = "asc";
  columnNames = [
    {
      id: "contact_Legal_Name",
      value: "Contact Legal Name",
      sortOrder: "asc",
      isHovered: false
    },
    {
      id: "employment_Status",
      value: "Employment Status",
      sortOrder: "asc",
      isHovered: false      
    },
    {
      id: "sales_Division",
      value: "Sales Division",
      sortOrder: "asc",
      isHovered: false
    },
    {
      id: "sales_Office",
      value: "Sales Office",
      sortOrder: "asc",
      isHovered: false
    },
    {
      id: "sales_Territory",
      value: "Sales Territory",
      sortOrder: "asc",
      isHovered: false
    },
    {
      id: "team",
      value: "Team",
      sortOrder: "asc",
      isHovered: false      
    },
    {
      id: "balance",
      value: "Balance",
      type: "currency",
      sortOrder: "asc",
      isHovered: false
    },
    {
      id: "open_Payments",
      value: "Open Payments",
      type: "currency",
      sortOrder: "asc",
      isHovered: false
    },
    {
      id: "open_Payment_Reclaims",
      value: "Open Payment Reclaims",
      type: "currency",
      sortOrder: "asc",
      isHovered: false
    },
    {
      id: "date_Contract_Signed",
      value: "Contract Date",
      type: "date",
      sortOrder: "asc",
      isHovered: false
    },
    {
      id: "actual_Install_Date",
      value: "Install Date",
      type: "date",
      sortOrder: "asc",
      isHovered: false
    },
    {
      id: "product",
      value: "Product",
      sortOrder: "asc",
      isHovered: false
    },
    {
      id: "opportunity_Name",
      value: "Opportunity",
      sortOrder: "asc",
      isHovered: false
    },
    {
      id: "System_Size_kWdc",
      value: "System Size (KWdc)",
      sortOrder: "asc",
      isHovered: false
    },
    {
      id: "payment_Due_Date",
      value: "Payment Due Date",
      type: "date",
      sortOrder: "asc",
      isHovered: false
    },
    {
      id: "opportunity_Amount",
      value: "Opportunity Amount",
      type: "currency",
      sortOrder: "asc",
      isHovered: false
    },
    {
      id: "commission_Id",
      value: "Commission",
      sortOrder: "asc",
      isHovered: false
    },
    {
      id: "commission_Rule_Name",
      value: "Commission Rule",
      sortOrder: "asc",
      isHovered: false
    },
    {
      id: "payment_Status_Name",
      value: "Payment Status",
      sortOrder: "asc",
      isHovered: false
    },
    {
      id: "payment_Type_Name",
      value: "Payment Type",
      sortOrder: "asc",
      isHovered: false
    },
    {
      id: "amount",
      value: "Payment Amount",
      type: "currency",
      sortOrder: "asc",
      isHovered: false
    },
    {
      id: "reclaim",
      value: "Reclaim",
      type: "currency",
      sortOrder: "asc",
      isHovered: false
    }
  ];
  dateFilters: string[] = ["Install Date"];
  filteredColumnNames: any[] = [];
  minDate = new Date();
  dateFrom: string = this.datePipe.transform(this.minDate, 'yyyy-MM-dd');
  dateTo: string = "";
  allPayments: boolean = false;

  dropdownSettings:IDropdownSettings = {
      singleSelection: false,
      // idField: 'item_id',
      // textField: 'item_text',
      selectAllText: 'Select All',
      unSelectAllText: 'Unselect All',
      itemsShowLimit: 3,
      allowSearchFilter: false
    };

    groupByDropdownSettings:IDropdownSettings = {
      singleSelection: false,
      // idField: 'item_id',
      // textField: 'item_text',
      selectAllText: 'Select All',
      unSelectAllText: 'Unselect All',
      itemsShowLimit: 3,
      allowSearchFilter: false,
      enableCheckAll: false
    };
  
  contactDropdownSettings:IDropdownSettings = {
      singleSelection: false,
      idField: 'contactId',
      textField: 'contactName',
      selectAllText: 'Select All',
      unSelectAllText: 'Unselect All',
      itemsShowLimit: 3,
      allowSearchFilter: true,
      enableCheckAll: false
    };

  constructor(private apiService: ApiService, private toastMsg: ToastrService, private http: HttpClient, private datePipe: DatePipe) { }

  ngOnInit(): void {
    if (!this.apiService.checkPermission('ViewPaymentBooks')) {
      this.apiService.goBack();
      this.toastMsg.error("Insufficient Permissions. Please make sure your account can access this information.", "Permissions");
    }
    this.getSalesOffices();
    this.getTerritories();
    this.getContacts();
  }

  getSalesOffices() {
  this.apiService.get('GetData/SalesOffices')
    .subscribe(data => {
      if (data && data.result) {
        this.salesOffices = data.result.map(office => { return <string>office });
        this.salesOffices.push("Blank");
        this.salesOffices.sort();
      }
    }, err => {
      this.toastMsg.error(err.message, "Error!");
    });
  }

  getTerritories(){
    this.apiService.get('GetData/StateCode')
    .subscribe(data => {
      if (data && data.result) {
        this.salesTerritories = data.result.map(territory => { return <string>territory.trim() });
      }
    }, err => {
      this.toastMsg.error(err.message, "Error!");
    });
  }

  getContacts() {
    this.apiService.get('GetData/GetActiveContacts')
      .subscribe(data => {
        if (data && data.result) {
          this.contacts = data.result.map(type => { return <IContact>{ contactId: type.id, contactName: type.name } })
        }
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      })
  }

  getPayments() {
    if (!this.dateFrom) {
      this.toastMsg.warning("Please enter a value for Install Date - From");
      return;
    }
    else if (this.dateFrom && this.dateTo && new Date(this.dateFrom) > new Date(this.dateTo)) {
      this.toastMsg.warning("Install Date - From cannot be greater than Install Date - To");
      return;
    }

    var body = {
      filterString: this.generateFilterString(),
      pageSize: this.pageSize,
      pageNumber: this.pageNumber,
      sortColumn: this.selectedGroupingOptions && this.selectedGroupingOptions.length > 0 && !this.sortBy.includes("Contact") ? this.selectedGroupingOptions[0] : this.sortBy,
      sortDirection: this.sortDirection
    }

    //console.log("Request Body", body);

    this.apiService.post('DynamicReport/GetPrepaidPayments', body)
      .subscribe(data => {
        if (data && data.result) {
          this.paymentData = data.result.data;
          console.log(this.paymentData)
          this.paymentDataSource = new MatTableDataSource<any>(this.paymentData);
          this.totalCount = data.result.totalCount;
          this.pageNumber = data.result.pageNumber;
          this.pageSize = data.result.pageSize;
          if (this.totalCount == 0) {
            this.totalAmount = 0;
            this.totalBalance = 0;
            this.totalOpenPayments = 0;
            this.totalOpenPaymentReclaims = 0;
          }
          if (this.paymentData && this.paymentData.length > 0) {
            var columns = Object.keys(this.paymentData[0]);
            //console.log("Columns", columns);

            this.filteredColumnNames = this.columnNames.map(x => columns.includes(x.id) ? x : null).filter(x => x !== null);
            this.displayedColumns = this.columnNames.map(x => columns.includes(x.id) ? x.id : null).filter(x => x !== null);
            
            this.totalAmount = columns.includes("amount") ? this.paymentData.map(x => x.amount).reduce((p, total) => p + total, 0) : 0;
            this.totalBalance = columns.includes("balance") ? this.paymentData.map(x => x.balance).reduce((p, total) => p + total, 0) : 0;
            this.totalOpenPayments = columns.includes("open_Payments") ? this.paymentData.map(x => x.open_Payments).reduce((p, total) => p + total, 0) : 0;
            this.totalOpenPaymentReclaims = columns.includes("open_Payment_Reclaims") ? this.paymentData.map(x => x.open_Payment_Reclaims).reduce((p, total) => p + total, 0) : 0;
            //console.log(this.totalAmount, this.totalBalance, this.totalOpenPayments, this.totalOpenPaymentReclaims);

          }
          else {
            this.toastMsg.warning("No Data Found for provided filters");
          }
        }
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      })
    }

    getExcelWorksheet() {
      var body = {
        filterString: this.generateFilterString(),
        reportType: this.selectedReport,
        isDownload: true
      }
      this.http.post(`${environment.apiBaseUrl}DynamicReport/GetPrepaidPayments`, body, { responseType: 'blob' })
        .subscribe(data => {
          this.downLoadFile(data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
        }, err => {
          this.toastMsg.error(err.message, "Error!");
        });
    }

    downLoadFile(data: any, type: string) {
      let blob = new Blob([data], { type: type });
      let date: Date = new Date();
  
      FileSaver.saveAs(blob, `report_${date}.xlsx`);
    }

  generateFilterString() {
    let filterString = "";
    filterString = filterString + 'Sales Office' + (this.selectedOffices.length > 0 ? "=(" + this.selectedOffices.map(x => `'${x}'`).join(", ") + ")," : "=,");
    filterString = filterString + 'Sales Division' + (this.selectedDivision.length > 0 ? "=(" + this.selectedDivision.map(x => `'${x}'`).join(", ") + ")," : "=,");
    filterString = filterString + 'Employment Status' + (this.selectedEmploymentStatuses.length > 0 ? "=(" + this.selectedEmploymentStatuses.map(x => `'${x}'`).join(", ") + ")," : "=,");
    filterString = filterString + 'Team' + (this.selectedTeams.length > 0 ? "=(" + this.selectedTeams.map(x => `'${x}'`).join(", ") + ")," : "=,");
    filterString = filterString + 'Sales Territory' + (this.selectedTerritories.length > 0 ? "=(" + this.selectedTerritories.map(x => `'${x}'`).join(", ") + ")," : "=,");
    filterString = filterString + 'Group By' + (this.selectedGroupingOptions.length > 0 ? "=(" + this.selectedGroupingOptions.join(", ") + ")," : "=,");
    filterString = filterString + 'Contact' + (this.selectedContacts.length > 0 ? "=(" + this.selectedContacts.map(x => x.contactId).join(", ") + ")," : "=,");
    filterString = filterString + (this.selectedDateFilter != "Not Selected" ? (this.dateFrom ? `${this.selectedDateFilter} - From=${this.dateFrom},` : "") + (this.dateTo ? `${this.selectedDateFilter} - To=${this.dateTo},` : "") : "");  
    filterString = filterString + `Report Type=${this.allPayments && this.selectedReport === 'accrual' ? this.selectedReport+'-all' : this.selectedReport},`;
    return filterString;
  }

  onItemSelect() {
    if (this.selectedOffices.length > 0) {
      this.officeDisabled = false;
      this.territoryDisabled = true;
      this.selectedTerritories = [];
    }
    else if (this.selectedTerritories.length > 0) {
      this.territoryDisabled = false;
      this.officeDisabled = true;
      this.selectedOffices = [];
    }
  }
  onItemDeSelect() {  
    if (this.selectedOffices.length == 0 && this.territoryDisabled) {
      this.territoryDisabled = false;
    }
    if (this.selectedTerritories.length == 0 && this.officeDisabled) {
      this.officeDisabled = false;
    }
  }
  onSelectAll(items: any, filter) {
    if (filter === 'salesOffices') {
      this.selectedOffices = [...items];
    }
    else if (filter === 'salesTerritories') {
      this.selectedTerritories = [...items];
    }

    if (this.selectedOffices.length > 0) {
      this.officeDisabled = false;
      this.territoryDisabled = true;
      this.selectedTerritories = [];
    }
    else if (this.selectedTerritories.length > 0) {
      this.territoryDisabled = false;
      this.officeDisabled = true;
      this.selectedOffices = [];
    }
  }
  onDeSelectAll(items: any, filter) {
    if (filter === 'salesOffices') {
      this.selectedOffices = [...items];
    }
    else if (filter === 'salesTerritories') {
      this.selectedTerritories = [...items];
    }    

    if (this.selectedOffices.length == 0 && this.territoryDisabled) {
      this.territoryDisabled = false;
    }
    if (this.selectedTerritories.length == 0 && this.officeDisabled) {
      this.officeDisabled = false;
    }
  }

  // onChangeDateFilter() {
  //   this.selectedDateFilter == "Not Selected" ? this.dateFiltersDisabled = true : this.dateFiltersDisabled = false;
  // }

  onSort(colheader,index){    
    if (colheader) {
      this.sortBy = colheader.id.charAt(0).toUpperCase() + colheader.id.slice(1);
      this.sortDirection = this.filteredColumnNames[index].sortOrder;    
      this.getPayments();    
      this.filteredColumnNames[index].sortOrder = this.filteredColumnNames[index].sortOrder === 'asc'? 'desc' : 'asc';
      this.filteredColumnNames.forEach((column, i) => {
        if (i !== index) {
          column.sortOrder = 'asc';
        }      
      });     
    }
  }

  onPageChange(event) {      
    this.pageNumber = event.pageIndex + 1;
    this.pageSize = event.pageSize;
    this.getPayments();
  }

  onChangeReportType() {
    this.paymentData = [];
    this.paymentDataSource = new MatTableDataSource<any>(this.paymentData);
    this.displayedColumns = [];
    this.totalCount = 0;
  }
}