<ng-container *ngIf="apiService.checkPermission('AccessSearchBar')">
  <form>
    
    <div class="row" id="search-row">   
      <div class="col-6 multi-selectDrpDwn">
        <ng-multiselect-dropdown class="header-multi-sel" [placeholder]="'Search'" [settings]="dropdownSettings" [data]="dropdownList"
        [(ngModel)]="multiselectControl.value" [ngModelOptions]="{standalone: true}"
          (onSelect)="onItemSelect($event)"
          (onDeSelect)="onDeSelect($event)"
          
          (onSelectAll)="onSelectAll($event)">
        </ng-multiselect-dropdown>        
      </div>  
      <div class="col-6">
        <mat-form-field id="search" color="accent" appearance="outline" [style.fontSize.px]="'10px'">
          <input matInput name="search" placeholder="Search" [(ngModel)]="keyword" [ngModelOptions]="{standalone: true}"
          (keyup.enter)="search(keyword)"
            >
            <button *ngIf="keyword" matSuffix mat-icon-button aria-label="search" (click)="search(keyword)">
              <mat-icon>search</mat-icon>
            </button>
        </mat-form-field>
      </div>      
    </div>
  </form>
</ng-container>
