<div class="dailog-title-bg">
    <div class="dailog-title">Contact Plan Inclusion History<button class="dailog-close"
            [mat-dialog-close]><span>X</span></button>
    </div>
  </div>

<div style="margin-top: 10px;">
    <mat-table #table [dataSource]="dataSource" matSort>
        <ng-container matColumnDef="{{column.id}}" *ngFor="let column of columnNames">
        <mat-header-cell *matHeaderCellDef mat-sort-header class="table-header"> {{column.value}} </mat-header-cell>
        <mat-cell  [attr.data-td-head]="column.value"   *matCellDef="let element"> {{element[column.id]}} </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns;" class="pointer table-content"></mat-row>
    </mat-table>
    <div style="margin-top: 30px;"><mat-paginator [pageSizeOptions]="[5, 10, 20, 50]" showFirstLastButtons></mat-paginator></div>
</div>
