.card-back-color{
    background: lavender;
}
.top-box {
    width: 20%;
    flex-basis: auto;
}
.top-box .header-custom{
    min-height:50px;
}
.card-min-height{
    min-height: 430px;
}
.spinner-container {
    position: fixed;
    display: block;
    top: 176px;
    left: 0;
    background-color:rgba(280, 260, 270, 0.8);
    z-index:99999;
    opacity: 1;
    width: 100%;
    height:100%;
  }
.spinner {
    position:absolute;
    top:50%;
    left:50%;
    width: 300px;
    height: 170px;
    margin-left: -150px;
    margin-top: -256px;
}
.top-box .col-10, .top-box .col-2{padding-left:10px !important; padding-right:5px !important;}
@media screen and (max-width: 1399px) {
    .top-box .header-custom{
        min-height: 75px;
        font-size: 13px;
    }
    
}

@media screen and (max-width: 1199px) {
    .top-box {
        width: 30%;
        flex-basis: auto;
    }
}

