import { Component, OnInit } from '@angular/core';
import { ApiService } from 'src/app/services/api.service';
import { ToastrService } from 'ngx-toastr';
import { IPaymentBookBalance } from 'src/app/model/payment.model';

@Component({
  selector: 'app-paybooks',
  templateUrl: './paybooks.component.html',
  styleUrls: ['./paybooks.component.css']
})
export class PaybooksComponent implements OnInit {
  balances: IPaymentBookBalance[] = [];
  p: number = 1;

  constructor(public apiService: ApiService, private toastMsg: ToastrService) { }

  ngOnInit() {
    this.getPaymentBookBalances();
  }

  getPaymentBookBalances() {
    this.apiService.get('Payments/ContactPaymentBookBalances?withdrawal=false')
      .subscribe(data => {
        if (data && data.result) {
          this.balances = data.result.map((bal: IPaymentBookBalance) => {return bal});

          // console.log("Balances", this.balances);
        }
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      })
  }

}
