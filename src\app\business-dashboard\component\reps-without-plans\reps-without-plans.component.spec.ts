import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { RepsWithoutPlansComponent } from './reps-without-plans.component';

describe('RepsWithoutPlansComponent', () => {
  let component: RepsWithoutPlansComponent;
  let fixture: ComponentFixture<RepsWithoutPlansComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ RepsWithoutPlansComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(RepsWithoutPlansComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
