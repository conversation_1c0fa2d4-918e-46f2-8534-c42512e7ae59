export const environment = {
  production: true,
  ssoBaseUrl: 'https://onepay_dev.trinity-solar.com/api/api/',
  apiBaseUrl: 'https://onepay_dev.trinity-solar.com/api/api/',
  workflowBaseUrl: 'https://devopsiis.trinity-solar.com:5004/api/',
  secretKey: "TEST-M@%$#*!21@$&#%$*#61",
  applicationId:57,
  oneReportUrl:'https://devopsiis.trinity-solar.com:5020/oneReport',
  
  // baseUrl:'https://devopsiis.trinity-solar.com:58980/',
  baseUrl:'https://onepay_dev.trinity-solar.com/api/',
  scopeUri: ['api://a82a939e-61d2-4675-82cf-122b5fd91169/onePAY'],
  authority:'https://login.microsoftonline.com/f1006ee5-f888-4308-92ea-fcaebe1c0b5e',
  tenantId: 'f1006ee5-f888-4308-92ea-fcaebe1c0b5e',
  uiClienId: 'a82a939e-61d2-4675-82cf-122b5fd91169',
  redirectUrl: 'https://onepay_dev.trinity-solar.com'
};
