import { state } from '@angular/animations';
import { DatePipe, DecimalPipe } from '@angular/common';
import { Component, OnInit, ViewChild } from '@angular/core';
import { AbstractControl, UntypedFormBuilder, UntypedFormGroup, ValidatorFn, Validators } from '@angular/forms';
import { MatLegacyPaginator } from '@angular/material/legacy-paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { ToastrService } from 'ngx-toastr';
import { Observable, OperatorFunction } from 'rxjs';
import { debounceTime, distinctUntilChanged, filter, map } from 'rxjs/operators';
import { IContact } from 'src/app/model/one-time-payment.model';
import { ApiService } from 'src/app/services/api.service';
import {ConfirmDialogComponent } from '../../confirm-dialog/confirm-dialog.component';
import { MatLegacyDialog } from '@angular/material/legacy-dialog'; 

@Component({
  selector: 'app-battery-employee-role',
  templateUrl: './battery-employee-role.component.html',
  styleUrls: ['./battery-employee-role.component.css']
})
export class BatteryEmployeeRoleComponent implements OnInit {
  columnNames = [{
    id: "contactName",
    value: "Contact Name"
  },
  {
    id: "paycomId",
    value: "Paycom Id"
  },
  {
    id: "salesDivision",
    value: "Sales Division"
  },
  {
    id: "stateName",
    value: "State"
  },
  {
    id: "salesOffice",
    value: "Sales Office"
  },
  {
    id: "roleName",
    value: "Role Name"
  },
  {
    id: "effectiveStartDate",
    value: "Effective Start Date",
    dataType:'Date'
  },
  {
    id: "effectiveEndDate",
    value: "Effective End Date",
    dataType:'Date'
  }];
displayedColumns = [];
employeeRoles: any;
tableArr: any[] = [];
dataSource: any;
originalDataSource: any;
addRow: boolean = false;
allowEdit: boolean = true;
editRow: boolean = false;
defaultEmployeeRole: any;
employeeRoleAddForm: UntypedFormGroup;
employeeRoleEditForm: UntypedFormGroup;
contacts: IContact[] = [];
roleNames: string[] = ["VP", "RM", "SRM", "RVP", "SM", "ADM", "DM"];
contactName: string;
roleName: string;
salesDivision: string;
salesOffice: string;
state:string;
effectiveStartDate: string;
effectiveEndDate: string;
employeeRoleId: number;
salesDivisions = ["Sales- Direct", "Sales- Traditional", "Sales- Outreach","Sales- Inside"];
saleState:string[]=[];
salesOffices: string[] = [];
dateColumns:any;
@ViewChild(MatSort, { static: true }) sort: MatSort;
@ViewChild(MatLegacyPaginator, { static: true }) paginator: MatLegacyPaginator;

formatter = (c: IContact) => c.contactName;
search: OperatorFunction<string, readonly { contactId, contactName }[]> = (text$: Observable<string>) => text$.pipe(
  debounceTime(0),
  distinctUntilChanged(),
  filter(term => term.length >= 2),
  map(term => this.contacts.filter(c => new RegExp(term, 'mi').test(c.contactName)).slice(0, 10))
)

  constructor(private formBuilder: UntypedFormBuilder, public apiService: ApiService, private toastMsg: ToastrService, private datePipe: DatePipe,
    private decimalPipe: DecimalPipe,private dialog: MatLegacyDialog) { }

  ngOnInit() {
    this.displayedColumns = this.columnNames.map(x => x.id);
    this.dateColumns = this.columnNames.filter(s => s.dataType =='Date');
    this.getContacts();
    this.getEmployeeRoles();
    this.getSalesOffices();
    this.getState();
    this.initializeForm();
  }

  getContacts() {
    this.apiService.get('GetData/GetActiveContacts')
      .subscribe(data => {
        if (data && data.result) {
          this.contacts = data.result.map(type => { return <IContact>{ contactId: type.id, contactName: type.name } })
        }
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      })
  }

  getEmployeeRoles() {
    this.apiService.get('EmployeeRole/getBatteryEmployeeRoles')
      .subscribe(data => {
          this.employeeRoles = data;
          this.createTable();
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      })
  }
  getSalesOffices() {
    this.apiService.get('GetData/SalesOffices')
      .subscribe(data => {
        if (data && data.result) {
          this.salesOffices = data.result.map(office => { return <string>office });
        }
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      });
  }
  getState(){
    this.apiService.get('GetData/StateCode')
    .subscribe(data => {
      if (data && data.result) {
        this.saleState = data.result.map(office => { return <string>office });
      }
    }, err => {
      this.toastMsg.error(err.message, "Error!");
    });
  }
  createTable() {
    let tableArr: any[] = [];
    for(let i:number = 0; i <= this.employeeRoles.length - 1; i++) {
      let currentRow = this.employeeRoles[i];
      if(i==0)
      {
        this.tableArr[0] =this.employeeRoles[0];
      }
      tableArr.push({employeeRoleId: currentRow.employeeRoleId, contactId: currentRow.contactId,contactName: currentRow.contactName, roleName: currentRow.roleName, effectiveStartDate: this.datePipe.transform(currentRow.effectiveStartDate), effectiveEndDate: this.datePipe.transform(currentRow.effectiveEndDate),
        rate: this.decimalPipe.transform(currentRow.rate, '1.2-2'),paycomId:currentRow.paycomId,salesDivision:currentRow.salesDivision,salesOffice:currentRow.salesOffice,stateName:currentRow.stateName});
    }
    this.dataSource = new MatTableDataSource(tableArr);
    this.originalDataSource = tableArr;
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }


  Add() {
    this.defaultEmployeeRole = this.tableArr;
    this.addRow = !this.addRow;

  }

  onEditSubmit() {
    if (this.allowEdit) {
      if (this.employeeRoleEditForm.controls['effectiveEndDate'].value < this.employeeRoleEditForm.controls['effectiveStartDate'].value) {
        this.toastMsg.error("Effective end date cannot be greater than effective start date.");
      } else {
        var values = {
          employeeRoleId: this.employeeRoleId,
          contactId: this.employeeRoleEditForm.controls.contactId.value.contactId,
          salesDivision: this.employeeRoleEditForm.controls.salesDivision.value,
          salesOffice: this.employeeRoleEditForm.controls.salesOffice.value,
          StateName: this.employeeRoleEditForm.controls.state.value,
          roleName: this.employeeRoleEditForm.controls.roleName.value,
          effectiveStartDate: this.employeeRoleEditForm.controls.effectiveStartDate.value,
          effectiveEndDate: this.employeeRoleEditForm.controls.effectiveEndDate.value
        }
        
        this.apiService.post('EmployeeRole/UpdateBatteryEmployeeRole', values)
        .subscribe(data => {
          this.toastMsg.success('Employee Role Updated Successfully');
          this.getEmployeeRoles();
          this.initializeForm();
        }, 
        (err: any) => {
          if (err?.err?.message)
            this.toastMsg.error(err.message, "Server Error!");
          if (typeof err === "string") {
            let isJsonObject = false;            
            let parsedErr: any;
            try {
              parsedErr = JSON.parse(err);
              isJsonObject =parsedErr && typeof parsedErr === "object" &&!Array.isArray(parsedErr);
            } catch (e) {
              isJsonObject = false;
            }
            if (isJsonObject) {
              this.toastMsg.error(parsedErr.result, "Error!");
            } else this.toastMsg.error(err, "Error!");
          }
          if (err?.result) {
            this.toastMsg.error(err.result, "Error!");
          }
        }
      );
      }
    }

  }

  onSubmit() {
    const salesOfficeValue = this.employeeRoleAddForm.controls.salesOffice.value;
    const contactIdValue = this.employeeRoleAddForm.controls.contactId.value.contactId;
    const salesDivisionValue = this.employeeRoleAddForm.controls.salesDivision.value;
    const matchingContact = this.originalDataSource.find(
      contact => contact.contactId === contactIdValue 
                 && contact.salesDivision === salesDivisionValue 
                 && contact.salesOffice !== '' 
                 && contact.salesOffice !== 'All'
    );
    if (salesOfficeValue === 'All' && matchingContact) {
      const dialogRef = this.dialog.open(ConfirmDialogComponent);
      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          this.onAddSubmit();
        }
      });
    } else {
      this.onAddSubmit();
    }
  }

  onAddSubmit() {
    var values = {
      contactId: this.employeeRoleAddForm.controls.contactId.value.contactId,
      salesDivision: this.employeeRoleAddForm.controls.salesDivision.value,
      salesOffice: this.employeeRoleAddForm.controls.salesOffice.value,
      StateName: this.employeeRoleAddForm.controls.state.value,
      roleName: this.employeeRoleAddForm.controls.roleName.value,
      effectiveStartDate: this.employeeRoleAddForm.controls.effectiveStartDate.value,
      effectiveEndDate: this.employeeRoleAddForm.controls.effectiveEndDate.value
    }
    var sDate = new Date(values.effectiveStartDate)
    sDate.setDate(sDate.getDate() + 1)
    var fom = new Date(sDate.getFullYear(), sDate.getMonth(), 1)
    var convertedEOM = null;

    var valid = true;
    if(values.effectiveEndDate){
      var eDate = new Date(values.effectiveEndDate);
      var eom = new Date(eDate.getFullYear(), eDate.getMonth()+1, 0);
      convertedEOM = this.datePipe.transform(eom, 'yyyy-MM-dd');
      valid = values.effectiveStartDate < values.effectiveEndDate ? true : false;
    }
    if(values.effectiveStartDate == this.datePipe.transform(fom,  'yyyy-MM-dd') && (values.effectiveEndDate == null || values.effectiveEndDate == convertedEOM) && valid){
      this.apiService.post('EmployeeRole/AddBatteryEmployeeRole', values)
      .subscribe(data => {
        this.toastMsg.success('Employee Role Added Successfully');
        this.getEmployeeRoles();
        this.addRow = !this.addRow;
        this.initializeForm();
      }, 
      (err: any) => {
          if (err?.err?.message)
            this.toastMsg.error(err.message, "Server Error!");
          if (typeof err === "string") {
            let isJsonObject = false;            
            let parsedErr: any;
            try {
              parsedErr = JSON.parse(err);
              isJsonObject =parsedErr && typeof parsedErr === "object" &&!Array.isArray(parsedErr);
            } catch (e) {
              isJsonObject = false;
            }
            if (isJsonObject) {
              this.toastMsg.error(parsedErr.result, "Error!");
            } else this.toastMsg.error(err, "Error!");
          }
          if (err?.result) {
            this.toastMsg.error(err.result, "Error!");
          }
        }
      );
    }
    else{
      this.toastMsg.error('Invalid Date Range, Submission must be first and last day of the month with end date effective after start date.');
    }
}

rowClick(employeeRole: any) {
  this.editRow = true;
  this.allowEdit = true;
  this.employeeRoleId = employeeRole.employeeRoleId;
  this.contactName = employeeRole.contactName;
  this.roleName = employeeRole.roleName;
  this.salesDivision = employeeRole.salesDivision;
  this.state = employeeRole.stateName;
  this.salesOffice = employeeRole.salesOffice;
  this.effectiveStartDate = employeeRole.effectiveStartDate;
  this.effectiveEndDate = employeeRole.effectiveEndDate;

  this.employeeRoleEditForm.controls['contactId'].setValue(employeeRole.contactName);
  this.employeeRoleEditForm.controls['roleName'].setValue(employeeRole.roleName);
  this.employeeRoleEditForm.controls['state'].setValue(employeeRole.stateName);
  this.employeeRoleEditForm.controls['salesDivision'].setValue(employeeRole.salesDivision);
  this.employeeRoleEditForm.controls['salesOffice'].setValue(employeeRole.salesOffice);
  this.employeeRoleEditForm.controls['effectiveStartDate'].setValue(this.datePipe.transform(employeeRole.effectiveStartDate, 'yyyy-MM-dd'));
  this.employeeRoleEditForm.controls['effectiveEndDate'].setValue(this.datePipe.transform(employeeRole.effectiveEndDate, 'yyyy-MM-dd'));
  if (this.employeeRoleEditForm.controls['effectiveEndDate'].value != null) {
   this.allowEdit = false;
  }
}

clearAddStartDate(date: HTMLInputElement) {
  date.value = "";
  this.employeeRoleAddForm.controls.effectiveStartDate.setValue('');
}

clearAddEndDate(date: HTMLInputElement) {
  date.value = null;
  this.employeeRoleAddForm.controls.effectiveEndDate.setValue(null);
}

clearEditEndDate(date: HTMLInputElement) {
  date.value = null;
  this.employeeRoleEditForm.controls.effectiveEndDate.setValue(null);
}

initializeForm(){
  this.employeeRoleAddForm = this.formBuilder.group(
    {
      employeeOverrideRateEmployeeId: [0],
      contactId: [0, [Validators.required]],
      state: [""],
      salesDivision: ["",[Validators.required]],
      salesOffice: [""],
      roleName: ["", [Validators.required]],
      effectiveStartDate: ["", [Validators.required]],
      effectiveEndDate: [null],
    },
    {
      validators: [this.requireOneOfTwo('state', 'salesOffice')],
    }
  );  

  this.employeeRoleEditForm = this.formBuilder.group({
    contactId: [{value: 0, disabled: true}],
    salesDivision: [{value: "", disabled: true}],
    state: [{value: "", disabled: true}],
    salesOffice: [{value: "", disabled: true}],
    roleName: [{value: "", disabled: true}],
    effectiveStartDate: [{value: "", disabled: true}],
    effectiveEndDate: [{value: ""}]
  });

}

requireOneOfTwo(controlName1: string, controlName2: string): ValidatorFn {
  return (formGroup: AbstractControl): { [key: string]: boolean } | null => {
    const control1 = formGroup.get(controlName1);
    const control2 = formGroup.get(controlName2);

    // Check if at least one of the controls has a non-empty value
    if ((control1 && control1.value) || (control2 && control2.value)) {
      return null; // Valid if at least one has a value
    }

    return { requireOneOfTwo: true }; // Invalid if both are empty
  };
}
changeState(event: any) {
  if (event.target.value === "") { // Check for empty string
    this.employeeRoleAddForm.get('salesOffice').enable();
  } else {
    this.employeeRoleAddForm.get('salesOffice').disable();
    this.employeeRoleAddForm.get('salesOffice').setValue("");
  }
}

changeSalesOffice(event: any) {
  if (event.target.value === "") { // Check for empty string
    this.employeeRoleAddForm.get('state').enable();
  } else {
    this.employeeRoleAddForm.get('state').disable();
    this.employeeRoleAddForm.get('state').setValue("");
  }
}
}

export interface EmployeeRoleModel{
  employeeRoleId: number,
  contactId: number,
  roleName: number,
  effectiveEndDate: string,
  effectiveStartDate: string
}
