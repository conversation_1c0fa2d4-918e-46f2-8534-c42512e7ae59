import { Component, OnInit } from '@angular/core';
import { ApiService } from '../../services/api.service'
import {Router} from "@angular/router";
import { ToastrService } from 'ngx-toastr';
import { DataService } from '../../services/data.service';

@Component({
  selector: 'app-commissions-sidebar',
  templateUrl: './commissions-sidebar.component.html',
  styleUrls: ['./commissions-sidebar.component.css']
})
export class CommissionsSidebarComponent implements OnInit {

  sideBgImg: String;
  currentUser: any;
  capabilities: any;
  empRole: String;
  subRateTables: boolean;
  subPayments: boolean;

  constructor(public apiService: ApiService, private router: Router, private toastMsg: ToastrService, private dataService: DataService) {
    this.subRateTables = false;
    this.subPayments = false;
  }

  ngOnInit() {
    if(!this.apiService.isLoggedIn()){
      // this.router.navigate['login']
      this.dataService.logout();
    }
    this.currentUser = this.apiService.getUserObj()
    //Get Capabilities
    // if(!this.apiService.getRole())
    //   this.getUserCapabilities(this.currentUser.empId)
    // else
    //   this.empRole = this.apiService.getRole()
  }

  // /**
  //  * Get User Role and Capabilities
  //  */
  // getUserCapabilities(empId: String){
  //   if(!empId)
  //     return false;

  //   this.apiService.ssoGet('commissions/'+empId)
  //   .subscribe(data => {
  //     if(data.statusCode === "201" && data.result) {
  //       this.empRole = data.result.role;
  //       localStorage.setItem('role', data.result.role)
  //       this.capabilities = (data.result.capabilities).split(',');
  //       console.log("capabilities",this.capabilities);
  //     }else {
  //       this.toastMsg.error(data.message, 'Server Error!')
  //     }
  //   },(err: any) => {
  //     console.log(err)
  //     this.toastMsg.error(err.message, 'Server Error!')
  //   });
  // }

  toggleRateTableDropdown() {
    this.subRateTables = !this.subRateTables;
  }
  togglePaymentsDropdown() {
    this.subPayments = !this.subPayments;
  }

}
