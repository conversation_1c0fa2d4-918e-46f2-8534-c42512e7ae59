import { Component, EventEmitter, OnInit, Output, Input, SimpleChanges } from '@angular/core';
import { ApiService } from '../../../services/api.service';
import { ToastrService } from 'ngx-toastr';
import { ApiResponse } from 'src/app/services/api.response';
import { HttpClient } from '@angular/common/http';

@Component({
  selector: 'plan-starter',
  templateUrl: './plan-starter.component.html',
  styleUrls: ['./plan-starter.component.css']
})
export class PlanStarterComponent implements OnInit {
  jobTypes: any = [];
  jobTypeId: number;
  isJobRunning: boolean = false;
  @Input() jobsStatus = {};
  @Output() submitted = new EventEmitter<boolean>();
  plansList: any;
  planId:number;
  selectedPlanJobType:string ='J';
  constructor(private apiService: ApiService, private toastMsg: ToastrService,private http: HttpClient) {
    this.getJobTypes();
  }

  ngOnInit() {
    // this.getJobTypes();
  }

  getJobTypes() {
    this.apiService.get('PlanExecution/JobTypes')
      .subscribe(data => {
        this.jobTypes = data;
      }, (err: any) => {
        this.toastMsg.error(err.message, "Server Error!");
      });
  }

  checkIfJobRunning() {
    this.isJobRunning = false;
    if (this.jobTypeId){
    this.apiService.get('PlanExecution/IsJobRuning/'+this.jobTypeId)
      .subscribe((data:any)=> {
        this.isJobRunning =data;
        if(this.isJobRunning)
        {
          this.toastMsg.error("Job already running", "Server Error!");
        }
        else{
        this.onSubmit();
        }
      }, (err: any) => {
        this.toastMsg.error(err.message, "Server Error!");
      });
    }
  }

  onSubmit() {
    if (this.jobTypeId && !this.isJobRunning) {
      let jobType = this.jobTypes.filter(x => x.peJobTypeId == this.jobTypeId)[0];
      let planName = this.plansList.filter(x => x.planId == this.planId).map(x => x.planName)[0];     
      var body = {
        "PlanExecutionJobTypeName": this.planId ? 'Plan Based Execution': jobType.peJobTypeName,
        "Plans": this.planId ? [planName]:[],
        "SalesDivision": "",
        "Opportunities": [],
        "Contacts": []
      }      
      this.http.post(`${this.apiService.workflowBaseUrl}Workflow/RunPlanExecutionV2Async`, body)
        .subscribe((data: any) => {
          if (data && data.result) {
            this.toastMsg.success(data.result);
            this.submitted.emit(true);
          }
        }, err => {
          this.toastMsg.error(err.message, "Error!");
        })
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.jobsStatus) {
      this.isJobRunning=false;
    }
    this.getPlanList();
  }

  onJobTypeChange() {
    this.isJobRunning=false;
    this.jobTypeId ? this.planId =null:'';
  }
  getPlanList() {
    this.apiService.get('Plans')
      .subscribe(data => {
        if (data["statusCode"] === "201" && data.result) {
          this.plansList = data.result;
        } else {
          this.toastMsg.error("No plans found.", 'Server Error!')
        }
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!')
      });
  }
  onPlanChange(){
    if(this.planId){
      this.jobTypeId = this.jobTypes.filter(x => x.peJobTypeName == "Plan Based Execution").map(x => x.peJobTypeId)[0];      
    }
  }

}
