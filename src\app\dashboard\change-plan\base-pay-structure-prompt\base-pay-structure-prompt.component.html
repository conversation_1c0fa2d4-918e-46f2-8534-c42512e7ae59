<div class="card">
  <div class="card-header card-header-info">
    <h4 class="card-title">{{rule.ruleName}} | {{rule.ruleTypeName}} </h4>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="col-md-12" *ngFor="let item of ruleItems; index as i;">

        <h6><span>Stage {{i + 1}} <i class="fas fa-chevron-right"></i> </span> &nbsp;
          {{basePayStructureForm.basePayStructures[i].basePayStructureName}}</h6>

        <div class="row">
          <div class="col-sm-5 mb-4">
            <div class="row">
              <label class="col-5 ">Start Date </label>
              <div class="col-7">
                <div class=" input-group date-picker">
                  <input #datepickerInput type="date" id="start-date"
                    (change)="onChange(item.itemId, 'Start_Date', $event.target.value);" class="custom-input"
                    placeholder="" [value]="getRuleItemValue(item.itemId, 'Start_Date')">
                    <span *ngIf="datepickerInput.value.length > 0" class="mat-icon cal-reset"  (click)="this.Start_Date = null; onChange(item.itemId, 'Start_Date', $event.target.value);"><i class="far fa-calendar-times"></i></span> 
                    <span *ngIf="datepickerInput.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span>  
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>