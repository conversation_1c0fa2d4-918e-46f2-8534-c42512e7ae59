import { Component, OnInit, Inject } from '@angular/core';
import { MatLegacyDialog, MAT_LEGACY_DIALOG_DATA } from '@angular/material/legacy-dialog';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';

@Component({
  selector: 'app-employee-override-rate-dialog',
  templateUrl: './employee-override-rate-dialog.component.html',
  styleUrls: ['./employee-override-rate-dialog.component.css']
})
export class EmployeeOverrideRateDialogComponent implements OnInit {
  errors: any;
  columnsToDisplay = ["reason", "paycomId", "employeeTitle", "salesDivision", "salesOffice", "effectiveStartDate", "effectiveEndDate"];
  constructor(@Inject(MAT_LEGACY_DIALOG_DATA) public data: any, private  dialogRef : MatLegacyDialog) { this.errors = data.errors }
  ngOnInit() {
  }

}
