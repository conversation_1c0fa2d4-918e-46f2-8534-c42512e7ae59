<table mat-table [dataSource]="dataSource" matSort class="mat-elevation-z4 w-100">
    <ng-container matColumnDef="columnDef">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Column Header </th>
        <td mat-cell *matCellDef="let element"> {{element.columnDef}} </td>
        <td mat-footer-cell *matFooterCellDef> Column Footer </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="columns"></tr>
    <tr mat-row *matRowDef="let row; columns: columns;"></tr>
    <tr mat-footer-row *matFooterRowDef="columns"></tr>
</table>
<mat-paginator [pageSizeOptions]="pageSizeOptions" style="margin-top: 2%;">
</mat-paginator>