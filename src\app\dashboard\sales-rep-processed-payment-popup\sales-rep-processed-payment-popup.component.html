<div class="dailog-title-bg">
    <div class="dailog-title"><i class="fas fa-dollar-sign"></i> Recent processed payment since {{withdrawalDate | date}}<button class="dailog-close"
            [mat-dialog-close]><span>X</span></button>
    </div>
</div>
<table mat-table [dataSource]="ProcessedPayments" matSort class="mt-3 my-table w-100">
<ng-container matColumnDef="paymentFor">
    <th mat-header-cell *matHeaderCellDef mat-sort-header>
        Payment For </th>
    <td data-td-head="Payment For" mat-cell *matCellDef="let element">
        {{element.paymentFor}} </td>
    <td mat-footer-cell *matFooterCellDef> Total </td>
</ng-container>

<ng-container matColumnDef="paymentType">
    <th mat-header-cell *matHeaderCellDef mat-sort-header>
        Payment Type </th>
    <td data-td-head="Payment Type"  mat-cell *matCellDef="let element">
        {{element.paymentType}} </td>
    <td mat-footer-cell *matFooterCellDef></td>
</ng-container>

<ng-container matColumnDef="dateProcessed">
    <th mat-header-cell *matHeaderCellDef mat-sort-header>
        Date </th>
    <td  data-td-head="Date Processed "   mat-cell *matCellDef="let element">
        {{element.dateProcessed | date}} </td>
    <td mat-footer-cell *matFooterCellDef></td>
</ng-container>

<ng-container matColumnDef="transactionType">
    <th mat-header-cell *matHeaderCellDef mat-sort-header>
        Transaction Type </th>
    <td data-td-head="Transaction Type"  mat-cell *matCellDef="let element">
        {{element.transactionType}} </td>
    <td mat-footer-cell *matFooterCellDef></td>
</ng-container>

<ng-container matColumnDef="systemSize">
    <th mat-header-cell *matHeaderCellDef mat-sort-header>
        System Size </th>
    <td  data-td-head="System Size "   mat-cell *matCellDef="let element">
        {{element.systemSize}} </td>
    <td mat-footer-cell *matFooterCellDef></td>
</ng-container>

<ng-container matColumnDef="cppw">
    <th mat-header-cell *matHeaderCellDef mat-sort-header>
        CPPW </th>
    <td  data-td-head="CPPW"   mat-cell *matCellDef="let element">
        {{element.cppw}} </td>
    <td mat-footer-cell *matFooterCellDef></td>
</ng-container>

<ng-container matColumnDef="amount">
    <th mat-header-cell *matHeaderCellDef mat-sort-header>
        Amount </th>
    <td  data-td-head="Amount "  mat-cell *matCellDef="let element">
        {{element.amount | currency}} </td>
    <td mat-footer-cell *matFooterCellDef></td>
        <!-- {{ProcessedPaymentsTotal | currency}} </td> -->
</ng-container>

<ng-container matColumnDef="actualInstallDate">
    <th mat-header-cell *matHeaderCellDef mat-sort-header>
        Actual Install Date </th>
    <td  data-td-head="Actual Install Date "   mat-cell *matCellDef="let element">
        {{element.actualInstallDate | date}} </td>
    <td mat-footer-cell *matFooterCellDef></td>
</ng-container>

<tr mat-header-row *matHeaderRowDef="processedPaymentColumns"></tr>
<tr mat-row *matRowDef="let row; columns: processedPaymentColumns;"></tr>
<!-- <tr mat-footer-row *matFooterRowDef="processedPaymentColumns"></tr> -->
</table>

