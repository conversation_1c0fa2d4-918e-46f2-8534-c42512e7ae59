import { Component, OnInit, ViewChild } from '@angular/core';
import { PaymentBookWithdrawalComponent } from './payment-book-withdrawal/payment-book-withdrawal.component';

@Component({
  selector: 'app-payments',
  templateUrl: './payments.component.html',
  styleUrls: ['./payments.component.css']
})
export class PaymentsComponent implements OnInit {
  @ViewChild(PaymentBookWithdrawalComponent) private paymentBookWithdrawalComponent: PaymentBookWithdrawalComponent;

  constructor() { }

  ngOnInit() {
  }

  onPaymentStatusUpdated() {
    this.paymentBookWithdrawalComponent.getAllPaymentBookBalances();
  }

}
