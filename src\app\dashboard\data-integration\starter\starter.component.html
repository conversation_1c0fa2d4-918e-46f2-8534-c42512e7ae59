<div class="card">
  <div class="card-header-info">
    <h4 class="card-title">Starter</h4>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="form-group col-md-6" id="jobTypeGroup">
        <div class="row">
        <label class="col-sm-5">Job Type</label>
        <div class="col-sm-7">
        <select class="custom-select" name="jobType" data-style="btn btn-link" id="jobType" (change)="onJobTypeChange();" [(ngModel)]="jobTypeId">
          <ng-container *ngIf="jobTypes">
            <ng-container *ngFor="let type of jobTypes">
              <option [value]="type.diJobTypeId">{{type.diJobTypeName}}</option>
            </ng-container>
          </ng-container>
        </select>
      </div>
    </div>
    </div>
   <div class="col-md-12 text-right">
      <button type="submit" class="btn btn-primary" style="height: auto;" id="starter-button" [disabled]="jobTypeId == null || !enabled" (click)="onSubmit();"><i class="fas fa-tasks"></i> Start Job</button>
    </div> 
      <p style="color: red;" [hidden]="enabled">Cannot start a job if another job of the same type is still running</p>
    </div>
  </div>
</div>