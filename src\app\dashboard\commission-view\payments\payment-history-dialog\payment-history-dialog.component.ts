import { Component, OnInit, Inject, ViewChild } from '@angular/core';
import { MatLegacyDialogRef, MAT_LEGACY_DIALOG_DATA, MatLegacyDialog } from '@angular/material/legacy-dialog';
import { MatLegacyPaginator } from '@angular/material/legacy-paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { IPaymentHistory } from 'src/app/model/payment-history.model';
import { ApiService } from 'src/app/services/api.service';
import { ToastrService } from 'ngx-toastr';
import { PaymentNoteModelComponent } from '../payment-note-model/payment-note-model.component';

@Component({
  selector: 'app-payment-history-dialog',
  templateUrl: './payment-history-dialog.component.html',
  styleUrls: ['./payment-history-dialog.component.css']
})
export class PaymentHistoryDialogComponent implements OnInit {
  @ViewChild(MatSort, { static: true }) sortPh: MatSort;
  @ViewChild(MatLegacyPaginator, {static: true}) paginatorPh: MatLegacyPaginator;
  paymentElements: MatTableDataSource<IPaymentHistory> = new MatTableDataSource();
  paymentColumns: string[] = ["paymentType","paymentNumber","amount", "paymentStatus","paymentDueDate", "paymentNote"];
  pageSizeOptionsPh: number[] = [5, 10, 25, 100];

  constructor(public dialogRef: MatLegacyDialogRef<PaymentHistoryDialogComponent>, 
    @Inject(MAT_LEGACY_DIALOG_DATA) public data: any,
     private apiService: ApiService, 
     private toastMsg: ToastrService,
     private dialog: MatLegacyDialog) {
  }

  ngOnInit() {
      this.paymentElements = this.data.paymentHistory;
      this.paymentElements.sort = this.sortPh;
      this.paymentElements.paginator = this.paginatorPh;
  }
}

export interface IPaymentHistoryDialogData {
  paymentHistory: MatTableDataSource<IPaymentHistory>;
}