import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { PaymentHoldComponent } from './payment-hold.component';

describe('PaymentHoldComponent', () => {
  let component: PaymentHoldComponent;
  let fixture: ComponentFixture<PaymentHoldComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ PaymentHoldComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PaymentHoldComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
