import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { IPaymentHold, IdateRange } from '../../models/models';
import { ApiService } from 'src/app/services/api.service';
import { ToastrService } from 'ngx-toastr';
import { CurrencyPipe, DatePipe } from '@angular/common';
import { MatLegacyPaginator } from '@angular/material/legacy-paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { Chart } from 'angular-highcharts';
import { DatezonePipe } from 'src/app/pipe/datezone.pipe';

interface CustomPoint extends Highcharts.Point {
  custom: any;
}

@Component({
  selector: 'app-payment-hold',
  templateUrl: './payment-hold.component.html',
  styleUrls: ['./payment-hold.component.css']
})
export class PaymentHoldComponent implements OnInit {
  @Input() dateRange: IdateRange | null = null;
  paymentHoldData:any;
  displayedColumns = [];
  dataSource:any;
  originalDataSource:any;
  @Input() tabNumber: number | null = null;
  previousDateRange: IdateRange | null = null;
  paymentTypeChart: any[] =[];
  salesDivisionChart: any[]=[];
  salesDivisionChartData:Chart;
  paymentTypeChartData:Chart;
  columnNames = [
    {
      id: "contactLegalName",
      value: "Contact Legal Name"
    },
    {
      id: "opportunityName",
      value: "Opportunity Name"
    },
    {
      id: "amount",
      value: "Amount"
    },
    {
      id: "paymentStatus",
      value: "Payment Status"
    },
    {
      id: "paymentType",
      value: "Payment Type"
    },
    {
      id: "salesDivision",
      value: "Sales Division"
    },
    {
      id: "modifiedDate",
      value: "Modified Date",
      dataType:'Date'
    },
  ];
  @ViewChild('table', { read: MatSort, static: true }) sort: MatSort;
  @ViewChild('paginator', { static: true }) paginator: MatLegacyPaginator;
  constructor(public apiService: ApiService, private toastMsg: ToastrService, private datePipe: DatePipe,private currencyPipe: CurrencyPipe,private dateZonePipe: DatezonePipe) { }
  ngOnInit() {
  }
  ngOnChanges(){
    if (this.tabNumber === 6) {
      if (this.dateRange) {
        if (this.previousDateRange === null || this.previousDateRange !== this.dateRange) {
          this.previousDateRange = this.dateRange;
          this.getPaymentHold();
        }       
      }
    }
  }
  getPaymentHold() {
    this.apiService.get(`BusinessDashboard/PaymentsOnHold?toDate=${this.dateRange.endDate}&fromDate=${this.dateRange.startDate}`)
      .subscribe((res: any) => {
        this.paymentHoldData = res.paymentsOnHold;
        this.paymentTypeChart = res.paymentsTypeByPaymentsChart;
        this.paymentTypeChart.forEach(s=>{
          s.custom = this.currencyPipe.transform(s.custom);
        })
        this.salesDivisionChart = res.salesDivisionsByPaymentsChart;
        this.salesDivisionChart.forEach(s=>{
          s.custom = this.currencyPipe.transform(s.custom);
        })
        this.displayedColumns = this.columnNames.map(x => x.id);
        this.createTable();
        this.getPaymentTypeChart();
        this.getSalesDivisionChart();
        
      }, (err: any) => {
        this.toastMsg.error(err.message, "Server Error!");
      });
  }
  createTable() {
    let tableArr: IPaymentHold[] = [];
    for (let i: number = 0; i <= this.paymentHoldData.length - 1; i++) {
      let currentRow = this.paymentHoldData[i];
      tableArr.push({
        contactId: currentRow.contactId, amount: currentRow.amount, contactLegalName: currentRow.contactLegalName,
        opportunityId: currentRow.opportunityId, opportunityName: currentRow.opportunityName, paymentStatus: currentRow.paymentStatusName,
        paymentType: currentRow.paymentTypeName, salesDivision: currentRow.salesDivision,
        commissionId:currentRow.commissionId,
        modifiedDate: this.dateZonePipe.transform(currentRow.userCreatedTimestamp)
      });
    }
    this.dataSource = new MatTableDataSource(tableArr);
    this.originalDataSource = tableArr;
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }
  onSortChange(event:any){
    let dateFieldData = this.columnNames.filter(s=>s.dataType === 'Date');
    let dateField = dateFieldData && dateFieldData.length > 0 ? dateFieldData.filter(s=>s.id === event.active).map(c=> c.id).toString():'';
    if(dateField){
      this.dataSource.sortingDataAccessor = (item, property) => {
        switch (property) {
          case dateField:
            return new Date(item[dateField]).toISOString();
          default:
            return item[property];
        }
      };

      this.dataSource.sortingFn = (a: any, b: any, active: string, direction: string) => {
        if (active === dateField) {
          const dateA = new Date(a);
          const dateB = new Date(b);
          if (direction === 'asc') {
            return dateA.getTime() - dateB.getTime();
          } else {
            return dateB.getTime() - dateA.getTime();
          }
        } else {
          return this.dataSource.sortingDataAccessor(a, active) > this.dataSource.sortingDataAccessor(b, active) ? 1 : -1;
        }
      };
    }
  }

  setChartData(title: string, chartData: any) {
    let chart = new Chart({
      chart: {
        plotBackgroundColor: null,
        plotBorderWidth: null,
        plotShadow: false,
      },
      title: {
        text: title
      },
      tooltip: {
        // pointFormat: '{series.name}: {point.custom:.1f} <br> Total Count :{point.y:.0f}',
        pointFormatter: function() {
          const point = this as CustomPoint;
          return `Total Amount: <b>${point.custom.toLocaleString("en-US")}</b> <br/> Total Count : ${point.y.toLocaleString("en-US")}<b></b>`;
        },
      },
      accessibility: {
        point: {
          valueSuffix: '%',
        },
      },
      credits: {
        enabled: false
      },
      legend: {
        maxHeight: 90,  
      },
      plotOptions: {
        pie: {
          allowPointSelect: true,
          shadow: false,
          innerSize: '50%',
          cursor: 'pointer',
          dataLabels: {
              enabled: true,
          },
          showInLegend: true
      }
      },
      series: [
        {
          type: 'pie',
          name: 'Total Amount',
          showInLegend: true,
          data: chartData
        }
      ]
    });
    return chart;
  }

  getPaymentTypeChart(){
    this.paymentTypeChartData = this.setChartData('By Payment Type',this.paymentTypeChart);
  }
  getSalesDivisionChart(){
    this.salesDivisionChartData = this.setChartData('By Sales Division',this.salesDivisionChart);
  }
}
