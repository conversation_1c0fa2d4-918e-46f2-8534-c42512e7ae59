import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { ApiService } from "../../services/api.service";
import { ToastrService } from 'ngx-toastr';
declare var $: any;
import { DatePipe } from '@angular/common';

@Component({
  selector: 'app-emp-history',
  templateUrl: './emp-history.component.html',
  styleUrls: ['./emp-history.component.css']
})
export class EmpHistoryComponent implements OnInit {

  contactsList: any;
  plansList: Array<Object> = [];
  
  constructor(private router: Router, public apiService: ApiService, private toastMsg: ToastrService, datePipe: DatePipe, private activatedRoute: ActivatedRoute) { 
    
  }

  ngOnInit() {
    this.getContactsDetails()
  }

  /**
   * Get contact details
   */
  getContactsDetails(){
    this.apiService.get('Contacts')
    .subscribe(data => {
      if (data["statusCode"] === "201" && data.result && data.result[0]) {
        this.contactsList = data.result
        this.plansList = data.result.contactPlan
        // console.log("Contacts Details", this.contactsList)
        //{{apiService.dateFormat(planDetails.startDate)}}
      }else{
        this.toastMsg.error("No contacts found.", 'Server Error!')
      }
    }, (err: any) => {
      // console.log(err)
      this.toastMsg.error(err.message, 'Server Error!')
    });
  }

}
