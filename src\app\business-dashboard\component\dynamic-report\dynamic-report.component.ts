import { DatePipe, CurrencyPipe } from '@angular/common';
import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { ApiService } from 'src/app/services/api.service';
import { IdateRange } from '../../models/models';
import { MatLegacyPaginator } from '@angular/material/legacy-paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';

@Component({
  selector: 'app-dynamic-report',
  templateUrl: './dynamic-report.component.html',
  styleUrls: ['./dynamic-report.component.css']
})
export class DynamicReportComponent implements OnInit {
  displayedColumns = [];
  dynamicReportData: any;  
  originalDataSource: any;
  dataSource: any;
  @ViewChild('table', { read: MatSort, static: true }) sort: MatSort;
  @ViewChild('paginator', { static: true }) paginator: MatLegacyPaginator;
  @Input() dateRange: IdateRange | null = null;
  @Input() tabNumber: number | null = null;
  previousDateRange: IdateRange | null = null;
  noDataText: string = "No Data Found";

  constructor(public apiService: ApiService, private toastMsg: ToastrService, private datePipe: DatePipe,private currencyPipe: CurrencyPipe) { }
  columnNames = [
    {
      id: "reportName",
      value: "Report Name"
    },
    {
      id: "totalCount",
      value: "Total Count"
    },
    {
      id: "reportCriteria",
      value: "Filter Criteria"
    },        
  ];

  ngOnInit() {
  }

  ngOnChanges() {
    if (this.tabNumber === 11) {
      if (this.dateRange) {
        if (this.previousDateRange === null || this.previousDateRange !== this.dateRange) {
          this.previousDateRange = this.dateRange;          
          const startDate:any = new Date(this.dateRange.startDate);
          const endDate:any = new Date(this.dateRange.endDate);
          const dateDifference = endDate - startDate;
          const daysDifference = dateDifference / (1000 * 60 * 60 * 24);          
          if (daysDifference > 7) {
            this.toastMsg.warning("Only Week range is supported On Dynamic Report","Warning" );
            this.noDataText = "Only Week date range is applicable for dynamic reports";
            this.dynamicReportData = [];
            this.createTable();
            return           
          } 
          this.getdynamicReportData();
        }
      }
    }
  }

  getdynamicReportData() {
    this.apiService.get(`BusinessDashboard/GetDynamicReportSummary?toDate=${this.dateRange.endDate}&fromDate=${this.dateRange.startDate}`)
      .subscribe((res: any) => {
        this.dynamicReportData = res; 
        if(this.dynamicReportData.length === 0){
          this.noDataText = "No Data Found";
        }       
        this.displayedColumns = this.columnNames.map(x => x.id);
        this.createTable();        
      }, (err: any) => {
        this.toastMsg.error(err.message, "Server Error!");
      });
  }

  createTable() {    
    let tableArr: any[] = [];
    for (let i: number = 0; i <= this.dynamicReportData.length - 1; i++) {
      let currentRow = this.dynamicReportData[i];
      tableArr.push({
        reportName: currentRow.reportName, totalCount: currentRow.totalCount, reportCriteria: currentRow.reportCriteria,
      });
    }
    this.dataSource = new MatTableDataSource(tableArr);
    this.originalDataSource = tableArr;
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }

}
