import { Component, OnInit, ViewChild, Input, SimpleChanges, Output, EventEmitter } from '@angular/core';
import { MatLegacyDialog } from '@angular/material/legacy-dialog';
import { MatLegacyPaginator } from '@angular/material/legacy-paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { AddOnRule } from '../../employee-incentive-dialog/employee-incentive-dialog.component';
import { ModifyPromptsDialogComponent } from './modify-prompts-dialog/modify-prompts-dialog.component';
import { IRule } from 'src/app/model/rule.model';

@Component({
  selector: 'app-addon-table',
  templateUrl: './addon-table.component.html',
  styleUrls: ['./addon-table.component.css']
})
export class AddonTableComponent implements OnInit {
  @Input() addons: AddOnRule[] = [];
  @Input() columns: string[] = [];
  @Input() basePayRules: any[] = [];
  @Output() addonsOutput: EventEmitter<AddOnRule[]> = new EventEmitter<AddOnRule[]>();
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatLegacyPaginator, {static: true}) paginator: MatLegacyPaginator;
  pageSizeOptions: number[] = [5, 10, 25, 100];
  dataSource: MatTableDataSource<AddOnRule> = new MatTableDataSource();
  
  constructor(private dialog: MatLegacyDialog) { }

  ngOnChanges(changes: SimpleChanges) {
    if (changes && changes.addons) {
      this.dataSource = new MatTableDataSource(changes.addons.currentValue);
      this.dataSource.sort = this.sort;
      this.dataSource.paginator = this.paginator;
    }
  }

  onSelect(rule, element) {
    element.basePayRuleId = rule.target.value;
    element.basePayRuleName = this.basePayRules.find(x => x.ruleId == rule.target.value).ruleName;
  }

  ngOnInit() {
  }

  onModify(rule: IRule) {
    // console.log(rule);
    const dialogRef = this.dialog.open(ModifyPromptsDialogComponent, {
      width: '80%',
   
      data: {
        rule: rule
      }
    });

    dialogRef.afterClosed().subscribe((result: AddOnRule) => {
      if (result != null && result != undefined) {
        // console.log(result);
        var existing = result.ruleTypeName === "Base Pay Structure" ?
          this.addons.find(x => x.ruleId == result.ruleId && x.basePayRuleId == result.basePayRuleId) :
          this.addons.find(x => x.ruleId == result.ruleId);

        if (existing != null && existing != undefined) {
          this.addons = this.addons.map(x => {
            if (x.ruleTypeName === "Base Pay Structure") {
              if (x.ruleId == result.ruleId && x.basePayRuleId == result.basePayRuleId) {
                x = result;
              }
            }
            else {
              if (x.ruleId == result.ruleId && x.basePayRuleId == result.basePayRuleId) {
                x = result;
              }
            }
            return x;
          });
        } else {
          this.addons.push(result);
          this.addons = this.addons.slice();
        }

        this.addonsOutput.emit(this.addons);
      }
    });
  }

  removeAddOn(addOn: any) {
    if (addOn) {
      if (addOn.ruleTypeName === "Base Pay Structure") {
        this.addons = this.addons.filter(x => x.basePayRuleId != addOn.basePayRuleId);
      }
      else {
        this.addons = this.addons.filter(x => x.ruleId != addOn.ruleId);
      }
      // console.log(addOn);
      this.addonsOutput.emit(this.addons);
    }
  }

}