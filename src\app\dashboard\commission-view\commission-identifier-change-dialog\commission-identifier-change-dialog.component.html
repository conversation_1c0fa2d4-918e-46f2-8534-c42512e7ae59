<table mat-table [dataSource]="data.changes" class="mat-elevation-z8 w-100">
    <ng-container matColumnDef="applicationMetadataDisplayName">
        <th mat-header-cell *matHeaderCellDef> Display Name </th>
        <td mat-cell *matCellDef="let element"> {{element.applicationMetadataDisplayName}} </td>
    </ng-container>

    <ng-container matColumnDef="oldValue">
        <th mat-header-cell *matHeaderCellDef> Old Value </th>
        <td mat-cell *matCellDef="let element"> {{element.oldValue}} </td>
    </ng-container>

    <ng-container matColumnDef="newValue">
        <th mat-header-cell *matHeaderCellDef> New Value </th>
        <td mat-cell *matCellDef="let element"> {{element.newValue}} </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayColumns;"></tr>
</table>