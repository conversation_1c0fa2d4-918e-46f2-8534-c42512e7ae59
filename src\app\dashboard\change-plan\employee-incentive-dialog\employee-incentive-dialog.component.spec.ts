import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { EmployeeIncentiveDialogComponent } from './employee-incentive-dialog.component';

describe('EmployeeIncentiveDialogComponent', () => {
  let component: EmployeeIncentiveDialogComponent;
  let fixture: ComponentFixture<EmployeeIncentiveDialogComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ EmployeeIncentiveDialogComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(EmployeeIncentiveDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
