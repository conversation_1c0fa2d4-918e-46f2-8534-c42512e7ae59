import { ComponentFixture, TestBed } from '@angular/core/testing';

import { EmployeeOverrideRoofRateComponent } from './employee-override-roof-rate.component';

describe('EmployeeOverrideRoofRateComponent', () => {
  let component: EmployeeOverrideRoofRateComponent;
  let fixture: ComponentFixture<EmployeeOverrideRoofRateComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ EmployeeOverrideRoofRateComponent ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(EmployeeOverrideRoofRateComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
