<div class="dailog-title-bg">
  <div class="dailog-title">History<button class="dailog-close" [mat-dialog-close]><span>X</span></button>
  </div>
</div>
<div class="row" *ngIf="installationTypeDeductionGroup">
 
        <!-- <hr style="width: 95%; border-color: #26c6da;" /> -->
        <div class="col-md-6"> <div class="row">

          <label class="col-sm-5">Installation Type</label>
          <span class="col-sm-7">{{installationTypeDeductionGroup[0].installationType}}</span>
                 </div></div>
        <table class="my-table mat-table w-100 mt-2">
          <thead>
            <tr class="mat-header-row">
              <th class="mat-header-cell"   scope="col">Sales Territory</th>  
              <th class="mat-header-cell"   scope="col">Effective Start Date</th>
              <th class="mat-header-cell"   scope="col">Effective End Date</th>
              <th class="mat-header-cell"   scope="col">Installation Type Deduction Rate</th>
            </tr>
          </thead>
          <tbody>
            <tr class="mat-row" *ngFor="let tr of installationTypeDeductionGroup">
              <td data-td-head="Sales Territory" class="mat-cell">{{tr.salesTerritory}}</td>
              <td data-td-head="Effective Start Date" class="mat-cell">{{tr.effectiveStartDate | date}}</td>
              <td data-td-head="Effective End Date" class="mat-cell">{{tr.effectiveEndDate | date}}</td>
              <td data-td-head="Installation Type Deduction Rate" class="mat-cell">{{tr.installationTypeDeductionRate | currency:'USD':true:'1.2-3'}}</td>
            </tr>
          </tbody>
        </table>
      
  </div>