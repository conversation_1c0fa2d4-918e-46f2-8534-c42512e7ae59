<div class="page-title col-md-12 ">
  <h1> Product Rate Maintenance</h1>
  <div class="breadcrumbs"><a href="#">Home</a>/<span>Product Rate Maintenance</span>
  </div>
</div>

<div class="content">

  <div class="card">
    <div class="card-header-info">
      <h4 class="card-title no-hover-effect"> <i class="fas fa-hand-holding-usd"></i> Product Rate</h4>
    </div>
    <div class="card-body">

      <div class="row">
        <div class="col-md-12">

          <div class="input-group float-right table-search">
            <input class="custom-input" type="text" id="searchTextId" [(ngModel)]="searchText" name="searchText"
              placeholder="Search" (input)="searchForItem()">
            <span class="input-group-icon">
              <i class="fas fa-search"></i>
            </span>
          </div>

        </div>
      </div>
      <mat-table #table [dataSource]="dataSource" matSort>
        <ng-container matColumnDef="{{column.id}}" *ngFor="let column of columnNames">
          <mat-header-cell *matHeaderCellDef mat-sort-header class="table-header"> {{column.value}} </mat-header-cell>
          <mat-cell [attr.data-td-head]="column.value" *matCellDef="let element"> {{element[column.id]}} </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns;" class="pointer table-content" (click)="rowClick(row)">
        </mat-row>
      </mat-table>
      <mat-paginator [pageSizeOptions]="[5, 10, 20, 50]" showFirstLastButtons></mat-paginator>
      <div *ngIf="apiService.checkPermission('CreateRateTables')">
        <a class="btn  btn-primary float-right" *ngIf="!addInd" (click)="addInd = !addInd"><i
            class="material-icons pointer">add_circle</i> Add</a>
        <a class="btn  btn-primary float-right" *ngIf="addInd" (click)="addInd = !addInd"><i
            class="material-icons pointer">remove_circle</i> Hide</a>
      </div>
    </div>
  </div>


  <div class="card" *ngIf="addInd">
    <div class="card-header-info">
      <h4 class="card-title no-hover-effect"><i class="fas fa-plus"></i> Add Product Rate</h4>
    </div>
    <div class="card-body">
      <div>
        <form [formGroup]="productRateForm" (ngSubmit)="onSubmit()" class="w-100">
          <div class="row" *ngIf="activeProductRates">
            <div class="form-group col-md-6">
              <div class="row">
                <label class="col-sm-5">Sales Territory</label>
                <div class="col-sm-7">
                  <select class="custom-select" name="finance_partner_dropdown" formControlName="salesTerritory"
                    data-style="btn btn-link" id="finance_partner_dropdown">
                    <option *ngFor="let fp of dropdowns.salesTerritories" value="{{fp.salesTerritoryId}}">
                      {{fp.salesTerritory1}}</option>
                  </select>
                </div>
              </div>
            </div>
            <div class="form-group col-md-6">
              <div class="row">
                <label class="col-sm-5">Product Type</label>
                <div class="col-sm-7">
                  <select class="custom-select" name="module_type_dropdown" formControlName="productType"
                    data-style="btn btn-link" id="module_type_dropdown">
                    <option *ngFor="let fp of dropdowns.productTypes" value="{{fp.productTypeId}}">
                      {{fp.productType1}}</option>
                  </select>
                </div>
              </div>
            </div>

            <div class="form-group col-md-6">
              <div class="row">
                <label class="col-sm-5">Effective Start Date</label>
                <div class="col-sm-7">
                  <div class="w-100  date-picker">
                    <input #StartDatePicker type="date" name="start_date" id="start_date" class="custom-input"
                      formControlName="effectiveStartDate" placeholder="">
                    <span *ngIf="StartDatePicker.value.length > 0" class="mat-icon cal-reset"
                      (click)="clearDate(StartDatePicker)"><i class="far fa-calendar-times"></i></span>
                    <span *ngIf="StartDatePicker.value.length <= 0" class="mat-icon cal-open"><i
                        class="far fa-calendar-alt"></i></span>

                    <div *ngIf="productRateForm.errors && productRateForm.errors.maxDate">
                      <p style="color: red;">New Effecting Start Date should be greater than any previous start dates
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="form-group col-md-6">
              <div class="row">
                <label class="col-sm-5">Minimum Commission</label>
                <div class="col-sm-7">
                  <input currencyMask [options]="{ allowNegative: false, align: 'left', precision: 3 }"
                    name="minimumCommission" formControlName="minimumCommission" class="custom-input">
                </div>
              </div>
            </div>
            <div class="form-group col-md-6">
              <div class="row">
                <label class="col-sm-5">Floor Rate</label>
                <div class="col-sm-7">
                  <input currencyMask [options]="{ allowNegative: false, align: 'left', precision: 3 }" name="floorRate"
                    formControlName="floorRate" class="custom-input">
                </div>
              </div>
            </div>
            <div class="form-group col-md-6">
              <div class="row">
                <label class="col-sm-5">Base Rate</label>
                <div class="col-sm-7">
                  <input currencyMask [options]="{ allowNegative: false, align: 'left', precision: 3 }" name="baseRate"
                    formControlName="baseRate" class="custom-input">

                </div>
              </div>
            </div>

          </div>
          <div class="col-md-12 align-button-right">
            <button type="submit" class="btn btn-primary" [disabled]="productRateForm.invalid"><i
                class="fas fa-plus"></i>
              Add Product Rate</button>
          </div>
        </form>
      </div>
    </div>
  </div>


  <div class="card" *ngIf="productRateGroup">
    <div class="card-header-info">
      <h4 class="card-title"><i class="fas fa-history"></i> History</h4>
    </div>
    <div class="card-body">
      <div class="row">
        <!-- <hr style="width: 95%; border-color: #26c6da;" /> -->
        <div class="col-md-12">
          <a class="text-info"><i class="material-icons float-right blue-icon"
              (click)="productRateGroup = null">cancel</i></a>
        </div>

        <div class="col-md-12">
          <div class="row">
            <div class="col-md-6">
              <label>Sales Territory</label>
              <span class="ml-2">{{productRateGroup[0].salesTerritory}} </span>

            </div>
            <div class="col-md-6">
              <label>Product Type</label>
              <span class="ml-2">{{productRateGroup[0].productType}} </span>
            </div>
          </div>

        </div>
        <div class="col-md-12 mt-3">
          <table class="my-table mat-table w-100">
            <thead>
              <tr class="mat-header-row">
                <th class="mat-header-cell" scope="col">Effective Start Date</th>
                <th class="mat-header-cell" scope="col">Effective End Date</th>
                <th class="mat-header-cell" scope="col">Minimum Commission</th>
              </tr>
            </thead>
            <tbody>
              <tr class="mat-row " *ngFor="let tr of productRateGroup">
                <td class="mat-cell" data-td-head="Effective Start Date">{{tr.effectiveStartDate | date}}</td>
                <td class="mat-cell" data-td-head="Effective End Date">{{tr.effectiveEndDate | date}}</td>

                <td class="mat-cell" data-td-head="Minimum Commission">{{tr.minimumCommission |
                  currency:'USD':true:'1.3-3'}}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

    </div>