p {
 
  margin-bottom: 1px;  
}
h5 {
  margin-bottom: 2px;
}
.indented-child {
  padding: 5px 5px 3px 5px;
  width: fit-content;
  margin-left: 0;
  border-radius: 0;
}
.indented-child i{color:#37b471; font-size: 14px;}
.parent {
  font-weight: 400; color:#333;

}
 .highlighted {
   transition: .5s;
   color: #fff !important;
   background-color: #37b471;
 /*  box-shadow: 10px 7px 5px #aaaaaa; */
 }
 .highlighted i{color:#fff;}
.treeview, .treeview ul {
  margin:0;
  padding:0;
  list-style:none
}
.treeview ul {
  margin-left:1em;
  position:relative
}
.treeview ul ul {
  margin-left:.5em
}
.treeview ul:before {
  content:"";
  display:block;
  width:0;
  position:absolute;
  top:0;
  bottom:0;
  left:0;
  border-left:1px solid
}
.treeview li {
  margin:0;
  padding:0 1em;
  line-height:2em;
  color:#369;
  font-weight:700;
  position:relative
}
.treeview ul li:before {
  content:"";
  display:block;
  width:10px;
  height:0;
  border-top:1px solid;
  margin-top:-1px;
  position:absolute;
  top:1em;
  left:0
}
.treeview ul li:last-child:before {
  background:#fff;
  height:auto;
  top:1em;
  bottom:0
}
.indicator {
  margin-right:5px;
}
.treeview li a {
  text-decoration: none;
  color:black;
}
.treeview li button, .treeview li button:active, .treeview li button:focus {
  text-decoration: none;
  color:#369;
  border:none;
  background:transparent;
  margin:0px 0px 0px 0px;
  padding:0px 0px 0px 0px;
  outline: 0;
}

.highlightBackground{
  background-color:#48c6f3;
  color: white !important;
}

.scroll-area{
  height: 350px;
  overflow: auto;
}

.tooltip1{
  position: relative;
  display: inline-block;
 
}

.tooltiptext {
    visibility: hidden;
    width: 250px;
    background-color: #555;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px; font-size: 12px; line-height: 18px;
    position: absolute;
    margin-left: 25%;
    margin-top: -3%;
    opacity: 0;
    transition: opacity 0.3s;
}

.tooltiptext::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: #555 transparent transparent transparent;
}

.tooltip1:hover .tooltiptext {
  visibility: visible;
  opacity: 1;
}

.align_box{
  position: relative;
  top: 20px;
}

.position_btn{
  float:left
}
