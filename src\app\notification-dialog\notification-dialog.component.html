<div class="row mt-1 ml-1">
  <div class="col-md-4"><h4> Notifications</h4></div>
  <div class="col-md-6"></div>
  <div class="col-md-1 setting-icon" (click)="navSettings()"><i class="material-icons">settings</i></div>
</div>
<ng-container *ngIf="notificationList === null || notificationList?.length == 0">
  <!-- <mat-progress-spinner color="primary" mode="indeterminate" diameter="50"></mat-progress-spinner> -->
  <h4 class="no-dispaly-msg">No notification to dispaly</h4>
</ng-container>
<div class="row notification-pan" *ngIf="notificationList?.length > 0"
  infinite-scroll
        [infiniteScrollDistance]="modalScrollDistance"
        [scrollWindow]="false"
        [infiniteScrollDisabled]="disableScroll"
        (scrolled)="onModalScrollDown()">
    <div class="notification-list">
      
    
    
        <ng-container >
          <ng-container *ngFor="let item of notificationList;let i= index;">
            <div class="notification" *ngIf="item.dealType !== 'R'" (click)="onClickItem(i)">            
              <div class="notification-content">
                <div class="notification-title" [ngClass]="{'text-dark': item.active}">{{ item.notificationTypeName}} </div>
                <div class="notification-message" [ngClass]="{'text-dark': item.active}"><ng-container *ngIf="item.amount !=null"> Amount </ng-container><strong>{{item.amount | currency:'USD':'symbol':'1.2-2'}}</strong> <ng-container *ngIf="item.opportunityName"> Opportunity </ng-container> <strong>{{item.opportunityName}}</strong> <ng-container *ngIf="item.contactName"> Contact </ng-container> <strong>{{item.contactName}}</strong> <ng-container *ngIf="item.paymentTypeName"> Type </ng-container>
                  <strong>{{item.paymentTypeName}}</strong> <ng-container *ngIf="item.systemSizeKWdc"> System Size </ng-container> <strong *ngIf="item.systemSizeKWdc">{{item.systemSizeKWdc}}</strong> <ng-container *ngIf="item.dateContractSigned"> Date Contract Signed </ng-container>
                  <strong>{{item.dateContractSigned ? (item.dateContractSigned | date) : ''}}</strong> <ng-container *ngIf="item.demoDate"> Demo Date </ng-container> <strong>{{ item.demoDate ?
                    (item.demoDate | date) : ''}}</strong></div>
                <div class="notification-time" [ngClass]="{'text-info': item.active}">{{item.userModifiedTimestamp | timeago}}</div>
              </div>
              <span *ngIf="item.active" class="dots"></span>
            </div>
            <div class="notification" *ngIf="item.dealType ==='R'" (click)="onClickItem(i)">
              <div class="notification-content">
                <div class="notification-title" [ngClass]="{'text-dark': item.active}">{{ item.notificationTypeName}} </div>
                <div class="notification-message" [ngClass]="{'text-dark': item.active}"><ng-container *ngIf="item.amount !=null"> Amount </ng-container><strong>{{item.amount | currency:'USD':'symbol':'1.2-2'}}</strong> <ng-container *ngIf="item.opportunityName"> Opportunity </ng-container>
                  <strong>{{item.opportunityName}}</strong> <ng-container *ngIf="item.contactName"> Contact </ng-container> <strong>{{item.contactName}}</strong> <ng-container *ngIf="item.paymentTypeName"> Type </ng-container>
                  <strong>{{item.paymentTypeName}}</strong> <ng-container *ngIf="item.roofSquares"> Roof Squares </ng-container> <strong *ngIf="item.roofSquares">{{item.roofSquares}}</strong><ng-container *ngIf="item.roofingContractDate"> Roof contract Signed </ng-container>
                  <strong>{{item.roofingContractDate ? (item.roofingContractDate | date) : ''}} <ng-container *ngIf="item.roofInsatllDate"> Roof Install Date </ng-container> </strong>{{ item.roofInsatllDate ? (item.roofInsatllDate | date) : '' }}</div>
                <div class="notification-time" [ngClass]="{'text-info': item.active}">{{ item.userModifiedTimestamp | timeago }}</div>
              </div>
              <span *ngIf="item.active" class="dots"></span>
            </div>
          </ng-container>          
        </ng-container>     
        <div class="row mt-1" *ngIf="isLoder">
          <div class="col-md-5"></div>
          <div class="col-md-2"><div class="loader text-center"></div></div>
          <div class="col-md-5"></div>
        </div>      
      </div>
</div>