import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { AddPlanInclusionDialogComponent } from './add-plan-inclusion-dialog.component';
import { ReactiveFormsModule } from '@angular/forms';

describe('AddPlanInclusionDialogComponent', () => {
  let component: AddPlanInclusionDialogComponent;
  let fixture: ComponentFixture<AddPlanInclusionDialogComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ AddPlanInclusionDialogComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AddPlanInclusionDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
