<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header-info">
                    <h4 class="card-title">Recent EOM Executions History</h4>
                </div>
                <div class="row">
                  <ul class="pl-5 pt-2">
                    <li class="text-info">Data of the on demand rule exections where rule executed during the date range</li>
                  </ul>
              </div>
                <div class="card-body">
                    <mat-table #table [dataSource]="eomDataSource" matSort #sort="matSort" (matSortChange)="onSortChange($event)">
                        <ng-container matColumnDef="{{column.id}}" *ngFor="let column of columnNames">
                          <mat-header-cell *matHeaderCellDef mat-sort-header class="table-header"> {{column.value}}
                          </mat-header-cell>
                          <mat-cell [attr.data-td-head]="column.value" *matCellDef="let element">
                            <ng-container *ngIf="column.value == 'Commission Rule Name'; else common">
                              <a [routerLink]="['/ui/commissions/viewRule', element.commissionRuleId]">{{element[column.id]}}</a>
                            </ng-container>
                            <ng-template #common>
                              {{element[column.id]}}
                            </ng-template>
                          </mat-cell>
                        </ng-container>
                        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
                        <mat-row *matRowDef="let row; columns: displayedColumns;" class="pointer table-content"
                          ></mat-row>
                      </mat-table>
                      <div class="text-center p-2" *ngIf="originalDataSource && originalDataSource.length === 0">
                        <h5>No Data Found</h5>
                    </div>
                      <mat-paginator #paginator [pageSizeOptions]="[10, 20]" showFirstLastButtons></mat-paginator>                
                </div>
            </div>
        </div>
    </div>
</div>