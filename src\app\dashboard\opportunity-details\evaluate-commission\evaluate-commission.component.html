<div class="card">
    <div class="card-header-info">
      <h4 class="card-title">Evaluated Commissions</h4>
    </div>
    <div class="card-body">
      <table mat-table  #table [dataSource]="dataSource" matSort class="w-100 my-table">
    <ng-container matColumnDef="paidTo">
          <th mat-header-cell  *matHeaderCellDef mat-sort-header> Paid To </th>
          <td  data-td-head="Paid To"  mat-cell  *matCellDef="let element">
              <a [routerLink]="['/ui/commissions/salesrep', element.contactId]" class="">{{element.paidTo}}</a>
          </td> 
        </ng-container>
  
        <ng-container matColumnDef="calculatedVia">
            <th mat-header-cell  *matHeaderCellDef mat-sort-header> Calculated Via (Plan/Rule) </th>
            <td  data-td-head="Calculated Via (Plan/Rule)"  mat-cell  *matCellDef="let element" class="no-hover-effect">{{element.calculatedVia}}</td> 
        </ng-container>
          
        <ng-container matColumnDef="commissionAmount">
            <th mat-header-cell  *matHeaderCellDef mat-sort-header> Commission Amount </th>
            <td  data-td-head="Commission Amount"  mat-cell  *matCellDef="let element" class="no-hover-effect">{{element.commissionAmount}}</td> 
        </ng-container>
        <ng-container matColumnDef="details"> 
          <th mat-header-cell  *matHeaderCellDef mat-sort-header> View Details </th>
          <td  data-td-head="Details"  mat-cell  *matCellDef="let element">
            <a title="View Details" [routerLink]="['/ui/commissions/evaluatecommission', element.commissionId]" class="">
              <i class="fas fa-eye"></i>
              </a>
            </td> 
        </ng-container>
        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row  *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>
      <mat-paginator [pageSizeOptions]="[5, 10, 20, 50]" showFirstLastButtons></mat-paginator>
    </div>
  </div>