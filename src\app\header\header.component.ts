import { Component, OnInit, AfterContentChecked } from '@angular/core';
import { ApiService } from "../services/api.service";
import { Router, NavigationEnd } from '@angular/router';
import { Location } from '@angular/common';
import { Subject } from 'rxjs';
import { MatLegacyDialog } from '@angular/material/legacy-dialog';
import { MatIconModule, MatIconRegistry } from "@angular/material/icon";
import { DomSanitizer} from '@angular/platform-browser';
import { LoginDialogComponent } from '../login-dialog/login-dialog.component';
import { SidenavService } from '../services/sidenav.service';
import { SharedSidebarService } from '../shared/sidebar-icon';
import { VersionDialogComponent } from './version-dialog/version-dialog.component';
import { NotificationDialogComponent } from '../notification-dialog/notification-dialog.component';
import { ToastrService } from 'ngx-toastr';
import { NotificationService } from '../services/notification.service';

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.css']
})
export class HeaderComponent implements OnInit, AfterContentChecked {
  authUser: boolean = false
  loggedUserName: String;
  title: String = 'Welcome Trinity Systems'
  homeUrl: boolean = false;
  isTokenExpired: boolean = false;
  icon: string = "menu";
  showSearch: boolean = true;
  sidebarToggle: boolean;

  sidebarvalue: string;
  notificationSubs: any;
  notificationCount:number;
  totalNotificationCount:number;
  notificationFreq:number;    
  showNoticationIcon:boolean = false;
  outputpathQuestion:string='forms.office.com';
  outputpathBook:string='trinitysolarsys.sharepoint.com';
  updateDate: Date ;
  empId:string;
  notificationList:any[] =[];
  menuValue: string;


  constructor(public apiService: ApiService, private router: Router, private location: Location, public dialog: MatLegacyDialog,public iconRegistry: MatIconRegistry,public sanitizer: DomSanitizer,  private sidenav: SidenavService, private sharedSidebarService: SharedSidebarService,private notificationSevice:NotificationService,private toastMsg: ToastrService) {
    router.events.subscribe((val) => {
      if (location.path() != '' && location.path().indexOf('ui/dashboard') !== -1 || this.apiService.getRole() == 'Sales Reps') {
        this.homeUrl = false;
      } else {
        this.homeUrl = true
      }
    });
    this.router.events.subscribe(val => {
      if (val instanceof NavigationEnd) {
        if (val.url == "/ui/usermanagement" || !this.apiService.checkPermission('AccessSearchBar')) {
          this.showSearch = false;
        } else {
          this.showSearch = true;
        }

        if (val.url == "/ui/dashboard/finance" || val.url == "/ui/dashboard" || val.url == "/ui/dashboard/sales" || val.url == "/ui/dashboard/legal") {
          //this.sidenav.sidenav.opened = false;
          this.icon = "keyboard_backspace"
          if (this.sidenav.sidenav != undefined) {
            this.sidenav.open();
          }
          this.sharedSidebarService.updateSidebarToggle("open");
          this.sharedSidebarService.updateMenuToggle("close");

          // this.sidebarToggle = false;
          //this.addValue("open");
        } else {
          this.sidebarToggle = true;
          this.showSearch = true;
          this.sidenav.open();
          this.sharedSidebarService.updateSidebarToggle("open");
          this.sharedSidebarService.updateMenuToggle("close");
        }
        if (val.url == "/ui/dashboard/finance" || val.url == "/ui/dashboard/sales" || val.url == "/ui/dashboard/legal") {

        }
      }
    })

    this.apiService.isTokenExpired.subscribe(v => {
      this.isTokenExpired = v;
      // dilip Com-1049
      // if(this.isTokenExpired){
      //   this.dialog.closeAll();
      //   const dialogRef = this.dialog.open(LoginDialogComponent, {
      //     width: '500px',
      //     data: {isTokenExpired: v}
      //   });

      //   dialogRef.afterClosed().subscribe(result => {
      //     console.log('The dialog was closed');
      //   });
      // }else{
      //   this.dialog.closeAll();
      // }
    });

    this.router.events.subscribe(val => {
      if (val instanceof NavigationEnd) {
        if (this.sidenav.sidenav != undefined && this.sidenav.sidenav.opened) {
          // this.icon = "close";
          this.icon = "menu"
        } else {
          this.icon = "menu"
        }
      }
    });

    this.sidebarToggle = true;
    this.notificationSubs = null;   
    this.iconRegistry.addSvgIcon(
      'book-open',
      this.sanitizer.bypassSecurityTrustResourceUrl('assets/img/book-open.svg'));
      this.iconRegistry.addSvgIcon(
        'question-mark',
        this.sanitizer.bypassSecurityTrustResourceUrl('assets/img/question-mark.svg')); 
    //this.sharedSidebarService.menuValue = "open";
  }
  ngAfterContentChecked() {
    this.sidebarvalue = this.sharedSidebarService.sidebarToggle;
    if (this.sharedSidebarService.sidebarToggle == "open") {
      this.sidebarToggle = false;
    } else {
      this.sidebarToggle = true;
    }
  }

  // addValue(str) {
  //   this.sharedSidebarService.updateComp2Val(str);
  // }

  toggleSidenav() {
    if (this.sidenav.sidenav.opened) {
      this.sidenav.close();
      this.icon = "menu"
    } else {
      this.sidenav.open();

      this.icon = "menu"
    }

    // console.log("Status", this.sidenav.sidenav.opened);
    // this.sharedSidebarService.updateSidebarToggle("open");
    // this.sharedSidebarService.updateMenuToggle("close");
    //   this.sidenav.open();

  }

  ngOnInit() {
    
        

    this.showNoticationIcon = false;
    if (this.sidenav.sidenav != undefined) {
      this.sidenav.sidenav.opened = true;
    }
    this.sharedSidebarService.updateSidebarToggle("open");
    this.sharedSidebarService.updateMenuToggle("close");
    this.sidebarToggle = true;
    if (this.sidenav.sidenav != undefined) {
      this.sidenav.open();
    }
    this.notificationFreq = Number(localStorage.getItem('notificationFrequency'));    
    let currentUser= JSON.parse(localStorage.getItem('currentUser'));    
    this.empId = currentUser.empId; 
    if(this.notificationSubs){      
      clearInterval(this.notificationSubs);

    }
    
    if(this.notificationFreq>0){
      this.startNotificationCount();
    }else{
      this.getNotifications();            

    }
    this.notificationSevice.updatedCount.subscribe((data)=>{
      this.notificationCount = Number(data);
    })
  }
  startNotificationCount() {    
    const intervalTimeMs = this.notificationFreq * 60000;  
    // Set up a new interval    
    this.notificationSubs = setInterval(() => {            
      this.getNotifications();   
    }, intervalTimeMs);   
    this.getNotifications();
 
  }
  getNotifications() {
    console.log(new Date())
    let apiList = [this.apiService.get(`Notifications/NotificationCount/${this.empId}`),
    this.apiService.get(`Notifications/NotificationList?empId=`+this.empId+'&pageSize=20'+'&pageNumber=1')
    ]
    this.apiService.forkJoinData(apiList).subscribe((res: any) => {
      this.notificationCount = res[0].result;
      this.notificationList = res[1].result.notificationList;
      this.totalNotificationCount = res[1].result.totalRecordCount;
        this.showNoticationIcon = true;
      if(this.notificationCount>0){
        this.notificationSevice.onUpdateList(this.notificationCount,this.notificationList);
      }
    },(err: any) => {
      // console.log(err)
      this.toastMsg.error(err, 'Server Error!')
    }
    )
  }

  updateNotification(){
    this.updateDate = new Date();
    let updateObj = {
      empId: this.empId,  
      LastNotifiedOn: this.updateDate
  }  
  this.apiService.post('Notifications/UpdateNotification',updateObj)
  .subscribe(data => {
    console.log(data)
    this.getNotifications();
    // this.getNotificationCount();

  },(err: any) => {
          // console.log(err)
          this.toastMsg.error(err, 'Server Error!')
        })
    
  }

  ngOnDestroy() {    
    clearInterval(this.notificationSubs);    
  }

  dashboard() {
    let role = this.apiService.getRole()
    if (role == 'Sales Reps' && this.apiService.checkPermission('ViewSalesRepDashboard')) {
      this.router.navigate(['/ui/commissions/salesrepdashboard', this.apiService.getContactId()]);
    } else if (role == 'Finance Admin' || role == 'Finance Admin With UAM' || role == 'Finance User') {
      this.router.navigate(['/ui/dashboard/finance']);

    } else if (role == 'Sales Admin-Admin' || role == 'Sales Admin-User') {
      this.router.navigate(['/ui/dashboard/sales']);
    } else if (role == 'Legal Admin' || role == 'Legal User' || role == 'BT Support' || role == 'BT Admin') {
      this.router.navigate(['/ui/dashboard/legal']);
    }
  }

  openVersionDialog() {
    this.apiService.get('AppInfo/LastPlanExecution')
      .subscribe(data => {
        if (data && data.result) {
          // var date : Date = data.result;
          var date : Date = new Date(new Date(data.result).toLocaleString() + ' UTC');
          // console.log(date);

          const dialogRef = this.dialog.open(VersionDialogComponent, {
            width: '500px',
            data: { 
              lastSyncDate: date,
              lastSyncDateUtc: data.result
            }
          });
        }
      })
  }

  openNotificationPopup(){  
    // this.updateNotification();    
    const dialogRef = this.dialog.open(NotificationDialogComponent, {
      data: {count: this.notificationCount,notificationList:this.notificationList,totalRecord:this.totalNotificationCount},
      backdropClass: 'dialog-bg-trans',
      width: '350px',
      position: { top: '55px', right: '10px' }, 
    });

    dialogRef.afterClosed().subscribe(result => { 
      this.notificationCount = 0;     
      this.updateNotification();  
    });


  }

  clearCache() {
    sessionStorage.clear();
    localStorage.clear();
    window.location.reload();
  }


}
