import { Component, OnInit } from '@angular/core';
import {ApiService} from "../../../services/api.service";
import {Router, ActivatedRoute} from "@angular/router";
import { ToastrService } from 'ngx-toastr';
declare var $:any;

@Component({
  selector: 'app-clone-rule',
  templateUrl: './clone-rule.component.html',
  styleUrls: ['./clone-rule.component.css']
})
export class CloneRuleComponent implements OnInit{

  ruleId: number;
  versionNo: number;
  ruleObj: any;
  cloneRule: any;
  loadPage: boolean = false
  constructor(public apiService: ApiService, private router: Router, private activatedRoute: ActivatedRoute, private toastMsg: ToastrService) {
    
  }

  ngOnInit() {
    if (!this.apiService.checkPermission('CloneRule')) {
      this.apiService.goBack();
      this.toastMsg.error("Insufficient Permissions. Please make sure your account can access this information.", "Permissions");
    }
    this.activatedRoute.params.subscribe(params => {
      this.ruleId = parseInt(params.rule_id);
      this.versionNo = parseInt(params.version_no);

      if (localStorage.getItem('LastRuleDetails')) {
        this.ruleObj = JSON.parse(localStorage.getItem('LastRuleDetails'))
        if (this.ruleObj.basePayStructures != undefined) {
          this.cloneRule = this.ruleObj;
          this.loadPage = true;
          return;
        }
        if(this.ruleObj.rule_id != this.ruleId && this.ruleObj.rule_id != 0) {
          this.apiService.get('Rule/' + this.ruleId + '/' + this.versionNo)
            .subscribe(data => {
              if (data.statusCode === "200" || data.statusCode === "201") {
                this.loadPage = true;
                localStorage.setItem('LastRuleDetails', JSON.stringify(data.result))
                this.ruleObj = JSON.parse(localStorage.getItem('LastRuleDetails'));
                this.cloneRule = this.ruleObj;
              }
              else {
                this.toastMsg.error("Server", 'Error!')
              }
            }, (err: any) => {
              this.toastMsg.error(err.message, 'Error!')
            });
        } else {
          this.cloneRule = this.ruleObj;
          this.loadPage = true;
        }
      }
    })
  }



}
