<div class="card">
    <div class="card-header-info">
      <h4 class="card-title">Starter</h4>
    </div>
    <div class="card-body">
      <div class="row">
        <div class="form-group col-md-6" id="jobTypeGroup">
          <div class="row radio-btn-style">
              <mat-radio-group [(ngModel)]="selectedPlanJobType" (click)="$event.stopPropagation()">
                <mat-radio-button class="mr-2 job-radio-btn"value="J">By Job Type</mat-radio-button>
                <mat-radio-button class="mr-2" value="P">By Plan Name</mat-radio-button>													
              </mat-radio-group>
          </div>
          <div class="row" *ngIf="selectedPlanJobType =='J'">
            <label class="col-sm-5 lbl-align">Plan Execution Job Type</label>
            <div class="col-sm-7">
              <select class="custom-select" name="jobType" data-style="btn btn-link" id="jobType"  (change)="onJobTypeChange();"  [(ngModel)]="jobTypeId">
                <ng-container *ngIf="jobTypes">
                  <ng-container *ngFor="let type of jobTypes">
                    <option [value]="type.peJobTypeId">{{type.peJobTypeName}}</option>
                  </ng-container>
                </ng-container>
              </select>
            </div>
          </div>
          <div class="row" *ngIf="selectedPlanJobType =='P'">
            <label class="col-sm-5">Plan Name</label>
            <div class="col-sm-7">
              <select class="custom-select" name="plans_list" data-style="btn btn-link" id="plans_list" (change)="onPlanChange()"    [(ngModel)]="planId">
                <ng-container *ngIf="plansList">
                  <ng-container *ngFor="let plan of plansList">
                    <option [value]="plan.planId">{{plan.planName}}</option>
                  </ng-container>
                </ng-container>
              </select>
            </div>
          </div>
        </div>
        <div class="col-md-12 text-right">
            <button type="submit" class="btn btn-primary" style="height: auto;" id="starter-button" [disabled]="jobTypeId == null" (click)="checkIfJobRunning()" ><i class="fas fa-tasks"></i> Start Job</button>
          </div> 
            <p style="color: red;" [hidden]="!isJobRunning">Cannot start a job if another job of the same type is still running</p>
          </div>
        </div>
  </div>