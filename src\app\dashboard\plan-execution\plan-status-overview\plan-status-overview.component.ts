import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { IPlanExecutionOverview } from 'src/app/model/plan-execution-overview.model';
import { IJob } from 'src/app/model/plan.model';
import { ApiService } from 'src/app/services/api.service';

@Component({
  selector: 'plan-status-overview',
  templateUrl: './plan-status-overview.component.html',
  styleUrls: ['./plan-status-overview.component.css']
})
export class PlanStatusOverviewComponent implements OnInit {

  jobs: IJob[];
  overview: object;
  selectedJobId: number;
  p: number = 1;
  @Output() jobsStatus = new EventEmitter();

  constructor(private apiService: ApiService, private toastMsg: ToastrService) { }

  ngOnInit() {
    this.getJobs();
    this.getStatusOverview();
  }

  refresh() {
    this.getJobs();
    this.getStatusOverview();
  }

  getJobs() {
    let jobsStatus = {};
    this.apiService.get('PlanExecution/Jobs')
      .subscribe((data: any) => {
        this.jobs = data.map(job => {
          return <IJob>{
            peJobId: job.peJobId,
            peJobTypeName: job.peJobTypeName,
            jobStartTimestamp: job.jobStartTime,
            jobEndTimestamp: job.jobEndTime,
            jobStatus: job.jobStatus
          };
        });

        this.jobs.forEach(job => {
          jobsStatus[job.peJobTypeName] = jobsStatus[job.peJobTypeName] || [];
          jobsStatus[job.peJobTypeName].push(job.jobStatus);
        });

        this.jobsStatus.emit(jobsStatus);
      }, (err: any) => {
        this.toastMsg.error(err.message, "Server Error!");
      });
  }

  getStatusOverview() {
    this.apiService.get('PlanExecution')
      .subscribe((data: any) => {
        this.overview = data.reduce((a, b) => {
          let ovr = <IPlanExecutionOverview>{
            peJobId:b.peJobId,
            peJobStepName : b.peJobStepName,
            jobStartTime: b.jobStartTime,
            jobEndTime: b.jobEndTime,
            statusId: b.statusId,
            attemptedNumber: b.attemptedNumber
          };
          (a[ovr.peJobId] = a[ovr.peJobId] || []).push(ovr);
          return a;
        }, {});
      }, (err: any) => {
        this.toastMsg.error(err.message, "Server Error!");
      });
  }

  onRowClick(jobId: number) {
    this.selectedJobId = jobId;
  }

  terminate(e:Event, job:IJob){

      if (job && job.jobStatus==null) {
        var body = {
          "PE_Job_Id": job.peJobId
        }
        this.apiService.post('PlanExecution/Terminate/', body)
          .subscribe(data => {
            this.toastMsg.success("Plan execution terminated successfully.", "Success!");
            job.jobStatus="Failed";
          }, (err: any) => {
            this.toastMsg.error(err.message, "Server Error!");
          });
      } else {
        this.toastMsg.error("Plan must be 'InProgress'.", "Missing Data!");
      }

    e.stopPropagation();
    e.stopImmediatePropagation();
  }
}
