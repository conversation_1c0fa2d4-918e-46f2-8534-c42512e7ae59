.treeview, .treeview ul {
  margin:0;
  padding:0;
  list-style:none
}
.treeview ul {
  margin-left:1em;
  position:relative
}
.treeview ul ul {
  margin-left:.5em
}
.treeview ul:before {
  content:"";
  display:block;
  width:0;
  position:absolute;
  top:0;
  bottom:0;
  left:0;
  border-left:1px solid
}
.treeview li {
  margin:0;
  padding:0 1em;
  line-height:2em;
  color:#369;
  font-weight:700;
  position:relative
}
.treeview ul li:before {
  content:"";
  display:block;
  width:10px;
  height:0;
  border-top:1px solid;
  margin-top:-1px;
  position:absolute;
  top:1em;
  left:0
}
.treeview ul li:last-child:before {
  background:#fff;
  height:auto;
  top:1em;
  bottom:0
}
.indicator {
  margin-right:5px;
}
.treeview li a {
  text-decoration: none;
  color:black;
}
.treeview li button, .treeview li button:active, .treeview li button:focus {
  text-decoration: none;
  color:#369;
  border:none;
  background:transparent;
  margin:0px 0px 0px 0px;
  padding:0px 0px 0px 0px;
  outline: 0;
}

.highlightBackground{
  background-color:#48c6f3;
  color: white !important;
}

.scroll-area{
  max-height: 350px;
  overflow: auto;
}
