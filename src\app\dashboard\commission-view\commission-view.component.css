.checkbox-section {
    display: flex;
    justify-content: space-around;
}

.bold {
    font-weight: 900;
}

.details-container {
    display: flex;
    justify-content: center;
    text-align: center;
}
.commission-details-row {
    display: flex;
    justify-content: space-between;
}
.commission-details-title {
    font-weight: 700;
}

.details {
    padding:10px;
    border-right: 1px dashed #7a7a7a;
}
.details-left {
    padding: 10px;
}

.details-content {
    font-size: .8rem;
}
 

.formula-container:hover {
    cursor: pointer;
    background-color: #f4f4f4; 
}
.base-formula {
    border-bottom: 1px solid #f4f4f4;
}
.base-formula:hover {
    cursor: pointer;
    background-color: #f4f4f4;
}
.formula-name {
    
}
.formula-content {
      
}
.formula-title {
    font-weight: 700;
    font-size: .9rem;
}
.table-hover tr.mat-row:hover{cursor: pointer;}
.base-formula-conditions {
    padding: 5px 10px;
    margin-top: 10px;
     
    transition: 0.3s;
  
    /* display: flex;
    justify-content: space-between; */
}
.base-formula-condition-title {
    font-weight: 700;
}
.base-formula-condition-title-container {
    display: flex;
    justify-content: space-between;
}
.base-formula-conditions-row {
    display: flex;
    justify-content: space-between;
}

.button-width {
    width: 205px;
}

.mat-paginator {
    margin-top: 2%
}
.no-preview-available{
    margin-left: 50%;
}