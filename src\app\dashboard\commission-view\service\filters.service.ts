import { Injectable } from '@angular/core';

@Injectable()
export class CommissionFilters {

    filterMetadata(metadata, metadataComparison, baseformulasList){
        var output = metadata;
        for(let i:number=0; i < metadata.length; i++) {
            for(let j:number=0; j < metadataComparison.length; j++) {
                if(metadata[i]) {
                    if ((i !== j) && metadata[i].displayName == metadataComparison[j].displayName) {
                        output.splice(i, 1);
                    }
                }
            }
        }
      let uniqueOutput = output;
        for(let i:number =0; i < output.length; i++) {
          for(let j:number =0; j < baseformulasList.length; j++) {
            if (output[i].displayName && output[i].displayName == baseformulasList[j].baseFormulaName) {
              uniqueOutput.splice(i, 1);
            } 
          }
          if (output[i].displayName && (output[i].displayName == output[i].value)) {
                uniqueOutput.splice(i, 1);
              }
        }
        
        for (let j: number = 0; j < baseformulasList.length; j++) {
          while (present(uniqueOutput, baseformulasList[j].baseFormulaName)) {
            var name = baseformulasList[j].baseFormulaName;
            var i = elementAt(uniqueOutput, name)
            if (i > -1) {
              uniqueOutput.splice(i, 1);
            } 
          }
        }
      
      uniqueOutput = uniqueOutput.reduce((acc, current) => {
        var x = acc.find(item => item.displayName === current.displayName);
        if (!x) {
          return acc.concat([current]);
        } else {
          return acc;
        }
      }, []);

      return uniqueOutput;     
    }

      filterBaseForumulas(baseformulas, baseformulasList) {
        if (baseformulas) {
          var output = baseformulas;
          for(let i:number =0; i < baseformulas.length; i++) {
            let repeatCount:number = 0; 
            for(let j:number =0; j < baseformulasList.length; j++) {
                if(baseformulas[i]) {
                    if ((i !== j) && baseformulas[i].baseFormulaName == baseformulasList[j].baseFormulaName) {
                      
                        output.splice(i, 1);
                      
                    }
                }
            }
          }
          return output;
        } else {
          return [];
        }
      }

      filterBaseFormulaBreakdown(baseformulas, baseFormulaBreakdown){
        let output = baseformulas;
        for (let i:number=0; i < baseformulas.length ; i++) {
          for (let j:number=0; j < baseFormulaBreakdown.length; j++){
            if(i !== j && baseformulas[i].baseFormulaName === baseFormulaBreakdown[j].baseFormulaName) {
              output.splice(i, 1);
            }
          }
        }
        return output;
      }
}

function present(array, name) {
  var count = array.length;
  for (var i = 0; i < count; i++) {
    if (array[i].displayName === name) {
      return true;
    }
  }
  return false;
}

function elementAt(uniqueOutput, name) {
  var count = uniqueOutput.length;
  for (var i = 0; i < count; i++) {
    if (uniqueOutput[i].displayName === name) {
      return i;
    }
  }
  return -1;
}