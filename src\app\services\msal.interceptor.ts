import { MsalGuardConfiguration, MsalInterceptorConfiguration } from "@azure/msal-angular";
import { BrowserCacheLocation, IPublicClientApplication, InteractionType, LogLevel, PublicClientApplication } from "@azure/msal-browser";
import { environment } from "src/environments/environment";

export function MSALInterceptorConfigFactory(): MsalInterceptorConfiguration {
    const protectedResourceMap = new Map<string, Array<string>>();
    protectedResourceMap.set(environment.baseUrl , environment.scopeUri);
    return {
      interactionType: InteractionType.Redirect,
      protectedResourceMap
    };
}  

export function MSALInstanceFactory(): IPublicClientApplication{
      return new PublicClientApplication({
        auth:{
          clientId:environment.uiClienId,
          redirectUri:environment.redirectUrl,
          authority:environment.authority,
        },cache: {
          cacheLocation: BrowserCacheLocation.LocalStorage
        }
      })
}

export function MSALGuardConfigFactory(): MsalGuardConfiguration {
  return {
    interactionType: InteractionType.Redirect,
    authRequest: {
      scopes: environment.scopeUri
    },
    loginFailedRoute: '/unauthorized'
  };
}
