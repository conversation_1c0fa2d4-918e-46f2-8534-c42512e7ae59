import { Component, Inject, OnInit, ViewChild } from '@angular/core';
import { Router, ActivatedRoute } from "@angular/router";
import { ApiService } from "../../services/api.service";
import { ToastrService } from "ngx-toastr";
import { MAT_LEGACY_DIALOG_DATA } from '@angular/material/legacy-dialog';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { ISalesRepProcessedPayment } from 'src/app/model/sales-rep-processed-payment.model';
@Component({
  selector: 'app-sales-rep-processed-payment-popup',
  templateUrl: './sales-rep-processed-payment-popup.component.html',
  styleUrls: ['./sales-rep-processed-payment-popup.component.css']
})
export class SalesRepProcessedPaymentPopupComponent implements OnInit {
 ProcessedPayments: Array<ISalesRepProcessedPayment>;
 withdrawalDate: any;
 @ViewChild(MatSort, { static: true }) sort: MatSort;
 dataSource: MatTableDataSource<ISalesRepProcessedPayment> = new MatTableDataSource();
 processedPaymentColumns: string[] = ["paymentFor", "paymentType", "dateProcessed", "transactionType", "systemSize", "cppw", "amount", "actualInstallDate"];
 employeeName: string;

  constructor(
    private router: Router,
    public apiService: ApiService,
    private toastMsg: ToastrService,
    @Inject(MAT_LEGACY_DIALOG_DATA) public data: any
    ) 
    {
      this.ProcessedPayments = data.ProcessedPayments;
      this.withdrawalDate = data.withdrawalDate;
    }
 
  ngOnInit() {
   
  }
}
