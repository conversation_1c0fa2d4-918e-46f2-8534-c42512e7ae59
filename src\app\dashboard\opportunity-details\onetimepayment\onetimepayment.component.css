.pointer {
    cursor: pointer;
}

.table-header {
    font-weight: 800;
    color:#5f5f5f;
    /* align-content: flex-start; */
}

mat-header-cell {
    display:flex;
    justify-content:flex-start;
   }

.custom-select:focus {
  border-color: #408bc0;
  outline: 0;
  box-shadow: #408bc0;
}

.dropdown-item.active,
.dropdown-item:active {
  color: white;
  text-decoration: none;
  background-color:#408bc0;
  width: 90%;
}

.dropdown-item.hover .dropdown-item:hover {
    color: white;
    background-color:#408bc0;
    width: 90%;
}

 .dropdown-menu .dropdown-item:hover, .dropdown-menu .dropdown-item:focus, .dropdown-menu a:hover, .dropdown-menu a:focus, .dropdown-menu a:active {
    color: white;
    background-color:#408bc0 !important;
    width: 90%;
}
