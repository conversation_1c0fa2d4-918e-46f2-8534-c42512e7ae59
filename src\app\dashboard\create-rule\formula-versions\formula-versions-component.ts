import { Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, FormGroup, Validators } from "@angular/forms";
import { ApiService } from "../../../services/api.service";
import { ToastrService } from 'ngx-toastr';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { DataSource } from '@angular/cdk/table';
import { MatLegacyPaginator } from '@angular/material/legacy-paginator';
import { Router, ActivatedRoute } from '@angular/router';
import { TableFilterPipe } from '../../../pipe/table-filter.pipe';
import { DatePipe, PercentPipe, CurrencyPipe } from '@angular/common';
import { MatLegacyDialog } from '@angular/material/legacy-dialog';
import { HttpParams, HttpClient } from '@angular/common/http';
import { ApiResponse } from '../../../services/api.response';
import { environment } from '../../../../environments/environment';
import { IRuleVersion } from '../../../model/rule-version';
import * as FileSaver from 'file-saver';

@Component({
    selector: 'app-formula-versions',
    templateUrl: './formula-versions-component.html',
    styleUrls: ['./formula-versions-component.css']
})
export class FormulaVersionsComponent implements OnInit {
    pageSizeOptions: number[] = [10, 20, 50];
    pageSize: number = 10;
    formulaId: number = 0;
    selectedVersion: number = 0;
    formulaVersionsDataSource: MatTableDataSource<IFormulaVersion> = new MatTableDataSource([]);
    formulaVersionCols: string[] = ["selected", "versionNumber", "formulaName", "createdBy", "createdDate", "active"];
    @ViewChild(MatSort, { static: true }) sort: MatSort;
    @ViewChild(MatLegacyPaginator, { static: true }) paginator: MatLegacyPaginator;

    constructor(public apiService: ApiService, private toastMsg: ToastrService, private formBuilder: UntypedFormBuilder, private pipe: TableFilterPipe, private http: HttpClient,
        private activatedRoute: ActivatedRoute, private datePipe: DatePipe, private percentPipe: PercentPipe, private currencyPipe: CurrencyPipe, private dialog: MatLegacyDialog) {
    }

    ngOnInit() {
        this.activatedRoute.params.subscribe(params => {
            this.formulaId = params.formula_id;
        });

        this.getFormulaVersions();
        if (!this.apiService.checkPermission('ViewRateTables')) {
            this.apiService.goBack();
            this.toastMsg.error("Insufficient Permissions. Please make sure your account can access this information.", "Permissions");
        }

    }

  viewRuleVersion(versionNo) {
    const url = `#/ui/commissions/viewBaseFormula/${this.formulaId}/${versionNo}`;
    window.open(url, '_self');
  }

  checkSelected() {
    return this.selectedVersion > 0;
    }

    getNumberSelected(): number {
        if (this.formulaVersionsDataSource) return this.formulaVersionsDataSource.data.filter(record => record.selected).length;
    }

    onSelectionChange() {
      let balances = this.formulaVersionsDataSource.filteredData.filter(bal => bal.selected);
    }

  setSelectedVersion() {
    
      var body = {
        ruleId: this.formulaId,
        versionNumber: this.selectedVersion
      }

      this.apiService.post('baseformula/SetActiveVersion', body)
        .subscribe(data => {
          this.toastMsg.success('Selected version ' + this.selectedVersion + ' activated successfully');
          this.getFormulaVersions();
          
        }, (err: any) => {
          this.toastMsg.error(err.message, 'Server Error!');
        });
    }


    getFormulaVersions() {
       
      this.http.get<ApiResponse>(`${environment.apiBaseUrl}baseFormula/formulaVersions/${this.formulaId}`)
            .subscribe(data => {
                if (data && data.result) {
                    var formulaVersionsDataSource = data.result.map((rows: IFormulaVersion) => { return rows });
                    this.formulaVersionsDataSource = new MatTableDataSource<IFormulaVersion>(formulaVersionsDataSource);
                    this.formulaVersionsDataSource.paginator = this.paginator;
                    this.formulaVersionsDataSource.sort = this.sort;
                }
            }, err => {
                this.toastMsg.error(err.message, "Error!");
            })
    }
}

export interface Element {

  versionNumber: number,
  formulaName: string,
  active: boolean,
  createdBy: string,
  createdDate: string
}

export interface IFormulaVersion {
  selected?: boolean;
  formulaId: number;
  versionNumber: number;
  formulaName: string;
  active: boolean;
  createdBy: string;
  createdDate: string;
}


