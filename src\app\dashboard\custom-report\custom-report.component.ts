import { Component, OnInit } from '@angular/core';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { MsalService } from '@azure/msal-angular';
import { AuthenticationResult } from '@azure/msal-browser';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-custom-report',
  templateUrl: './custom-report.component.html',
  styleUrls: ['./custom-report.component.css']
})
export class CustomReportComponent implements OnInit {
  iframeUrl = environment.oneReportUrl;
  accessToken!:string;
  showIframe:boolean = false;
  url:SafeResourceUrl;


  constructor(private sanitizer:DomSanitizer,private authService: MsalService
    ) { }

  ngOnInit() {
    this.getAccessToken();
  }

  // getSafeUrl(): SafeResourceUrl {
  //   return this.sanitizer.bypassSecurityTrustResourceUrl(this.iframeUrl+'?access_token='+this.accessToken);
  // }

  getAccessToken() {
    this.authService.acquireTokenSilent({
      scopes: environment.scopeUri,
    }).subscribe({
      next: (response: AuthenticationResult) => {
        this.accessToken = response.accessToken;        
        const queryParams = `?access_token=${this.accessToken}&application_id=${environment.applicationId}`;
            this.url = this.sanitizer.bypassSecurityTrustResourceUrl(`${this.iframeUrl}${queryParams}`);            
            this.showIframe = true;
      },
      error: (error) => {
        console.error('Error acquiring token:', error);
      }
    });
  }

}
