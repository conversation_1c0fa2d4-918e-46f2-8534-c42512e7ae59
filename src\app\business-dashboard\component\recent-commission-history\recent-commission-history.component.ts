import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { IRecentCommissionHistory, IdateRange } from '../../models/models';
import { MatLegacyPaginator } from '@angular/material/legacy-paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { ApiService } from 'src/app/services/api.service';
import { ToastrService } from 'ngx-toastr';
import { CurrencyPipe, DatePipe } from '@angular/common';
import { Chart } from 'angular-highcharts';
import { DatezonePipe } from 'src/app/pipe/datezone.pipe';

interface CustomPoint extends Highcharts.Point {
  custom: any;
}
@Component({
  selector: 'app-recent-commission-history',
  templateUrl: './recent-commission-history.component.html',
  styleUrls: ['./recent-commission-history.component.css']
})
export class RecentCommissionHistoryComponent implements OnInit {
  @Input() dateRange: IdateRange | null = null;
  originalDataSource: any;
  dataSource: any;
  displayedColumns = [];
  historyData:any;
  chartData:any;
  commissionTypeChart:any;
  @Input() tabNumber: number | null = null;
  previousDateRange: IdateRange | null = null;
  columnNames = [
    {
      id: "contactLegalName",
      value: "Contact Legal Name"
    },
    {
      id: "opportunityName",
      value: "Opportunity Name"
    },
    {
      id: "amount",
      value: "Amount"
    },
    {
      id: "commissionType",
      value: "Commission Type"
    },
    {
      id: "modifiedDate",
      value: "Modified Date",
      dataType:'Date'
    },
    {
      id: "createdDate",
      value: "Created Date",
      dataType:'Date'
    },
  ];
  @ViewChild('table', { read: MatSort, static: true }) sort: MatSort;
  @ViewChild('paginator', { static: true }) paginator: MatLegacyPaginator;
  commissionType:string = 'c';
  columnDisplay:any = this.columnNames;
  displayGrid:boolean = true;
  description:string = 'Created';
  constructor(public apiService: ApiService, private toastMsg: ToastrService, private datePipe: DatePipe,private currencyPipe: CurrencyPipe,private dateZonePipe: DatezonePipe) { }
  ngOnInit() {
  }
  ngOnChanges(){
    if (this.tabNumber === 7) {
      if (this.dateRange) {
        if (this.previousDateRange === null || this.previousDateRange !== this.dateRange) {
          this.previousDateRange = this.dateRange;
          this.onChangeCommissionType();
        }       
      }
    }    
  }
  getCommissionHistory() {
    this.apiService.get(`BusinessDashboard/RecentCommissionHistory?toDate=${this.dateRange.endDate}&fromDate=${this.dateRange.startDate}&dateType=${this.commissionType}`)
      .subscribe((res: any) => {
        this.historyData = res.commissionHistoryList;
        this.chartData = res.commissionTypeChart;
        this.displayedColumns = this.columnDisplay.map(x => x.id);
        this.chartData.forEach(s=>{
          s.custom = this.currencyPipe.transform(s.custom);
        })
        this.createTable();
        this.getCommissionTypeChart();
      }, (err: any) => {
        this.toastMsg.error(err.message, "Server Error!");
      });
  }
  createTable() {
    let tableArr: IRecentCommissionHistory[] = [];
    for (let i: number = 0; i <= this.historyData.length - 1; i++) {
      let currentRow = this.historyData[i];
      tableArr.push({
        contactId: currentRow.contactId, amount: currentRow.commissionAmount, contactLegalName: currentRow.contactLegalName,
        opportunityId: currentRow.opportunityId, opportunityName: currentRow.opportunityName,commissionType:currentRow.commissionTypeName,
        commissionId:currentRow.commissionId,
        modifiedDate: this.dateZonePipe.transform(currentRow.userModifiedTimestamp),createdDate:this.dateZonePipe.transform(currentRow.userCreatedTimestamp)
      });
    }
    this.dataSource = new MatTableDataSource(tableArr);
    this.originalDataSource = tableArr;
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }
  onSortChange(event:any){
    let dateFieldData = this.columnNames.filter(s=>s.dataType === 'Date');
    let dateField = dateFieldData && dateFieldData.length > 0 ? dateFieldData.filter(s=>s.id === event.active).map(c=> c.id).toString():'';
    if(dateField){
      this.dataSource.sortingDataAccessor = (item, property) => {
        switch (property) {
          case dateField:
            return new Date(item[dateField]).toISOString();
          default:
            return item[property];
        }
      };

      this.dataSource.sortingFn = (a: any, b: any, active: string, direction: string) => {
        if (active === dateField) {
          const dateA = new Date(a);
          const dateB = new Date(b);
          if (direction === 'asc') {
            return dateA.getTime() - dateB.getTime();
          } else {
            return dateB.getTime() - dateA.getTime();
          }
        } else {
          return this.dataSource.sortingDataAccessor(a, active) > this.dataSource.sortingDataAccessor(b, active) ? 1 : -1;
        }
      };
    }
    
    
  }
  getCommissionTypeChart() {
    this.commissionTypeChart = this.setChartData('Commission Type', this.chartData);
  }
  setChartData(title: string, chartData: any) {
    let chart = new Chart({
      chart: {
        plotBackgroundColor: null,
        plotBorderWidth: null,
        plotShadow: false,
      },
      title: {
        text: title
      },
      tooltip: {
        pointFormatter: function() {
          const point = this as CustomPoint;
          return `Total Amount: <b>${point.custom.toLocaleString("en-US")}</b> <br/> Total Count : ${point.y.toLocaleString("en-US")}<b></b>`;
        },
      },
      accessibility: {
        point: {
          valueSuffix: '%',
        },
      },
      credits: {
        enabled: false
      },
      legend: {
        maxHeight: 90,  
      },
      plotOptions: {
        pie: {
          allowPointSelect: true,
          innerSize: '50%',
          cursor: 'pointer',
          dataLabels: {
              enabled: true,
          },
          showInLegend: true
      }
      },
      series: [
        {
          type: 'pie',
          name: 'Total Amount',
          showInLegend: true,
          data: chartData
        }
      ]
    });
    return chart;
  }

  onChangeCommissionType(){    
    this.displayGrid = false;
    switch(this.commissionType){
      case 'a':{
        this.columnDisplay = this.columnNames;
        this.description = 'Created and Modified';
        this.displayedColumns = this.columnDisplay.map(x => x.id);
        this.displayGrid = true;
        this.getCommissionHistory();
        break;
      }
      case 'm':{
        this.columnDisplay = this.columnNames.filter(column => column.id !== "createdDate");
        this.description = 'Modified after creation';
        this.displayedColumns = this.columnDisplay.map(x => x.id);
        this.displayGrid = true;
        this.getCommissionHistory();
        break;
      }
      case 'c':{
        this.columnDisplay = this.columnNames.filter(column => column.id !== "modifiedDate");
        this.description = 'Created and not undergone any modification';
        this.displayedColumns = this.columnDisplay.map(x => x.id);
        this.displayGrid = true;
        this.getCommissionHistory();
        break;
      }
    }
  }
}
