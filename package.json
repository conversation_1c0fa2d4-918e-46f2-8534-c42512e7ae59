{"name": "ag8-ui", "version": "0.0.0", "scripts": {"ng": "ng", "start": "set NODE_OPTIONS=--openssl-legacy-provider && ng serve", "start-local": "ng serve --configuration=local", "build": "ng build", "build-prod": "ng build --prod", "build-staging": "ng build --configuration=staging", "build-test": "ng build --configuration=test", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular/animations": "^16.2.12", "@angular/cdk": "^16.2.14", "@angular/common": "^16.2.12", "@angular/compiler": "^16.2.12", "@angular/core": "^16.2.12", "@angular/forms": "^16.2.12", "@angular/localize": "^16.2.12", "@angular/material": "^16.2.14", "@angular/platform-browser": "^16.2.12", "@angular/platform-browser-dynamic": "^16.2.12", "@angular/router": "^16.2.12", "@azure/msal-angular": "^3.0.13", "@azure/msal-browser": "^3.10.0", "@highcharts/map-collection": "^1.1.3", "@ng-bootstrap/ng-bootstrap": "^15.1.2", "@popperjs/core": "^2.10.0", "angular-highcharts": "^16.0.0", "crypto-js": "^4.2.0", "file-saver": "^2.0.5", "file-saver-es": "^2.0.5", "highcharts": "^11.0.0", "highcharts-angular": "^3.0.0", "moment": "^2.24.0", "ng-multiselect-dropdown": "^1.0.0", "ngx-currency": "^4.0.0", "ngx-pagination": "^6.0.3", "ngx-timeago": "^3.0.0", "ngx-toastr": "^19.0.0", "rxjs": "~7.8.1", "rxjs-compat": "^6.6.7", "tslib": "^2.3.0", "zone.js": "^0.13.0"}, "devDependencies": {"@angular-devkit/build-angular": "^16.2.12", "@angular/cli": "^16.2.12", "@angular/compiler-cli": "^16.2.12", "@angular/language-service": "^16.2.12", "@types/jasmine": "^4.3.0", "@types/jasminewd2": "^2.0.8", "@types/node": "^12.20.55", "jasmine-core": "~4.6.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.4.2", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "protractor": "^7.0.0", "ts-node": "~7.0.0", "typescript": "^5.1.3"}}