<div class="page-title col-md-12 ">
  <h1 class="">Lead Generator Demos</h1>
  <div class="breadcrumbs"><a href="#">Home</a>/<span>Lead Generator Demos</span>
  </div>
</div>

<div class=" w-100">
  <div class="content">
    <div class="card">
      <div class="card-header-info outreachopportunities">
        <h4 class="card-title">Lead Generator Demos</h4>

      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-12">
            <div class="float-right ">
              <button class="btn btn-primary" (click)="filter = !filter"><i class="material-icons">filter_list</i>
                Filter</button>

              <button class="btn btn-primary" (click)="getOpportunityWorksheet()"><i class="material-icons">save_alt</i>
                Download</button>
            </div>
            <div class="float-right col-md-3 text-right pr-2">
              <div class="form-group input-group ">

                <input class="custom-input ng-pristine ng-valid ng-touched" type="text" id="searchTextId"
                  [(ngModel)]="searchText" (keyup)="applyFilter($event)" name="searchText" placeholder="Search">
                <span class="input-group-icon">
                  <i class="fas fa-search"></i>
                </span>
              </div>
            </div>
          </div>
        </div>
        <ng-container *ngIf="filter">
          <div class="row">
            <div class="col-md-12  gray-bg">
              <div class="row">
                <div class="form-group col-md-4">
                  <label>Demo Date - From</label>
                  <div class="input-group date-picker">
                    <input #DemoDateRangeStartGreater type="date" class="custom-input " [(ngModel)]="DemoDateRangeStart"
                      (change)="onChangeFilter()">                     
                    <span *ngIf="DemoDateRangeStartGreater.value.length > 0" class="mat-icon cal-reset"  (click)="this.DemoDateRangeStart = null; onChangeFilter();"><i class="far fa-calendar-times"></i></span> 
                    <span *ngIf="DemoDateRangeStartGreater.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span>  
                   
                  </div>
                </div>
                <div class="form-group col-md-4">
                  <label>Demo Date - To</label>
                  <div class="input-group date-picker">
                    <input #DemoDateRangeEndLess type="date" class="custom-input " [(ngModel)]="DemoDateRangeEnd" (change)="onChangeFilter()">
                    <span *ngIf="DemoDateRangeEndLess.value.length > 0" class="mat-icon cal-reset"  (click)="this.DemoDateRangeEnd = null; onChangeFilter();"><i class="far fa-calendar-times"></i></span> 
                    <span *ngIf="DemoDateRangeEndLess.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span>  
                    
                  </div>
                </div>
                <div class="form-group col-md-4">
                  <label >Lead Generator</label> <br>
                  <input type="text" class="custom-input" [(ngModel)]="LeadGenerator" (change)="onChangeFilter()">
                </div>
              </div>
              <div class="row">
                <div class="form-group col-md-4">
                  <label >Lead Generator Plan Name</label> <br>
                  <input type="text" class="custom-input" [(ngModel)]="PlanName" (change)="onChangeFilter()">
                </div>
                <div class="form-group col-md-4">
                  <label >Hire Date - From</label>
                  <div class="input-group date-picker">
                    <input #HireDatestartGreater type="date" class="custom-input " [(ngModel)]="HireDatestart" (change)="onChangeFilter()">
                    
                    <span *ngIf="HireDatestartGreater.value.length > 0" class="mat-icon cal-reset"  (click)="this.HireDatestart = null; onChangeFilter();"><i class="far fa-calendar-times"></i></span> 
                    <span *ngIf="HireDatestartGreater.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span>  
                     
                  </div>
                </div>
                <div class="form-group col-md-4">
                  <label>Hire Date - To</label>
                  <div class="input-group date-picker">
                     <input #HireDateEndLess type="date" class="custom-input " [(ngModel)]="HireDateEnd" (change)="onChangeFilter()">
                     
                    <span *ngIf="HireDateEndLess.value.length > 0" class="mat-icon cal-reset"  (click)="this.HireDateEnd = null; onChangeFilter();"><i class="far fa-calendar-times"></i></span> 
                    <span *ngIf="HireDateEndLess.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span>  
                </div>
                </div>
              </div>
            </div>
          </div>
        </ng-container>

        <table mat-table [dataSource]="outreachOpportunities" matSort class="my-table" style="width: 100%">

          <ng-container matColumnDef="opportunityName">
            <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Opportunity </th>
            <td data-td-head="Opportunity" mat-cell *matCellDef="let element" class="hover-approval">
              <a (click)="openOpportunity(element.opportunityId)">{{element.opportunityName}}</a>
            </td>
          </ng-container>

          <ng-container matColumnDef="demoDate">
            <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Demo Date </th>
            <td data-td-head="Demo Date" mat-cell *matCellDef="let element"> {{element.demoDate}} </td>
          </ng-container>

          <ng-container matColumnDef="hireDate">
            <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Hire Date </th>
            <td data-td-head="Hire Date" mat-cell *matCellDef="let element"> {{element.hireDate}} </td>
          </ng-container>

          <ng-container matColumnDef="monthlyDemoNumber">
            <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Monthly Demo Number</th>
            <td data-td-head="Monthly Demo Number" mat-cell *matCellDef="let element">
              {{element.monthlyDemoNumber}}
            </td>
          </ng-container>

          <ng-container matColumnDef="sumofPayments">
            <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Sum of Payments</th>
            <td data-td-head="Sum of Payments" mat-cell *matCellDef="let element">
              {{element.sumofPayments | currency}}
            </td>
          </ng-container>

          <ng-container matColumnDef="leadGenerator">
            <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Lead Generator </th>
            <td data-td-head="Contact" mat-cell *matCellDef="let element" (click)="openSalesrep(element.contactId)"
              class="hover-withdrawal">
              <a>{{element.leadGenerator}}</a>
            </td>
          </ng-container>
          <ng-container matColumnDef="leadGeneratorPlanName">
            <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header>Lead Generator Plan Name</th>
            <td data-td-head="Lead Generator Plan Name" mat-cell *matCellDef="let element">
              {{element.leadGeneratorPlanName}}
            </td>
          </ng-container>
          <ng-container matColumnDef="employeeStatus">
            <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header>Employee Status</th>
            <td data-td-head="Employee Status" mat-cell *matCellDef="let element">
              {{element.employeeStatus}}
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="outreachColumns"></tr>
          <tr mat-row *matRowDef="let row; columns outreachColumns;"></tr>
        </table>
        <mat-paginator [pageSize]="pageSize" [pageSizeOptions]="pageSizeOptions">
        </mat-paginator>

      </div>
    </div>
  </div>
</div>