<div class="page-title col-md-12 ">
    <h1 class="">Evaluated Commission Details</h1>
    <div class="breadcrumbs"><a href="#">Home</a>/<a
        [routerLink]="['/ui/commissions/opportunitydetails', commission.opportunityId]">Opportunity
        Details</a>/<span>Commission Details</span>
    </div>
  </div>
  <div class=" w-100">
    <div class="content">
      <div class="card">
        <div class="card-header-info paymentwithdrawal">
          <h4 class="card-title">Opportunity Details</h4>
  
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-4 ">
              <div class="row">
                <label class="col-5">Calculated Via</label>
                <span class="col-7">{{commission.planName}} <span *ngIf="commission.planName && commission.ruleName">/</span> {{commission.ruleName}}</span>
              </div>
            </div>
            <div class="col-md-4 ">
              <div class="row">
                <label class="col-5">Paid To</label>
                <span class="col-7"><a
                    [routerLink]="['/ui/commissions/salesrep', commission.contactId]">{{commission.paidTo}}</a></span>
              </div>
            </div>
            <div class="col-md-4 ">
              <div class="row">
                <label class="col-5">Opportunity</label>
                <span class="col-7"><a
                    [routerLink]="['/ui/commissions/opportunitydetails/', commission.opportunityId]">{{commission.opportunityName}}</a></span>
              </div>
            </div>
            <div class="col-md-4 ">
              <div class="row">
                <label class="col-5">Opportunity Finalized</label>
                <!-- <p>{{commission.opportunityFinalized}}</p> -->
                <!-- Dilip 06/22/2020 COM-1037 -->
                <span class="col-7">
                  <mat-checkbox [(ngModel)]="commission.opportunityFinalized" [disabled]="true"></mat-checkbox>
                </span>
              </div>
            </div>
            <div class="col-md-4 ">
              <div class="row">
                <label class="col-5">Sales Division</label>
                <span class="col-7">{{commission.salesDivision}}</span>
  
              </div>
            </div>
            <div class="col-md-4 ">
              <div class="row">
                <label class="col-5">Sales Office</label>
                <span class="col-7">{{commission.salesOffice}}</span>
  
              </div>
            </div>
            <div class="col-md-4 ">
              <div class="row">
                <label class="col-5">Stage</label>
                <span class="col-7">{{commission.stage}}</span>
              </div>
            </div>
            <div class="col-md-4 ">
              <div class="row">
                <label class="col-5">Lead Source</label>
                <span class="col-7">{{commission.leadSource}}</span>
              </div>
            </div>
            <div class="col-md-4 ">
              <div class="row">
                <label class="col-5">Territory</label>
                <span class="col-7">{{commission.salesTerritory}}</span>
              </div>
            </div>
            <div class="col-md-4 ">
              <div class="row">
                <label class="col-5">Utility</label>
                <span class="col-7">{{commission.utilityCompany}}</span>
              </div>
            </div>
            <div class="col-md-4 ">
              <div class="row">
                <label class="col-5">IOS Track</label>
                <span class="col-7">{{commission.iosTrack}}</span>
              </div>
            </div>
            <div class="col-md-4 ">
              <div class="row">
                <label class="col-5">Last Run Date</label>
                <span class="col-7">{{commission.lastRunDate ? (commission.lastRunDate.toString() | datezone) : ""}}</span>
              </div>
            </div>
            <ng-container *ngIf="commission?.viewCategory?.includes('S')">
              <div class="col-md-4 ">
                <div class="row">
                  <label class="col-5">Actual Install Date</label>
                  <span class="col-7">{{commission.actualInstallDate ? (commission.actualInstallDate | date) : '-'}}</span>
    
                </div>
              </div>
              <div class="col-md-4 ">
                <div class="row">
                  <label class="col-5">Date Contract Signed</label>
                  <span
                    class="col-7">{{commission.dateContractSigned ? (commission.dateContractSigned | date) : '-'}}</span>
    
                </div>
              </div>
              <div class="col-md-4 ">
                <div class="row">
                  <label class="col-5">System Size</label>
                  <span class="col-7">{{commission.systemSizeKWdc}}</span>
                </div>
              </div>
              <div class="col-md-4 ">
                <div class="row">
                  <label class="col-5">Purchase Method</label>
                  <span class="col-7">{{commission.purchaseMethod}}</span>
                </div>
              </div>
              <div class="col-md-4 ">
                <div class="row">
                  <label class="col-5">Finance Partner</label>
                  <span class="col-7">{{commission.financePartner}}</span>
                </div>
              </div>
              <div class="col-md-4 ">
                <div class="row">
                  <label class="col-5">Inverter Type</label>
                  <span class="col-7">{{commission?.inverterType}}</span>
                </div>
              </div>
              <div class="col-md-4 ">
                <div class="row">
                  <label class="col-5">Module Type</label>
                  <span class="col-7">{{commission?.moduleType}}</span>
                </div>
              </div>
            </ng-container>          
            <ng-container *ngIf="commission?.viewCategory?.includes('R')">
              <div class="col-md-4 ">
              <div class="row">
                <label class="col-5">Roofing Install Date</label>
                <span class="col-7">{{commission?.roofingInstallDate ? (commission.roofingInstallDate | date) : '-'}}</span>
              </div>
            </div>
            <div class="col-md-4 ">
              <div class="row">
                <label class="col-5">Roofing Contract Date</label>
                <span class="col-7">{{commission?.roofingContractDate ? (commission.roofingContractDate | date) : '-'}}</span>
              </div>
            </div>
            <div class="col-md-4 ">
              <div class="row">
                <label class="col-5">Roofing Purchase Method</label>
                <span class="col-7">{{commission?.roofingPurchaseMethod}}</span>
              </div>
            </div>
            <div class="col-md-4 ">
              <div class="row">
                <label class="col-5">Roofing Sale Amount</label>
                <span class="col-7">{{commission?.roofingsaleamount | currency}}</span>
              </div>
            </div>
            <div class="col-md-4 ">
              <div class="row">
                <label class="col-5">Roof Squares Including Waste</label>
                <span class="col-7">{{commission?.roofSquaresIncludingwaste}}</span>
              </div>
            </div>
            <div class="col-md-4 ">
              <div class="row">
                <label class="col-5">Roofing Adders</label>
                <span class="col-7">{{commission?.roofingadders}}</span>
              </div>
            </div>  
          </ng-container>
          <ng-container *ngIf="commission?.viewCategory?.includes('B')">
              <div class="col-md-4 ">
              <div class="row">
                <label class="col-5">Battery Install Date</label>
                <span class="col-7">{{commission?.batteryInstallDate ? (commission.batteryInstallDate | date) : '-'}}</span>
              </div>
            </div>
            <div class="col-md-4 ">
              <div class="row">
                <label class="col-5">Battery Contract Date</label>
                <span class="col-7">{{commission?.batteryContractDate ? (commission.batteryContractDate | date) : '-'}}</span>
              </div>
            </div>
            <div class="col-md-4 ">
              <div class="row">
                <label class="col-5">Battery Purchase Method</label>
                <span class="col-7">{{commission?.batteryPurchaseMethod}}</span>
              </div>
            </div>
            <div class="col-md-4 ">
              <div class="row">
                <label class="col-5">Battery Amount</label>
                <span class="col-7">{{commission?.batteryAmount | currency}}</span>
              </div>
            </div>
            <div class="col-md-4 ">
              <div class="row">
                <label class="col-5">Battery Sale Amount</label>
                <span class="col-7">{{commission?.batterySaleAmount | currency}}</span>
              </div>
            </div>
            <div class="col-md-4 ">
              <div class="row">
                <label class="col-5">Battery Type</label>
                <span class="col-7">{{commission?.batteryType}}</span>
              </div>
            </div>
            <div class="col-md-4 ">
              <div class="row">
                <label class="col-5">Battery Quantity</label>
                <span class="col-7">{{commission?.batteryQuantity}}</span>
              </div>
            </div>
            <div class="col-md-4 ">
              <div class="row">
                <label class="col-5">Battery Adder Amount</label>
                <span class="col-7">{{commission?.batteryAdderAmount | currency}}</span>
              </div>
            </div>
            <div class="col-md-4 ">
              <div class="row">
                <label class="col-5">Battery Finance Partner</label>
                <span class="col-7">{{commission?.batteryFinancePartner}}</span>
              </div>
            </div>
            <div class="col-md-4 ">
              <div class="row">
                <label class="col-5">Battery Contract Id</label>
                <span class="col-7">{{commission?.batteryContractID}}</span>
              </div>
            </div>
            <div class="col-md-4 ">
              <div class="row">
                <label class="col-5">Battery PTO Date</label>
                <span class="col-7">{{commission?.batteryPTODate ? (commission.batteryPTODate | date) : '-'}}</span>
              </div>
            </div>
            <div class="col-md-4 ">
              <div class="row">
                <label class="col-5">Battery Install Complete Date</label>
                <span class="col-7">{{commission?.batteryinstallcompletedate ? (commission.batteryinstallcompletedate | date) : '-'}}</span>
              </div>
            </div>
          </ng-container>
          </div>
  
        </div>
      </div>
      <div class="card">
        <div class="card-header-info">
          <p class="card-title">Commission Note</p>
        </div>
        <div class="card-body">
          {{commission.commissionNote}}
        </div>
      </div>
      <div class="card">
        <div class="card-header-info">
          <h5 class="card-title">Rule Execution : {{ commissionRuleName }}</h5>
  
        </div>
        <div class="card-body">
  
          <ng-container *ngIf="commissionRuleInfo;else other_content">
            <!-- <div class="row">
              <div class="col-md-12 text-right">
                <button class="btn btn-primary" [hidden]="!commission.commissionFinalized"
                  (click)="unfinalizeCommission(commission.commissionId)" *ngIf="checkCanUnfinalize()"><i
                    class="fas fa-times"></i> Unfinalize</button>
  
                <ng-container *ngIf="!override && checkCanOverride()">
                  <button class="btn btn-primary" (click)="onOverrideClick()"><i class="fas fa-check-double"></i>
                    Override</button>
                </ng-container>
  
  
                <button class="btn btn-primary" (click)="openChangesDialog()"><i class="fas fa-edit"></i> CHANGES</button>
              </div>
            </div> -->
            <div class="row">
              <div class="col-md-6">
                <div class="row">
                  <label class="col-5">Commission Amount</label>
                  <div class="col-7">
                    <ng-container>
                      <p>{{commission.commissionAmount | currency}}</p>
                    </ng-container>
                  </div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="row">
                  <label class="col-5">Calculated Commission Amount </label>
                  <ng-container>
                    <div class="col-7">{{calculatedCommissionAmount | currency}}</div>
                  </ng-container>
                </div>
              </div>
  
              <section class=" col-md-12">
                <mat-checkbox [(ngModel)]="commission.commissionFinalized" [disabled]="true">Commission Finalized
                </mat-checkbox> <br>
                <mat-checkbox [(ngModel)]="commission.commissionOverridden" [disabled]="true">Commission Overridden
                </mat-checkbox>
              </section>
  
            </div>
            <table class="my-table table-hover mat-table w-100">
              <thead>
                <tr class="mat-header-row">
                  <th class="mat-header-cell">Step</th>
                  <th class="mat-header-cell">Evaluation</th>
                  <th class="mat-header-cell">Criteria</th>
                  <th class="mat-header-cell">Action</th>
                  <th class="mat-header-cell">Action Evaluated</th>
                  <th class="mat-header-cell">Action Value</th>
                </tr>
              </thead>
              <tbody>
                <ng-container *ngIf="commissionRuleInfo">
                  <ng-container *ngFor="let ruleStep of commissionRuleInfo.steps; let i = index;">
                    <tr class="commission-details-table hover" (click)="setConditions(ruleStep)"
                      [ngStyle]="{'background-color':ruleStep.isTrue ? '#daf3c1' : '#fff' }">
                      <td class="mat-cell" data-td-head="Step">{{ ruleStep.stepName}}</td>
                      <td class="mat-cell" data-td-head="Evaluation">{{ ruleStep.isTrue }}</td>
                      <td class="mat-cell" data-td-head="Criteria">
                        <span *ngIf="ruleStep.criteria == '1'">All Conditions Should Meet</span>
                        <span *ngIf="ruleStep.criteria == '2'">One or More Conditions Should Meet</span>
                        <span
                          *ngIf="ruleStep.criteria != '1' && ruleStep.criteria != '2' && ruleStep.criteria">{{ruleStep.criteria}}</span>
                      </td>
                      <td class="mat-cell" data-td-head="Action">{{ ruleStep.actionString }}</td>
                      <td class="mat-cell" data-td-head="Action Evaluated">
                        {{ ruleStep.isTrue ? ruleStep.actionIdentifierWithValues : 'NA' }}</td>
                      <td class="mat-cell" data-td-head="Action Value">{{ ruleStep.isTrue?ruleStep.actionValue: 'NA' }}
                      </td>
                    </tr>
                  </ng-container>
                  <tr *ngFor="let ruleStep of stepConditions; let k = index">
  
                    <td colspan="7">
                      <div>
  
                        <table class="my-table mt-4 mb-3 mat-table w-100">
                          <thead>
                            <tr class="mat-header-row">
                              <th class="mat-header-cell">Condition ID</th>
                              <th class="mat-header-cell">Condition</th>
                              <th class="mat-header-cell">Condition Value</th>
                              <th class="mat-header-cell">Evaluation</th>
                            </tr>
                          </thead>
                          <tbody>
                            <ng-container *ngFor="let condition of ruleStep.conditions; let k = index">
                              <tr class="mat-row">
                                <td data-td-head="Condition ID" class="mat-cell">
                                  {{k+1}}.
                                </td>
                                <td data-td-head="Condition" class="mat-cell">
                                  {{ condition.leftString + condition.operator + condition.rightString }}</td>
                                <td data-td-head="Condition Value" class="mat-cell">
                                  {{ condition.leftValue + "  "+ condition.operator + "  " + condition.rightValue }}
                                </td>
                                <td data-td-head="Evaluation" class="mat-cell">{{ condition.isTrue }}</td>
                              </tr>
                            </ng-container>
                          </tbody>
                        </table>
  
                      </div>
                    </td>
                  </tr>
                </ng-container>
  
              </tbody>
            </table>
          </ng-container>
          <ng-template #other_content>
            <div class="row">
              <!-- <div class="col-md-12 text-right">
                <ng-container *ngIf="!override && checkCanOverride()">
                  <button class="btn btn-primary" (click)="onOverrideClick()"><i class="fas fa-check-double"></i>
                    Override</button>
                </ng-container>
  
                <button class="btn btn-primary" (click)="openChangesDialog()"><i class="fas fa-edit"></i> CHANGES</button>
              </div> -->
              <div class="col-md-6">
                <div class="row">
                  <label class="col-5">Commission Amount</label>
                </div>
              </div>
              <div class="col-md-6">
              </div>
                  <!-- <ng-container *ngIf="!override; else overrideButtonBlock"></ng-container>
                  <ng-template #overrideButtonBlock>
                    <div class="col-md-6">
                      <div class="row">
                        <label class="col-sm-5">Commission Overwrite Note <span style="color: red;">*</span></label>
                        <div class="col-sm-7">
                          <textarea rows="4" class="custom-input" [(ngModel)]="overwriteCommissionNote"></textarea>
                        </div>
      
                      </div>
                    </div>
                    <div class="col-md-12 text-right">
                      <button class="btn btn-primary float-right " (click)="onOverrideConfirm()"><i class="fas fa-check"></i>
                        Confirm</button>
                      <ng-container *ngIf="override">
                        <button class="btn btn-primary float-right" (click)="onOverrideCancel()"><i class="fas fa-times"></i>
                          Cancel</button>
                      </ng-container>
                    </div>
                  </ng-template> -->
              <h3 class="no-preview-available">
                No Preview Available
              </h3>
            </div>
          </ng-template>
  
  
        </div>
      </div>
      <div class="row">
        <div class="col-md-12">
          <app-commission-data class="w-100" [baseformulasList]="baseformulasList"
            [baseFormulaBreakdown]="baseFormulaBreakdown" [baseFormulaConditions]="baseFormulaConditions"
            [metadata]="metadata"></app-commission-data>
        </div>
  
        <div class="col-md-12">
          <app-commission-payments class="w-100" [commissionId]="commissionId" [evaluateCommission]="true"></app-commission-payments>
        </div>
      </div>