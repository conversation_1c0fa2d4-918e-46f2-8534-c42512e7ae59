// This file can be replaced during build by using the `fileReplacements` array.
// `ng build --prod` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
  production: true,
  ssoBaseUrl: 'https://onepay_dev.trinity-solar.com/api/api/',
  apiBaseUrl: 'https://onepay_dev.trinity-solar.com/api/api/',
  workflowBaseUrl: 'https://devopsiis.trinity-solar.com:5004/api/',
  secretKey: "TEST-M@%$#*!21@$&#%$*#61",
  applicationId:57,
  oneReportUrl:'https://devopsiis.trinity-solar.com:5020/oneReport',
 
  baseUrl:'https://onepay_dev.trinity-solar.com/api/',
  scopeUri: ['api://a82a939e-61d2-4675-82cf-122b5fd91169/onePAY'],
  authority:'https://login.microsoftonline.com/f1006ee5-f888-4308-92ea-fcaebe1c0b5e',
  tenantId: 'f1006ee5-f888-4308-92ea-fcaebe1c0b5e',
  uiClienId: 'a82a939e-61d2-4675-82cf-122b5fd91169',
  redirectUrl: 'http://localhost:4200'
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/dist/zone-error';  // Included with Angular CLI.
