<div class="dailog-title-bg">
    <div class="dailog-title">Add Payment Book Schedule<button class="dailog-close"
            [mat-dialog-close]><span>X</span></button></div>
</div>

<select class="custom-select hover w-25" [(ngModel)]="selectedRuleId" (change)="onSelect()">
    <option [value]="null">Payment Book</option>
    <option *ngFor="let rule of data.paymentBookRules[0].rules" [value]="rule.ruleId">{{rule.ruleName}}</option>
</select>

<!-- Rule Preview -->
<!-- Prompts -->
<div class="row mt-3" *ngIf="prompts">
    <div class="col-md-12">
        <!-- <ng-container *ngFor="let prompt of prompts"> -->
        <!-- <ng-container
                        *ngIf="prompt.ruleTypeName == 'Base Pay Structure'; else elseBlock">
                        <app-base-pay-structure-prompt [rule]="prompt"
                            (rulePrompt)="onRulePromptChange($event)">
                        </app-base-pay-structure-prompt>
                    </ng-container>
                    <ng-template #elseBlock> -->
        <ng-container
            *ngIf="data.paymentBookRules && data.paymentBookRules[0].ruleTypeName == 'Payment Book'"> 
            <app-payment-book-schedule-prompt [(rule)]="prompts" (rulePrompt)="onRulePromptChange($event)">
            </app-payment-book-schedule-prompt>
        </ng-container>
        <!-- </ng-template> -->
        <!-- </ng-container> -->
    </div>
</div>
<div class="text-right">
    <p style="color: red" *ngIf="paymentBookSchedulePrompt && !checkValidDates()">Dates must be in chronological order</p>
    <p style="color: red" *ngIf="!checkAllPromptsEntered()">All prompts must be filled</p>
</div>
<div class="text-right">
    <button class="btn btn-primary" [mat-dialog-close]="addOn" [disabled]="!checkAllPromptsEntered() || !checkValidDates()"><i class="fas fa-check"></i> OK</button>
</div>