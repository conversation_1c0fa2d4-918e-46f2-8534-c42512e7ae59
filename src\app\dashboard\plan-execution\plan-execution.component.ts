import { Component, OnInit, ViewChild } from '@angular/core';
import { PlanStatusOverviewComponent } from './plan-status-overview/plan-status-overview.component';
import { ApiService } from 'src/app/services/api.service';

@Component({
  selector: 'app-plan-execution',
  templateUrl: './plan-execution.component.html',
  styleUrls: ['./plan-execution.component.css']
})
export class PlanExecutionComponent implements OnInit {

  @ViewChild(PlanStatusOverviewComponent) private planstatusOverviewComponent: PlanStatusOverviewComponent;
  startButtonEnabled: boolean = true;
  jobsStatus;

  constructor(public apiService: ApiService) { }

  onJobStarted(submitted: boolean){
    this.planstatusOverviewComponent.getStatusOverview();
  }

  getJobs(jobsStatus) {
    this.jobsStatus = jobsStatus;
  }

  ngOnInit() {
  }

}
