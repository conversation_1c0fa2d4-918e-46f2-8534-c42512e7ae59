<div class="dailog-title-bg">
  <div class="dailog-title"><i class="fas fa-history"></i> History<button class="dailog-close"
      [mat-dialog-close]><span>X</span></button>
  </div>
</div>
<div class="row" *ngIf="productRateGroup">

  <div class="col-md-6">
    <div class="row">
      <label class="col-sm-5">Product Type</label>
      <span class="col-sm-7">{{productRateGroup[0].productType}}</span>
    </div>
  </div>

  <div class="col-md-6">
    <div class="row">
      <label class="col-sm-5">Sales Territory</label>
      <span class="col-sm-7">{{productRateGroup[0].salesTerritory}}</span>
    </div>
  </div>


  <table class="my-table mat-table w-100 mt-2">
    <thead>
      <tr class="mat-header-row">
        <th class="mat-header-cell" scope="col">Effective Start Date</th>
        <th class="mat-header-cell" scope="col">Effective End Date</th>
        <th class="mat-header-cell" scope="col">Minimum Commission</th>
        <th class="mat-header-cell" scope="col">Base Rate</th>
        <th class="mat-header-cell" scope="col">Floor Rate</th>

      </tr>
    </thead>
    <tbody>
      <tr class="mat-row" *ngFor="let tr of productRateGroup">
        <td data-td-head="Effective Start Date" class="mat-cell">{{tr.effectiveStartDate | date}}</td>
        <td data-td-head="Effective End Date" class="mat-cell">{{tr.effectiveEndDate | date}}</td>
        <td data-td-head="Minimum Commission" class="mat-cell">{{tr.minimumCommission | currency:'USD':true:'1.2-3'}}
        </td>
        <td data-td-head="Base Rate" class="mat-cell">{{tr.baseRate | currency:'USD':true:'1.2-3'}}</td>
        <td data-td-head="Floor Rate" class="mat-cell">{{tr.floorRate | currency:'USD':true:'1.2-3'}}</td>

      </tr>
    </tbody>
  </table>

</div>