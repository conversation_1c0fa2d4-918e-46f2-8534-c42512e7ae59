import { Component, OnInit, Input, Output, EventEmitter, SimpleChanges } from '@angular/core';
import { IRule } from 'src/app/model/rule.model';
import { IRulePrompt } from 'src/app/model/rule-prompt.model';
import { IBasePayStructureForm } from 'src/app/model/base-pay-structure.model';
import { IRuleItem } from 'src/app/model/rule-item.model';
import { ApiService } from 'src/app/services/api.service';
import { ToastrService } from 'ngx-toastr';
import { IRuleInput } from 'src/app/model/rule-input.model';
import { Prompt } from 'src/app/model/prompt.model';

@Component({
  selector: 'app-employee-incentive-prompt',
  templateUrl: './employee-incentive-prompt.component.html',
  styleUrls: ['./employee-incentive-prompt.component.css']
})
export class EmployeeIncentivePromptComponent implements OnInit {
  @Input() rule: IRule;
  @Output() rulePrompt: EventEmitter<IRulePrompt> = new EventEmitter<IRulePrompt>();
  startDate: Date;
  endDate: Date;
  basePayStructureForm: IBasePayStructureForm;
  ruleItems: IRuleItem[] = [];
  extraPrompts: Prompt[];

  constructor(private apiService: ApiService, private toastMsg: ToastrService) { }

  ngOnInit() {
    // this.extraPrompts = this.rule.prompts.map(prompt => { return <Prompt>{ ...prompt } }).sort((a, b) => {
    //   var textA = a.displayName.toUpperCase();
    //   var textB = b.displayName.toUpperCase();
    //   return (textA < textB) ? -1 : (textA > textB) ? 1 : 0;
    // });
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.rule) {
      this.extraPrompts = this.rule.prompts.map(prompt => { return <Prompt>{ ...prompt } }).sort((a, b) => {
        var textA = a.displayName.toUpperCase();
        var textB = b.displayName.toUpperCase();
        return (textA < textB) ? -1 : (textA > textB) ? 1 : 0;
      });

      this.extraPrompts = this.extraPrompts.map(prompt => {
        // if (prompt.value == null) {
        //   prompt.value = 0;
        // }
        return prompt;
      })

      if (changes.rule.currentValue.promptValues && changes.rule.currentValue.promptValues[changes.rule.currentValue.ruleId] && changes.rule.currentValue.promptValues[changes.rule.currentValue.ruleId].ruleItems) {
        this.ruleItems = changes.rule.currentValue.promptValues[changes.rule.currentValue.ruleId].ruleItems;
      }

      if (this.ruleItems && this.ruleItems.length > 0 && this.ruleItems.find(x => x.ruleItemInputs != null)) {
        if (this.ruleItems.filter(x => x.ruleItemInputs.filter(y => y.columnName == "Start_Date").length > 0)) {
          let ruleItem = this.ruleItems.filter(x => x.ruleItemInputs.filter(y => y.columnName == "Start_Date").length > 0)[0];
          
          this.startDate = ruleItem.ruleItemInputs.filter(y => y.columnName == "Start_Date")[0].columnValue;
        }

        if (this.ruleItems.filter(x => x.ruleItemInputs.filter(y => y.columnName == "End_Date").length > 0)) {
          let ruleItem = this.ruleItems.filter(x => x.ruleItemInputs.filter(y => y.columnName == "End_Date").length > 0)[0];
          
          this.endDate = ruleItem.ruleItemInputs.filter(y => y.columnName == "End_Date")[0].columnValue;
        }

        this.extraPrompts = this.extraPrompts.map(prompt => {
          if (this.ruleItems[0].ruleItemInputs.find(x => x.columnName == prompt.columnName) != null) {
            prompt.value = this.ruleItems[0].ruleItemInputs.find(x => x.columnName == prompt.columnName).columnValue;
          }
          return prompt;
        })
      }

      this.onChange();
    }
  }

  onChange() {
    this.ruleItems = [
      <IRuleItem>{
        itemId: null,
        ruleItemInputs: [
          <IRuleInput>{
            columnName: "Start_Date",
            columnValue: this.startDate
          },
          <IRuleInput>{
            columnName: "End_Date",
            columnValue: this.endDate
          }
        ]
      }
    ];

    if (this.extraPrompts && this.extraPrompts.length > 0) {
      this.extraPrompts.forEach(prompt => {
        this.ruleItems[0].ruleItemInputs.push(<IRuleInput>{
          columnName: prompt.columnName,
          columnValue: prompt.value
        })
      });
    }

    // console.log(`Commission Rule ${this.rule.ruleId} Rule Items`, this.ruleItems);

    this.rulePrompt.emit(<IRulePrompt>{
      commissionRuleId: this.rule.ruleId,
      ruleItems: this.ruleItems
    })
  }

}
