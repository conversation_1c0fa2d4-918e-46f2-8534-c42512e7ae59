import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { CommissionIdentifierChangeDialogComponent } from './commission-identifier-change-dialog.component';

describe('CommissionIdentifierChangeDialogComponent', () => {
  let component: CommissionIdentifierChangeDialogComponent;
  let fixture: ComponentFixture<CommissionIdentifierChangeDialogComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ CommissionIdentifierChangeDialogComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(CommissionIdentifierChangeDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
