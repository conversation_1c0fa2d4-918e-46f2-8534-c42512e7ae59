import { Component, OnInit, Inject } from '@angular/core';
import { MAT_LEGACY_DIALOG_DATA, MatLegacyDialogRef } from '@angular/material/legacy-dialog';

@Component({
  selector: 'app-confirmation-dialog',
  templateUrl: './confirmation-dialog.component.html',
  styleUrls: ['./confirmation-dialog.component.css']
})
export class ConfirmationDialogComponent implements OnInit {

  constructor(@Inject(MAT_LEGACY_DIALOG_DATA) public data: IConfirmationDialogData, private dialogRef: MatLegacyDialogRef<ConfirmationDialogComponent>) { }

  ngOnInit() {
  }

}

export interface IConfirmationDialogData {
  message: string;
}