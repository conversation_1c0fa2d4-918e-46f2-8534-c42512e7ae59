import { Component, OnInit, ViewChild, Input } from '@angular/core';
import { MatLegacyPaginator } from '@angular/material/legacy-paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { CommissionFilters } from '../service/filters.service';
import { ICommissionData } from 'src/app/model/commission-data.model';

@Component({
  selector: 'app-commission-data',
  templateUrl: './commission-data.component.html',
  styleUrls: ['./commission-data.component.css']
})
export class CommissionDataComponent implements OnInit {
  @ViewChild(MatSort) set content(sort: MatSort) {
    this.commissionDataElements.sort = sort;
  }  
  @ViewChild(MatLegacyPaginator) set content1(paginator: MatLegacyPaginator) {
    this.commissionDataElements.paginator = paginator;    
  }
  @Input() baseformulasList: any[];
  @Input() baseFormulaBreakdown: any[];
  @Input() baseFormulaConditions: any[];
  @Input() metadata: any[];
  setConditionName: string = '';
  showCommissionData: boolean = false;
  commissionDataElements: MatTableDataSource<ICommissionData> = new MatTableDataSource();  
  commissionDataColumns: string[] = ["baseFormulaName", "commissionAmount"];
  pageSizeOptions: number[] = [10, 25, 100];
  dataSource: ICommissionData[];
  isShowData: boolean = false;

  constructor(private commissionFilters: CommissionFilters) { }

  ngOnInit() {
  }

  showData() {
    if (this.showCommissionData == false) {
      this.showCommissionData = true;
      this.dataSource = this.baseformulasList.map(x => {
        return <ICommissionData>{
          baseFormulaName: x.baseFormulaName,
          commissionAmount: x.commissionAmount
        }
      });
      this.commissionDataElements = new MatTableDataSource(this.dataSource);
    }
    else {
      this.showCommissionData = false
    }
  }

  findBaseFormula(name: string) {
    this.baseFormulaBreakdown = [];
    this.baseFormulaConditions = [];
    for (let i: number = 0; i < this.baseformulasList.length; i++) {
      if (this.baseformulasList[i].baseFormulaName === name) {
        this.baseFormulaBreakdown.push(this.baseformulasList[i]);
      }
    }
    this.baseFormulaBreakdown = this.commissionFilters.filterBaseFormulaBreakdown(this.baseFormulaBreakdown, this.baseFormulaBreakdown);
    console.log(this.baseFormulaBreakdown);
  }

  setConditions(name: string) {
    if (this.setConditionName == name) {
      this.baseFormulaConditions = [];
      this.setConditionName = '';
    } else {
      this.baseFormulaConditions = [];
      this.setConditionName = name;
      for (let i: number = 0; i < this.baseFormulaBreakdown.length; i++) {
        let currentRow = this.baseFormulaBreakdown[i];
        for (let j: number = 0; j < currentRow.steps.length; j++) {
          let rowSteps = currentRow.steps[j];
          if (rowSteps.stepName === name) {
            for (let k: number = 0; k < rowSteps.conditions.length; k++) {
              this.baseFormulaConditions.push(rowSteps.conditions[k]);
            }
          }
        }
      }
    }
  }
}
