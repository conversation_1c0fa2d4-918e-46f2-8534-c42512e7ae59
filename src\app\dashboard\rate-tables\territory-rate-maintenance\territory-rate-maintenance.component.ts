import { Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from "@angular/forms";
import { ApiService } from "../../../services/api.service";
import { ToastrService } from 'ngx-toastr';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { MatLegacyPaginator } from '@angular/material/legacy-paginator';
import { TableFilterPipe } from '../../../pipe/table-filter.pipe';
import { DatePipe, PercentPipe, CurrencyPipe } from '@angular/common';
import { TerritoryRateMaintenanceDialogComponent } from '../territory-rate-maintenance-dialog/territory-rate-maintenance-dialog.component';
import { MatLegacyDialog } from '@angular/material/legacy-dialog';

@Component({
  selector: 'app-territory-rate-maintenance',
  templateUrl: './territory-rate-maintenance.component.html',
  styleUrls: ['./territory-rate-maintenance.component.css']
})
export class TerritoryRateMaintenanceComponent implements OnInit {
  allTerritoryRates: any;
  activeTerritoryRates: any;
  territoryRateGroup: any;
  dropdowns: any;
  territoryRateForm: UntypedFormGroup;
  addInd: boolean = false;
  salesTerritoryDefault: number = 1;
  utilityCompanyDefault: number = 1;
  financePartnerDefault: number = 1;
  purchaseMethodDefault: number = 1;
  p: number = 1;
  tableArr: Element[] = [];
  searchText: string = "";
  public date: Date;
  selfGenShareIndicatorVal: boolean = false;
  commissionOnFloorIndicatorVal: boolean = false;
  originalDataSource;
  territoryRate; 
  isterritoryRateSelected : boolean = false;
  dataSource;
  displayedColumns = [];
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatLegacyPaginator, { static: true }) paginator: MatLegacyPaginator;

  columnNames = [{
    id: "salesTerritory",
    value: "Sales Territory"

  }, {
    id: "utilityCompany",
    value: "Utility Company"
  },
  {
    id: "financePartner",
    value: "Finance Partner"
  },
  {
    id: "purchaseMethod",
    value: "Purchase Method"
  },
  {
    id: "effectiveStartDate",
    value: "Effective Start Date"
  },
  {
    id: "effectiveEndDate",
    value: "Effective End Date"
  },
  {
    id: "baseRate",
    value: "Base Rate"
  },
  {
    id: "basePercent",
    value: "Base %"
  },
  {
    id: "selfGenBonusPercent",
    value: "Self Gen Bonus %"
  },
  {
    id: "overagePercent",
    value: "Overage %"
  },
  {
    id: "referralPercent",
    value: "Referral %"
  },
  {
    id: "leadFee",
    value: "Lead Fee"
  },
  {
    id: "minimumCommission",
    value: "Traditional Minimum Commission"
  },
  {
    id: "directMinimumCommission",
    value: "Direct Minimum Commission"
  },
  {
    id: "selfGenOveragePercentage",
    value: "Self Gen Overage %"
  },
  {
    id: "floorRate",
    value: "Floor Rate"
  },
  {
    id: "selfGenShareIndicator",
    value: "Self Gen Share Indicator"
  },
  {
    id: "commissionOnFloorIndicator",
    value: "Use Floor for Base Rate Indicator"
  }];

  constructor(public apiService: ApiService, private toastMsg: ToastrService, private formBuilder: UntypedFormBuilder, private pipe: TableFilterPipe,
    private datePipe: DatePipe, private percentPipe: PercentPipe, private currencyPipe: CurrencyPipe, private dialog: MatLegacyDialog) {

  }

  ngOnInit() {
    if (!this.apiService.checkPermission('ViewRateTables')) {
      this.apiService.goBack();
      this.toastMsg.error("Insufficient Permissions. Please make sure your account can access this information.", "Permissions");
    }
    this.getDropdowns();

    this.territoryRateForm = this.formBuilder.group({
      salesTerritory: [this.salesTerritoryDefault, [Validators.required]],
      utilityCompany: [this.utilityCompanyDefault, [Validators.required]],
      financePartner: [this.financePartnerDefault, [Validators.required]],
      purchaseMethod: [this.purchaseMethodDefault, [Validators.required]],
      effectiveStartDate: ['', [Validators.required]],
      baseRate: [0, [Validators.required, Validators.max(20)]],
      basePerc: [0, [Validators.required, Validators.max(100)]],
      selfGenBonusPerc: [0, [Validators.required, Validators.max(100)]],
      overagePerc: [0, [Validators.required, Validators.max(100)]],
      referralPerc: [0, [Validators.required, Validators.max(100)]],
      leadFee: [0, [Validators.required]],
      minimumCommission: [0, [Validators.required]],
      directMinimumCommission: [0, [Validators.required]],
      selfGenOveragePercentage: [0, [Validators.max(100)]],
      floorRate: [0, [Validators.required]],
      selfGenShareIndicator: [0],
      commissionOnFloorIndicator: [0]
    });

    this.onChanges();
  }

  selfChecked(event: any) {
    this.selfGenShareIndicatorVal = event.srcElement.checked;
  }

  commChecked(event: any) {
    this.commissionOnFloorIndicatorVal = event.srcElement.checked;
  }
  onChanges() {
    this.territoryRateForm.valueChanges.subscribe(val => {
    });
  }

  clearDate(date: HTMLInputElement) {
    date.value = "";
    this.territoryRateForm.controls.effectiveStartDate.setValue('');
    this.date = null;
    event.stopPropagation();
  }

  onSubmit() {
    if (!this.territoryRateForm.invalid) {
      var values = {
        salesTerritoryId: this.territoryRateForm.controls.salesTerritory.value,
        utilityCompanyId: this.territoryRateForm.controls.utilityCompany.value,
        financePartnerId: this.territoryRateForm.controls.financePartner.value,
        purchaseMethodId: this.territoryRateForm.controls.purchaseMethod.value,
        effectiveStartDate: this.territoryRateForm.controls.effectiveStartDate.value,
        baseRate: this.territoryRateForm.controls.baseRate.value,
        basePercentage: this.territoryRateForm.controls.basePerc.value,
        selfGenBonusPercentage: this.territoryRateForm.controls.selfGenBonusPerc.value,
        overagePercentage: this.territoryRateForm.controls.overagePerc.value,
        referralPercentage: this.territoryRateForm.controls.referralPerc.value,
        leadFee: this.territoryRateForm.controls.leadFee.value,
        minimumCommission: this.territoryRateForm.controls.minimumCommission.value,
        directMinimumCommission: this.territoryRateForm.controls.directMinimumCommission.value,
        floorRate: this.territoryRateForm.controls.floorRate.value,
        selfGenOveragePercentage: this.territoryRateForm.controls.selfGenOveragePercentage.value,
        commissionOnFloorIndicator: this.commissionOnFloorIndicatorVal,
        selfGenShareIndicator: this.selfGenShareIndicatorVal,
      }
      var body = {
        newTerritoryRate: values
      }
    
      this.apiService.post('TerritoryRateMaintenance', body)
        .subscribe(data => {
          this.toastMsg.success('Territory Rate Successfully Added');
          this.getAllTerritoryRates();
          this.getActiveTerritoryRates();
          this.addInd = !this.addInd;
        }, (err: any) => {
          if (err?.err?.message)
            this.toastMsg.error(err.message, "Server Error!");
          if (typeof err === "string") {
            let isJsonObject = false;            
            let parsedErr: any;
            try {
              parsedErr = JSON.parse(err);
              isJsonObject =parsedErr && typeof parsedErr === "object" &&!Array.isArray(parsedErr);
            } catch (e) {
              isJsonObject = false;
            }
            if (isJsonObject) {
              this.toastMsg.error(parsedErr.result, "Error!");
            } else this.toastMsg.error(err, "Error!");
          }
          if (err?.result) {
            this.toastMsg.error(err.result, "Error!");
          }
        });
    }
  }

  getAllTerritoryRates() {
    this.apiService.get('TerritoryRateMaintenance/retrieveall')
      .subscribe(data => {
        this.allTerritoryRates = data;
        let allTerritoryRatesList;
        allTerritoryRatesList =data;
        if (this.territoryRateGroup) this.getTerritoryRateGroup(this.territoryRateGroup[0]);
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }

  getActiveTerritoryRates() {
    this.apiService.get('TerritoryRateMaintenance/retrieveactive')
      .subscribe(data => {
        this.activeTerritoryRates = data;
        this.displayedColumns = this.columnNames.map(x => x.id);
        this.createTable();

      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }

  getDropdowns() {
    this.apiService.get('TerritoryRateMaintenance/dropdowns')
      .subscribe(data => {
        this.dropdowns = data;
        this.getAllTerritoryRates();
        this.getActiveTerritoryRates();
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }

  getTerritoryRateGroup(territoryRate: any) {
    var territoryRates = this.allTerritoryRates.filter(x => x.salesTerritoryId === territoryRate.salesTerritoryId && x.utilityCompanyId === territoryRate.utilityCompanyId && x.financePartnerId === territoryRate.financePartnerId && x.purchaseMethod === territoryRate.purchaseMethod);

    this.territoryRateGroup = territoryRates;
  }

  get baseRate() { return this.territoryRateForm.get('baseRate'); }

  get floorRate() { return this.territoryRateForm.get('floorRate'); }

  get basePerc() { return this.territoryRateForm.get('basePerc'); }

  get selfGenBonusPerc() { return this.territoryRateForm.get('selfGenBonusPerc'); }

  get overagePerc() { return this.territoryRateForm.get('overagePerc'); }

  get referralPerc() { return this.territoryRateForm.get('referralPerc'); }

  get leadFee() { return this.territoryRateForm.get('leadFee'); }

  get selfGenOveragePercentage() { return this.territoryRateForm.get('selfGenOveragePercentage'); }

  rowClick(territoryRate: any) {
    let rates = territoryRate;
    var territoryRate = this.allTerritoryRates.filter(x => x.salesTerritoryId === territoryRate.salesTerritoryId && x.utilityCompanyId === territoryRate.utilityCompanyId && x.financePartnerId === territoryRate.financePartnerId && x.purchaseMethod === territoryRate.purchaseMethod);
    this.territoryRate = territoryRate;
    this.isterritoryRateSelected = true;
    let effectiveDate = this.transformDate(rates?.effectiveStartDate);
    this.territoryRateForm.controls.salesTerritory.setValue(this.territoryRate[0].salesTerritoryId);
    this.territoryRateForm.controls.utilityCompany.setValue(this.territoryRate[0].utilityCompanyId);
    this.territoryRateForm.controls.financePartner.setValue(this.territoryRate[0].financePartnerId);
    this.territoryRateForm.controls.effectiveStartDate.setValue(effectiveDate);
    this.territoryRateForm.controls.baseRate.setValue(this.territoryRate[0].baseRate);
    this.territoryRateForm.controls.basePerc.setValue(this.territoryRate[0].basePercentage);
    this.territoryRateForm.controls.selfGenBonusPerc.setValue(this.territoryRate[0].selfGenBonusPercentage);
    this.territoryRateForm.controls.overagePerc.setValue(this.territoryRate[0].overagePercentage);
    this.territoryRateForm.controls.referralPerc.setValue(this.territoryRate[0].referralPercentage);
    this.territoryRateForm.controls.leadFee.setValue(this.territoryRate[0].leadFee);
    this.territoryRateForm.controls.minimumCommission.setValue(this.territoryRate[0].minimumCommission);
    this.territoryRateForm.controls.directMinimumCommission.setValue(this.territoryRate[0].directMinimumCommission);
    this.territoryRateForm.controls.floorRate.setValue(this.territoryRate[0].floorRate);
    this.territoryRateForm.controls.selfGenOveragePercentage.setValue(this.territoryRate[0].selfGenOveragePercentage);
    this.selfGenShareIndicatorVal = rates.selfGenShareIndicator;
    this.commissionOnFloorIndicatorVal = rates.commissionOnFloorIndicator;
    const dialogRef = this.dialog.open(TerritoryRateMaintenanceDialogComponent, {
      width: '80%', data: { territoryRate }
    });
    dialogRef.afterClosed().subscribe(result => {
    });
  }
  transformDate(dateStr: string): string | null {
    return this.datePipe.transform(dateStr, 'yyyy-MM-dd');
  }

Add(){
  this.addInd = !this.addInd;
  this.territoryRate = this.tableArr;
  this.territoryRateForm.controls.salesTerritory.setValue(this.territoryRate[0].salesTerritoryId);
  this.territoryRateForm.controls.utilityCompany.setValue(this.territoryRate[0].utilityCompanyId);
  this.territoryRateForm.controls.financePartner.setValue(this.territoryRate[0].financePartnerId);
  this.territoryRateForm.controls.effectiveStartDate.setValue(this.territoryRate[0].effectiveStartDate);
  this.territoryRateForm.controls.baseRate.setValue(this.territoryRate[0].baseRate);
  this.territoryRateForm.controls.basePerc.setValue(this.territoryRate[0].basePercentage);
  this.territoryRateForm.controls.selfGenBonusPerc.setValue(this.territoryRate[0].selfGenBonusPercentage);
  this.territoryRateForm.controls.overagePerc.setValue(this.territoryRate[0].overagePercentage);
  this.territoryRateForm.controls.referralPerc.setValue(this.territoryRate[0].referralPercentage);
  this.territoryRateForm.controls.leadFee.setValue(this.territoryRate[0].leadFee);
  this.territoryRateForm.controls.minimumCommission.setValue(this.territoryRate[0].minimumCommission);
  this.territoryRateForm.controls.directMinimumCommission.setValue(this.territoryRate[0].directMinimumCommission);
  this.territoryRateForm.controls.floorRate.setValue(this.territoryRate[0].floorRate);
  this.territoryRateForm.controls.selfGenOveragePercentage.setValue(this.territoryRate[0].selfGenOveragePercentage);
}

  createTable() {
    let tableArr: Element[] = [];
    for (let i: number = 0; i <= this.activeTerritoryRates.length - 1; i++) {
      let currentRow = this.activeTerritoryRates[i];
      if(i==0)
      {
        this.tableArr[0] =this.activeTerritoryRates[0];
      }
      tableArr.push({
        salesTerritory: currentRow.salesTerritory, utilityCompany: currentRow.utilityCompany, financePartner: currentRow.financePartner, purchaseMethod: currentRow.purchaseMethod,
        effectiveStartDate: this.datePipe.transform(currentRow.effectiveStartDate), effectiveEndDate: this.datePipe.transform(currentRow.effectiveEndDate), baseRate: this.currencyPipe.transform(currentRow.baseRate), basePercent: this.percentPipe.transform(currentRow.basePercentage),
        selfGenBonusPercent: this.percentPipe.transform(currentRow.selfGenBonusPercentage), overagePercent: this.percentPipe.transform(currentRow.overagePercentage), referralPercent: this.percentPipe.transform(currentRow.referralPercentage), leadFee: this.currencyPipe.transform(currentRow.leadFee),
        minimumCommission: this.currencyPipe.transform(currentRow.minimumCommission),directMinimumCommission:this.currencyPipe.transform(currentRow.directMinimumCommission), financePartnerId: currentRow.financePartnerId, purchaseMethodId: currentRow.purchaseMethodId, salesTerritoryId: currentRow.salesTerritoryId,
        territoryRateId: currentRow.territoryRateId, utilityCompanyId: currentRow.utilityCompanyId, floorRate: this.currencyPipe.transform(currentRow.floorRate), selfGenShareIndicator: currentRow.selfGenShareIndicator, commissionOnFloorIndicator: currentRow.commissionOnFloorIndicator, selfGenOveragePercentage: this.percentPipe.transform(currentRow.selfGenOveragePercentage)
      });
    }
    this.dataSource = new MatTableDataSource(tableArr);
    this.originalDataSource = tableArr;
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }

  searchForItem(): void {
    let filteredResults: Element[] = [];
    if (this.searchText == '') {
      this.dataSource = new MatTableDataSource(this.originalDataSource);
      this.dataSource.sort = this.sort;
      this.dataSource.paginator = this.paginator;
    } else {
      filteredResults = this.pipe.transform(this.originalDataSource, this.searchText);
      this.dataSource = new MatTableDataSource(filteredResults);
      this.dataSource.sort = this.sort;
      this.dataSource.paginator = this.paginator;
    }
  }
}


export interface Element {
  salesTerritory: string,
  utilityCompany: string,
  financePartner: string,
  purchaseMethod: string;
  effectiveStartDate: string,
  effectiveEndDate: string,
  baseRate: string,
  basePercent: string,
  selfGenBonusPercent: string,
  overagePercent: string,
  referralPercent: string,
  leadFee: string,
  minimumCommission: string,
  directMinimumCommission:string,
  financePartnerId: number,
  purchaseMethodId: number,
  salesTerritoryId: number,
  territoryRateId: number,
  utilityCompanyId: number,
  floorRate: string,
  selfGenShareIndicator: boolean,
  commissionOnFloorIndicator: boolean,
  selfGenOveragePercentage: string
}

