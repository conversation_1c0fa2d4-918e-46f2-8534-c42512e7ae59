<div class="card-header-info p-4">
  <div class="row">
    <div class="col-md-6">
      <div class="row">


        <label class="col-sm-5">Name</label>
        <div class="col-sm-7">
          <input type="text" class="custom-input" [(ngModel)]="rulesForm.ruleName" [disabled]="true">
        </div>
      </div>
    </div>



    <div class="col-md-6" [hidden]="activeCreationType != 'Rule'">
      <div class="row">
        <label class="col-sm-5">Type</label>
        <div class="col-sm-7">
          <select (change)="onChangeRuleType($event)" [(ngModel)]="rulesForm.ruleTypeId" class="custom-select"
            id="mySelect" [disabled]="true">
            <option *ngFor="let type of ruleTypes" [value]="type.ruleTypeId">
              {{type.ruleCd}}
            </option>
          </select>
        </div>
      </div>
    </div>
    <div class="col-md-12" [hidden]="!employeeIncentive">
      <div class="row">

        <!-- Employee Incentive One Time Rule Check -->
        <div class="col-md-6 pl-3 pr-3">
          <div class="form-group pl-4 ">
            <input class="form-check-input" type="checkbox" id="no-reclaim" [checked]="rulesForm.noReclaim"
              [disabled]="true">
            <label class="form-check-label">No Reclaim</label>
          </div>
        </div>
        <!-- Employee Incentive Effective Start/End Date Check -->
        <div class="col-md-6  pl-3 pr-3">
          <div class="form-group  pl-4">
            <input class="form-check-input" type="checkbox" id="effective-start-end-date-ind"
              [checked]="effectiveStartEndDateInd" [disabled]="true">
            <label class="form-check-label ">Effective Start/End Date</label>
          </div>
        </div>

      </div>
    </div>
    <div class="col-md-6" *ngIf="showChildRuleTypes">
      <div class="row">

        <label class="col-sm-5 ">Subcategory</label>
        <div class="col-sm-7">
          <select name="child_rule_type_id" class="custom-select" id="child_rule_type_id" [(ngModel)]="incentiveTypeId"
            [disabled]="true">
            <option *ngFor="let type of childRuleTypes" [value]="type.ruleTypeId">
              {{type.ruleCd}}
            </option>
          </select>
        </div>
      </div>


    </div>

    <div class="col-md-6">
      <div class="row">
    
    
        <label class="col-sm-5">Commission Type</label>
        <div class="col-sm-7">
          <input type="text" class="custom-input" [(ngModel)]="commissionType" [disabled]="true">
        </div>
      </div>
    </div>

    <div class="col-md-6">
      <div class="row">
        <label class="col-sm-5">Payment Type</label>
        <div class="col-sm-7">
          <input type="text" class="custom-input" [(ngModel)]="paymentType" [disabled]="true">
        </div>
      </div>
    </div>

    <div class="col-md-6">
      <div class="row">
        <label class="col-sm-5">Commission Trigger Type</label>
        <div class="col-sm-7">
          <input type="text" class="custom-input" [(ngModel)]="commissionTriggerType" [disabled]="true">
        </div>
      </div>
    </div>

    <div class="col-md-6">

      <label class="">Description</label>
      <textarea class="custom-input" [(ngModel)]="rulesForm.description" [disabled]="true"> </textarea>

    </div>

    <div class="col-md-6" *ngIf="inclusionRuleSelected">
      <div class="row">
        <label class="col-sm-5">Inclusion Effective Start Date</label>
        <div class="col-sm-7">
          <input type="text" class="custom-input" [(ngModel)]="rulesForm.inclusionEffectiveStartDate" [disabled]="true">
        </div>
      </div>
    </div>
    <div class="col-md-6" *ngIf="inclusionRuleSelected">
      <div class="row">
        <label class="col-sm-5">Inclusion Effective End Date</label>
        <div class="col-sm-7">
          <div class="date-picker" style="width: 65%">
            <input #InclusionEndDatePicker type="date" class="custom-input" [ngModel]="rulesForm.inclusionEffectiveEndDate" (change)="inclusionEndDateSubmit($event)" [disabled]="rulesForm.inclusionEffectiveEndDate">
            <span *ngIf="InclusionEndDatePicker.value.length > 0 && setInclusionEffectiveEndDate" class="mat-icon cal-reset" (click)="clearEndDate(InclusionEndDatePicker)"><i class="far fa-calendar-times"></i></span>
            <span *ngIf="InclusionEndDatePicker.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-6">
      <div class="row">
        <label class="col-sm-5">Contact Plan Date Criteria</label>
        <div class="col-sm-7">
          <input type="text" class="custom-input" [(ngModel)]="contactPlanDateCriteria" [disabled]="true">
        </div>
      </div>
    </div>
    <div *ngIf="inclusionRuleSelected" style="flex:auto; margin-left: 81%;">
      <button class="btn btn-primary" type="submit" (click)="viewInclusionHistory()"><i class="fas fa-light fa-calendar"></i> Inclusion History</button>
    </div>
    <div *ngIf="inclusionRuleSelected && !rulesForm.inclusionEffectiveEndDate" class="align-button-right" style="flex:auto; margin-right: 5px;">
      <button class="btn btn-primary" type="submit" (click)="addInclusionEndDate()" [disabled]="!setInclusionEffectiveEndDate"><i class="fas fa-save"></i> Save</button>
    </div>
    <div class="col-md-6  pl-3 pr-3" *ngIf="contactPlanId && !inclusionRuleSelected">
      <div class="form-group  pl-4">
        <input class="form-check-input" type="checkbox" id="activeInd" [(ngModel)]="activeInd" [checked]="activeInd"
          [disabled]="exclusionWithInclusion">
        <label class="form-check-label ">Exclude {{subcategoryName}}?</label>
      </div>
    </div>
  </div>
  <div class="row justify-content-end" *ngIf="contactPlanId && !inclusionRuleSelected">
    <button class="btn btn-primary" [disabled]="activeInd == existingActiveInd" (click)="SaveActiveInd()"><i
        class="fas fa-save"></i> Save</button>
  </div>
</div>
<div class="w-100 steps-div mt-3 showBasePay">

  <div id="tbl" class="row mb-3" *ngFor="let step of stepsArray;let i=index">
    <div class="col-md-12">
      <div class="w-100 steps-title">
        <h3 class="step_index">Step <span>{{i+1}}</span></h3>
      </div>
    </div>
    <div class="col-md-12">
      <div class="step_tr_class p-3">
        <div class="row">
          <div class="col-md-12">
            <input type="text" class="custom-input head-input" [(ngModel)]=step.step_name [disabled]="true"> </div>
          <div class="col-md-12">
            <fieldset>

              <h4>Conditions</h4>
              <div id="inputbox_table_1" class="gray-bg mb-2  pb-1 lft-green-brdr"
                *ngFor="let item of step.conditions;let j=index">
                <div class="col-md-12  condition-container">
                  <div class="row">
                    <span class="condition-count">{{j+1}} </span>
                    <div class="col-sm-4">

                      <input type="text" class="custom-input" id="div1" [(ngModel)]=item.left_side [disabled]="true">
                    </div>
                    <div class="col-sm-4">

                      <select class="custom-select" id="step_1_operator_1" [(ngModel)]=item.operators
                        [disabled]="true">{{item.operators}}
                        <option value="<">Less than</option>
                        <option value=">">Greater than</option>
                        <option value="<=">Less than or equal</option>
                        <option value=">=">Greater than or equal</option>
                        <option value="=">Equal</option>
                      </select>
                    </div>
                    <div class="col-sm-4">

                      <input type="text" class="custom-input" [(ngModel)]=item.right_side [disabled]="true">
                    </div>
                  </div>
                </div>

              </div>
              <div class="row">
                <div class="col-md-7">
                  <div class="form-check form-check-radio">
                    <label class="form-check-label">
                      <input class="form-check-input" type="radio" value="1" [(ngModel)]=step.criteria
                        [disabled]="true">All
                      conditions should meet
                      <span class="circle">
                        <span class="check"></span>
                      </span>
                    </label>
                  </div>

                  <div class="form-check form-check-radio">
                    <label class="form-check-label">
                      <input class="form-check-input" type="radio" value="2" [(ngModel)]=step.criteria
                        [disabled]="true">One or
                      more conditions should meet

                      <span class="circle">
                        <span class="check"></span>
                      </span>
                    </label>
                  </div>

                  <div class="form-check form-check-radio">
                    <label class="form-check-label">
                      <input class="form-check-input" type="radio" value="3" ngModel={{getValueType(step.criteria)}}
                        [disabled]="true">Advanced
                      <span class="circle">
                        <span class="check"></span>
                      </span>
                    </label>
                  </div>

                  <div class="form-group">
                    <label>Advanced Condition </label>
                    <input type="text" class="custom-input" [ngModelOptions]="{standalone:true}"
                      [(ngModel)]=step.comment [disabled]="true">
                  </div>

                </div>
                <div class="col-md-5">
                  <div>
                    <label class="" for="action">Action</label>
                    <textarea rows="6" id="action" class="custom-input p-0" value="" [(ngModel)]=step.action
                      [disabled]="true"></textarea>
                  </div>
                  <!-- <input type="text" class="custom-control p-0" > -->
                  <div *ngIf="step.roundDepth != null">
                    <label class="bmd-label w-100" for="rounding">Rounding Depth</label>
                    <select class="custom-select w-25" id="rounding" [(ngModel)]="step.roundDepth" [disabled]="true">
                      <option *ngFor="let i of [0,1,2,3,4,5,6,7,8,9]" [value]="i">
                        {{i}}
                      </option>
                    </select>
                  </div>
                </div>

              </div>


            </fieldset>
          </div>


        </div>
      </div>
    </div>

  </div>


</div>

<!-- Contact Employee Incentive Values -->

<div class="col-md-12 mt-3" *ngIf="rulesForm.ruleTypeName == 'Employee Incentive'">
  <div class="row">
    <div class="col-md-12 text-right mb-3">
      <ng-container *ngIf="apiService.checkPermission('AssignPlan') && contactPlanId != null">
        <button class="btn btn-primary float-right" (click)="onEditChange()" *ngIf="!edit; else editElseBlock;"><i
            class="fas fa-edit"></i> EDIT</button>
      </ng-container>
      <ng-template #editElseBlock>
        <button class="btn btn-primary  float-right" (click)="onEditChange()"><i class="fas fa-times"></i>
          CANCEL</button>
        <button class="btn btn-primary  float-right" (click)="onSubmitChange()"><i class="fas fa-save"></i>
          SAVE</button>
      </ng-template>
    </div>
  </div>
  <div class="row">
    <div class="form-group col-md-6">
      <div class="row">
        <label class="col-sm-5" for="start-date">Start Date</label>
        <div class="date-output col-sm-7" *ngIf="!edit; else startDateElseBlock">
          {{startDate | date}}
        </div>

        <ng-template #startDateElseBlock>
          <div class="col-sm-7">
            <input type="date" id="start-date" class="custom-input" [ngModel]="startDate | date:'yyyy-MM-dd'"
              (ngModelChange)="startDate = $event">
          </div>
        </ng-template>

      </div>
    </div>
    <div class="col-md-6">
      <div class="row">
        <label class="col-sm-5" for="end-date">End Date</label>
        <div class="date-output col-sm-7" *ngIf="!edit; else endDateElseBlock">
          {{endDate | date}}
        </div>
        <ng-template #endDateElseBlock>
          <div class="col-sm-7">
            <input type="date" id="end-date" class="custom-input" [ngModel]="endDate | date:'yyyy-MM-dd'"
              (ngModelChange)="endDate = $event">
          </div>
        </ng-template>
      </div>
    </div>

    <div class=" col-md-6">
      <div class="row">
        <label class="col-sm-5" for="number-of-bonuses">Number Of Bonuses</label>
        <div class="date-output col-sm-7" *ngIf="!edit; else numberOfBonusesElseBlock">
          {{numberOfBonuses}}
        </div>
        <ng-template #numberOfBonusesElseBlock>
          <div class="col-sm-7">
            <input type="number" id="number-of-bonuses" class="custom-input" (change)="calcBonus()" [(ngModel)]="numberOfBonuses">
          </div>
        </ng-template>
      </div>
    </div>

     <div class=" col-md-6">
      <div class="row">
        <label class="col-sm-5" for="number-of-bonuses">Incentive Closed</label>
        <div class="date-output col-sm-7" *ngIf="!edit; else closedBlock">
          {{isClosed ? 'True' : 'False'}}
        </div>
        <ng-template #closedBlock>
          <div class="col-sm-7 closed-checkbox">
            <input class="form-check-input" type="checkbox" id="isClosed" [checked]="isClosed" [(ngModel)]="isClosed">
          </div>
        </ng-template>
      </div>
    </div> 

    <div class=" col-md-6">
      <div class="row">
        <label class="col-sm-5" for="remaining">Remaining No Of Bonuses</label>
        <div class="date-output col-sm-7">
          {{numberOfRemaining}}
        </div>
      </div>
    </div>
    <div class="col-md-6">
      <div class="row">
        <label class="col-sm-5" for="offer-amount">Offer Amount</label>
        <div class="date-output col-sm-7" *ngIf="!edit; else offerAmountElseBlock">
          {{offerAmount}}
        </div>
        <ng-template #offerAmountElseBlock>
          <div class="col-sm-7">
            <input type="number" id="offer-amount" class="custom-input" [(ngModel)]="offerAmount">
          </div>
        </ng-template>
      </div>
    </div>

<!-- Incentive Commissions -->
<div class="card" *ngIf="apiService.checkPermission('ViewSalesRepRecentCommissions')">
  <div class="card-header-info">
    <h4 class="card-title no-hover-effect">Recent Incentive Commissions</h4>
  </div>
  <div class="card-body">
    <div class="table-responsive-sm ">
      <table mat-table [dataSource]="recentCommissionsList" matSort class=" w-100 my-table">
        <ng-container matColumnDef="commissionId">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Commission ID </th>
          <td data-td-head="Commission ID" mat-cell *matCellDef="let element">
            <ng-container *ngIf="element.opportunityName; else noLink">
              <a (click)="setUrl()"
                [routerLink]="['/ui/commissions/commission', element.commissionId]">{{element.commissionId}}</a>
            </ng-container>
            <ng-template #noLink>
              <span matTooltip="Cannot view Commission due to Rule Type">{{element.commissionId}}</span>
            </ng-template>
          </td>
        </ng-container>

        <ng-container matColumnDef="opportunityName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Opportunity Name </th>
          <td data-td-head="Opportunity Name" mat-cell *matCellDef="let element">
            <ng-container *ngIf="checkCanViewOpp(); else noOppLink">
              <a (click)="setUrl()"
                [routerLink]="['/ui/commissions/opportunitydetails', element.opportunityId]">{{element.opportunityName}}</a>
            </ng-container>
            <ng-template #noOppLink>
              <span>{{element.opportunityName}}</span>
            </ng-template>
          </td>
        </ng-container>

        <ng-container matColumnDef="commissionAmount">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Total Amount </th>
          <td data-td-head="Total Amount" mat-cell *matCellDef="let element"> {{element.commissionAmount | currency}}
          </td>
        </ng-container>

        <ng-container matColumnDef="createdDate">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Created Date </th>
          <td data-td-head="Created Date" mat-cell *matCellDef="let element"> {{element.createdDate | date}} </td>
        </ng-container>

        <ng-container matColumnDef="opportunityFinalized">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Opportunity Finalized </th>
          <td data-td-head="Actual Install Start Date" mat-cell *matCellDef="let element">
            <span class="col-7">
              <mat-checkbox [ngModel]="element.opportunityFinalized" [disabled]="true"></mat-checkbox>
            </span>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="recentCommissionColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: recentCommissionColumns;"></tr>
      </table>
    
    </div>
  </div>
</div>


  </div>
