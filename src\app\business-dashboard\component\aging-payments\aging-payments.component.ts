import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { MatLegacyPaginator } from '@angular/material/legacy-paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { Chart } from 'angular-highcharts';
import { IPendingPayment, IdateRange } from '../../models/models';
import { DatePipe, CurrencyPipe } from '@angular/common';
import { ToastrService } from 'ngx-toastr';
import { ApiService } from 'src/app/services/api.service';
import { DatezonePipe } from 'src/app/pipe/datezone.pipe';
interface CustomPoint extends Highcharts.Point {
  custom: any;
}

@Component({
  selector: 'app-aging-payments',
  templateUrl: './aging-payments.component.html',
  styleUrls: ['./aging-payments.component.css']
})
export class AgingPaymentsComponent {

  @Input() dateRange: IdateRange | null = null;
  displayedColumns = [];
  agingPaymentData: any[] = [];
  agingPaymentChartData: any;
  originalDataSource: any;
  agingPaymentDataSource: any;
  @ViewChild('table', { read: MatSort, static: true }) sort: MatSort;
  @ViewChild('paginator', { static: true }) paginator: MatLegacyPaginator;
  agingPaymentChart: Chart;
  @Input() tabNumber: number | null = null;
  previousDateRange: IdateRange | null = null;


  columnNames = [
    {
      id: "contactLegalName",
      value: "Contact Legal Name"
    },
    {
      id: "opportunityName",
      value: "Opportunity Name"
    },   
    {
      id: "paymentType",
      value: "Payment Type"
    },
    {
      id: "paymentStatus",
      value: "Payment Status"
    },
    {
      id: "amount",
      value: "Amount"
    },
    {
      id: "salesDivision",
      value: "Sales Division"
    },
           {
      id: "userCreatedTimeStamp",
      value: "Payment Created",
      dataType:'Date'
    },
    {
      id: "agingGroup",
      value: "Aging Group"
    },
  ];

  constructor(public apiService: ApiService, private toastMsg: ToastrService, private datePipe: DatePipe,private currencyPipe: CurrencyPipe,private dateZonePipe: DatezonePipe) { }

  ngOnInit() {
    // this.getAgingPayments();
  }
  ngOnChanges(){
    if (this.tabNumber === 10) {
      if (this.dateRange) {
        if (this.previousDateRange === null || this.previousDateRange !== this.dateRange) {
          this.previousDateRange = this.dateRange;
          this.getAgingPayments();
        }       
      }
    }    
  }
  
  getAgingPayments(){
    this.apiService.get(`BusinessDashboard/AgePayment`)
      .subscribe((res: any) => {
        this.agingPaymentData = res.agePaymentList;
        this.agingPaymentChartData = res.ageGroupCounts;
        this.agingPaymentChartData.forEach(s=>{
          s.custom = this.currencyPipe.transform(s.custom);
        })
        this.displayedColumns = this.columnNames.map(x => x.id);
        this.createTable();        
        this.getAgingGroupChart();
      }, (err: any) => {
        this.toastMsg.error(err.message, "Server Error!");
      });
  }

  createTable() {
    let tableArr: any[] = [];
    for (let i: number = 0; i <= this.agingPaymentData.length - 1; i++) {
      let currentRow = this.agingPaymentData[i];
      tableArr.push({
        contactId: currentRow.contact_Id, amount: currentRow.amount, contactLegalName: currentRow.contact_Legal_Name,
        opportunityId: currentRow.opportunity_Id, opportunityName: currentRow.opportunity_Name, paymentStatus: currentRow.payment_Status_Name,
        salesDivision: currentRow.sales_Division,paymentType: currentRow.payment_Type_Name,
        commissionId:currentRow.commissionId,
        userCreatedTimeStamp: this.dateZonePipe.transform(currentRow.user_Created_Timestamp),agingGroup: currentRow.aging_Group
      });
    }
    this.agingPaymentDataSource = new MatTableDataSource(tableArr);
    this.originalDataSource = tableArr;
    this.agingPaymentDataSource.sort = this.sort;
    this.agingPaymentDataSource.paginator = this.paginator;
  }
  onSortChange(event:any){
    let dateFieldData = this.columnNames.filter(s=>s.dataType === 'Date');
    let dateField = dateFieldData && dateFieldData.length > 0 ? dateFieldData.filter(s=>s.id === event.active).map(c=> c.id).toString():'';
    if(dateField){
      this.agingPaymentDataSource.sortingDataAccessor = (item, property) => {
        switch (property) {
          case dateField:
            return new Date(item[dateField]).toISOString();
          default:
            return item[property];
        }
      };

      this.agingPaymentDataSource.sortingFn = (a: any, b: any, active: string, direction: string) => {
        if (active === dateField) {
          const dateA = new Date(a);
          const dateB = new Date(b);
          if (direction === 'asc') {
            return dateA.getTime() - dateB.getTime();
          } else {
            return dateB.getTime() - dateA.getTime();
          }
        } else {
          return this.agingPaymentDataSource.sortingDataAccessor(a, active) > this.agingPaymentDataSource.sortingDataAccessor(b, active) ? 1 : -1;
        }
      };
    }
    
    
  }
  getAgingGroupChart() {
    this.agingPaymentChart = this.setChartData('Aging Group', this.agingPaymentChartData);
  }

  setChartData(title: string, chartData: any) {
    let chart = new Chart({
      chart: {
        plotBackgroundColor: null,
        plotBorderWidth: null,
        plotShadow: false,
      },
      title: {
        text: title
      },
      tooltip: {        
        pointFormatter: function() {
          const point = this as CustomPoint;
          return `Total Amount: <b>${point.custom}</b> <br/> Total Count :${point.y.toLocaleString("en-US")} <b></b>`;
        },
      },
      accessibility: {
        point: {
          valueSuffix: '%',
        },
      },
      credits: {
        enabled: false
      },
      legend: {
        maxHeight: 90,  
      },
      plotOptions: {
        pie: {
          allowPointSelect: true,
          innerSize: '50%',
          cursor: 'pointer',
          dataLabels: {
            enabled: true,
            position: 'right',
            format: '<b>{point.name}</b>: {point.y:.0f} ',
          },
          showInLegend: true
        },
      },
      series: [
        {
          type: 'pie',
          name: 'Total Amount',
          showInLegend: true,
          data: chartData
        }
      ]
    });
    return chart;    
  }


}
