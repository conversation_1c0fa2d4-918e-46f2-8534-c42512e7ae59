<div class="content">
		<div class="container">
			<div class="row justify-content-center">
				<div class="col-md-5 offset-md-0 p-md-3">
						<div class="text-center">
								<a [routerLink] = "['/']" ><img src="/assets/images/Trinity.png" width="150px" alt=""></a>
							</div>
					<div class="card" style="box-shadow: 0 8px 8px 0 rgba(0, 0, 0, 0.2), 0 8px 20px 0 rgba(0, 0, 0, 0.19);">
						<div class="card-header">
								<div class="text-center">
										<h3 style="font-weight: 400;"> FORGOT PASSWORD </h3>
									</div>
						</div>
						
						<div class="card-body">
								<form [formGroup]="resetForm" (ngSubmit)="onSubmit()">
								<div class="row">
									<div class="col-md-12">
										<div class="form-group">
											<label class="bmd-label-floating">E-mail</label>
											<input type="email" formControlName="email" name="email" class="form-control" autocomplete="off" placeholder="E-mail">
											<div class="error" *ngIf="resetForm.controls['email'].hasError('required') && resetForm.controls['email'].touched">E-mail is required</div>
											<div class="error" *ngIf="resetForm.controls['email'].hasError('email') && resetForm.controls['email'].touched">Email must be a valid email address</div>
										</div>
									</div>
								</div>
								<button type="submit" [disabled]="resetForm.invalid" class="btn col-md-12">Submit</button>
								<div class="clearfix"></div>
								<a [routerLink] = "['/login']" class="col-md-12" style="margin-left: 170px;">Login</a>
							</form>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	