<!-- <div class=" w-100 increase-size">
  <div class="content">
   
      <h1>Trinity Solar Applications: </h1>
  

    <div class="container-fluid" *ngIf="appsBody">
      <div class="row" *ngIf="appsList">
        <div class="col-lg-3 col-md-6 col-sm-6" *ngFor="let app of appsList">
          <a href="javascript:void(0);" (click)="redirectDashboard()">
            <div class="card card-stats">
              <div class="card-header card-header-warning card-header-icon">
                <div class="card-icon">
                  <i class="material-icons">web</i>
                </div>
                <p class="card-title"><img src="/assets/images/{{app.imageUrl}}" width="100px" alt="">
                </p>
              </div>
              <div class="card-footer">
                <div class="stats">
                  <i class="material-icons text-danger">transit_enterexit</i>
                  <a href="javascript:void(0);" class="text-center card-font-weight text-warning">
                    {{apiService.capitalize(app.applicationName)}}</a>
                </div>
              </div>
            </div>
          </a>
        </div>
      </div>
    </div>
    <app-users-list *ngIf="userMngtBody"></app-users-list>
  </div>
</div> -->