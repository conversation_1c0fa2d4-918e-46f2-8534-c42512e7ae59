import { Component, OnInit, Input, SimpleChanges, Output, EventEmitter } from '@angular/core';
import { AddOnRule } from '../employee-incentive-dialog/employee-incentive-dialog.component';

@Component({
  selector: 'app-addons',
  templateUrl: './addons.component.html',
  styleUrls: ['./addons.component.css']
})
export class AddonsComponent implements OnInit {
  @Input() addOns: AddOnRule[] = [];
  @Input() basePayRules: any[];
  @Output() addOnsOutput: EventEmitter<AddOnRule[]> = new EventEmitter<AddOnRule[]>();
  employeeIncentives: AddOnRule[] = [];
  basePayStructures: AddOnRule[] = [];
  paymentBooks: AddOnRule[] = [];
  paymentBookSchedules: AddOnRule[] = [];
  basePayColumns: string[] = ["ruleName", "basePayRuleName", "promptValues", "delete"];
  standardColumns: string[] = ["ruleName", "promptValues", "delete"];
  
  constructor() { }

  ngOnInit() {
  }

  ngOnChanges(changes: SimpleChanges) {
    // console.log(changes);
    //alert(this.addOns);
    if (changes && changes.addOns) {
      this.filterAddOns(changes.addOns.currentValue);
    }
  }

  filterAddOns(addOns) {
    this.employeeIncentives = addOns.filter(x => x.ruleTypeName == "Employee Incentive");
    this.basePayStructures = addOns.filter(x => x.ruleTypeName == "Base Pay Structure");
    this.paymentBooks = addOns.filter(x => x.ruleTypeName == "Payment Book");
    this.paymentBookSchedules = addOns.filter(x => x.ruleTypeName == "Payment Book Schedule");
  }

  onEiChange(addons: AddOnRule[]) {
    var mergedAddons = [...this.paymentBooks, ...this.basePayStructures, ...this.paymentBookSchedules, ...addons];

    this.addOns = mergedAddons;
    this.filterAddOns(this.addOns);
    this.addOnsOutput.emit(this.addOns);
  }

  onBpsChange(addons: AddOnRule[]) {
    var mergedAddons = [...this.employeeIncentives, ...this.paymentBooks, ...this.paymentBookSchedules, ...addons];

    this.addOns = mergedAddons;
    this.filterAddOns(this.addOns);
    this.addOnsOutput.emit(this.addOns);
  }

  onPbChange(addons: AddOnRule[]) {
    var mergedAddons = [...this.employeeIncentives, ...this.basePayStructures, ...this.paymentBookSchedules, ...addons];

    this.addOns = mergedAddons;
    this.filterAddOns(this.addOns);
    this.addOnsOutput.emit(this.addOns);
  }

  onPbsChange(addons: AddOnRule[]) {
    var mergedAddons = [...this.employeeIncentives, ...this.basePayStructures, ...this.paymentBooks, ...addons];

    this.addOns = mergedAddons;
    this.filterAddOns(this.addOns);
    this.addOnsOutput.emit(this.addOns);
  }

  onAddonOutput(addons: AddOnRule[]) {
    console.log("Existing Addons", this.addOns);
    console.log("Modified Addons", addons);

    var addonType: string;
    if (addons[0] != null) {
      addonType = addons[0].ruleTypeName;
    } else {
      addonType = null;
    }
  
    console.log("Rule Type", addonType);
    var mergedAddons = this.addOns.filter(x => x.ruleTypeName != addonType);
    console.log("Other Rule Type Addons", mergedAddons);
    mergedAddons = mergedAddons.concat(addons);
    this.addOns = mergedAddons;
    this.filterAddOns(this.addOns);
    this.addOnsOutput.emit(this.addOns);
  }

}
