<div class="page-title col-md-12 "><h1 class="">Paycom Exports</h1>
	<div class="breadcrumbs"><a href="#">Home</a>/<span>Paycom Exports</span>
	</div>
	</div>

 
  <div class="content">
 
      <div class="card">
        <div class="card-header-info">
          <h4 class="card-title no-hover-effect">Paycom Exports</h4>
        </div>
        <div class="card-body">
        
            <table class="my-table w-100 mat-table">
            
                <thead>
                <tr class="mat-header-row">
                  
                  <th  class="mat-header-cell">Transaction Name</th>
                  <th  class="mat-header-cell">Commission Transaction ID</th>
                  <th   class="mat-header-cell">Number of Withdrawals</th>
                  <th  class="mat-header-cell">Total Amount Withdrawn</th>
                  <th  class="mat-header-cell">Date Processed</th>
                  <th  class="mat-header-cell">Modified user</th>
                  <th  class="mat-header-cell">Download</th>
                </tr>
                </thead>
                <tbody>
                <tr class="mat-row " *ngFor="let hist of paycomHistory | paginate: { id: 'report', itemsPerPage: pageSize, currentPage: reportPage }">
                  <!-- <td><a class="text-info hover" (click)="getPaycomExport(hist.commissionTransactionId)" ><i class="material-icons blue-icon">system_update_alt</i></a></td> -->
                  <!-- Dilip 05/22/2020 -->
                  <td class="mat-cell" data-td-head="Transaction Name">{{hist.transactionName}}</td>
                  <td class="mat-cell" data-td-head="Commission Transaction ID" >{{hist.commissionTransactionId}}</td>
                  <td class="mat-cell" data-td-head="Number of Withdrawals">{{hist.numberOfWithdrawals}}</td>
                  <td  class="mat-cell" data-td-head="Total Amount Withdrawn">{{hist.totalAmountWithdrawn | currency}}</td>
                  <td class="mat-cell" data-td-head="Date Processed">{{hist.dateProcessed | date}}</td>
                  <td class="mat-cell" data-td-head="Modified user">{{hist.userModifiedId}}</td>
                  <td class="mat-cell" data-td-head="Download" align="center"><a class="" (click)="getPaycomExport(hist.commissionTransactionId)" *ngIf="apiService.checkPermission('ReportExport')" ><i class="fas fa-download"></i></a></td>  

                </tr>
              </tbody>
            </table>
           <hr>
              <div class="float-right mt-3">
                <pagination-controls class="float-right" id="report" (pageChange)="reportPage = $event"></pagination-controls>
                <div class="pagination-select float-right">
                 <label>Items per page:</label>
                  <select class="custom-select" [(ngModel)]="pageSize" name="pageSize">
                    <option value="5">5 items</option>
                    <option value="10">10 items</option>
                    <!-- <mat-option value="20">20 items</mat-option>
                    <mat-option value="50">50 items</mat-option> -->
                  </select>
                </div>
              </div>
             
            
          
        </div>
      </div>
    </div>