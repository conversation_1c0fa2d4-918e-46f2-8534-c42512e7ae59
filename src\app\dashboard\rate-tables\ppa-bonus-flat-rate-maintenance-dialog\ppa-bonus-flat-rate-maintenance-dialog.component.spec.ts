import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { PPABonusFlatRateMaintenanceDialogComponent } from './ppa-bonus-flat-rate-maintenance-dialog.component';

describe('PPABonusFlatRateMaintenanceDialogComponent', () => {
  let component: PPABonusFlatRateMaintenanceDialogComponent;
  let fixture: ComponentFixture<PPABonusFlatRateMaintenanceDialogComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ PPABonusFlatRateMaintenanceDialogComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PPABonusFlatRateMaintenanceDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
