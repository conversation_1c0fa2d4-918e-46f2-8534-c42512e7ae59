import { CurrencyPipe, DatePipe } from '@angular/common';
import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { ApiService } from 'src/app/services/api.service';
import { MatTableDataSource } from '@angular/material/table';
import { MatLegacyPaginator } from '@angular/material/legacy-paginator';
import { MatSort } from '@angular/material/sort';
import { Chart } from 'angular-highcharts';
import { IPendingPayment, IdateRange } from '../../models/models';
import { DatezonePipe } from 'src/app/pipe/datezone.pipe';

interface CustomPoint extends Highcharts.Point {
  custom: any;
}

@Component({
  selector: 'app-pending-payments',
  templateUrl: './pending-payments.component.html',
  styleUrls: ['./pending-payments.component.css']
})
export class PendingPaymentsComponent implements OnInit {
  displayedColumns = [];
  paymentData: any;
  paymentChart: any;
  salesChart: any;
  originalDataSource: any;
  paymentDataSource: any;
  @ViewChild('table', { read: MatSort, static: true }) sort: MatSort;
  @ViewChild('paginator', { static: true }) paginator: MatLegacyPaginator;
  paymentTypeChart: Chart;
  salesDivisionChart: Chart;
  @Input() dateRange: IdateRange | null = null;
  @Input() tabNumber: number | null = null;
  previousDateRange: IdateRange | null = null;

  columnNames = [
    {
      id: "contactLegalName",
      value: "Contact Legal Name"
    },
    {
      id: "opportunityName",
      value: "Opportunity Name"
    },
    {
      id: "amount",
      value: "Amount"
    },
    {
      id: "paymentStatus",
      value: "Payment Status"
    },
    {
      id: "paymentType",
      value: "Payment Type"
    },
    {
      id: "salesDivision",
      value: "Sales Division"
    },
    {
      id: "modifiedDate",
      value: "Modified Date",
      dataType:'Date'
    },
  ];
  constructor(public apiService: ApiService, private toastMsg: ToastrService, private datePipe: DatePipe,private currencyPipe: CurrencyPipe,private dateZonePipe: DatezonePipe) { }
  ngOnInit() {
  }
  ngOnChanges(){
    if (this.tabNumber === 3) {
      if (this.dateRange) {
        if (this.previousDateRange === null || this.previousDateRange !== this.dateRange) {
          this.previousDateRange = this.dateRange;
          this.getPendingPayments()
        }       
      }
    }
  }
  getPendingPayments() {
    this.apiService.get(`BusinessDashboard/GetPendingPaymentsApproval?toDate=${this.dateRange.endDate}&fromDate=${this.dateRange.startDate}`)
      .subscribe((res: any) => {
        this.paymentData = res.paymentData;
        this.paymentChart = res.paymentChart;
        this.paymentChart.forEach(s=>{
          s.custom = this.currencyPipe.transform(s.custom);
        })
        this.salesChart = res.salesDivisionChart;
        this.salesChart.forEach(s=>{
          s.custom = this.currencyPipe.transform(s.custom);
        })
        this.displayedColumns = this.columnNames.map(x => x.id);
        this.createTable();
        this.getPaymentChart();
        this.getSalesDivisionChart();
        
      }, (err: any) => {
        this.toastMsg.error(err.message, "Server Error!");
      });
  }
  createTable() {
    let tableArr: IPendingPayment[] = [];
    for (let i: number = 0; i <= this.paymentData.length - 1; i++) {
      let currentRow = this.paymentData[i];
      tableArr.push({
        contactId: currentRow.contactId, amount: currentRow.amount, contactLegalName: currentRow.contactLegalName,
        opportunityId: currentRow.opportunityId, opportunityName: currentRow.opportunityName, paymentStatus: currentRow.paymentStatusName,
        paymentType: currentRow.paymentTypeName, salesDivision: currentRow.salesDivision, salesTerritoryStateCode: currentRow.salesTerritoryStateCode,
        commissionId:currentRow.commissionId,
        modifiedDate: this.dateZonePipe.transform(currentRow.userModifiedTimestamp)
      });
    }
    this.paymentDataSource = new MatTableDataSource(tableArr);
    this.originalDataSource = tableArr;
    this.paymentDataSource.sort = this.sort;
    this.paymentDataSource.paginator = this.paginator;
  }
  onSortChange(event:any){
    let dateFieldData = this.columnNames.filter(s=>s.dataType === 'Date');
    let dateField = dateFieldData && dateFieldData.length > 0 ? dateFieldData.filter(s=>s.id === event.active).map(c=> c.id).toString():'';
    if(dateField){
      this.paymentDataSource.sortingDataAccessor = (item, property) => {
        switch (property) {
          case dateField:
            return new Date(item[dateField]).toISOString();
          
          default:
            return item[property];
        }
      };

      this.paymentDataSource.sortingFn = (a: any, b: any, active: string, direction: string) => {
        if (active === dateField) {
          const dateA = new Date(a);
          const dateB = new Date(b);
          if (direction === 'asc') {
            return dateA.getTime() - dateB.getTime();
          } else {
            return dateB.getTime() - dateA.getTime();
          }
        } 
        else {
          return this.paymentDataSource.sortingDataAccessor(a, active) > this.paymentDataSource.sortingDataAccessor(b, active) ? 1 : -1;
        }
      };
    }
  }
  getPaymentChart() {
    this.paymentTypeChart = this.setChartData('Payment Type',this.paymentChart);
  }
  getSalesDivisionChart() {
    this.salesDivisionChart = this.setChartData('Payments by Sales Division', this.salesChart);
  }
  setChartData(title: string, chartData: any) {
    let chart = new Chart({
      chart: {
        plotBackgroundColor: null,
        plotBorderWidth: null,
        plotShadow: false,
      },
      title: {
        text: title
      },
      tooltip: {
        // pointFormat: '{series.name}: {point.custom:.1f} <br> Total Count :{point.y:.0f}',
        pointFormatter: function() {
          const point = this as CustomPoint;
          return `Total Amount: <b>${point.custom.toLocaleString("en-US")}</b> <br/> Total Count : ${point.y.toLocaleString("en-US")}<b></b>`;
        },
      },
      accessibility: {
        point: {
          valueSuffix: '%',
        },
      },
      credits: {
        enabled: false
      },
      legend: {
        maxHeight: 90,  
      },
      plotOptions: {
        pie: {
          allowPointSelect: true,
          shadow: false,
          innerSize: '50%',
          cursor: 'pointer',
          dataLabels: {
              enabled: true,
          },
          showInLegend: true
      }
      },
      series: [
        {
          type: 'pie',
          name: 'Total Amount',
          showInLegend: true,
          data: chartData
        }
      ]
    });
    return chart;
  }

}


