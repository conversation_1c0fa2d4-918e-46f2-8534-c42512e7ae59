import { Component, OnInit, ViewChild } from '@angular/core';
import { UntypedForm<PERSON>uilder, UntypedFormGroup, Validators, UntypedFormControl, AbstractControl } from "@angular/forms";
import { ApiService } from "../../../services/api.service";
import { ToastrService } from 'ngx-toastr';
import { maxPermitDeductionDate, rateNotExisting } from '../../../shared/validators';
import { groupBy } from '../../../shared/group-by';
import { getControlName } from '../../../shared/get-control-name';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { DataSource } from '@angular/cdk/table';
import { MatLegacyPaginator } from '@angular/material/legacy-paginator';
import { TableFilterPipe } from '../../../pipe/table-filter.pipe';
import { DatePipe, PercentPipe, CurrencyPipe } from '@angular/common';
// import { StringMap } from '@angular/compiler/src/compiler_facade_interface';
import { MatLegacyDialog } from '@angular/material/legacy-dialog';
import { PpwBonusRateMaintenanceDialogComponent } from '../ppw-bonus-rate-maintenance-dialog/ppw-bonus-rate-maintenance-dialog.component';

@Component({
  selector: 'app-ppw-bonus-rate-maintenance',
  templateUrl: './ppw-bonus-rate-maintenance.component.html',
  styleUrls: ['./ppw-bonus-rate-maintenance.component.css']
})
export class PpwBonusRateMaintenanceComponent implements OnInit {
  allPpwBonusRates: any;
  activePpwBonusRates: any;
  ppwBonusRateGroup: any;
  ppwBonusRateSelectedGroup: any;
  dropdowns: any;
  ppwBonusRateForm: UntypedFormGroup;
  ppwPpkwGroup: AbstractControl[][];
  addInd: boolean = false;
  isReloading :boolean = false ;
  stateCodeDefault: number = 1;
  financePartnerDefault: number = 1;
  purchaseMethodDefault: number = 1;
  utilityCompanyDefault: number = 1;
  p: number = 1;
  tableArr: Element[] = [];
  PPWBonusRateRate1;
  isPPWBonusRateRateSelected: boolean = false;
  searchText: string = "";
  public date: Date;
  originalDataSource;
  dataSource;
  displayedColumns = [];
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatLegacyPaginator, { static: true }) paginator: MatLegacyPaginator;

  columnNames = [{
    id: "stateCode",
    value: "State Code"

  }, {
    id: "utilityCompany",
    value: "Utility Company"
  },
  {
    id: "financePartner",
    value: "Finance Partner"
  },
  {
    id: "purchaseMethod",
    value: "Purchase Method"
  },
  {
    id: "effectiveStartDate",
    value: "Effective Start Date"
  },
  {
    id: "effectiveEndDate",
    value: "Effective End Date"
  }];

  constructor(public apiService: ApiService, private toastMsg: ToastrService, private formBuilder: UntypedFormBuilder, private pipe: TableFilterPipe,
    private datePipe: DatePipe, private percentPipe: PercentPipe, private currencyPipe: CurrencyPipe, private dialog: MatLegacyDialog) {

  }

  ngOnInit() {
    if (!this.apiService.checkPermission('ViewRateTables')) {
      // this.router.navigate(['/ui/dashboard'])
      this.apiService.goBack();
      this.toastMsg.error("Insufficient Permissions. Please make sure your account can access this information.", "Permissions");
    }
    this.ppwBonusRateForm = this.formBuilder.group({
      stateCode: [this.stateCodeDefault, [Validators.required]],
      financePartner: [this.financePartnerDefault, [Validators.required]],
      purchaseMethod: [this.purchaseMethodDefault, [Validators.required]],
      utilityCompany: [this.utilityCompanyDefault, [Validators.required]],
      effectiveStartDate: ['', [Validators.required]],
      ppwRate: [0, []],
      pricePerKw: [0, [Validators.required]],
    });

    this.getDropdowns();

    this.ppwPpkwGroup = [[this.ppwBonusRateForm.controls.ppwRate, this.ppwBonusRateForm.controls.pricePerKw]];

    this.onChanges();
  }

  onChanges() {
    this.ppwBonusRateForm.valueChanges.subscribe(val => {
      // console.log(this.ppwBonusRateForm.errors);
    });
  }

  clearDate(date: HTMLInputElement) {
    date.value = "";
    this.date = null;
    this.ppwBonusRateForm.controls.effectiveStartDate.setValue('');
    event.stopPropagation();
  }

  onSubmit() {
    if (!this.ppwBonusRateForm.invalid) {
      var groupArr = [];
      this.ppwPpkwGroup.forEach(x => {
        groupArr.push(
          {
            "PpwRate": x[0].value,
            "PricePerKw": x[1].value
          }
        );
      });

      // console.log(groupArr);

      var body = {
        isPpw: true,
        stateCodeId: this.ppwBonusRateForm.controls.stateCode.value,
        financePartnerId: this.ppwBonusRateForm.controls.financePartner.value,
        purchaseMethodId: this.ppwBonusRateForm.controls.purchaseMethod.value,
        utilityCompanyId: this.ppwBonusRateForm.controls.utilityCompany.value,
        effectiveStartDate: this.ppwBonusRateForm.controls.effectiveStartDate.value,
        ppaPpwGroups: groupArr
      }
      // console.log("Body  136 => " + JSON.stringify(body));

      this.apiService.post('PpaBonusRateMaintenance', body)
        .subscribe(data => {
          this.toastMsg.success('PPW bonus has been updated successfully.');
          this.isReloading = true ;
          this.getAllPpwBonusRates();
          this.getActivePpwBonusRates();
          this.addInd = !this.addInd;
        }, (err: any) => {
          if (err?.err?.message)
            this.toastMsg.error(err.message, "Server Error!");
          if (typeof err === "string") {
            let isJsonObject = false;            
            let parsedErr: any;
            try {
              parsedErr = JSON.parse(err);
              isJsonObject =parsedErr && typeof parsedErr === "object" &&!Array.isArray(parsedErr);
            } catch (e) {
              isJsonObject = false;
            }
            if (isJsonObject) {
              this.toastMsg.error(parsedErr.result, "Error!");
            } else this.toastMsg.error(err, "Error!");
          }
          if (err?.result) {
            this.toastMsg.error(err.result, "Error!");
          }
        });
    }
  }

  getAllPpwBonusRates() {
    this.apiService.get('PpaBonusRateMaintenance/retrieveallppw')
      .subscribe(data => {
        this.allPpwBonusRates = data;
        if (!this.isReloading) {
          if (this.ppwBonusRateForm.controls.ppwRate) this.ppwBonusRateForm.controls.ppwRate.setValidators([Validators.required, rateNotExisting(this.allPpwBonusRates)]);
        } else {
          this.ppwBonusRateForm.clearValidators();
        }
        if (this.ppwBonusRateGroup) {
          this.getPpwBonusRateGroup(this.ppwBonusRateGroup[0][0]);
          this.ppwBonusRateSelectedGroup = this.ppwBonusRateGroup;
        }
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }

  getActivePpwBonusRates() {
    this.apiService.get('PpaBonusRateMaintenance/retrieveactiveppw')
      .subscribe(data => {
        // console.log('ppw', data);
        this.activePpwBonusRates = data;
        this.displayedColumns = this.columnNames.map(x => x.id);
        this.createTable();

      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }

  getDropdowns() {
    this.apiService.get('PpaBonusRateMaintenance/dropdowns')
      .subscribe(data => {
        this.dropdowns = data;
        this.getAllPpwBonusRates();
        this.getActivePpwBonusRates();
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }

  getPpwBonusRateGroup(ppwBonusRate: any) {
    var ppwBonusRates = this.allPpwBonusRates.filter(x => x.financePartnerId === ppwBonusRate.financePartnerId && x.purchaseMethodId === ppwBonusRate.purchaseMethodId && x.stateCodeId === ppwBonusRate.stateCodeId && x.utilityCompanyId === ppwBonusRate.utilityCompanyId);
    ppwBonusRates = Object.values(groupBy(ppwBonusRates, 'effectiveStartDate'));
    this.ppwBonusRateGroup = ppwBonusRates;
    this.ppwBonusRateSelectedGroup = null;
  }

  get ppwRate() { return this.ppwBonusRateForm.get('ppwRate'); }

  get pricePerKw() { return this.ppwBonusRateForm.get('pricePerKw'); }

  rowClick(ppwBonusRate: any) {
    var ppwBonusRate = this.allPpwBonusRates.filter(x => x.financePartnerId === ppwBonusRate.financePartnerId && x.purchaseMethodId === ppwBonusRate.purchaseMethodId && x.stateCodeId === ppwBonusRate.stateCodeId && x.utilityCompanyId === ppwBonusRate.utilityCompanyId);
    this.PPWBonusRateRate1 = ppwBonusRate;
    this.isPPWBonusRateRateSelected = true;
    ppwBonusRate = Object.values(groupBy(ppwBonusRate, 'effectiveStartDate'));
    const dialogRef = this.dialog.open(PpwBonusRateMaintenanceDialogComponent, {
      width: '80%', data: { ppwBonusRate }
    });
    // console.log("PPWBonusRateRate1 = >" + JSON.stringify(this.PPWBonusRateRate1));
    // console.log("this.PPWBonusRateRate1  = >" + JSON.stringify(this.PPWBonusRateRate1));
   
    this.ppwBonusRateForm.controls['financePartner'].setValue(this.PPWBonusRateRate1[0].financePartnerId);
    this.ppwBonusRateForm.controls['purchaseMethod'].setValue(this.PPWBonusRateRate1[0].purchaseMethodId);
    this.ppwBonusRateForm.controls['utilityCompany'].setValue(this.PPWBonusRateRate1[0].utilityCompanyId);

    dialogRef.afterClosed().subscribe(result => {
      // console.log(result);
    });
  }


  Add() {
    this.addInd = !this.addInd;
    this.PPWBonusRateRate1 = this.tableArr;
    this.isReloading = true;
    if (!this.isReloading) {
      if (this.ppwBonusRateForm.controls.ppwRate) this.ppwBonusRateForm.controls.ppwRate.setValidators([Validators.required, rateNotExisting(this.allPpwBonusRates)]);
    } else {
      this.ppwBonusRateForm.clearValidators();
    }
    // console.log("Add this.PPWBonusRateRate1 => 292   " + JSON.stringify(this.PPWBonusRateRate1));
    this.ppwBonusRateForm.controls['financePartner'].setValue(this.PPWBonusRateRate1[0].financePartnerId);
    this.ppwBonusRateForm.controls['purchaseMethod'].setValue(this.PPWBonusRateRate1[0].purchaseMethodId);
    this.ppwBonusRateForm.controls['utilityCompany'].setValue(this.PPWBonusRateRate1[0].utilityCompanyId);
  }

  groupClick(group: any) {
    this.ppwBonusRateSelectedGroup = group;
  }

  addFormRow() {
    this.ppwBonusRateForm.addControl(`ppwRate${this.ppwPpkwGroup.length}`, new UntypedFormControl(0, []));
    this.ppwBonusRateForm.addControl(`pricePerKw${this.ppwPpkwGroup.length}`, new UntypedFormControl(0, []));
    var c1 = this.ppwBonusRateForm.get(`ppwRate${this.ppwPpkwGroup.length}`);
    var c2 = this.ppwBonusRateForm.get(`pricePerKw${this.ppwPpkwGroup.length}`);
    c1.setValidators([Validators.required, rateNotExisting(this.allPpwBonusRates)]);
    c2.setValidators([Validators.required]);
    this.ppwPpkwGroup.push([c1, c2]);
  }

  removeFormRow(index: number) {
    if (this.ppwPpkwGroup.length == 1) return;

    this.ppwPpkwGroup[index].slice(0).forEach(x => {
      this.ppwBonusRateForm.removeControl(getControlName(x));
    });

    this.ppwPpkwGroup.splice(index, 1);
  }

  getControlName(control: AbstractControl) {
    return getControlName(control);
  }

  createTable() {
    let tableArr: Element[] = [];
    for (let i: number = 0; i <= this.activePpwBonusRates.length - 1; i++) {
      let currentRow = this.activePpwBonusRates[i];
      if(i==0)
      {
        this.tableArr[0] = this.activePpwBonusRates[0];
        // console.log(" this.tableArr = 301 > " + JSON.stringify(this.tableArr));
      }
      tableArr.push({
        effectiveEndDate: this.datePipe.transform(currentRow.effectiveEndDate), effectiveStartDate: this.datePipe.transform(currentRow.effectiveStartDate),
        financePartner: currentRow.financePartner, financePartnerId: currentRow.financePartnerId, ppwRate: this.currencyPipe.transform(currentRow.ppwRate),
        pricePerKw: this.currencyPipe.transform(currentRow.pricePerKw), purchaseMethod: currentRow.purchaseMethod, purchaseMethodId: currentRow.purchaseMethodId,
        purchaseMethodPpwBonusMappingId: currentRow.purchaseMethodPpwBonusMappingId, stateCode: currentRow.stateCode, stateCodeId: currentRow.stateCodeId,
        utilityCompany: currentRow.utilityCompany, utilityCompanyId: currentRow.utilityCompanyId, ppwBonusPricePerKw: currentRow.ppwBonusPricePerKw, ppwBonusMetric: currentRow.ppwBonusMetric
      });
    }
    this.dataSource = new MatTableDataSource(tableArr);
    this.originalDataSource = tableArr;
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }
  searchForItem(): void {
    let filteredResults: Element[] = [];
    if (this.searchText == '') {
      this.dataSource = new MatTableDataSource(this.originalDataSource);
      this.dataSource.sort = this.sort;
      this.dataSource.paginator = this.paginator;
    } else {
      // filteredResults = this.originalDataSource.filter(option => option.stateCode.toLowerCase().includes(this.searchText));
      filteredResults = this.pipe.transform(this.originalDataSource, this.searchText);
      this.dataSource = new MatTableDataSource(filteredResults);
      this.dataSource.sort = this.sort;
      this.dataSource.paginator = this.paginator;
    }
  }
}

export interface Element {
  effectiveEndDate: string,
  effectiveStartDate: string,
  financePartner: string,
  financePartnerId: number,
  ppwRate: string,
  pricePerKw: string,
  purchaseMethod: string,
  purchaseMethodId: number,
  purchaseMethodPpwBonusMappingId: number,
  stateCode: string,
  stateCodeId: number,
  utilityCompany: string,
  utilityCompanyId: number,
  ppwBonusPricePerKw: number,
  ppwBonusMetric: number
}
