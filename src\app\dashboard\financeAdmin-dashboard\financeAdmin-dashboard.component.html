<div class="page-title col-md-12 ">
  <h1>Dashboard</h1>

</div>

<div class="content">



  <div class="row">

    <div class="col-md-4">
      <div class="card bg-purple-dark info-card hover" (click)='moveToSelectedTab("Payments")'>
        <div class="card-body">
          <div class="row">
            <div class="col-8 ">
              <div class="h4 mt-0">{{infoBox.blueInfo.name}}</div>
              <div class="text-uppercase">
                <h1>{{infoBox.blueInfo.number}}</h1>
              </div>
            </div>
            <div class="col-4 text-right info-icon"><i class="fas fa-user-tie"></i></div>

          </div>

        </div>
      </div>

    </div>
    <div class="col-md-4">
      <div class="card bg-green-dark info-card hover" (click)='moveToSelectedTab("Active Sales Reps")'>
        <div class="card-body">
          <div class="row">
            <div class="col-8 ">
              <div class="h4 mt-0">{{infoBox.greenInfo.name}}</div>
              <div class="text-uppercase">
                <h1>{{infoBox.greenInfo.number}}</h1>
              </div>
            </div>
            <div class="col-4 text-right info-icon"><i class="fas fa-tasks"></i></div>

          </div>

        </div>
      </div>

    </div>
    <div class="col-md-4">
      <div class="card bg-gray-dark info-card hover" (click)='moveToSelectedTab("Open Incentive Bonuses")'>
        <div class="card-body">
          <div class="row">
            <div class="col-8 ">
              <div class="h4 mt-0">{{infoBox.greyInfo.name}}</div>
              <div class="text-uppercase">
                <h1>{{infoBox.greyInfo.number}}</h1>
              </div>
            </div>
            <div class="col-4 text-right info-icon"><i class="fas fa-dollar-sign"></i></div>

          </div>

        </div>
      </div>

    </div>
  </div>


  <div class="row">
    <div class="col-md-12 mt-2">
      <div class="card" style='min-height:400px'>
        <mat-tab-group animationDuration="2000ms">
          <mat-tab label="Payments">
            <div class="w-100 text-right">              
              <button class="btn btn-primary" (click)="getexportTable('Payments')"><i class="fas fa-download"></i> Download</button>
              <!-- <button class="btn btn-primary" *ngIf="paymentsDownloadData?.length > 0" (click)="exportTable('Payments')"><i class="fas fa-download"></i> Download</button> -->
            </div>
            <mat-table #table1 [dataSource]="dataSourcePayments" matSort>
              <ng-container matColumnDef="{{column.id}}" *ngFor="let column of columnNamesPayments">
                <mat-header-cell *matHeaderCellDef class="table-header" (mouseenter)="column.isHovered = true"
                (mouseleave)="column.isHovered = false"  (click)="onSort(column.id)"> {{column.value}}
                  <button mat-icon-button aria-label="Example icon-button with a menu"
                  >
                  <ng-container *ngIf="column.isHovered">                    
                    <ng-container *ngIf="sortField?.includes(column.id); else elseBlock">
                      <i *ngIf="sortOrder === 'desc'" class="fa fa-arrow-down" aria-hidden="true"></i>
                      <i *ngIf="sortOrder === 'asc'" class="fa fa-arrow-up" aria-hidden="true"></i>                      
                    </ng-container>
                    <ng-template #elseBlock>                      
                      <i (click)="onSort(column.id); this.sortOrder = 'desc';" class="fa fa-arrow-up" aria-hidden="true"></i>
                    </ng-template>
                  </ng-container>                    
                  </button>
                </mat-header-cell>
                <mat-cell [attr.data-td-head]="column.value" *matCellDef="let element">
                  <ng-container *ngIf="column.value == 'Contact Name'; else oppLink">
                    <a [routerLink]="['/ui/commissions/salesrep', element.Contact_Id]">{{element[column.id]}}</a>
                  </ng-container>
                  <ng-template #oppLink>
                    <ng-container *ngIf="column.value == 'Opportunity Name'; else ruleLink">
                      <a
                        [routerLink]="['/ui/commissions/opportunitydetails', element.Opportunity_Id]">{{element[column.id]}}</a>
                    </ng-container>
                  </ng-template>
                  <ng-template #ruleLink>
                    <ng-container *ngIf="column.value == 'Rule Name'; else noPayments">
                      <a
                        [routerLink]="['/ui/commissions/viewRule', element.Commission_Rule_Id]">{{element[column.id]}}</a>
                    </ng-container>
                  </ng-template>
                  <ng-template #noPayments>
                    {{element[column.id]}}
                  </ng-template>
                </mat-cell>
              </ng-container>
              <mat-header-row *matHeaderRowDef="displayedColumnsPayments"></mat-header-row>
              <mat-row *matRowDef="let row; columns: displayedColumnsPayments;" class="pointer table-content"
                (click)="rowClick(row)"></mat-row>
            </mat-table>
            <mat-paginator pageSize="itempPerPage" [length]="totalNumberofItems" [pageSize]="itempPerPage" (page)="pageEvent($event)" [pageIndex]="pageNumber-1" #paginator1 [pageSizeOptions]="[5, 10, 20, 50]" showFirstLastButtons></mat-paginator>
          </mat-tab>
          <mat-tab label="Active Sales Reps">
            <div class="w-100 text-right">
              <button class="btn btn-primary" (click)="exportTable('Active Sales Reps')"><i class="fas fa-download"></i> Download</button>
            </div>
            <mat-table #table2 [dataSource]="dataSourceActiveSalesReps" matSort #sort2="matSort">
              <ng-container matColumnDef="{{column.id}}" *ngFor="let column of columnNamesActive">
                <mat-header-cell *matHeaderCellDef mat-sort-header class="table-header"> {{column.value}}
                </mat-header-cell>
                <mat-cell [attr.data-td-head]="column.value" *matCellDef="let element">
                  <ng-container *ngIf="column.value == 'Contact Name'; else noActiveSalesReps">
                    <a [routerLink]="['/ui/commissions/salesrep', element.Contact_Id]">{{element[column.id]}}</a>
                  </ng-container>
                  <ng-template #noActiveSalesReps>
                    {{element[column.id]}}
                  </ng-template>
                </mat-cell>
              </ng-container>
              <mat-header-row *matHeaderRowDef="displayedColumnsActiveSalesReps"></mat-header-row>
              <mat-row *matRowDef="let row; columns: displayedColumnsActiveSalesReps;" class="pointer table-content"
                (click)="rowClick(row)"></mat-row>
            </mat-table>
            <mat-paginator #paginator2 [pageSizeOptions]="[5, 10, 20, 50]" showFirstLastButtons></mat-paginator>
          </mat-tab>
          <mat-tab label="Open Incentive Bonuses">
            <div class="w-100 text-right">
              <button class="btn btn-primary" (click)="exportTable('Open Incentive Bonuses')"><i class="fas fa-download"></i> Download</button>
            </div>
            <mat-table #table3 [dataSource]="dataSourceOpenIncentiveBonuses" matSort #sort3="matSort">
              <ng-container matColumnDef="{{column.id}}" *ngFor="let column of columnNamesOpen">
                <mat-header-cell *matHeaderCellDef mat-sort-header class="table-header"> {{column.value}}
                </mat-header-cell>
                <mat-cell [attr.data-td-head]="column.value" *matCellDef="let element">
                  <ng-container *ngIf="column.value == 'Contact Name'; else ruleLink">
                    <a [routerLink]="['/ui/commissions/salesrep', element.Contact_Id]">{{element[column.id]}}</a>
                  </ng-container>
                  <ng-template #ruleLink>
                    <ng-container *ngIf="column.value == 'Commission Rule Name'; else noOpenIncentiveBonuses">
                      <a
                        [routerLink]="['/ui/commissions/viewRule', element.Commission_Rule_Id]">{{element[column.id]}}</a>
                    </ng-container>
                  </ng-template>
                  <ng-template #noOpenIncentiveBonuses>
                    {{element[column.id]}}
                  </ng-template>
                </mat-cell>
              </ng-container>
              <mat-header-row *matHeaderRowDef="displayedColumnsOpenIncentiveBonuses"></mat-header-row>
              <mat-row *matRowDef="let row; columns: displayedColumnsOpenIncentiveBonuses;"
                class="pointer table-content" (click)="rowClick(row)"></mat-row>
            </mat-table>
            <mat-paginator #paginator3 [pageSizeOptions]="[5, 10, 20, 50]" showFirstLastButtons></mat-paginator>
          </mat-tab>

        </mat-tab-group>
      </div>
    </div>
  </div>
</div>