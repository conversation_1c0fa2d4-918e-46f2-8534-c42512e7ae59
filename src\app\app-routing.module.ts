import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { LogoutComponent } from './logout/logout.component';
import { ForgotpasswordComponent } from './forgotpassword/forgotpassword.component';
import { ResetpasswordComponent } from './resetpassword/resetpassword.component';
import { NotfoundComponent } from './notfound/notfound.component';

import { AppComponent } from './app.component';
// import { MsalGuard } from '@azure/msal-angular';
import { UnauthorizedComponent } from './unauthorized/unauthorized.component';

// const routes: Routes = [
//   { path: '', redirectTo: 'login', pathMatch: 'full' },
//   {
//     path: 'login',
//     loadChildren: 'src/app/login/login.module#LoginModule' 
//   },
//   { path: 'logout', component: LogoutComponent },
//   { path: 'forgotpassword', component: ForgotpasswordComponent },
//   { path: 'resetpassword/:token', component: ResetpasswordComponent },
//   { path: 'ui', loadChildren: 'src/app/dashboard/dashboard.module#DashboardModule' },
//   {path: '**', redirectTo: 'notfound'},
//   {path: 'notfound', component: NotfoundComponent},
// ];

const routes: Routes = [
  {
    path: '',
    component: AppComponent, pathMatch: 'full',
    // canActivate: [MsalGuard]
  },

  {
    path: 'login', component: AppComponent, pathMatch: 'full', 
    // canActivate: [MsalGuard]
  },
  { path: 'logout', component: LogoutComponent },
  { path: 'ui', loadChildren: () => import('src/app/dashboard/dashboard.module').then(m => m.DashboardModule) },
  { path: 'business', loadChildren: () => import('src/app/business-dashboard/business-dashboard.module').then(m => m.BusinessDashboardModule) },  
  { path: 'unauthorized', component: UnauthorizedComponent },
  { path: '**', redirectTo: 'notfound' },
  { path: 'notfound', component: NotfoundComponent },

];

@NgModule({
  imports: [RouterModule.forRoot(routes, { useHash: true })],
  exports: [RouterModule]
})
export class AppRoutingModule { }
