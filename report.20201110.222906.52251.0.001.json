{"header": {"reportVersion": 1, "event": "Allocation failed - JavaScript heap out of memory", "trigger": "FatalE<PERSON>r", "filename": "report.20201110.222906.52251.0.001.json", "dumpEventTime": "2020-11-10T22:29:06Z", "dumpEventTimeStamp": "1605027546134", "processId": 52251, "cwd": "/Users/<USER>/Angular/workspace/dev-deepak/Trinity-Commissions-UI", "commandLine": ["node", "/usr/local/bin/ng", "build", "--prod"], "nodejsVersion": "v12.13.0", "wordSize": 64, "arch": "x64", "platform": "darwin", "componentVersions": {"node": "12.13.0", "v8": "7.7.299.13-node.12", "uv": "1.32.0", "zlib": "1.2.11", "brotli": "1.0.7", "ares": "1.15.0", "modules": "72", "nghttp2": "1.39.2", "napi": "5", "llhttp": "1.1.4", "http_parser": "2.8.0", "openssl": "1.1.1d", "cldr": "35.1", "icu": "64.2", "tz": "2019a", "unicode": "12.1"}, "release": {"name": "node", "lts": "Erbium", "headersUrl": "https://nodejs.org/download/release/v12.13.0/node-v12.13.0-headers.tar.gz", "sourceUrl": "https://nodejs.org/download/release/v12.13.0/node-v12.13.0.tar.gz"}, "osName": "<PERSON>", "osRelease": "18.7.0", "osVersion": "Darwin Kernel Version 18.7.0: Mon Aug 31 20:53:32 PDT 2020; root:xnu-4903.278.44~1/RELEASE_X86_64", "osMachine": "x86_64", "cpus": [{"model": "Intel(R) Core(TM) i7-9750H CPU @ 2.60GHz", "speed": 2600, "user": 8620660, "nice": 0, "sys": 4731600, "idle": 23385550, "irq": 0}, {"model": "Intel(R) Core(TM) i7-9750H CPU @ 2.60GHz", "speed": 2600, "user": 330750, "nice": 0, "sys": 699020, "idle": 35705790, "irq": 0}, {"model": "Intel(R) Core(TM) i7-9750H CPU @ 2.60GHz", "speed": 2600, "user": 5933950, "nice": 0, "sys": 3286960, "idle": 27514750, "irq": 0}, {"model": "Intel(R) Core(TM) i7-9750H CPU @ 2.60GHz", "speed": 2600, "user": 300070, "nice": 0, "sys": 637540, "idle": 35797930, "irq": 0}, {"model": "Intel(R) Core(TM) i7-9750H CPU @ 2.60GHz", "speed": 2600, "user": 5154980, "nice": 0, "sys": 2827140, "idle": 28753530, "irq": 0}, {"model": "Intel(R) Core(TM) i7-9750H CPU @ 2.60GHz", "speed": 2600, "user": 304200, "nice": 0, "sys": 616810, "idle": 35814520, "irq": 0}, {"model": "Intel(R) Core(TM) i7-9750H CPU @ 2.60GHz", "speed": 2600, "user": 4475010, "nice": 0, "sys": 2341270, "idle": 29919350, "irq": 0}, {"model": "Intel(R) Core(TM) i7-9750H CPU @ 2.60GHz", "speed": 2600, "user": 313950, "nice": 0, "sys": 597930, "idle": 35823650, "irq": 0}, {"model": "Intel(R) Core(TM) i7-9750H CPU @ 2.60GHz", "speed": 2600, "user": 3910550, "nice": 0, "sys": 2016630, "idle": 30808440, "irq": 0}, {"model": "Intel(R) Core(TM) i7-9750H CPU @ 2.60GHz", "speed": 2600, "user": 309950, "nice": 0, "sys": 576560, "idle": 35849000, "irq": 0}, {"model": "Intel(R) Core(TM) i7-9750H CPU @ 2.60GHz", "speed": 2600, "user": 3543170, "nice": 0, "sys": 1807150, "idle": 31385270, "irq": 0}, {"model": "Intel(R) Core(TM) i7-9750H CPU @ 2.60GHz", "speed": 2600, "user": 301740, "nice": 0, "sys": 557770, "idle": 35876000, "irq": 0}], "networkInterfaces": [{"name": "lo0", "internal": true, "mac": "00:00:00:00:00:00", "address": "127.0.0.1", "netmask": "*********", "family": "IPv4"}, {"name": "lo0", "internal": true, "mac": "00:00:00:00:00:00", "address": "::1", "netmask": "ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff", "family": "IPv6", "scopeid": 0}, {"name": "lo0", "internal": true, "mac": "00:00:00:00:00:00", "address": "fe80::1", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 1}, {"name": "en0", "internal": false, "mac": "a4:83:e7:60:c0:0e", "address": "fe80::1c25:5f49:8ab6:65cb", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 10}, {"name": "en0", "internal": false, "mac": "a4:83:e7:60:c0:0e", "address": "**************", "netmask": "*************", "family": "IPv4"}, {"name": "en0", "internal": false, "mac": "a4:83:e7:60:c0:0e", "address": "2405:201:1008:401a:455:43e3:7056:2f92", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 0}, {"name": "en0", "internal": false, "mac": "a4:83:e7:60:c0:0e", "address": "2405:201:1008:401a:c9da:ca7e:a418:b268", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 0}, {"name": "awdl0", "internal": false, "mac": "a2:7d:6e:89:79:74", "address": "fe80::a07d:6eff:fe89:7974", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 12}, {"name": "utun0", "internal": false, "mac": "00:00:00:00:00:00", "address": "fe80::773e:fadf:78:b368", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 18}, {"name": "utun1", "internal": false, "mac": "00:00:00:00:00:00", "address": "fe80::aede:48ff:fe00:1122", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 19}, {"name": "utun1", "internal": false, "mac": "00:00:00:00:00:00", "address": "*************", "netmask": "***************", "family": "IPv4"}, {"name": "utun1", "internal": false, "mac": "00:00:00:00:00:00", "address": "2001:420:c0e0:1003::901", "netmask": "ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff", "family": "IPv6", "scopeid": 0}, {"name": "en5", "internal": false, "mac": "ac:de:48:00:11:22", "address": "fe80::aede:48ff:fe00:1122", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 8}], "host": "DWANJARK-M-34V5"}, "javascriptStack": {"message": "No stack.", "stack": ["Unavailable."]}, "nativeStack": [{"pc": "0x000000010014da5c", "symbol": "report::TriggerNodeReport(v8::Isolate*, node::Environment*, char const*, char const*, std::__1::basic_string<char, std::__1::char_traits<char>, std::__1::allocator<char> > const&, v8::Local<v8::String>) [/usr/local/bin/node]"}, {"pc": "0x000000010007e8a3", "symbol": "node::OnFatalError(char const*, char const*) [/usr/local/bin/node]"}, {"pc": "0x0000000100176267", "symbol": "v8::Utils::ReportOOMFailure(v8::internal::Isolate*, char const*, bool) [/usr/local/bin/node]"}, {"pc": "0x0000000100176203", "symbol": "v8::internal::V8::FatalProcessOutOfMemory(v8::internal::Isolate*, char const*, bool) [/usr/local/bin/node]"}, {"pc": "0x00000001002fa2b5", "symbol": "v8::internal::Heap::FatalProcessOutOfMemory(char const*) [/usr/local/bin/node]"}, {"pc": "0x00000001002fb984", "symbol": "v8::internal::Heap::RecomputeLimits(v8::internal::GarbageCollector) [/usr/local/bin/node]"}, {"pc": "0x00000001002f8857", "symbol": "v8::internal::Heap::PerformGarbageCollection(v8::internal::GarbageCollector, v8::GCCallbackFlags) [/usr/local/bin/node]"}, {"pc": "0x00000001002f683d", "symbol": "v8::internal::Heap::CollectGarbage(v8::internal::AllocationSpace, v8::internal::GarbageCollectionReason, v8::GCCallbackFlags) [/usr/local/bin/node]"}, {"pc": "0x0000000100301f54", "symbol": "v8::internal::Heap::AllocateRawWithLightRetry(int, v8::internal::AllocationType, v8::internal::AllocationAlignment) [/usr/local/bin/node]"}, {"pc": "0x0000000100301fcf", "symbol": "v8::internal::Heap::AllocateRawWithRetryOrFail(int, v8::internal::AllocationType, v8::internal::AllocationAlignment) [/usr/local/bin/node]"}, {"pc": "0x00000001002cebc7", "symbol": "v8::internal::Factory::NewFillerObject(int, bool, v8::internal::AllocationType) [/usr/local/bin/node]"}, {"pc": "0x00000001005f7725", "symbol": "v8::internal::Runtime_AllocateInYoungGeneration(int, unsigned long*, v8::internal::Isolate*) [/usr/local/bin/node]"}, {"pc": "0x000000010092fbd9", "symbol": "Builtins_CEntry_Return1_DontSaveFPRegs_ArgvOnStack_NoBuiltinExit [/usr/local/bin/node]"}], "javascriptHeap": {"totalMemory": 2174177280, "totalCommittedMemory": 2164544752, "usedMemory": 2057673224, "availableMemory": 86226168, "memoryLimit": 2197815296, "heapSpaces": {"read_only_space": {"memorySize": 262144, "committedMemory": 32568, "capacity": 261872, "used": 32296, "available": 229576}, "new_space": {"memorySize": 33554432, "committedMemory": 25150568, "capacity": 16759808, "used": 4345296, "available": 12414512}, "old_space": {"memorySize": 2003152896, "committedMemory": 2002343872, "capacity": 1976909512, "used": 1943725256, "available": 33184256}, "code_space": {"memorySize": 7241728, "committedMemory": 7072960, "capacity": 6206880, "used": 6206880, "available": 0}, "map_space": {"memorySize": 36966400, "committedMemory": 36945104, "capacity": 10898720, "used": 10898720, "available": 0}, "large_object_space": {"memorySize": 92377088, "committedMemory": 92377088, "capacity": 91914120, "used": 91914120, "available": 0}, "code_large_object_space": {"memorySize": 622592, "committedMemory": 622592, "capacity": 550656, "used": 550656, "available": 0}, "new_large_object_space": {"memorySize": 0, "committedMemory": 0, "capacity": 16759808, "used": 0, "available": 16759808}}}, "resourceUsage": {"userCpuSeconds": 132.631, "kernelCpuSeconds": 4.98666, "cpuConsumptionPercent": 163.83, "maxRss": 2477315325952, "pageFaults": {"IORequired": 3, "IONotRequired": 1034718}, "fsActivity": {"reads": 0, "writes": 0}}, "libuv": [], "environmentVariables": {"TERM_PROGRAM": "Apple_Terminal", "SHELL": "/bin/bash", "TERM": "xterm-256color", "TMPDIR": "/var/folders/mj/_5fpvc7d4pz8zr33fc2cbmqw0000gn/T/", "Apple_PubSub_Socket_Render": "/private/tmp/com.apple.launchd.KZjiRodjmp/Render", "GRACEFUL_SHUTDOWN": "true", "TERM_PROGRAM_VERSION": "421.2", "TERM_SESSION_ID": "85146A3B-00D2-4C3E-A238-1CA2ED858E1D", "USER": "<PERSON><PERSON><PERSON><PERSON>", "SSH_AUTH_SOCK": "/private/tmp/com.apple.launchd.0sQOBNEUwo/Listeners", "PATH": "/Users/<USER>/gocode/app-infra/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin:/usr/local/go/bin:/usr/local/Cellar/protobuf/3.10.1/bin", "PWD": "/Users/<USER>/Angular/workspace/dev-deepak/Trinity-Commissions-UI", "XPC_FLAGS": "0x0", "XPC_SERVICE_NAME": "0", "SHLVL": "1", "HOME": "/Users/<USER>", "LOGNAME": "<PERSON><PERSON><PERSON><PERSON>", "LC_CTYPE": "UTF-8", "GOPATH": "/Users/<USER>/gocode/app-infra", "_": "/usr/local/bin/ng", "__CF_USER_TEXT_ENCODING": "0x1F5:0x0:0x0", "BROWSERSLIST_IGNORE_OLD_DATA": "1"}, "userLimits": {"core_file_size_blocks": {"soft": 0, "hard": "unlimited"}, "data_seg_size_kbytes": {"soft": "unlimited", "hard": "unlimited"}, "file_size_blocks": {"soft": "unlimited", "hard": "unlimited"}, "max_locked_memory_bytes": {"soft": "unlimited", "hard": "unlimited"}, "max_memory_size_kbytes": {"soft": "unlimited", "hard": "unlimited"}, "open_files": {"soft": 24576, "hard": "unlimited"}, "stack_size_bytes": {"soft": 8388608, "hard": 67104768}, "cpu_time_seconds": {"soft": "unlimited", "hard": "unlimited"}, "max_user_processes": {"soft": 1418, "hard": 2128}, "virtual_memory_kbytes": {"soft": "unlimited", "hard": "unlimited"}}, "sharedObjects": ["/usr/local/bin/node", "/System/Library/Frameworks/CoreFoundation.framework/Versions/A/CoreFoundation", "/usr/lib/libSystem.B.dylib", "/usr/lib/libc++.1.dylib", "/usr/lib/libobjc.A.dylib", "/usr/lib/libDiagnosticMessagesClient.dylib", "/usr/lib/libicucore.A.dylib", "/usr/lib/libz.1.dylib", "/usr/lib/libc++abi.dylib", "/usr/lib/system/libcache.dylib", "/usr/lib/system/libcommonCrypto.dylib", "/usr/lib/system/libcompiler_rt.dylib", "/usr/lib/system/libcopyfile.dylib", "/usr/lib/system/libcorecrypto.dylib", "/usr/lib/system/libdispatch.dylib", "/usr/lib/system/libdyld.dylib", "/usr/lib/system/libkeymgr.dylib", "/usr/lib/system/liblaunch.dylib", "/usr/lib/system/libmacho.dylib", "/usr/lib/system/libquarantine.dylib", "/usr/lib/system/libremovefile.dylib", "/usr/lib/system/libsystem_asl.dylib", "/usr/lib/system/libsystem_blocks.dylib", "/usr/lib/system/libsystem_c.dylib", "/usr/lib/system/libsystem_configuration.dylib", "/usr/lib/system/libsystem_coreservices.dylib", "/usr/lib/system/libsystem_darwin.dylib", "/usr/lib/system/libsystem_dnssd.dylib", "/usr/lib/system/libsystem_info.dylib", "/usr/lib/system/libsystem_m.dylib", "/usr/lib/system/libsystem_malloc.dylib", "/usr/lib/system/libsystem_networkextension.dylib", "/usr/lib/system/libsystem_notify.dylib", "/usr/lib/system/libsystem_sandbox.dylib", "/usr/lib/system/libsystem_secinit.dylib", "/usr/lib/system/libsystem_kernel.dylib", "/usr/lib/system/libsystem_platform.dylib", "/usr/lib/system/libsystem_pthread.dylib", "/usr/lib/system/libsystem_symptoms.dylib", "/usr/lib/system/libsystem_trace.dylib", "/usr/lib/system/libunwind.dylib", "/usr/lib/system/libxpc.dylib", "/System/Library/Frameworks/ApplicationServices.framework/Versions/A/ApplicationServices", "/System/Library/Frameworks/CoreGraphics.framework/Versions/A/CoreGraphics", "/System/Library/Frameworks/CoreText.framework/Versions/A/CoreText", "/System/Library/Frameworks/ImageIO.framework/Versions/A/ImageIO", "/System/Library/Frameworks/ColorSync.framework/Versions/A/ColorSync", "/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/ATS", "/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ColorSyncLegacy.framework/Versions/A/ColorSyncLegacy", "/System/Library/Frameworks/CoreServices.framework/Versions/A/CoreServices", "/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/HIServices.framework/Versions/A/HIServices", "/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/LangAnalysis.framework/Versions/A/LangAnalysis", "/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/PrintCore.framework/Versions/A/PrintCore", "/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/QD.framework/Versions/A/QD", "/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/SpeechSynthesis.framework/Versions/A/SpeechSynthesis", "/System/Library/PrivateFrameworks/SkyLight.framework/Versions/A/SkyLight", "/System/Library/Frameworks/IOSurface.framework/Versions/A/IOSurface", "/usr/lib/libxml2.2.dylib", "/System/Library/Frameworks/CFNetwork.framework/Versions/A/CFNetwork", "/System/Library/Frameworks/Accelerate.framework/Versions/A/Accelerate", "/System/Library/Frameworks/Foundation.framework/Versions/C/Foundation", "/usr/lib/libcompression.dylib", "/System/Library/Frameworks/SystemConfiguration.framework/Versions/A/SystemConfiguration", "/System/Library/Frameworks/CoreDisplay.framework/Versions/A/CoreDisplay", "/System/Library/Frameworks/IOKit.framework/Versions/A/IOKit", "/System/Library/Frameworks/Metal.framework/Versions/A/Metal", "/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/MetalPerformanceShaders", "/System/Library/PrivateFrameworks/MultitouchSupport.framework/Versions/A/MultitouchSupport", "/System/Library/Frameworks/Security.framework/Versions/A/Security", "/System/Library/Frameworks/QuartzCore.framework/Versions/A/QuartzCore", "/usr/lib/libbsm.0.dylib", "/usr/lib/liblzma.5.dylib", "/usr/lib/libauto.dylib", "/System/Library/Frameworks/DiskArbitration.framework/Versions/A/DiskArbitration", "/usr/lib/libarchive.2.dylib", "/usr/lib/liblangid.dylib", "/usr/lib/libCRFSuite.dylib", "/usr/lib/libenergytrace.dylib", "/usr/lib/system/libkxld.dylib", "/System/Library/PrivateFrameworks/AppleFSCompression.framework/Versions/A/AppleFSCompression", "/usr/lib/libOpenScriptingUtil.dylib", "/usr/lib/libcoretls.dylib", "/usr/lib/libcoretls_cfhelpers.dylib", "/usr/lib/libpam.2.dylib", "/usr/lib/libsqlite3.dylib", "/usr/lib/libxar.1.dylib", "/usr/lib/libbz2.1.0.dylib", "/usr/lib/libnetwork.dylib", "/usr/lib/libapple_nghttp2.dylib", "/usr/lib/libpcap.A.dylib", "/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/FSEvents.framework/Versions/A/FSEvents", "/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/CarbonCore.framework/Versions/A/CarbonCore", "/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/Metadata.framework/Versions/A/Metadata", "/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/OSServices.framework/Versions/A/OSServices", "/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SearchKit.framework/Versions/A/SearchKit", "/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/AE.framework/Versions/A/AE", "/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/LaunchServices.framework/Versions/A/LaunchServices", "/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/DictionaryServices.framework/Versions/A/DictionaryServices", "/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SharedFileList.framework/Versions/A/SharedFileList", "/System/Library/Frameworks/NetFS.framework/Versions/A/NetFS", "/System/Library/PrivateFrameworks/NetAuth.framework/Versions/A/NetAuth", "/System/Library/PrivateFrameworks/login.framework/Versions/A/Frameworks/loginsupport.framework/Versions/A/loginsupport", "/System/Library/PrivateFrameworks/TCC.framework/Versions/A/TCC", "/System/Library/PrivateFrameworks/CoreNLP.framework/Versions/A/CoreNLP", "/System/Library/PrivateFrameworks/MetadataUtilities.framework/Versions/A/MetadataUtilities", "/usr/lib/libmecabra.dylib", "/usr/lib/libmecab.1.0.0.dylib", "/usr/lib/libgermantok.dylib", "/usr/lib/libThaiTokenizer.dylib", "/usr/lib/libChineseTokenizer.dylib", "/usr/lib/libiconv.2.dylib", "/usr/lib/libcharset.1.dylib", "/System/Library/PrivateFrameworks/LanguageModeling.framework/Versions/A/LanguageModeling", "/System/Library/PrivateFrameworks/CoreEmoji.framework/Versions/A/CoreEmoji", "/System/Library/PrivateFrameworks/Lexicon.framework/Versions/A/Lexicon", "/System/Library/PrivateFrameworks/LinguisticData.framework/Versions/A/LinguisticData", "/usr/lib/libcmph.dylib", "/System/Library/Frameworks/CoreData.framework/Versions/A/CoreData", "/System/Library/Frameworks/OpenDirectory.framework/Versions/A/Frameworks/CFOpenDirectory.framework/Versions/A/CFOpenDirectory", "/System/Library/PrivateFrameworks/APFS.framework/Versions/A/APFS", "/usr/lib/libutil.dylib", "/System/Library/Frameworks/ServiceManagement.framework/Versions/A/ServiceManagement", "/System/Library/PrivateFrameworks/BackgroundTaskManagement.framework/Versions/A/BackgroundTaskManagement", "/usr/lib/libxslt.1.dylib", "/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vImage.framework/Versions/A/vImage", "/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/vecLib", "/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvMisc.dylib", "/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvDSP.dylib", "/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBLAS.dylib", "/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLAPACK.dylib", "/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLinearAlgebra.dylib", "/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparseBLAS.dylib", "/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libQuadrature.dylib", "/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBNNS.dylib", "/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparse.dylib", "/System/Library/PrivateFrameworks/GPUWrangler.framework/Versions/A/GPUWrangler", "/System/Library/PrivateFrameworks/IOAccelerator.framework/Versions/A/IOAccelerator", "/System/Library/PrivateFrameworks/IOPresentment.framework/Versions/A/IOPresentment", "/System/Library/PrivateFrameworks/DSExternalDisplay.framework/Versions/A/DSExternalDisplay", "/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreFSCache.dylib", "/System/Library/Frameworks/MetalPerformanceShaders.framework/Frameworks/MPSCore.framework/Versions/A/MPSCore", "/System/Library/Frameworks/MetalPerformanceShaders.framework/Frameworks/MPSImage.framework/Versions/A/MPSImage", "/System/Library/Frameworks/MetalPerformanceShaders.framework/Frameworks/MPSNeuralNetwork.framework/Versions/A/MPSNeuralNetwork", "/System/Library/Frameworks/MetalPerformanceShaders.framework/Frameworks/MPSMatrix.framework/Versions/A/MPSMatrix", "/System/Library/Frameworks/MetalPerformanceShaders.framework/Frameworks/MPSRayIntersector.framework/Versions/A/MPSRayIntersector", "/System/Library/PrivateFrameworks/MetalTools.framework/Versions/A/MetalTools", "/System/Library/PrivateFrameworks/AggregateDictionary.framework/Versions/A/AggregateDictionary", "/usr/lib/libMobileGestalt.dylib", "/System/Library/Frameworks/CoreImage.framework/Versions/A/CoreImage", "/System/Library/Frameworks/CoreVideo.framework/Versions/A/CoreVideo", "/System/Library/Frameworks/OpenGL.framework/Versions/A/OpenGL", "/System/Library/PrivateFrameworks/GraphVisualizer.framework/Versions/A/GraphVisualizer", "/System/Library/PrivateFrameworks/FaceCore.framework/Versions/A/FaceCore", "/System/Library/Frameworks/OpenCL.framework/Versions/A/OpenCL", "/usr/lib/libFosl_dynamic.dylib", "/System/Library/PrivateFrameworks/OTSVG.framework/Versions/A/OTSVG", "/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/Resources/libFontParser.dylib", "/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/Resources/libFontRegistry.dylib", "/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJPEG.dylib", "/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libTIFF.dylib", "/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libPng.dylib", "/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libGIF.dylib", "/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJP2.dylib", "/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libRadiance.dylib", "/System/Library/PrivateFrameworks/AppleJPEG.framework/Versions/A/AppleJPEG", "/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGFXShared.dylib", "/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLU.dylib", "/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGL.dylib", "/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLImage.dylib", "/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCVMSPluginSupport.dylib", "/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreVMClient.dylib", "/usr/lib/libcups.2.dylib", "/System/Library/Frameworks/Kerberos.framework/Versions/A/Kerberos", "/System/Library/Frameworks/GSS.framework/Versions/A/GSS", "/usr/lib/libresolv.9.dylib", "/System/Library/PrivateFrameworks/Heimdal.framework/Versions/A/Heimdal", "/usr/lib/libheimdal-asn1.dylib", "/System/Library/Frameworks/OpenDirectory.framework/Versions/A/OpenDirectory", "/System/Library/PrivateFrameworks/CommonAuth.framework/Versions/A/CommonAuth", "/System/Library/Frameworks/SecurityFoundation.framework/Versions/A/SecurityFoundation", "/System/Library/Frameworks/CoreAudio.framework/Versions/A/CoreAudio", "/System/Library/Frameworks/AudioToolbox.framework/Versions/A/AudioToolbox", "/System/Library/PrivateFrameworks/AppleSauce.framework/Versions/A/AppleSauce", "/System/Library/PrivateFrameworks/AssertionServices.framework/Versions/A/AssertionServices", "/System/Library/PrivateFrameworks/BaseBoard.framework/Versions/A/BaseBoard", "/System/Library/PrivateFrameworks/CoreServicesInternal.framework/Versions/A/CoreServicesInternal", "/Users/<USER>/Angular/workspace/dev-deepak/Trinity-Commissions-UI/node_modules/fsevents/lib/binding/Release/node-v72-darwin-x64/fse.node"]}