import { Component, OnInit, Input } from '@angular/core';
import { UntypedFormBuilder, FormGroup, Validators } from "@angular/forms";
import { Router } from "@angular/router";
import { ApiService } from "../../../services/api.service";
import { ToastrService } from 'ngx-toastr';
import { User } from '../../../model/user.model';
import { MustMatch } from '../../../services/must-match.service'

@Component({
  selector: 'app-users-list',
  templateUrl: './users-list.component.html',
  styleUrls: ['./users-list.component.css']
})
export class UsersListComponent implements OnInit {
  searchText: string = "";
  usersList: any = [];
  rolesList: any = [];
  role_id: string = "0";
  page: number = 1;

  constructor(private formBuilder: UntypedFormBuilder,
    private router: Router,
    public apiService: ApiService,
    private toastMsg: ToastrService) {

  }

  ngOnInit() {
    if (!this.apiService.checkPermission('AssignRolesToUser') && !this.apiService.checkPermission('CreateNewUser')) {
      this.apiService.goBack();
      this.toastMsg.error("Insufficient Permissions. Please make sure your account can access this information.", "Permissions");
    }
    this.getUsersList('automatic')
    this.getRolesList()
  }

  /**
   * Filter Department
   */
  filterDepartment() {
    this.getUsersList('trigger');
  }

  /**
   * Users List
   */
  getUsersList(flag: string) {
    // let url = 'userdetails/getuserdetails?roleid=';
    // if(flag == 'trigger' && this.rolesList){
    //   url = 'UserInfoDetails?roleid='+this.role_id
    // }
    this.apiService.ssoGet('UserDetails/GetUserDetails?roleid=' + this.role_id)
      .subscribe(data => {
        //console.log("Get Users", data)
        if (data.statusCode === "201" && data.result) {
          this.usersList = data.result;
        } else {
          this.toastMsg.error(data.message, "Error!")
        }
      }, (err: any) => {
        // console.log(err)
        this.toastMsg.error(err.message, "Error!")
      });
  }

  /**
   * Role List
   */
  getRolesList() {
    this.apiService.get('commissions/getRoles')
      .subscribe(data => {
        //console.log("Get Roles", data)
        if (data.statusCode === "201" && data.result) {
          this.rolesList = data.result;
        } else {
          this.toastMsg.error(data.message, "Error!")
        }
      }, (err: any) => {
        // console.log(err)
        this.toastMsg.error(err.message, "Error!")
      });
  }

  /**
   *
   */
  onEditUser(user: any) {
    // console.log("Edit User", user)
    localStorage.setItem('EditUser', JSON.stringify(user))
    this.router.navigate(['/ui/commissions/usermanagement/users/edit'])
  }
}
