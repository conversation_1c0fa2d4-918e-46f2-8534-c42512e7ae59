<div class="container-fluid">
  <div class="row" *ngIf="enableChart;else spinner">
    <div class="col">
      <div class="row">
        <div class="col top-box">
          <div class="card info-card-black hover">
            <div class="card-body">
              <div class="row">
                <div class="col-10">
                  <div class="header-custom mt-0">Total Commision Payments</div>
                  <div class="text-uppercase">
                    <h1> {{totalCommissionPayments}}</h1>
                  </div>
                </div>
                <div class="col-2 text-right info-icon"><i class="fas fa-money-bill"></i></div>
              </div>
            </div>
          </div>
        </div>
        <div class="col top-box">
          <div class="card info-card-black hover" (click)="moveToSelectedTab('Kilowatts By Division And Territory')">
            <div class="card-body">
              <div class="row">
                <div class="col-10">
                  <div class="header-custom mt-0">Total Kilowatts Sold</div>
                  <div class="text-uppercase">
                    <h1>{{ totalKilowattsSold }}</h1>
                  </div>
                </div>
                <div class="col-2 text-right info-icon"><i class="fas fa-lightbulb"></i></div>
              </div>
            </div>
          </div>
        </div>
        <div class="col top-box">
          <div class="card info-card-black hover" (click)="moveToSelectedTab('Pending Payments')">
            <div class="card-body">
              <div class="row">
                <div class="col-10">
                  <div class="header-custom mt-0">Pending Payments Approval</div>
                  <div class="text-uppercase">
                    <h1>{{ pendingPaymentApproval }}</h1>
                  </div>
                </div>
                <div class="col-2 text-right info-icon"><i class="fas fa-money-bill-wave"></i></div>
              </div>
            </div>
          </div>
        </div>
        <div class="col top-box">
          <div class="card info-card-black hover" (click)="moveToSelectedTab('Processed Payments')">
            <div class="card-body">
              <div class="row">
                <div class="col-10">
                  <div class="header-custom mt-0">Processed Payments</div>
                  <div class="text-uppercase">
                    <h1>{{ processedPayments }}</h1>
                  </div>
                </div>
                <div class="col-2 text-right info-icon"><i class="fas fa-credit-card"></i></div>
              </div>
            </div>
          </div>
        </div>
        <div class="col top-box">
          <div class="card info-card-black hover" (click)="moveToSelectedTab('Reps With No Plans')">
            <div class="card-body">
              <div class="row">
                <div class="col-10">
                  <div class="header-custom mt-0">Reps with No Plans</div>
                  <div class="text-uppercase">
                    <h1>{{ repsWithNoPlan }}</h1>
                  </div>
                </div>
                <div class="col-2 text-right info-icon"><i class="fas fa-user-tie"></i></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-6 p-1">
          <div class="card info-card-black hover card-min-height">
            <div class="card-body">
              <div *ngIf="commissionProductChartData.length ==0" class="text-center">
                <h5 class="business-chart-title">Payments By Product Type</h5> 
                <p>No Chart to Display</p>
              </div>
              <div *ngIf="commissionProductChartData.length !=0" [chart]="commissionChartsData"></div>
            </div>
          </div>
        </div>
        <div class="col-6 p-1">
          <div class="card info-card-black hover card-min-height">
            <div class="card-body">
              <div *ngIf="kilowattXaxisData.length ==0" class="text-center">
                <h5 class="business-chart-title">Total Kilo Watts by Division</h5> 
                <p>No Chart to Display</p>
              </div> 
              <div *ngIf="kilowattXaxisData.length !=0" [chart]="kiloWattChartsData"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-6">
          <div class="card info-card-black hover card-min-height">
            <div class="card-body">
              <div *ngIf="typeofPendingChartsData.length ==0" class="text-center">
                <h5 class="business-chart-title">Types of Pending Payment</h5> 
                <p>No Chart to Display</p>
              </div>                           
              <div *ngIf="typeofPendingChartsData.length !=0" [chart]="pendingPaymentsChartsData"></div>
            </div>
          </div>
        </div>
        <div class="col-6">
          <div class="card info-card-black hover card-min-height">
            <div class="card-body">
              <div *ngIf="commissionDivisionChartData.length ==0" class="text-center">
                <h5 class="business-chart-title">Payments By Division</h5> 
                <p>No Chart to Display</p>
              </div> 
              <div *ngIf="commissionDivisionChartData.length !=0" [chart]="commissionByDivisionChartsData"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-6 p-1">
          <div class="card info-card-black hover card-min-height">
            <div class="card-body">
              <div *ngIf="quaterListData.length ==0" class="text-center h-50">
                <h5 class="business-chart-title">Total Commissions Paid by Quarter</h5> 
                <p>No Chart to Display</p>
              </div> 
              <div  *ngIf="quaterListData.length !=0" [chart]="totalCommissionChartData"></div>
            </div>
          </div>
        </div>
        <div class="col-6 p-1" *ngIf="showMap">
          <div class="card info-card-black hover">
            <div class="card-body">
              <app-map [data]="mapData"></app-map>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <ng-template #spinner>
      <div class="spinner-container">
        <img *ngIf="isEnableLoader" src="assets/images/Sun-GIF.gif" alt="Trinity Loading Spinner" class="spinner">
      </div>
  </ng-template>
</div>