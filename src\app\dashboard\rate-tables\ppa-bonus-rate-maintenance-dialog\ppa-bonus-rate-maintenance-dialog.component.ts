import { Component, OnInit, Inject } from '@angular/core';
import { MatLegacyDialogRef, MAT_LEGACY_DIALOG_DATA } from '@angular/material/legacy-dialog';
import { ApiService } from '../../../services/api.service';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-ppa-bonus-rate-maintenance-dialog',
  templateUrl: './ppa-bonus-rate-maintenance-dialog.component.html',
  styleUrls: ['./ppa-bonus-rate-maintenance-dialog.component.css']
})
export class PpaBonusRateMaintenanceDialogComponent implements OnInit {
  ppaBonusRateGroup: Element[] = [];
  ppaBonusRateSelectedGroup: any;
  
  constructor(public dialogRef: MatLegacyDialogRef<PpaBonusRateMaintenanceDialogComponent>,
    private apiService: ApiService, private toastMsg: ToastrService, @Inject(MAT_LEGACY_DIALOG_DATA) public data: any) { }


  ngOnInit() {
    this.ppaBonusRateGroup = this.data.ppaBonusRate; 
    this.ppaBonusRateGroup.sort((a, b) => {
      return <any>new Date(b[0].effectiveStartDate) - <any>new Date(a[0].effectiveStartDate);
    });
  }

  groupClick(group: any) {
    this.ppaBonusRateSelectedGroup = group;
  }

}

export interface Element {
  effectiveStartDate: string,
  effectiveEndDate: string
}

