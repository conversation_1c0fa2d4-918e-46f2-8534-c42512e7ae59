<div class="sidebar" data-color="azure" data-background-color="white" style="z-index: 1029 !important">

  <!--
  Tip 1: You can change the color of the sidebar using: data-color="purple | azure | green | orange | danger"
  
  Tip 2: you can also add an image using data-image tag
  -->
  <div class="logo main-head">
    <a [routerLink]="['/']" class="simple-text logo-normal"><img src="/assets/images/Trinity.png" width="100px"
        alt=""></a>
  </div>
  <div class="sidebar-wrapper">
    <ul class="nav">
      <li class="nav-item" *ngIf="apiService.checkPermission('CreateRule')">
        <a class="nav-link" [routerLink]="['/ui/commissions/rule/create']">
          <i class="material-icons">create</i>
          <p class="custom-font-weight">Rule Builder</p>
        </a>
      </li>
      <li class="nav-item " *ngIf="apiService.checkPermission('CreatePlan')">
        <a class="nav-link" [routerLink]="['/ui/commissions/createplan']">
          <i class="material-icons">restore_page</i>
          <p class="custom-font-weight">Plan Builder</p>
        </a>
      </li>
      <!-- <li class="nav-item " *ngIf="empRole && (['Legal', 'Admin'].indexOf(empRole) > -1)">
        <a class="nav-link" [routerLink] = "['/ui/commissions/salesrepconfiguration']">
          <i class="material-icons">stars</i>
          <p class="custom-font-weight">Sales Rep Configuration</p>
        </a>
      </li>
      <li class="nav-item " *ngIf="empRole && (['Legal', 'Admin'].indexOf(empRole) > -1)">
        <a class="nav-link" [routerLink] = "['/ui/commissions/opportunitydetails']">
          <i class="material-icons">shop</i>
          <p class="custom-font-weight">Opportunity Details</p>
        </a>
      </li>
      <li class="nav-item " *ngIf="['Legal', 'Admin'].indexOf(empRole) > -1">
        <a class="nav-link" [routerLink] = "['/ui/commissions/salesrep']">
          <i class="material-icons">shop</i>
          <p class="custom-font-weight">Sales Rep Details</p>
        </a>
      </li> -->
      
      <!-- <li class="nav-item " *ngIf="empRole">
        <a class="nav-link" [routerLink] = "['/ui/commissions/userdashboard']">
          <i class="material-icons">pages</i>
          <p class="custom-font-weight">User Dashboard</p>
        </a>
      </li>
      <li class="nav-item" *ngIf="empRole">
        <a class="nav-link" [routerLink] = "['/ui/commissions/emphistory']">
          <i class="material-icons">history</i>
          <p class="custom-font-weight">Employee History</p>
        </a>
      </li>
      <li class="nav-item" *ngIf="empRole">
        <a class="nav-link" [routerLink] = "['/ui/commissions/paybooks']">
          <i class="material-icons">book</i>
          <p class="custom-font-weight">Payment Books</p>
        </a>
      </li> -->
      <li class="nav-item" *ngIf="apiService.checkPermission('CommissionReports') || apiService.checkPermission('PaymentReports') || apiService.checkPermission('PaymentBookReports')">
        <a class="nav-link" [routerLink]="['/ui/commissions/report']">
          <i class="material-icons">report</i>
          <p class="custom-font-weight">Reports</p>
        </a>
      </li>
      <li class="nav-item" *ngIf="apiService.checkPermission('ViewRateTables')">
        <a class="nav-link">
          <div class="rate-table-row toggler" (click)="toggleRateTableDropdown()">
            <i class="material-icons">settings_applications</i>
            <p class="custom-font-weight">Rate Tables</p>
            <ng-container *ngIf="!subRateTables; else showRateTable">
              <i class="material-icons">keyboard_arrow_down</i>
            </ng-container>
            <ng-template #showRateTable>
              <i class="material-icons">keyboard_arrow_up</i>
            </ng-template>
          </div>
        </a>
        <ng-container *ngIf="subRateTables">
          <div class="sub-rates">
            <div class="sub-rate-table-row" [routerLink]="['/ui/commissions/ratetables/territoryrate']">
              <a class="custom-font-weight mt-0">Territory Rates</a>
            </div>
            <div class="sub-rate-table-row" [routerLink]="['/ui/commissions/ratetables/financepartnerdeduction']">
              <a class="custom-font-weight mt-0">Finance Partner
                Deductions</a>
            </div>
            <div class="sub-rate-table-row" [routerLink]="['/ui/commissions/ratetables/modulededuction']">
              <a class="custom-font-weight mt-0">Module Deductions</a>
            </div>
            <div class="sub-rate-table-row" [routerLink]="['/ui/commissions/ratetables/inverterdeduction']">
              <a class="custom-font-weight mt-0">Inverter Deductions</a>
            </div>
            <div class="sub-rate-table-row" [routerLink]="['/ui/commissions/ratetables/installationtypededuction']">
              <a class="custom-font-weight mt-0">Installation Type Deductions</a>
            </div>
            <div class="sub-rate-table-row" [routerLink]="['/ui/commissions/ratetables/purchasemethoddeduction']">
              <a class="custom-font-weight mt-0">Purchase Method Deductions(Solar)</a>
            </div>
            <div class="sub-rate-table-row" [routerLink]="['/ui/commissions/ratetables/productpurchasemethoddeduction']">
              <a class="custom-font-weight mt-0">Purchase Method Deductions(Roof)</a>
            </div>            
            <div class="sub-rate-table-row" [routerLink]="['/ui/commissions/ratetables/permitdeduction']">
              <a class="custom-font-weight mt-0">Permit Deductions</a>
            </div>
            <div class="sub-rate-table-row" [routerLink]="['/ui/commissions/ratetables/ppabonusrate']">
              <a class="custom-font-weight mt-0">PPA Bonus Rates</a>
            </div>
            <div class="sub-rate-table-row" [routerLink]="['/ui/commissions/ratetables/BatteryCommissionRate']">
              <a class="custom-font-weight mt-0"> Battery Commission Rates</a>
            </div>
            <div class="sub-rate-table-row" [routerLink]="['/ui/commissions/ratetables/outreachconfig']">
              <a class="custom-font-weight mt-0">OurReach Configuration</a>
            </div>
          </div>
        </ng-container>
      </li>
      <li class="nav-item" *ngIf="apiService.checkPermission('ViewRateTables')">
        <a class="nav-link">
          <div class="rate-table-row toggler" (click)="togglePaymentsDropdown()">
            <i class="material-icons">attach_money</i>
            <p class="custom-font-weight">Payments</p>
            <ng-container *ngIf="!subPayments; else showPayments">
              <i class="material-icons">keyboard_arrow_down</i>
            </ng-container>
            <ng-template #showPayments>
              <i class="material-icons">keyboard_arrow_up</i>
            </ng-template>
          </div>
        </a>
        <ng-container *ngIf="subPayments">
          <div class="sub-rates">
            <div class="sub-rate-table-row" [routerLink]="['/ui/commissions/payments']">
              <a class="custom-font-weight mt-0">Payment Approvals</a>
            </div>
            <div class="sub-rate-table-row" [routerLink]="['/ui/commissions/paymentwithdrawals']">
              <a class="custom-font-weight mt-0">Payment Book Withdrawals</a>
            </div>
          </div>
        </ng-container>
      </li>
      <!-- <li class="nav-item" *ngIf="empRole && (['admin'].indexOf(empRole.toLowerCase()) > -1)">
        <a class="nav-link" [routerLink]="['/ui/commissions/usermanagment/users']">
          <i class="material-icons">report</i>
          <p class="custom-font-weight">User Managment</p>
        </a>
      </li> -->
      <li class="nav-item">
        <a class="nav-link" [routerLink]="['/ui/commissions/search']">
          <i class="material-icons">search</i>
          <p class="custom-font-weight">Search</p>
        </a>
      </li>
    </ul>
  </div>
</div>