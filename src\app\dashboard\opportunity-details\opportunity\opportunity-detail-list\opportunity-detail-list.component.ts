import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { Observable, of } from 'rxjs';
import { debounceTime, distinctUntilChanged, switchMap } from 'rxjs/operators';
import { IContact } from 'src/app/model/one-time-payment.model';
import { IOpportunityDetailRow } from 'src/app/model/opportunity-detail-row.model';
import { SlopeType } from 'src/app/model/opportunity.model';
import { ApiService } from 'src/app/services/api.service';

@Component({
  selector: 'app-opportunity-detail-list',
  templateUrl: './opportunity-detail-list.component.html',
  styleUrls: ['./opportunity-detail-list.component.css']
})
export class OpportunityDetailListComponent implements OnInit {
  @Input() header: string;
  @Input() rows: IOpportunityDetailRow[];
  @Input() show: boolean = false;
  @Input() isRoof: boolean = false;
  @Input() slopeType: SlopeType = {};
  @Input() enableEdit: boolean = false;
  @Input() updatedOpportunityValuesForm: {[key: string]: any};
  insideSalesCampaignOptions: any[] = [];
  roofingFinancePartnerOptions: any[] = [];
  icon: string = "remove_circle_outline";
  activeContacts: IContact[] = [];

  constructor(private apiService: ApiService, private toastMsg: ToastrService) { }

  ngOnInit() {
    if (this.show) {
      this.icon = "remove_circle_outline";
    } else {
      this.icon = "add_circle_outline";
    }

    if (this.header === 'OPPORTUNITY DETAILS' && (!this.insideSalesCampaignOptions || this.insideSalesCampaignOptions.length === 0)) {
      this.apiService.get('GetData/InsideSalesCampaign')
        .subscribe(data => {
          if (data && data.result) {
            this.insideSalesCampaignOptions = data.result;
          }
        }, err => {
          this.toastMsg.error(err.message, "Error!");
        });
    }

    if (this.header === 'TRINITY CONTACTS' && (!this.activeContacts || this.activeContacts.length == 0)) {
      this.apiService.get('GetData/GetActiveContacts')
        .subscribe(data => {
          if (data && data.result) {
            this.activeContacts = data.result.map(type => { return <IContact>{ contactId: type.id, contactName: type.name } });
          }
        }, err => {
          this.toastMsg.error(err.message, "Error!");
        });        
    }
    if (this.header === 'ROOFING DETAILS' && (!this.roofingFinancePartnerOptions || this.roofingFinancePartnerOptions.length === 0)) {
      this.apiService.get('TerritoryRateMaintenance/dropdowns').subscribe(data => {
        const resData = data as any;
        this.roofingFinancePartnerOptions = (resData?.financePartners || []).filter(
          (partner: any) => partner.financePartner1 !== 'Any'
        );
      },
        (err: any) => {
          this.toastMsg.error(err.message, 'Server Error!');
        });
    }
  }

  onClick() {
     this.show = !this.show;
    if (this.show) {
     
       this.icon = "remove_circle_outline";
    } else {
      
      this.icon = "add_circle_outline";
    }
  }

  editFieldValue(row: any, event: any, dataType?: string) {
    let rowName = row.replace(/\s/g, "");
    if (dataType == 'dropdown') rowName = rowName + 'Id';
    switch (rowName) {
      case 'R&RSalespersonId':
        rowName = 'rrSalespersonId';
        break;
      case 'RebateAmount':
        rowName = 'contractRebateAmount';
        break;
      case 'DepositAmount':
        rowName = 'contractDepositAmount';
        break;
      case 'InverterType2':
        rowName = 'Inverter2Type';
        break;
      case 'InverterType3':
        rowName = 'Inverter3Type';
        break;
      case 'InverterType4':
        rowName = 'Inverter4Type';
        break;
      case 'RoofingContractSignedDate':
        rowName = 'RoofingContractDate';
        break;
      case 'RoofSalesChargebackAmount':
        rowName = 'RoofChargebackAmount';
        break;
      case 'RoofingAdders':
        rowName = 'RoofAdderAmount';
        break;
      case 'RoofingFinancePartnerId':
        rowName = 'RoofingFinancePartner';
        break;
      case 'BatterySalesChargebackAmount':
        rowName = 'BatteryChargebackAmount';
        break;
      case 'R&RInstallationStage':
        rowName = 'RRInstallationStage';
        break;
      case 'R&RSystemSize':
        rowName = 'RRSystemSize';
        break;
      case 'R&RAmount':
        rowName = 'RRAmount';
        break;
      case 'R&RChargebackAmount':
        rowName = 'RRChargebackAmount';
        break;
      case 'R&RContractId':
        rowName = 'RRContractId';
        break;
      case 'R&RContractDate':
        rowName = 'RRContractDate';
        break;
      case 'R&RPurchaseMethod':
        rowName = 'RRPurchaseMethod';
        break;
      case 'RoofSquares(includingwaste)':
        rowName = 'roofSquares';
        break;        
    }
    let inputValue: any;
    if (event?.target?.value !== undefined) {
      // Old select/text input
      inputValue = event.target.value;
    } 
    else if (event?.item) {
      // Typeahead selectItem event
      inputValue = event.item.contactId ?? event.item;
    } 

    if (typeof inputValue !== 'undefined') {
      // Remove $ symbol if present
      const cleanedValue = inputValue.toString().replace(/\$/g, '');

      // Check if it's a numeric value (pure digits or digits with decimals)
      const isNumeric = !isNaN(cleanedValue) && cleanedValue.trim() !== '';
      this.updatedOpportunityValuesForm[rowName] = isNumeric ? Number(cleanedValue) : cleanedValue; // Keep string if not numeric
    } else if (typeof event?.checked !== 'undefined') {
      // For checkboxes
      this.updatedOpportunityValuesForm[rowName] = event.checked;
    }
    // this.updatedOpportunityValuesForm[rowName] = event.target?.value ? event.target?.value.replace('$', ''):event?.checked;
  }

  getContactObject(contactId: number) {
    return this.activeContacts.find(c => c.contactId === contactId) || null;
  }

  formatter = (c: IContact) => c && c.contactName ? c.contactName : '';
  searchContacts = (text$: Observable<string>) =>
    text$.pipe(
      debounceTime(200),
      distinctUntilChanged(),
      switchMap(term =>
        term.length < 2
          ? of([]) // don't search until at least 2 chars
          : this.activeContacts
            ? of(this.activeContacts.filter(c =>
              c.contactName.toLowerCase().includes(term.toLowerCase())
            ))
            : of([])
      )
    );

  selectContact(row: any, event: any) {
    let rowName = row.replace(/\s/g, "");
    this.updatedOpportunityValuesForm[rowName] = event.target.value;
  }

 
}