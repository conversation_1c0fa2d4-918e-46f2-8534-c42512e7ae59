<div class="card">
    <div class="card-header-info">
        <h4 class="card-title">Status Overview</h4>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-12 text-right">
                <button class="btn btn-primary" (click)="refresh();"><i class="fas fa-sync-alt"></i> Refresh</button>
            </div>
        </div>
        <table class="my-table mt-3 w-100 mat-table">
            <thead>
                <tr class="mat-header-row">
                    <th class="mat-header-cell">Plan Execution ID</th>
                    <th class="mat-header-cell">Plan Execution Type</th>
                    <th class="mat-header-cell">Start Time</th>
                    <th class="mat-header-cell">End Time</th>
                    <th class="mat-header-cell">Status</th>
                </tr>
            </thead>
            <tbody>
                <ng-container *ngIf="overview">
                    <ng-container *ngFor="let job of jobs | paginate: { itemsPerPage: 10, currentPage: p }">
                        <tr class="mat-row" (click)="onRowClick(job.peJobId)">
                            <td data-td-head="Job Type" class="mat-cell">{{job.peJobId}}</td>
                            <td data-td-head="Job Type" class="mat-cell">{{job.peJobTypeName}}</td>
                            <td data-td-head="Start Time" class="mat-cell">{{job.jobStartTimestamp ? (job.jobStartTimestamp.toString() | timezoneDate) : ""}}
                            </td>
                            <td data-td-head="Job Type" class="mat-cell">{{job.jobEndTimestamp ? (job.jobEndTimestamp.toString() | timezoneDate) : ""}}</td>
                            <td data-td-head="End Time" class="mat-cell">{{job.jobStatus==null?'InProgress':job.jobStatus}}
                                <button *ngIf="job.jobStatus==null" matTooltip="Click to terminate" (click)="terminate($event, job)" style="border: none; background-color:transparent;"><span class="material-icons" style="transform: rotate(0deg); font-size: 13px; -webkit-text-stroke-width: 2px; cursor: pointer; color: rgb(255, 0, 0);">close</span></button>
                            </td>
                        </tr>
                        <td colspan="5" class="gray-bg"
                            *ngIf="selectedJobId && overview && overview[selectedJobId] && job.peJobId == selectedJobId">
                            <div class="gray-bg p-3">
                                <table class="my-table  w-100 mat-table">
                                    <thead>
                                        <tr class="mat-header-row">
                                            <th class="mat-header-cell">Status ID</th>
                                            <th class="mat-header-cell">Step</th>
                                            <th class="mat-header-cell">Attempted Number</th>
                                            <th class="mat-header-cell">Start Time</th>
                                            <th class="mat-header-cell">End Time</th>
                                        </tr>

                                    </thead>
                                    <tbody>
                                        <tr class="mat-row" *ngFor="let comp of overview[selectedJobId]">
                                            <td class="mat-cell"> {{comp.statusId}}</td>
                                            <td class="mat-cell"> {{comp.peJobStepName}}</td>
                                            <td class="mat-cell"> {{comp.attemptedNumber}}</td>
                                            <td class="mat-cell"> {{comp.jobStartTime | timezoneDate}}</td>
                                            <td class="mat-cell"> {{comp.jobEndTime | timezoneDate}}</td>
                                        </tr>


                                    </tbody>
                                </table>


                            </div>
                        </td>
                    </ng-container>
                </ng-container>
            </tbody>
        </table>
        <pagination-controls class="float-right" (pageChange)="p = $event"></pagination-controls>
    </div>
</div>