import { Component, OnInit, Inject, ViewChild } from '@angular/core';
import { ApiService } from "../../../services/api.service";
import { ToastrService } from 'ngx-toastr';
import { dateLessThanDate, maxInverterDeductionDate } from '../../../shared/validators';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { DataSource } from '@angular/cdk/table';
import { TableFilterPipe } from '../../../pipe/table-filter.pipe';
import { DatePipe, PercentPipe, CurrencyPipe } from '@angular/common';
import { MatLegacyDialogRef, MAT_LEGACY_DIALOG_DATA } from '@angular/material/legacy-dialog';
import { FormBuilder, FormGroup, Validators } from "@angular/forms";

@Component({
  selector: 'app-inverter-deduction-dialog',
  templateUrl: './inverter-deduction-dialog.component.html',
  styleUrls: ['./inverter-deduction-dialog.component.css']
})

export class InverterDeductionDialogComponent implements OnInit {
  inverterDeductionGroup: Element[] = [];

  constructor(public dialogRef: MatLegacyDialogRef<InverterDeductionDialogComponent>,
    private apiService: ApiService, private toastMsg: ToastrService, @Inject(MAT_LEGACY_DIALOG_DATA) public data: any) { }

  ngOnInit() {
    this.inverterDeductionGroup = this.data.inverterDeductions;
    this.inverterDeductionGroup.sort((a, b) => {
      return <any>new Date(b.effectiveStartDate) - <any>new Date(a.effectiveStartDate);
    });
  }
}

export interface Element {
  effectiveStartDate: string,
  effectiveEndDate: string,
  inverterDeductionRate: string,
  inverterType: string,
  financePartner: string
}