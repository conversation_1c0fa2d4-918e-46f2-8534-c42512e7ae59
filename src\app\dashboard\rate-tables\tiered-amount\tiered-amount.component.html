<div class="page-title col-md-12 ">
    <h1> Tiered Amounts</h1>
    <div class="breadcrumbs"><a href="#">Home</a>/<span>Tiered Amounts</span>
    </div>
</div>
  
  <div class="content">
    <div class="card">
      <div class="card-header-info">
        <h4 class="card-title no-hover-effect">Tiered Amounts</h4>
      </div>
      <div class="card-body">
        <app-grid-mat-table [gridData]="originalDataSource"  
            [columnData]="columnNames" 
            [displayColumnData]="displayedColumns"
            [dateFields]="dateColumns"
            [isScrollWidth]="false"
            [isSearchAvailable]="true"
            (rowClick)="rowClick($event)">
        </app-grid-mat-table>
        <div *ngIf="apiService.checkPermission('CreateRateTables')">
          <a class="btn  btn-primary float-right" *ngIf="!addInd" (click)="onAdd()"><i
              class="material-icons pointer">add_circle</i> Add</a>
          <a class="btn  btn-primary float-right" *ngIf="addInd" (click)="addInd = !addInd"><i
              class="material-icons pointer">remove_circle</i> Hide</a>
        </div>
      </div>
    </div>
    <div class="card" *ngIf="addInd">
      <div class="card-header-info">
        <h4 class="card-title no-hover-effect"><i class="fas fa-plus"></i> Add Tiered Amount</h4>
      </div>
      <div class="card-body">
        <div>
          <form [formGroup]="tierForm" (ngSubmit)="onSubmit()" class="w-100">
            <div class="row" >
              <div class="form-group col-md-4">
                <div class="row">
                  <label class="col-sm-5">Tier</label>
                  <div class="col-sm-7">
                    <select class="custom-select" name="tier_dropdown" formControlName="tierName"
                      data-style="btn btn-link" id="tier_dropdown">
                      <option *ngFor="let tier of tieredData?.BatteryTier" value="{{tier.batteryTierId}}">
                        {{tier.tierName}}</option>
                    </select>
                  </div>
                </div>
              </div>
              <div class="form-group col-md-4">
                <div class="row">
                  <label class="col-sm-5">State</label>
                  <div class="col-sm-7">
                    <select class="custom-select" name="state" formControlName="state"
                      data-style="btn btn-link" id="state">
                      <option *ngFor="let state of stateData.stateCodes" value="{{state.stateCodeId}}">
                        {{state.stateCode1}}</option>
                    </select>
                  </div>
                </div>
              </div>
              <div class="form-group col-md-4">
                <div class="row">
                  <label class="col-sm-5">Amount</label>
                  <div class="col-sm-7">
                    <input type="number" name="amount" formControlName="amount" min="0" class="custom-input">
                  </div>
                </div>
              </div>
              <div class="form-group col-md-4">
                <div class="row">
                  <label class="col-sm-5">Effective Start Date</label>
                  <div class="col-sm-7">
                    <div class="date-picker w-100">
                      <input #AddStartDatePicker type="date" name="start_date" id="start_date" class="custom-input" formControlName="effectiveStartDate" placeholder=""/>
                      <span *ngIf="AddStartDatePicker.value.length > 0" class="mat-icon cal-reset" (click)="clearDate(AddStartDatePicker,'startDate',$event)"><i class="far fa-calendar-times"></i></span>
                      <span *ngIf="AddStartDatePicker.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="form-group col-md-4">
                <div class="row">
                  <label class="col-sm-5">Effective End Date</label>
                  <div class="col-sm-7">
                    <div class="date-picker w-100">
                      <input #AddEndDatePicker type="date" name="effectiveEndDate" id="effectiveEndDate" class="custom-input" formControlName="effectiveEndDate" placeholder=""/>
                      <span *ngIf="AddEndDatePicker.value.length > 0" class="mat-icon cal-reset" (click)="clearDate(AddEndDatePicker,'endDate',$event)"><i class="far fa-calendar-times"></i></span>
                      <span *ngIf="AddEndDatePicker.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span>
                    </div>
                  </div>
                </div>
              </div>
            </div>  
  
              
            <div class="row align-button-right">
              <button type="submit" class="btn btn-primary" [disabled]="tierForm.invalid"><i class="fas fa-plus"></i> Add Tiered Amount</button>
            </div>
          </form>
        </div>
      </div>
    </div>
 </div>
    