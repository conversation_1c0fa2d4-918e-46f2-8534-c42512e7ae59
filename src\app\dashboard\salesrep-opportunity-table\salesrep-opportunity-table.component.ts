import { Component, OnInit, ViewChild, Input, SimpleChanges, Output, EventEmitter, ElementRef } from '@angular/core';
import { MatLegacyPaginator } from '@angular/material/legacy-paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { IMyOpportinity } from 'src/app/model/pay-book.model';
import { Sort, MatSortModule} from '@angular/material/sort';

@Component({
  selector: 'app-salesrep-opportunity-table',
  templateUrl: './salesrep-opportunity-table.component.html',
  styleUrls: ['./salesrep-opportunity-table.component.css']
})
export class SalesrepOpportunityTableComponent implements OnInit {
  @Input() opportunities: IMyOpportinity[] = [];
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatLegacyPaginator, {static: true}) paginator: MatLegacyPaginator;
  pageSizeOptions: number[] = [5, 10, 25, 100];
  dataSource: MatTableDataSource<IMyOpportinity> = new MatTableDataSource();
  searchText: string = "";
  columns: string[] = ["opportunityName", "demoDate", "dateContractSigned", "actualInstallDate", "stage", "systemSize", "appointmentConfirmed"];
  
  constructor(private el: ElementRef) { }

  ngOnChanges(changes: SimpleChanges) {
    if (changes && changes.opportunities) {
      this.dataSource = new MatTableDataSource(changes.opportunities.currentValue);
      this.dataSource.sort = this.sort;
      this.dataSource.sortingDataAccessor = (item, property) => {
        switch (property) {
          case 'demoDate': return item.demoDate === ''? new Date(null) : new Date(item.demoDate);
          case 'dateContractSigned': return item.dateContractSigned === ''? new Date(null) : new Date(item.dateContractSigned);
          default: return item[property];
        }
      };      
      this.dataSource.paginator = this.paginator;
    }
  }
 

applyFilter(input: string): void;

  applyFilter(input: Event): void;

  applyFilter(input: any): any {
    var filterValue: string;
    if (typeof input === "string") {
      filterValue = input;
    } else {
      filterValue = (input.target as HTMLInputElement).value;
    }
    this.dataSource.filter = filterValue.trim().toLowerCase();
  }

  ngOnInit() {

  }
}