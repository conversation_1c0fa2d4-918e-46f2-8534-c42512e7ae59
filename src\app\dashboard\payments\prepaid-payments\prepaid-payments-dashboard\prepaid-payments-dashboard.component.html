<div class="page-title col-md-12 ">
   <h1 class="">Prepaid Payments</h1>
   <div class="breadcrumbs"><a href="#">Home</a>/<span>Prepaid Payments</span>
   </div>
</div>
<div class="content">
   <div class="row">
      <div class="col-md-12 mt-2">
         <div class="card" style='min-height:800px'>
            <div class="card-header-info">
               <h4 class="card-title">Prepaid Payments</h4>
            </div>
            <div class="container-fluid">
               <div class="row">
                  <div class="col-md-12">
                     <div class=" w-100">
                        <div class="content">
                           <div class="card">
                              <div class="card-header-info">
                                 <h4 class="card-title">Filters</h4>
                              </div>
                              <div class="card-body">
                                <div class="row filter-row">
                                 <div class="form-group col-md-2">
                                    <label>Sales Division(s)</label>
                                    <ng-multiselect-dropdown
                                    [placeholder]="'Not Selected'"
                                    [settings]="dropdownSettings"
                                    [data]="salesDivisions"
                                    [(ngModel)]="selectedDivision"
                                    (onSelect)="onItemSelect()"
                                    (onSelectAll)="onSelectAll($event, '')"
                                    >
                                    </ng-multiselect-dropdown> 
                                  </div>
                                  <div class="form-group col-md-2">
                                    <label>Sales Office(s)</label>
                                    <ng-multiselect-dropdown
                                    [placeholder]="'Not Selected'"
                                    [settings]="dropdownSettings"
                                    [data]="salesOffices"
                                    [(ngModel)]="selectedOffices"
                                    (onSelect)="onItemSelect()"
                                    (onDeSelect)="onItemDeSelect()"
                                    (onSelectAll)="onSelectAll($event, 'salesOffices')"
                                    (onDeSelectAll)="onDeSelectAll($event, 'salesOffices')"
                                    [disabled]="officeDisabled"
                                    >
                                    </ng-multiselect-dropdown> 
                                  </div>
                                  <div class="form-group col-md-2">
                                    <label>Sales Territory(s)</label>
                                    <ng-multiselect-dropdown
                                    [placeholder]="'Not Selected'"
                                    [settings]="dropdownSettings"
                                    [data]="salesTerritories"
                                    [(ngModel)]="selectedTerritories"
                                    (onSelect)="onItemSelect()"
                                    (onDeSelect)="onItemDeSelect()"
                                    (onSelectAll)="onSelectAll($event, 'salesTerritories')"
                                    (onDeSelectAll)="onDeSelectAll($event, 'salesTerritories')"
                                    [disabled]="territoryDisabled"
                                    >
                                    </ng-multiselect-dropdown> 
                                  </div>
                                  <div class="form-group col-md-2">
                                    <label>Contact(s)</label>
                                    <ng-multiselect-dropdown
                                    [placeholder]="'Not Selected'"
                                    [settings]="contactDropdownSettings"
                                    [data]="contacts"
                                    [(ngModel)]="selectedContacts"
                                    (onSelect)="onItemSelect()"
                                    (onSelectAll)="onSelectAll($event, '')"
                                    >
                                    </ng-multiselect-dropdown> 
                                 </div>  
                                 <div class="form-group col-md-2">
                                    <label>Employment Status(es)</label>
                                    <ng-multiselect-dropdown
                                    [placeholder]="'Not Selected'"
                                    [settings]="dropdownSettings"
                                    [data]="employmentStatuses"
                                    [(ngModel)]="selectedEmploymentStatuses"
                                    (onSelect)="onItemSelect()"
                                    (onSelectAll)="onSelectAll($event, '')"
                                    >
                                    </ng-multiselect-dropdown> 
                                  </div>
                                  <div class="form-group col-md-2">
                                    <label>Team(s)</label>
                                    <ng-multiselect-dropdown
                                    [placeholder]="'Not Selected'"
                                    [settings]="dropdownSettings"
                                    [data]="teams"
                                    [(ngModel)]="selectedTeams"
                                    (onSelect)="onItemSelect()"
                                    (onSelectAll)="onSelectAll($event, '')"
                                    >
                                    </ng-multiselect-dropdown> 
                                  </div>                                                                   
                                </div>   
                              <div class="row filter-row">
                                 <!-- <div class="form-group col-md-2">
                                 <label>Date Filter</label>
                                 <select class="custom-select hover" [(ngModel)]="selectedDateFilter" (change)="onChangeDateFilter()">
                                    <option value="Not Selected">Not Selected</option>
                                    <option *ngFor="let d of dateFilters" [value]="d">
                                       {{d}}
                                    </option>
                                 </select>                                    
                                 </div>  -->
                                 <div class="form-group col-md-2">
                                 <label>Install Date - From</label>
                                 <div class="input-group date-picker">
                                       <input #datepickerInput type="date" min="{{minDate | date: 'yyyy-MM-dd'}}"  class="custom-input" [(ngModel)]="dateFrom" [disabled]="dateFiltersDisabled"
                                       >
                                       <span *ngIf="datepickerInput.value.length > 0" class="mat-icon cal-reset" (click)="this.dateFrom = null;"><i class="far fa-calendar-times"></i></span> 
                                       <span *ngIf="datepickerInput.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span> 
                                 </div>
                                 </div> 
                                 <div class="form-group col-md-2">
                                 <label>Install Date - To</label>
                                 <div class="input-group date-picker">
                                       <input #datepickerInput1 type="date" min="{{minDate | date: 'yyyy-MM-dd'}}"  class="custom-input" [(ngModel)]="dateTo" [disabled]="dateFiltersDisabled"
                                       >
                                       <span *ngIf="datepickerInput1.value.length > 0" class="mat-icon cal-reset" (click)="this.dateTo = null;"><i class="far fa-calendar-times"></i></span> 
                                       <span *ngIf="datepickerInput1.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span> 
                                 </div>
                                 </div>   
                                  <div class="form-group col-md-2">
                                    <label>Group By</label>
                                    <ng-multiselect-dropdown
                                    [placeholder]="'Not Selected'"
                                    [settings]="groupByDropdownSettings"
                                    [data]="groupingOptions"
                                    [(ngModel)]="selectedGroupingOptions"
                                    (onSelect)="onItemSelect()"
                                    (onSelectAll)="onSelectAll($event, '')"
                                    (onDeSelect)="onItemDeSelect()"
                                    >
                                    </ng-multiselect-dropdown> 
                                  </div> 
                                  <div class="form-group col-md-2">
                                    <label>Page Size</label>
                                    <input type="text" class="custom-input"  [(ngModel)]="pageSize" [value]="pageSize">
                                  </div>
                                  <div class="form-group col-md-2">
                                    <label>Page Number</label>
                                    <input type="text" class="custom-input" [(ngModel)]="pageNumber" [value]="pageNumber">
                                  </div>
                                </div>
                                <div class="form-group col-md-2" style="margin: 0; transform: translateY(1%); padding: 0px; justify-self: flex-end; width: 235px;">
                                    <mat-radio-group [(ngModel)]="selectedReport" (change)="onChangeReportType()">
                                       <mat-radio-button style="margin-right: 15px" [value]="'prepaid'">Prepaid</mat-radio-button>
                                       <mat-radio-button [value]="'accrual'">Accrual</mat-radio-button>
                                    </mat-radio-group>
                                    <div *ngIf="selectedReport === 'accrual'" matTooltip="Defaulted to BP Payment 2/Install Payments" matTooltipPosition="right">
                                       <mat-checkbox [(ngModel)]="allPayments"> All Payments </mat-checkbox>
                                    </div>
                                    <button class="btn btn-primary payments-button-group" (click)="getPayments()">
                                       <i class="material-icons">filter_list</i> 
                                       Search
                                    </button>                                     
                                    <button class="btn btn-primary payments-button-group" (click)="getExcelWorksheet()">
                                       <i class="material-icons">save_alt</i> 
                                       Export
                                    </button>                                    
                                </div>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
            <div class="container-fluid">
               <div class="row">
                  <div class="col-md-12">
                     <div class=" w-100">
                        <div class="content">
                           <div class="card" style="overflow: auto; max-height: 650px;">
                              <div class="card-header-info" style="position: sticky; top: 0; z-index: 1;">
                                 <h4 class="card-title">{{selectedReport == "prepaid" ? "Prepaid " : "Accrual "}} Details</h4>
                              </div>
                              <div class="card-body">
                                 <mat-table #table [dataSource]="paymentDataSource">
                                 <ng-container matColumnDef="{{column.id}}" *ngFor="let column of filteredColumnNames; let i = index">
                                    <mat-header-cell *matHeaderCellDef class="table-header" (mouseenter)="column.isHovered = true" (mouseleave)="column.isHovered = false" 
                                    (click)="onSort(column, i)" style="overflow:visible !important;"> {{column.value}}
                                       <div class="sort-header">
                                       <mat-icon-button class="icon-button">
                                          <ng-container *ngIf="column.isHovered">
                                             <ng-container>
                                             <ng-container *ngIf="column.sortOrder === 'desc'">                                                    
                                                <i class="fa fa-arrow-down" aria-hidden="true"></i> 
                                             </ng-container>                        
                                             <ng-container *ngIf="column.sortOrder === 'asc'">
                                                <i class="fa fa-arrow-up" aria-hidden="true"></i>                           
                                             </ng-container>                        
                                             </ng-container>                      
                                          </ng-container>
                                       </mat-icon-button>
                                 
                                       </div>                                       
                                    </mat-header-cell>
                                    <mat-cell [attr.data-td-head]="column.value" *matCellDef="let element">
                                    <ng-container *ngIf="column.value == 'Opportunity'; else contactName">
                                       <a [routerLink]="['/ui/commissions/opportunitydetails', element.opportunity_Id]">{{element[column.id]}}</a>
                                    </ng-container>
                                    <ng-template #contactName>
                                       <ng-container *ngIf="column.value == 'Contact Legal Name'; else commission">
                                          <a [routerLink]="['/ui/commissions/salesrep', element.contact_Id]">{{element[column.id]}}</a>
                                       </ng-container>
                                    </ng-template>                             
                                    <ng-template #commission>
                                       <ng-container *ngIf="column.value == 'Commission'; else common">
                                          <a [routerLink]="['/ui/commissions/commission', element.commission_Id]">{{element[column.id]}}</a>
                                       </ng-container>
                                    </ng-template>
                                    <ng-template [ngSwitch]="column.type" #common>
                                       <span *ngSwitchCase="'date'">{{ element[column.id] | date:'MM/dd/yyyy' }}</span>
                                       <span *ngSwitchCase="'currency'">{{ element[column.id] | currency:'USD':'symbol':'1.2-2' }}</span>
                                       <span *ngSwitchDefault>{{ element[column.id] }} {{ column.type}}</span>
                                    </ng-template>
                                    </mat-cell>
                                    <mat-footer-cell *matFooterCellDef class="mat-cell">
                                       <ng-container [ngSwitch]="column.id">
                                          <span *ngSwitchCase="'balance'">Total: {{totalBalance | currency:'USD':'symbol':'1.2-2' }}</span>
                                          <span *ngSwitchCase="'open_Payments'">Total: {{totalOpenPayments | currency:'USD':'symbol':'1.2-2' }}</span>
                                          <span *ngSwitchCase="'open_Payment_Reclaims'">Total: {{totalOpenPaymentReclaims | currency:'USD':'symbol':'1.2-2' }}</span>
                                          <span *ngSwitchCase="'amount'">Total: {{totalAmount | currency:'USD':'symbol':'1.2-2' }}</span>
                                          <span *ngSwitchDefault></span>
                                       </ng-container>
                                    </mat-footer-cell>
                                 </ng-container>
                                 <mat-header-row *matHeaderRowDef="displayedColumns" class="mat-header-sticky"></mat-header-row>
                                 <mat-row *matRowDef="let row; columns: displayedColumns;" class="pointer table-content"
                                    ></mat-row>
                                 <mat-footer-row *matFooterRowDef="displayedColumns" class="mat-footer-sticky-row"></mat-footer-row>
                                 </mat-table>
                                 <div class="text-center p-2" *ngIf="paymentDataSource && paymentDataSource.length === 0">
                                    <h5>No Data Found</h5>
                                 </div>
                                 <div class="paginator-container mat-footer-sticky">
                                    <div class="mat-paginator">
                                       Page: {{ pageNumber }} of {{ totalCount && totalCount > 0 ? Math.ceil(totalCount / pageSize) : 1}}
                                    </div>                                      
                                    <mat-paginator #paginator [length]="totalCount" [pageSize]="pageSize" (page)="onPageChange($event)" [pageIndex]="pageNumber-1" showFirstLastButtons></mat-paginator>       
                                 </div>                                     
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </div>
   </div>
</div>