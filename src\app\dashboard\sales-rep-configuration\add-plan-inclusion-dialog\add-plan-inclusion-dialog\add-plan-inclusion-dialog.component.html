<div class="dailog-title-bg">
    <div class="dailog-title"><i class="fas fa-dollar-sign"></i> Add Plan Inclusion<button class="dailog-close"
            [mat-dialog-close]><span>X</span></button>
    </div>
  </div>
  <div class="card-body">
    <div>
      <form [formGroup]="planInclusionForm" (ngSubmit)="submit()" class="w-100">
        <div class="row">
            <div class="form-group col-md-4">
              <div class="row">
                <label class="col-sm-5">Inclusion Rule Type</label>
                <div class="col-sm-7">
                  <select class="custom-select hover" formControlName="selectedInclusionRuleType">
                    <option *ngFor="let type of planInclusionRuleTypes" value="{{ type.id }}"> {{ type.value }} </option>
                </select>
                </div>
              </div>
            </div>

            <div class="form-group col-md-4" *ngIf="planInclusionForm.controls['selectedInclusionRuleType'].value == 0 || planInclusionForm.controls['selectedInclusionRuleType'].value == 3 else incentives">
              <div class="row">
                <label class="col-sm-5">Commission Rule</label>
                <div class="col-sm-7">
                    <select class="custom-select hover" formControlName="selectedInclusionRuleId">
                        <option *ngFor="let rule of activeRules" [value]="rule.commissionRuleId">{{rule.commissionRuleName}}</option>
                    </select>
                </div>
              </div>
            </div>
            <ng-template #incentives>
              <div class="form-group col-md-4">
                <div class="row">
                  <label class="col-sm-3">Incentive</label>
                  <div class="col-sm-9">
                      <select class="custom-select hover" formControlName="selectedInclusionRuleTypeId">
                          <option *ngFor="let rule of activeRules" [value]="rule.commissionRuleTypeId">{{rule.commissionRuleTypeName}}</option>
                      </select>
                  </div>
                </div>
              </div>
            </ng-template>

            <div class="form-group col-md-4">
              <div class="row">
                <label class="col-sm-5">Effective Start Date</label>
                <div class="col-sm-7">
                  <div class="date-picker w-100">
                    <input #AddStartDatePicker type="date" name="start_date" id="start_date" class="custom-input" formControlName="effectiveStartDate" placeholder=""/>
                    <span *ngIf="AddStartDatePicker.value.length > 0" class="mat-icon cal-reset" (click)="clearStartDate(AddStartDatePicker)"><i class="far fa-calendar-times"></i></span>
                    <span *ngIf="AddStartDatePicker.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span>
                  </div>
                </div>
              </div>
            </div>

            <div class="form-group col-md-4">
              <div class="row">
                <label class="col-sm-5">Effective End Date</label>
                <div class="col-sm-7">
                  <div class="date-picker w-100">
                    <input #AddEndDatePicker type="date" name="effectiveEndDate" id="effectiveEndDate" class="custom-input" formControlName="effectiveEndDate" placeholder=""/>
                    <span *ngIf="AddEndDatePicker.value.length > 0" class="mat-icon cal-reset" (click)="clearEndDate(AddEndDatePicker)"><i class="far fa-calendar-times"></i></span>
                    <span *ngIf="AddEndDatePicker.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span>
                  </div>
                </div>
              </div>
            </div>            
        </div>

        <div *ngIf="enableBpForm" style="margin-top: 10px;">
          <div class="dailog-title-bg">
            <div class="dailog-title">Add Base Pay Structure</div>
          </div>
        
          <div class="form-group row">
            <div class="col-md-4">
                <div class="row">
                    <div class="col-10">
                        <select class="custom-select hover" formControlName="selectedRuleId" (change)="onSelect()">
                            <option [value]="null">Base Pay Structure</option>
                            <option *ngFor="let rule of basePayStructureRules[0].rules" [value]="rule.ruleId">
                                {{rule.ruleName}}</option>
                        </select>
                    </div>
                </div>
            </div>
          
            <div class="col-md-6">
                <div class="row">
                    <label class="col-4" style="margin-top: 5px;">Base Pay Rule Mapping</label>
                    <div class="col-7">
                        <select class="custom-select hover" formControlName="selectedBasePayRuleId" (change)="onSelect()">
                            <option [value]="getSelectedBasePayRule().commissionRuleId">
                                {{getSelectedBasePayRule().commissionRuleName}}</option>
                        </select>
                    </div>
                </div>
            </div>
          </div>
        
          <!-- Rule Preview -->
          <!-- Prompts -->
          <div class="row mt-3" *ngIf="prompts">
              <div class="col-md-12">
                  <!-- <ng-container *ngFor="let prompt of prompts"> -->
                  <!-- <ng-container
                                  *ngIf="prompt.ruleTypeName == 'Base Pay Structure'; else elseBlock"> -->
                  <ng-container
                      *ngIf="basePayStructureRules && basePayStructureRules[0].ruleTypeName == 'Base Pay Structure'">
                      <app-base-pay-structure-prompt [(rule)]="prompts" (rulePrompt)="onRulePromptChange($event)">
                      </app-base-pay-structure-prompt>
                  </ng-container>
                  <!-- </ng-container>
                              <ng-template #elseBlock>
                          <ng-container
                              *ngIf="data.employeeIncentiveRules && data.employeeIncentiveRules[0].ruleTypeName == 'Employee Incentive'">
                              <app-employee-incentive-prompt [(rule)]="prompts" (rulePrompt)="onRulePromptChange($event)">
                              </app-employee-incentive-prompt>
                          </ng-container>
                          </ng-template> -->
                  <!-- </ng-container> -->
              </div>
          </div>
          <div class="text-right">
              <p style="color: red" *ngIf="basePayStructurePrompt != null && basePayStructurePrompt != undefined && !checkValidDates()">Dates must be in chronological order</p>
              <p style="color: red" *ngIf="basePayStructurePrompt != null && basePayStructurePrompt != undefined && !checkAllPromptsEntered()">All prompts must be filled</p>
          </div>
          <!-- <div class="row justify-content-end">
              <button class="btn btn-primary" [mat-dialog-close]="addOn"
                  [disabled]="!checkAllPromptsEntered() || !checkValidDates()"><i class="fas fa-check"></i> OK</button>
          </div> -->
        </div>

        <div class="row align-button-right" style="margin-bottom: -40px">
          <button type="submit" class="btn btn-primary" name="edit" [disabled]="planInclusionForm.invalid || existingPlanBpInclusion()">
            <i class="fas fa-plus"></i> Submit
          </button>
        </div>
      </form>
    </div>
 </div>