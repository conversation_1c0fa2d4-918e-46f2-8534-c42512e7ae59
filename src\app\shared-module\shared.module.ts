import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ConfirmationModalComponent } from '../confirmation-modal/confirmation-modal.component';
import { CommissionsSidebarComponent } from '../dashboard/commissions-sidebar/commissions-sidebar.component';
import { RouterModule } from '@angular/router';
import { GoBackComponent } from '../go-back/go-back.component';
import { LoginDialogComponent } from '../login-dialog/login-dialog.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatLegacyDialogModule } from '@angular/material/legacy-dialog';
import { InverterDeductionDialogComponent } from '../dashboard/rate-tables/inverter-deduction-form/inverter-deduction-dialog.component';
import { FinancePartnerDeductionDialogComponent } from '../dashboard/rate-tables/finance-partner-deduction-dialog/finance-partner-deduction-dialog.component';
import { TerritoryRateMaintenanceDialogComponent } from '../dashboard/rate-tables/territory-rate-maintenance-dialog/territory-rate-maintenance-dialog.component';
import { BatteryRateMaintenanceDialogComponent } from '../dashboard/rate-tables/Battery_Commission_Rate-maintenance-dialog/Battery_Commission_Rate-maintenance-dialog.component';
import { BatteryRetrofitCommissionRateMaintenanceDialogComponent } from '../dashboard/rate-tables/battery-retrofit-commission-rate-maintenance-dialog/battery-retrofit-commission-rate-maintenance-dialog.component'
import { ModuleDeductionMaintenanceDialogComponent } from '../dashboard/rate-tables/module-deduction-maintenance-dialog/module-deduction-maintenance-dialog.component';
import { PurchaseMethodDeductionMaintenanceDialogComponent } from '../dashboard/rate-tables/purchase-method-deduction-maintenance-dialog/purchase-method-deduction-maintenance-dialog.component';
import { ProductPurchaseMethodDeductionMaintenanceDialogComponent } from '../dashboard/rate-tables/product-purchase-method-deduction-maintenance-dialog/product-purchase-method-deduction-maintenance-dialog.component';
import { PpaBonusRateMaintenanceDialogComponent } from '../dashboard/rate-tables/ppa-bonus-rate-maintenance-dialog/ppa-bonus-rate-maintenance-dialog.component';
import { PpwBonusRateMaintenanceDialogComponent } from '../dashboard/rate-tables/ppw-bonus-rate-maintenance-dialog/ppw-bonus-rate-maintenance-dialog.component';
import { TieredOverageMaintenanceDialogComponent } from '../dashboard/rate-tables/teired-overage-dialog/teired-overage-dialog.component';
import { OutReachConfigurationMaintenanceDialogComponent } from '../dashboard/rate-tables/out-reach-configuration-maintenance-dialog/out-reach-configuration-maintenance-dialog.component';
import { PermitDeductionMaintenanceDialogComponent } from '../dashboard/rate-tables/permit-deduction-maintenance-dialog/permit-deduction-maintenance-dialog.component';
import { InstallationTypeDeductionMaintenanceDialogComponent } from '../dashboard/rate-tables/installation-type-deduction-maintenance-dialog/installation-type-deduction-maintenance-dialog.component';
import { ShowRuleComponent } from '../dashboard/sales-rep-configuration/show-rule/show-rule.component';
import { ShowRuleTypeComponent } from '../dashboard/sales-rep-configuration/show-rule-type/show-rule-type.component';
import { VersionDialogComponent } from '../header/version-dialog/version-dialog.component';
import { ProductRateDialogComponent } from '../dashboard/rate-tables/product-rate-dialog/product-rate-dialog.component';
import { MatLegacyCheckboxModule } from '@angular/material/legacy-checkbox';
import { MatLegacyFormFieldModule } from '@angular/material/legacy-form-field';
import { MatTableModule } from '@angular/material/table';
import { PPABonusFlatRateMaintenanceDialogComponent } from '../dashboard/rate-tables/ppa-bonus-flat-rate-maintenance-dialog/ppa-bonus-flat-rate-maintenance-dialog.component';

@NgModule({
    declarations: [
        ConfirmationModalComponent,
        CommissionsSidebarComponent,
        GoBackComponent,
        LoginDialogComponent,
        InverterDeductionDialogComponent,
        FinancePartnerDeductionDialogComponent,
        TerritoryRateMaintenanceDialogComponent,
        BatteryRateMaintenanceDialogComponent,
        BatteryRetrofitCommissionRateMaintenanceDialogComponent,
        ModuleDeductionMaintenanceDialogComponent,
        PurchaseMethodDeductionMaintenanceDialogComponent,
        ProductPurchaseMethodDeductionMaintenanceDialogComponent,
        PpaBonusRateMaintenanceDialogComponent,
        PpwBonusRateMaintenanceDialogComponent,
        TieredOverageMaintenanceDialogComponent,
        OutReachConfigurationMaintenanceDialogComponent,
        PermitDeductionMaintenanceDialogComponent,
        InstallationTypeDeductionMaintenanceDialogComponent,
        ShowRuleComponent,
        ShowRuleTypeComponent,
        VersionDialogComponent,
        ProductRateDialogComponent,
        PPABonusFlatRateMaintenanceDialogComponent
    ],
    imports: [
        CommonModule,
        RouterModule,
        FormsModule,
        ReactiveFormsModule.withConfig({callSetDisabledState: 'whenDisabledForLegacyCode'}),
        MatLegacyDialogModule,
        MatTableModule,
        MatLegacyCheckboxModule,
        MatLegacyFormFieldModule ,
    ],
    exports: [
        ConfirmationModalComponent,
        CommissionsSidebarComponent,
        GoBackComponent,
        LoginDialogComponent,
        ShowRuleComponent,
        ShowRuleTypeComponent
    ]
})
export class SharedModule { }
