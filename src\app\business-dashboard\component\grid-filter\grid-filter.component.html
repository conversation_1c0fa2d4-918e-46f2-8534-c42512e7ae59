
<div mat-dialog-content>
    <div class="row">
        <select class="custom-select custom-border-rad" name="filterBy" [(ngModel)]="filterOperator"
        data-style="btn btn-link">
        <option value="contains" *ngIf="data.data3.dataType!=='Date'">Contains</option>
        <option value="notcontains" *ngIf="data.data3.dataType!=='Date'">Not Contains</option>
        <option value="equals">Equals</option>
        <option value="notequals">Not Equals</option>
      </select>
    </div>
    <div class="row">
        <input *ngIf="data.data3.dataType==='Date';else normalTextbox" type="date"  class="custom-input custom-border-rad" [(ngModel)]="inputDate" (change)="onChangeDate($event)">
        <ng-template #normalTextbox>
            <input type="text" class="custom-input custom-border-rad" name="filterName" autocomplete="off" [(ngModel)]="filterValue" (keydown.enter)="OnChangeFilterValue($event)"/>
        </ng-template>
    </div>
    
</div>
