import { Component, OnInit, ViewChild, Input } from '@angular/core';
import { MatLegacyDialog } from '@angular/material/legacy-dialog';
import { MatLegacyPaginator } from '@angular/material/legacy-paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { IPayment, IPaymentElement } from 'src/app/model/payment.model';
import { ApiService } from 'src/app/services/api.service';
import { ToastrService } from 'ngx-toastr';
import { HttpClient } from '@angular/common/http';
import { ApiResponse } from 'src/app/services/api.response';
import { environment } from 'src/environments/environment';
import { PaymentHistoryDialogComponent } from './payment-history-dialog/payment-history-dialog.component';
import { IPaymentHistory } from 'src/app/model/payment-history.model';
import { PaymentNoteModelComponent } from './payment-note-model/payment-note-model.component';

@Component({
  selector: 'app-commission-payments',
  templateUrl: './payments.component.html',
  styleUrls: ['./payments.component.css']
})
export class CommissionPaymentsComponent implements OnInit {
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatLegacyPaginator, { static: true }) paginator: MatLegacyPaginator;
  @Input() evaluateCommission: boolean;
 
  @Input() commissionId: number;
  showPayments: boolean = false;
  pageSizeOptions: number[] = [5, 10, 25, 100];
  paymentElements: MatTableDataSource<IPayment> = new MatTableDataSource();
  paymentColumns: string[] = ["paymentTypeName", "commissionTypeName", "amount", "paymentStatusName", "paymentDueDate", "paymentNote", "modifiedUser"];
  evaluatePaymentColumns: string[] = ["paymentTypeName", "commissionTypeName", "amount", "paymentStatusName", "paymentDueDate", "paymentNote", "modifiedUser"]

  constructor(public apiService: ApiService, 
    private toastMsg: ToastrService, 
    private http: HttpClient, 
    public dialog: MatLegacyDialog) { }

  ngOnInit() {
    if (this.evaluateCommission === false) {
      this.paymentColumns.push("paymentHistory");
    }
    this.getPayments(this.commissionId);
  }

  getPayments(commissionId: number) {
    this.http.get<ApiResponse>(`${environment.apiBaseUrl}Payments/ByCommission/${commissionId}/${this.evaluateCommission}`)
      .subscribe(data => {
        if (data && data.result) {
          var payments = data.result.map((pay: IPayment) => { return <IPaymentElement>{
            paymentId: pay.paymentId,
            paymentTypeName: pay.paymentTypeName,
            commissionTypeName: pay.commissionTypeName,
            amount: pay.amount,
            paymentStatusName: pay.paymentStatus.paymentStatusName,
            modifiedUser: pay.userModifiedId,
            paymentDueDate: pay.paymentDueDate,
            paymentNote: pay.paymentNote
          } });
          this.paymentElements = new MatTableDataSource(payments);
          this.paymentElements.sort = this.sort;
          this.paymentElements.paginator = this.paginator;
        }
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      })
      
  }

  getPaymentHistory(paymentId: number) {
    this.http.get(`${this.apiService.baseUrl}PaymentHistory/${paymentId}`)
      .subscribe((data: any) => {
        if (data) {
          console.log('Payment history', data);
          var paymentHistory = data;
          var paymentHistoryElements = new MatTableDataSource(paymentHistory);
          
          const dialogRef = this.dialog.open(PaymentHistoryDialogComponent, {
            data: {paymentHistory: paymentHistoryElements}
          });

          dialogRef.afterClosed().subscribe(result => {
            console.log('The dialog was closed');
          });
        }
      }, (err) => {
        this.toastMsg.error(err.message, "Error!");
      })
  }

  openDialog(paymentId: number): void {
    
    const dialogRef = this.dialog.open(PaymentHistoryDialogComponent, {
      data: {paymentId: paymentId}
    });

    dialogRef.afterClosed().subscribe(result => {
      console.log('The dialog was closed');
    });
  }

  openPaymentNoteModel(paymentId:number,paymentNote:string){
    const dialogRef = this.dialog.open(PaymentNoteModelComponent,{
      panelClass: 'roundEdges',
      data: {
        paymentId: paymentId,
        paymentNote: paymentNote,
        url:'Payments/UpdatePaymentNote'
      }
    });
    dialogRef.afterClosed().subscribe(result => {
      this.getPayments(this.commissionId);
    });
  }
}