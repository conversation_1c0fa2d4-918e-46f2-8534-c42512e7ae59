import { DatePipe } from '@angular/common';
import { Component, OnInit, ViewChild } from '@angular/core';
import { IdateRange } from '../../models/models';

@Component({
  selector: 'app-dashboard-home',
  templateUrl: './dashboard-home.component.html',
  styleUrls: ['./dashboard-home.component.css']
})
export class DashboardHomeComponent implements OnInit {

  selectedTab = 0;
  data: any[] = [];
  fromDate: string;
  toDate: string;
  selectedTimeRange = 'week';
  dateRange: IdateRange;
  currentDateRange: IdateRange;
  selectedDateRange: IdateRange;
  isSelectedMonth = false;
  isSelectedYear = false;
  isSelectedWeek = false;
  year:string;
  monthNames = [
    "Jan", "Feb", "Mar", "Apr", "May", "Jun",
    "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"
  ];
  month:string;

  constructor(private datePipe: DatePipe) { }

  ngOnInit() {
    this.fetchData(this.selectedTimeRange)
  }

  OnChangeTab(event) {
    this.selectedTab = event.index;
  }
  onDisplayMonthYear(){
    this.month = this.monthNames[new Date(this.selectedDateRange.startDate).getMonth()];
    this.year = new Date(this.selectedDateRange.startDate).getFullYear().toString();
  }
  fetchData(timeRange: string) {
    this.selectedTimeRange = timeRange;
    if (timeRange == 'month') {
      this.isSelectedMonth = true;
      this.isSelectedYear = false;
      this.isSelectedWeek = false;
    }
    else if (timeRange == 'year') {
      this.isSelectedMonth = false;
      this.isSelectedYear = true;
      this.isSelectedWeek = false;
    }
    else {
      this.isSelectedMonth = false;
      this.isSelectedYear = false;
      this.isSelectedWeek = true;
    }
    // Generate sample data and set fromDate and toDate based on the selected time range
    const { startDate, endDate } = this.generateSampleData(timeRange);

    // this.data = generatedData;

    this.fromDate = this.formatDate(startDate);
    this.toDate = this.formatDate(endDate);
    this.dateRange = {
      startDate: this.fromDate,
      endDate: this.toDate
    }

    this.currentDateRange = this.dateRange;
    this.selectedDateRange = this.dateRange;
    this.onDisplayMonthYear();

  }

  previous() {
    const currentDate = new Date(this.selectedDateRange.startDate);
  
    switch (this.selectedTimeRange) {
      case 'week':
        currentDate.setDate(currentDate.getDate() - 7);
        break;
      case 'month':
        currentDate.setMonth(currentDate.getMonth() - 1);
        break;
      case 'year':
        currentDate.setFullYear(currentDate.getFullYear() - 1);
        break;
    }
  
    const endDate = new Date(currentDate);
  
    switch (this.selectedTimeRange) {
      case 'week':
        endDate.setDate(endDate.getDate() + 6);
        break;
      case 'month':
        endDate.setMonth(endDate.getMonth() + 1, 0);
        break;
      case 'year':
        endDate.setFullYear(endDate.getFullYear(), 11, 31);
        break;
    }
  
    this.fromDate = this.formatDate(currentDate);
    this.toDate = this.formatDate(endDate);
  
    this.selectedDateRange = {
      startDate: this.fromDate,
      endDate: this.toDate
    };
    this.onDisplayMonthYear();
  }

  // Function to get the next week's dates
  next() {
    let currentDate;
    let endDate;
    if (this.selectedTimeRange == 'week') {
      currentDate = new Date(this.toDate);
      currentDate.setDate(currentDate.getDate() + 1);
      this.fromDate = this.formatDate(currentDate);

      endDate = new Date(this.fromDate);
      endDate.setDate(endDate.getDate() + 6);
      this.toDate = this.formatDate(endDate);
      this.selectedDateRange = {
        startDate: this.fromDate,
        endDate: this.toDate
      }
    } else if (this.selectedTimeRange == 'month') {
      currentDate = new Date(this.fromDate);
      currentDate.setMonth(currentDate.getMonth() + 1);
      this.fromDate = this.formatDate(currentDate);

      endDate = new Date(this.fromDate);
      endDate.setMonth(endDate.getMonth() + 1, 0);
      this.toDate = this.formatDate(endDate);
      this.selectedDateRange = {
        startDate: this.fromDate,
        endDate: this.toDate
      }
    } else if (this.selectedTimeRange == 'year') {
      currentDate = new Date(this.fromDate);
      currentDate.setFullYear(currentDate.getFullYear() + 1);
      this.fromDate = this.formatDate(currentDate);

      endDate = new Date(this.fromDate);
      endDate.setFullYear(endDate.getFullYear(), 11, 31);
      this.toDate = this.formatDate(endDate);
      this.selectedDateRange = {
        startDate: this.fromDate,
        endDate: this.toDate
      }      
    }
    this.onDisplayMonthYear();

    // this.fetchData('week');
  }

  private generateSampleData(timeRange: string): { startDate: Date, endDate: Date } {
    const currentDate = new Date();
    let startDate = new Date();
    let endDate = new Date();

    switch (timeRange) {
      case 'week':
        // Set startDate to the most recent Friday and endDate to the upcoming Thursday
        startDate.setDate(currentDate.getDate() - (currentDate.getDay()+2)); // Friday
        endDate = new Date(startDate);
        endDate.setDate(startDate.getDate() + 6);

        break;
      case 'month':
        // Set startDate to the first day of the current month and endDate to the last day of the current month
        startDate.setDate(1);
        // startDate = new Date(startDate.getFullYear(), startDate.getMonth(), 1);
        endDate = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0); // Last day of the current month
        break;
      case 'year':
        // Set startDate to the first day of the current year and endDate to the last day of the current year
        startDate.setDate(1);
        startDate.setMonth(0); // January
        endDate = new Date(currentDate.getFullYear(), 11, 31); // Last day of December
        break;
      default:
        break;
    }

    // If the selected time range is 'year', set endDate to the last day of the current month
    // if (timeRange === 'year') {
    //   endDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
    // }
    return { startDate, endDate };
  }

  isDateRangeDisabled() {
    return (
      this.currentDateRange.startDate === this.fromDate &&
      this.currentDateRange.endDate === this.toDate
    );
  }

  private formatDate(date: Date): string {
    // Format the date to your desired format using Angular's DatePipe
    return this.datePipe.transform(date, 'yyyy-MM-dd') || '';
  }

}
