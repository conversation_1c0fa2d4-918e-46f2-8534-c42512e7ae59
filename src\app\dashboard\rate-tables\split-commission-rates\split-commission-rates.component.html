<div class="page-title col-md-12">
  <h1>Split Commission Rates</h1>
  <div class="breadcrumbs">
    <a href="#">Home</a>/<span>Split Commission Rates</span>
  </div>
</div>

<div class="content">
  <div class="card">
    <div class="card-header-info">
      <h4 class="card-title no-hover-effect">Split Commission Rates</h4>
    </div>

    <div class="card-body">
      <div class="row">
        <div class="card-body">
          <app-grid-mat-table [gridData]="originalDataSource" [columnData]="columnNames"
            [displayColumnData]="displayedColumns" [dateFields]="dateColumns" [isScrollWidth]="false"
            [isSearchAvailable]="true" (rowClick)="rowClick($event)">
          </app-grid-mat-table>
          <div>
            <a class="btn btn-primary float-right" *ngIf="!addRow" (click)="add()"><i
                class="material-icons pointer">add_circle</i> Add</a>
            <a class="btn btn-primary float-right" *ngIf="addRow" (click)="addRow = !addRow"><i
                class="material-icons pointer">remove_circle</i> Hide</a>
          </div>
        </div>
      </div>
    </div>

    <div class="card" *ngIf="addRow">
      <div class="card-header-info">
        <h4 class="card-title no-hover-effect">
          <i class="fas fa-plus"></i> Add Split Commission Rate
        </h4>
      </div>

      <div class="card-body">
        <div>
          <form [formGroup]="addSplitCommission" (ngSubmit)="onSubmit()" class="w-100">
            <div class="row">
              <div class="form-group col-md-4">
                <div class="row">
                  <label class="col-sm-5">Sales Division</label>
                  <div class="col-sm-7">
                    <select class="custom-select" name="salesDivision_add_dropdown" formControlName="salesDivision"
                      data-style="btn btn-link" id="salesDivision_add_dropdown">
                      <option *ngFor="let value of salesDivisions" value="{{value}}"> {{value}}</option>
                    </select>
                  </div>
                </div>
              </div>
              <div class="form-group col-md-4">
                <div class="row">
                  <label class="col-sm-5">State</label>
                  <div class="col-sm-7">
                    <select class="custom-select" name="state_add_dropdown" formControlName="stateCode"
                      data-style="btn btn-link" id="state_add_dropdown">
                      <option *ngFor="let value of stateData" value="{{value}}"> {{value}}</option>
                    </select>
                  </div>
                </div>
              </div>
              <div class="form-group col-md-4">
                <div class="row">
                  <label class="col-sm-5">Team</label>
                  <div class="col-sm-7">
                    <select class="custom-select" name="team_add_dropdown" formControlName="team"
                      data-style="btn btn-link" id="team_add_dropdown">
                      <option *ngFor="let value of teamData" value="{{value}}"> {{value}}</option>
                    </select>
                  </div>
                </div>
              </div>
              <div class="form-group col-md-4">
                <div class="row">
                  <label class="col-sm-5">Product Type</label>
                  <div class="col-sm-7">
                    <select class="custom-select" name="product_add_dropdown" formControlName="productType"
                      data-style="btn btn-link" id="product_add_dropdown">
                      <option *ngFor="let pt of productTypeData" value="{{pt.productTypeId}}"> {{pt.productTypeName}}
                      </option>
                    </select>
                  </div>
                </div>
              </div>
              <div class="form-group col-md-4">
                <div class="row">
                  <label class="col-sm-5">Split Type</label>
                  <div class="col-sm-7">
                    <select class="custom-select" name="splitType_add_dropdown" formControlName="splitType"
                      data-style="btn btn-link" id="splitType_add_dropdown">
                      <option value="Amount">Amount</option>
                      <option value="Percentage">Percentage</option>
                    </select>
                  </div>
                </div>
              </div>
              <div class="form-group col-md-4">
                <div class="row">
                  <label class="col-sm-5">Primary Split Type Value</label>
                  <div class="col-sm-7">
                    <input type="number" class="custom-input" formControlName="splitTypePrimaryValue" />
                  </div>
                </div>
              </div>
              <div class="form-group col-md-4">
                <div class="row">
                  <label class="col-sm-5">Secondary Split Type Value</label>
                  <div class="col-sm-7">
                    <input type="number" class="custom-input" formControlName="splitTypeSecondaryValue" />
                  </div>
                </div>
              </div>
              <div class="form-group col-md-4">
                <div class="row">
                  <label class="col-sm-5">Split Description</label>
                  <div class="col-sm-7">
                    <input class="custom-input" formControlName="splitDescription" />
                  </div>
                </div>
              </div>
              <div class="form-group col-md-4">
                <div class="row">
                  <label class="col-sm-5">Effective Start Date</label>
                  <div class="col-sm-7">
                    <div class="date-picker w-100">
                      <input #AddStartDatePicker type="date" name="start_date" id="start_date" class="custom-input"
                        formControlName="effectiveStartDate" placeholder="" />
                      <span *ngIf="AddStartDatePicker.value.length > 0" class="mat-icon cal-reset"
                        (click)="clearAddStartDate(AddStartDatePicker)"><i class="far fa-calendar-times"></i></span>
                      <span *ngIf="AddStartDatePicker.value.length <= 0" class="mat-icon cal-open"><i
                          class="far fa-calendar-alt"></i></span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="form-group col-md-4">
                <div class="row">
                  <label class="col-sm-5">Effective End Date</label>
                  <div class="col-sm-7">
                    <div class="date-picker w-100">
                      <input #AddEndDatePicker type="date" name="effectiveEndDate" id="effectiveEndDate"
                        class="custom-input" formControlName="effectiveEndDate"
                        [min]="addSplitCommission.controls.effectiveStartDate.value" placeholder="" />
                      <span *ngIf="AddEndDatePicker.value.length > 0" class="mat-icon cal-reset"
                        (click)="clearAddEndDate(AddEndDatePicker)"><i class="far fa-calendar-times"></i></span>
                      <span *ngIf="AddEndDatePicker.value.length <= 0" class="mat-icon cal-open"><i
                          class="far fa-calendar-alt"></i></span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="row align-button-right">
              <button type="submit" class="btn btn-primary" [disabled]="addSplitCommission.invalid"><i
                  class="fas fa-plus"></i> Add Split Commission Rate</button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <div class="card" *ngIf="editRow">
      <div class="card-header-info">
        <h4 class="card-title no-hover-effect">
          <i class="fas fa-plus"></i> Edit Split Commission Rate
        </h4>
      </div>

      <div class="card-body">
        <div>
          <form [formGroup]="editSplitCommission" (ngSubmit)="onEditSubmit()" class="w-100">
            <div class="row">
              <div class="form-group col-md-4">
                <div class="row">
                  <label class="col-sm-5">Sales Division</label>
                  <div class="col-sm-7">
                    <input class="custom-input" formControlName="salesDivision" />
                  </div>
                </div>
              </div>
              <div class="form-group col-md-4">
                <div class="row">
                  <label class="col-sm-5">State</label>
                  <div class="col-sm-7">
                    <input class="custom-input" formControlName="stateCode" />
                  </div>
                </div>
              </div>
              <div class="form-group col-md-4">
                <div class="row">
                  <label class="col-sm-5">Team</label>
                  <div class="col-sm-7">
                    <input class="custom-input" formControlName="team" />
                  </div>
                </div>
              </div>
              <div class="form-group col-md-4">
                <div class="row">
                  <label class="col-sm-5">Product Type</label>
                  <div class="col-sm-7">
                    <input class="custom-input" formControlName="productType" />
                  </div>
                </div>
              </div>
              <div class="form-group col-md-4">
                <div class="row">
                  <label class="col-sm-5">Split Type</label>
                  <div class="col-sm-7">
                    <input class="custom-input" formControlName="splitType" />
                  </div>
                </div>
              </div>
              <div class="form-group col-md-4">
                <div class="row">
                  <label class="col-sm-5">Primary Split Type Value</label>
                  <div class="col-sm-7">
                    <input type="number" class="custom-input" formControlName="splitTypePrimaryValue" />
                  </div>
                </div>
              </div>
              <div class="form-group col-md-4">
                <div class="row">
                  <label class="col-sm-5">Secondary Split Type Value</label>
                  <div class="col-sm-7">
                    <input type="number" class="custom-input" formControlName="splitTypeSecondaryValue" />
                  </div>
                </div>
              </div>
              <div class="form-group col-md-4">
                <div class="row">
                  <label class="col-sm-5">Split Description</label>
                  <div class="col-sm-7">
                    <input class="custom-input" formControlName="splitDescription" />
                  </div>
                </div>
              </div>
              <div class="form-group col-md-4">
                <div class="row">
                  <label class="col-sm-5">Effective Start Date</label>
                  <div class="col-sm-7">
                    <input class="custom-input" formControlName="effectiveStartDate" />
                  </div>
                </div>
              </div>

              <div class="form-group col-md-4">
                <div class="row">
                  <label class="col-sm-5">Effective End Date</label>
                  <div class="col-sm-7">
                    <div class="date-picker w-100" *ngIf="!disableDateField">
                      <input #AddEditEndDatePicker type="date" name="effectiveEndDate_edit" id="effectiveEndDate_edit"
                        class="custom-input" formControlName="effectiveEndDate"
                        [min]="editSplitCommission.controls.effectiveStartDate.value" placeholder="" />
                      <span *ngIf="AddEditEndDatePicker.value.length > 0" class="mat-icon cal-reset"
                        (click)="clearEditEndDate(AddEditEndDatePicker)"><i class="far fa-calendar-times"></i></span>
                      <span *ngIf="AddEditEndDatePicker.value.length <= 0" class="mat-icon cal-open"><i
                          class="far fa-calendar-alt"></i></span>
                    </div>
                    <input type="text" formControlName="effectiveEndDate" class="custom-input" *ngIf="disableDateField">
                  </div>
                </div>
              </div>
            </div>

            <div class="row align-button-right">
              <button type="submit" class="btn btn-primary" *ngIf="!disableDateField"><i class="fas fa-plus"></i> Update
                Split Commission Rate</button>
            </div>
          </form>
        </div>
      </div>
    </div>

  </div>
</div>