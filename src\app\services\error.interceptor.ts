import { Injectable } from '@angular/core';
import { HttpRequest, HttpHandler, HttpEvent, HttpInterceptor } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, retry } from 'rxjs/operators';
import { ApiService } from '../services/api.service'
import { Router } from "@angular/router";

@Injectable()
export class ErrorInterceptor implements HttpInterceptor {
    constructor(private apiService: ApiService, private router: Router) { }

    intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
        return next.handle(request).pipe(catchError(err => {
            console.log("Error Interceptor ", err)
            if (err.statusCode == 401 || err.status === 401) {
                localStorage.clear();
                return throwError("Token out of sync, please try again");
            } else if (err == "user_login_error|User login is required") {
                localStorage.clear();
            } else {
                this.apiService.closeLogin()
            }
            var error;
            if (err != null && err.error != null) {
                error = err.error || err.error.message || err.error.title || err.statusText;
            }
            if (error == undefined) {
                localStorage.clear();
                location.reload();
            } else {
                return throwError(error);
            }
        }))
    }
}