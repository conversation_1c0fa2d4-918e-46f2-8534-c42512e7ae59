<div class="page-title col-md-12">
    <h1>Employee Override Rates Tier</h1>
    <div class="breadcrumbs">
      <a href="#">Home</a>/<span>Employee Override Rates Tier</span>
    </div>
</div>
  
<div class="content">
    <div class="card">
        <div class="card-header-info">
            <h4 class="card-title no-hover-effect">Employee Override Rates Tier</h4>
        </div>


        <div class="card-body">
            <div class="row">
                <div class="card-body">
      
                    <mat-table #table [dataSource]="dataSource" matSort>
                    <ng-container matColumnDef="{{ column.id }}" *ngFor="let column of columnNames">
                        <mat-header-cell *matHeaderCellDef mat-sort-header class="table-header"> {{ column.value }} </mat-header-cell>
                        <mat-cell [attr.data-td-head]="column.value" *matCellDef="let element">{{ element[column.id] }}</mat-cell>
                    </ng-container>
                    <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
                    <mat-row *matRowDef="let row; columns: displayedColumns" class="pointer table-content"></mat-row>
                    </mat-table>
                    
                    <mat-paginator [pageSizeOptions]="[5, 10, 20, 50]"showFirstLastButtons></mat-paginator>
                    <div>
                    <a class="btn btn-primary float-right" *ngIf="!addRow" (click)="Add()"><i class="material-icons pointer">add_circle</i> Add</a>
                    <a class="btn btn-primary float-right" *ngIf="addRow" (click)="addRow = !addRow"><i class="material-icons pointer">remove_circle</i> Hide</a>
                    </div>


                </div>
            </div>
        </div>

        <div class="card" *ngIf="addRow">
            <div class="card-header-info">
              <h4 class="card-title no-hover-effect">
                <i class="fas fa-plus"></i> Add Employee Override Rate Tier
              </h4>
            </div>
    
            <div class="card-body">
              <div>
                <form [formGroup]="tierAddForm" (ngSubmit)="onAddSubmit()" class="w-100">
                <div class="row">
                      <div class="form-group col-md-4">
                      <div class="row">
                        <label class="col-sm-5">From - To Count</label>
                        <div class="col-sm-7">
                            <select class="custom-select" name="fromToCount" formControlName="fromToCount" data-style="btn btn-link" id="salesOffice_add_dropdown">
                              <option *ngFor="let vals of fromToCountValues"
                                [selected]="vals.value === defaultOverrideTier[0].fromToCount "value="{{ vals.id }}"> {{ vals.value }} </option>
                            </select>
                          </div>    
                      </div>
                    </div>

                    <div class="form-group col-md-4">
                      <div class="row">
                        <label class="col-sm-5">Effective Start Date</label>
                        <div class="col-sm-7">
                          <div class="date-picker w-100">
                            <input #AddStartDatePicker type="date" name="start_date" id="start_date" class="custom-input" formControlName="effectiveStartDate" placeholder=""/>
                            <span *ngIf="AddStartDatePicker.value.length > 0" class="mat-icon cal-reset" (click)="clearAddStartDate(AddStartDatePicker)"><i class="far fa-calendar-times"></i></span>
                            <span *ngIf="AddStartDatePicker.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="form-group col-md-4">
                        <div class="row">
                          <label class="col-sm-5">Effective End Date</label>
                          <div class="col-sm-7">
                            <div class="date-picker w-100">
                              <input #AddEndDatePicker type="date" name="effectiveEndDate" id="effectiveEndDate" class="custom-input" formControlName="effectiveEndDate" placeholder="" 
                              />
                              <span *ngIf="AddEndDatePicker.value.length > 0" class="mat-icon cal-reset" (click)="clearAddEndDate(AddEndDatePicker)"><i class="far fa-calendar-times"></i></span>
                              <span *ngIf="AddEndDatePicker.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span>
                            </div>
                          </div>
                        </div>
                      </div>
    
                    <div class="form-group col-md-4">
                      <div class="row">
                        <label class="col-sm-5">Rate</label>
                        <div class="col-sm-7">
                          <input class="custom-input" formControlName="rate" id="rate_add" name="rate_add" />
                        </div>
                      </div>
                    </div> 
                    <div class="form-group col-md-4">
                      <div class="row">
                        <label class="col-sm-5">Rate Type</label>
                        <div class="col-sm-7">
                          <select class="custom-select" name="rateType" formControlName="rateType" id="rateType_dropdown">
                            <option *ngFor="let val of rateTypesData" value="{{val}}"> {{val}}</option>
                          </select>
                        </div>
                      </div>
                    </div> 
                </div>
    
                <div class="row align-button-right">
                    <button type="submit" class="btn btn-primary" [disabled]="tierAddForm.invalid"><i class="fas fa-plus"></i> Add Employee Override Rate Tier</button>
                </div>
                </form>
              </div>
            </div>
        </div>
    </div>
</div>