import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class UserService {
  //service for login re-work with new token claims
  private role: any;
  private contactId: any;
  private empId: any;

  setRole(role: any) {
    this.role = role;
  }
  setContact(contactId: any) {
    this.contactId = contactId;
  }
  setEmpId(empId: any) {
    this.empId = empId;
  }

  getRole() {
    return this.role;
  }
  getContact() {
    return this.contactId;
  }
  getEmpId() {
    return this.empId;
  }
}