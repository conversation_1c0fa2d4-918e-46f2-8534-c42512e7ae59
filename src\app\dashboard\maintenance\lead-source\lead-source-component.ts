import { Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from "@angular/forms";
import { ApiService } from "../../../services/api.service";
import { ToastrService } from 'ngx-toastr';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { DataSource } from '@angular/cdk/table';
import { MatLegacyPaginator } from '@angular/material/legacy-paginator';

import { TableFilterPipe } from '../../../pipe/table-filter.pipe';
import { DatePipe, PercentPipe, CurrencyPipe } from '@angular/common';
import { MatLegacyDialog } from '@angular/material/legacy-dialog';
import { HttpParams, HttpClient } from '@angular/common/http';
import { ApiResponse } from '../../../services/api.response';
import { environment } from '../../../../environments/environment';
import { ILeadSource } from '../../../model/lead-source';
import * as FileSaver from 'file-saver';

@Component({
    selector: 'app-lead-source',
    templateUrl: './lead-source-component.html',
    styleUrls: ['./lead-source-component.css']
})
export class LeadSourceComponent implements OnInit {
    leadSourceForm: UntypedFormGroup;
    addInd: boolean = false;
    pageSizeOptions: number[] = [10, 20, 50];
    pageSize: number = 10;
    allSelected: boolean = false;
    leadSource: string = null;
    selectedLeadSourceName: string = "";
    searchText: string = "";
    originalDataSource: any;
    dataSource: any;
    leadSourcesDataSource: MatTableDataSource<ILeadSource> = new MatTableDataSource([]);
    leadSourceCols: string[] = ["leadSourceName"];
    filter: boolean;
    @ViewChild(MatSort, { static: true }) sort: MatSort;
    @ViewChild(MatLegacyPaginator, { static: true }) paginator: MatLegacyPaginator;

    constructor(public apiService: ApiService, private toastMsg: ToastrService, private formBuilder: UntypedFormBuilder, private pipe: TableFilterPipe, private http: HttpClient,
        private datePipe: DatePipe, private percentPipe: PercentPipe, private currencyPipe: CurrencyPipe, private dialog: MatLegacyDialog) {
    }

    ngOnInit() {
        this.getLeadSources();
        if (!this.apiService.checkPermission('ViewRateTables')) {
            this.apiService.goBack();
            this.toastMsg.error("Insufficient Permissions. Please make sure your account can access this information.", "Permissions");
        }
        this.leadSourceForm = this.formBuilder.group({
            leadSource: [this.leadSource, [Validators.required]]
        });
    }

    onChangeFilter() {
        this.getLeadSources();
    }

    checkSelected() {
        return this.leadSourcesDataSource && this.leadSourcesDataSource.data.filter(record => record.selected).length > 0;
    }

    getNumberSelected(): number {
        if (this.leadSourcesDataSource) return this.leadSourcesDataSource.data.filter(record => record.selected).length;
    }

    onSelectionChange() {
        let balances = this.leadSourcesDataSource.filteredData.filter(bal => bal.selected);
        if (balances.length == this.leadSourcesDataSource.filteredData.length) {
            this.allSelected = true;
        } else {
            this.allSelected = false;
        }
    }

    onBulkSelectionChange() {
        this.leadSourcesDataSource.filteredData.map(bal => bal.selected = this.allSelected);
    }

    deleteSelected() {
        let leadSourcesDataSource = this.leadSourcesDataSource.data.filter(record => record.selected);
        var ids = new Array();
        for (var i = 0; i < leadSourcesDataSource.length; i++) {
            ids.push(leadSourcesDataSource[i].leadSourceId)
        }
        this.apiService.post('leadSource/deleteRows', ids)
            .subscribe(data => {
                this.toastMsg.success("Deleted selected Records successfully.");
                this.getLeadSources();
                this.applyFilter(this.searchText);
            }, err => {
                this.toastMsg.error(err.message, "Error!");
            })
    }


    getLeadSources() {
        let params = new HttpParams();

        params = params.append('leadSourceName', this.selectedLeadSourceName + "");
        this.http.get<ApiResponse>(`${environment.apiBaseUrl}leadSource/RetrieveAll`, { params: params })
            .subscribe(data => {
                if (data && data.result) {
                    var leadSourcesDataSource = data.result.map((rows: ILeadSource) => { return rows });
                    this.leadSourcesDataSource = new MatTableDataSource<ILeadSource>(leadSourcesDataSource);
                    this.leadSourcesDataSource.paginator = this.paginator;
                    this.leadSourcesDataSource.sort = this.sort;
                    this.applyFilter(this.searchText);
                }
            }, err => {
                this.toastMsg.error(err.message, "Error!");
            })
    }


    getLeadSourcesWorksheet() {
        let params = new HttpParams();
        params = params.append('leadSourceName', this.selectedLeadSourceName + "");
        this.http.get(`${environment.apiBaseUrl}leadsource/export?leadSourceName=${this.searchText}`, { responseType: 'blob' })
            .subscribe(data => {
                this.downLoadFile(data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
            }, err => {
                this.toastMsg.error(err.message, "Error!");
            });
    }


    /**
    * Method is use to download file.
    * @param data - Array Buffer data
    * @param type - type of the document.
    */
    downLoadFile(data: any, type: string) {
        let blob = new Blob([data], { type: type });
        let date: Date = new Date();

        FileSaver.saveAs(blob, `Lead_Sources_${date}.xlsx`);
    }


    applyFilter(input: string): void;

    applyFilter(input: Event): void;

    applyFilter(input: any): any {
        var filterValue: string;
        if (typeof input === "string") {
            filterValue = input;
        } else {
            filterValue = (input.target as HTMLInputElement).value;
        }
        this.leadSourcesDataSource.filter = filterValue.trim().toLowerCase();
    }


    onSubmit() {
        if (!this.leadSourceForm.invalid) {
            var body = {
                LeadsourceName: this.leadSourceForm.controls.leadSource.value,
            }
            this.apiService.post('Leadsource', body)
                .subscribe(data => {
                    this.toastMsg.success('Lead source added successfully');
                    this.getLeadSources()
                    this.addInd = !this.addInd;
                }, (err: any) => {
                    this.toastMsg.error(err.message, 'Server Error!');
                });
        }
    }

    searchForItem(): void {
        let filteredResults: Element[] = [];
        if (this.searchText == '') {
            this.dataSource = new MatTableDataSource(this.originalDataSource);
            this.dataSource.sort = this.sort;
            this.dataSource.paginator = this.paginator;
        } else {
            filteredResults = this.pipe.transform(this.originalDataSource, this.searchText);
            this.dataSource = new MatTableDataSource(filteredResults);
            this.dataSource.sort = this.sort;
            this.dataSource.paginator = this.paginator;
        }
    }
}

export interface Element {
    leadsource: string;
}

