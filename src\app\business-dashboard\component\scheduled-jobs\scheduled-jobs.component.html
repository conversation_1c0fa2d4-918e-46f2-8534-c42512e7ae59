<div class="container-fluid">
  <div class="row">
    <div class="col-md-12">
      <div class="card">
        <div class="card-header-info">
          <h4 class="card-title">Status Overview</h4>
        </div>
        <div class="row">
          <ul class="pl-5 pt-2">
            <li class="text-info">Data of the scheduled jobs (Plan execution, Data Integration) where the job executed
              during the date range</li>
          </ul>
        </div>
        <div class="row">
          <div class="col d-flex justify-content-end mr-4">
            <div class="d-flex flex-row">
              <div class="circle bg-success"></div>
              <div class="mx-1 font-weight-bold">Success</div>
              <div class="circle bg-danger"></div>
              <div class="mx-1 font-weight-bold">Failure</div>
              <div class="circle-orange"></div>
              <div class="mx-1 font-weight-bold">In Progress</div>
            </div>
          </div>
        </div>
        <div class="card-body">
          <table class="table table-bordered">
            <tr>
              <th></th>
              <th>{{ getDateRange()[0] | date:'EEEE, MMM dd yyyy' }}</th>
              <th>{{ getDateRange()[1] | date:'EEEE, MMM dd yyyy' }}</th>
              <th>{{ getDateRange()[2] | date:'EEEE, MMM dd yyyy' }}</th>
              <th>{{ getDateRange()[3] | date:'EEEE, MMM dd yyyy' }}</th>
              <th>{{ getDateRange()[4] | date:'EEEE, MMM dd yyyy' }}</th>
              <th>{{ getDateRange()[5] | date:'EEEE, MMM dd yyyy' }}</th>
              <th>{{ getDateRange()[6] | date:'EEEE, MMM dd yyyy' }}</th>
            </tr>

            <ng-container *ngIf="jobData?.jobs?.length > 0">
              <tr *ngFor="let job of jobData.jobs">
                <td>{{ job.jobName}}</td>
                <td *ngIf="job.dayStatus.Friday && job.dayStatus.Friday.length > 0; else emptyFri">
                  <span *ngFor="let item of job.dayStatus.Friday">
                    <span *ngIf="item == 1" title="Recent execution status of the job : Success" class="circle bg-success mx-1"></span>
                    <span *ngIf="item == 2 && job.dayStatus.Friday[0] == 0" title="Recent execution status of the job : Failure" class="circle bg-danger mx-1"></span>
                    <span *ngIf="item == 2 && job.dayStatus.Friday[0] == 2" title="One of the execution Failed in the day" class="circle bg-danger mx-1"></span>                    
                    <span *ngIf="item == 3" class="circle-orange mx-1" title="Recent execution status of the job : In Progress"></span>
                  </span>
                </td>
                <ng-template #emptyFri>
                  <td></td>
                </ng-template>
                <td *ngIf="job.dayStatus.Saturday && job.dayStatus.Saturday.length > 0; else emptySat">
                  <span *ngFor="let item of job.dayStatus.Saturday">
                    <span *ngIf="item == 1" title="Recent execution status of the job : Success" class="circle bg-success mx-1"></span>
                    <span *ngIf="item == 2 && job.dayStatus.Saturday[0] == 0" title="Recent execution status of the job : Failure" class="circle bg-danger mx-1"></span>
                    <span *ngIf="item == 2 && job.dayStatus.Saturday[0] == 2" title="One of the execution Failed in the day" class="circle bg-danger mx-1"></span>
                    <span *ngIf="item == 3" class="circle-orange mx-1" title="Recent execution status of the job : In Progress"></span>
                  </span>
                </td>
                <ng-template #emptySat>
                  <td></td>
                </ng-template>
                <td *ngIf="job.dayStatus.Sunday && job.dayStatus.Sunday.length > 0; else emptySun">
                  <span *ngFor="let item of job.dayStatus.Sunday">
                    <span *ngIf="item == 1" title="Recent execution status of the job : Success" class="circle bg-success mx-1"></span>
                    <span *ngIf="item == 2 && job.dayStatus.Sunday[0] == 0" title="Recent execution status of the job : Failure" class="circle bg-danger mx-1"></span>
                    <span *ngIf="item == 2 && job.dayStatus.Sunday[0] == 2" title="One of the execution Failed in the day" class="circle bg-danger mx-1"></span>
                    <span *ngIf="item == 3" class="circle-orange mx-1" title="Recent execution status of the job : In Progress"></span>
                  </span>
                </td>
                <ng-template #emptySun>
                  <td></td>
                </ng-template>
                <td *ngIf="job.dayStatus.Monday && job.dayStatus.Monday > 0; else emptyMon">
                  <span *ngFor="let item of job.dayStatus.Monday">
                    <span *ngIf="item == 1" title="Recent execution status of the job : Success" class="circle bg-success mx-1"></span>
                    <span *ngIf="item == 2 && job.dayStatus.Monday[0] == 0" title="Recent execution status of the job : Failure" class="circle bg-danger mx-1"></span>
                    <span *ngIf="item == 2 && job.dayStatus.Monday[0] == 2" title="One of the execution Failed in the day" class="circle bg-danger mx-1"></span>
                    <span *ngIf="item == 3" class="circle-orange mx-1" title="Recent execution status of the job : In Progress"></span>
                  </span>
                </td>
                <ng-template #emptyMon>
                  <td></td>
                </ng-template>
                <td *ngIf="job.dayStatus.Tuesday && job.dayStatus.Tuesday.length > 0; else emptyTue">
                  <span *ngFor="let item of job.dayStatus.Tuesday">
                    <span *ngIf="item == 1" title="Recent execution status of the job : Success" class="circle bg-success mx-1"></span>
                    <span *ngIf="item == 2 && job.dayStatus.Tuesday[0] == 0" title="Recent execution status of the job : Failure" class="circle bg-danger mx-1"></span>
                    <span *ngIf="item == 2 && job.dayStatus.Tuesday[0] == 2" title="One of the execution Failed in the day" class="circle bg-danger mx-1"></span>
                    <span *ngIf="item == 3" class="circle-orange mx-1" title="Recent execution status of the job : In Progress"></span>
                  </span>
                </td>
                <ng-template #emptyTue>
                  <td></td>
                </ng-template>
                <td *ngIf="job.dayStatus.Wednesday && job.dayStatus.Wednesday.length > 0; else emptyWed">
                  <span *ngFor="let item of job.dayStatus.Wednesday">
                    <span *ngIf="item == 1" title="Recent execution status of the job : Success" class="circle bg-success mx-1"></span>
                    <span *ngIf="item == 2 && job.dayStatus.Wednesday[0] == 0" title="Recent execution status of the job : Failure" class="circle bg-danger mx-1"></span>
                    <span *ngIf="item == 2 && job.dayStatus.Wednesday[0] == 2" title="One of the execution Failed in the day" class="circle bg-danger mx-1"></span>
                    <span *ngIf="item == 3" class="circle-orange mx-1" title="Recent execution status of the job : In Progress"></span>
                  </span>
                </td>
                <ng-template #emptyWed>
                  <td></td>
                </ng-template>
                <td *ngIf="job.dayStatus.Thursday && job.dayStatus.Thursday.length > 0; else emptyThu">
                  <span *ngFor="let item of job.dayStatus.Thursday">
                    <span *ngIf="item == 1" title="Recent execution status of the job : Success" class="circle bg-success mx-1"></span>
                    <span *ngIf="item == 2 && job.dayStatus.Thursday[0] == 0" title="Recent execution status of the job : Failure" class="circle bg-danger mx-1"></span>
                    <span *ngIf="item == 2 && job.dayStatus.Thursday[0] == 2" title="One of the execution Failed in the day" class="circle bg-danger mx-1"></span>
                    <span *ngIf="item == 3" class="circle-orange mx-1" title="Recent execution status of the job : In Progress"></span>
                  </span>
                </td>
                <ng-template #emptyThu>
                  <td></td>
                </ng-template>
              </tr>
            </ng-container>            
          </table>
        </div>
      </div>
    </div>
  </div>
</div>