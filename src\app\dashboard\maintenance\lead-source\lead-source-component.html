<div class="page-title col-md-12 ">
  <h1> Lead Source</h1>
  <div class="breadcrumbs">
    <a href="#">Home</a>/<span>Lead Sources</span>
  </div>
</div>
<div class="content">

  <div class="card">
    <div class="card-header-info">
      <h4 class="card-title no-hover-effect">Lead Sources</h4>
    </div>
    <div class="card-body">
      <div class="row">
        <div class="col-md-12">
          <div class="float-right">
            <button class="btn btn-primary" (click)="getLeadSourcesWorksheet()">
              <i class="material-icons">save_alt</i>
              Download
            </button>
          </div>
          <div class="form-group  col-sm-3 input-group  float-right pr-0 mr-2">
            <input class="custom-input" type="text" id="searchTextId" [(ngModel)]="searchText"
              (keyup)="applyFilter($event)" name="searchText" placeholder="Search">
            <span class="input-group-icon">
              <i class="fas fa-search"></i>
            </span>
          </div>
        </div>
      </div>
      <ng-container *ngIf="filter">
        <div class="gray-bg row">
          <div class="col-md-12 pt-3 pb-3 gray-bg">
            <div class="row filter-row">
              <div class="form-group col-md-3">
                <label class="bmd-label-floating">Lead Source</label>
                <input type="text" class="custom-input" [(ngModel)]="selectedLeadSourceName"
                  (change)="onChangeFilter()">
              </div>
            </div>
          </div>
        </div>
      </ng-container>
      <table mat-table [dataSource]="leadSourcesDataSource" matSort class="my-table mt-3" style="width: 100%">
        <ng-container matColumnDef="leadSourceName">
          <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Lead Source </th>
          <td data-td-head=" Lead Source" mat-cell *matCellDef="let element">
            {{element.leadSourceName}}
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="leadSourceCols"></tr>
        <tr mat-row *matRowDef="let row; columns leadSourceCols;"></tr>
      </table>

      <mat-paginator [pageSize]="pageSize" [pageSizeOptions]="pageSizeOptions">
      </mat-paginator>

      <div>


      </div>
    </div>
  </div>

  <div class="card" *ngIf="addInd">
    <div class="card-header-info">
      <h4 class="card-title no-hover-effect">Add Lead Source </h4>
    </div>
    <div class="card-body">

      <form [formGroup]="leadSourceForm" (ngSubmit)="onSubmit()" class="w-100">
        <div class="row">
          <div class="form-group col-md-4">
            <div class="row">
              <label class="col-sm-5">Lead Source Name </label>
              <div class="col-sm-7">
                <input type="text" name="leadSource" id="leadSource" class="custom-input" formControlName="leadSource"
                  placeholder="">
                <div class="error"
                  *ngIf="leadSourceForm.controls['leadSource'].hasError('required') && leadSourceForm.controls['leadSource'].touched">
                  Lead Source Name
                  is
                  required.</div>
              </div>
            </div>
          </div>
          <div class="col-md-12 text-right">
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-plus"></i> Add Lead
              source
            </button>
          </div>

        </div>
      </form>


    </div>
  </div>
