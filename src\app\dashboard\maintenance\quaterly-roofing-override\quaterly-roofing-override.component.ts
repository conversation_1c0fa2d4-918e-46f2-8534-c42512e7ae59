import { Component, OnInit } from '@angular/core';
import { ApiService } from "../../../services/api.service";
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-quaterly-roofing-override',
  templateUrl: './quaterly-roofing-override.component.html',
  styleUrls: ['./quaterly-roofing-override.component.css']
})
export class QuaterlyRoofingOverrideComponent implements OnInit {
  selectedYear: number;
  quarters = [{
      id: "Q1",
      value: 1
      },
      {
      id: "Q2",
      value: 2
      }, 
      {
      id: "Q3",
      value: 3
      },
      {
      id: "Q4",
      value: 4
      }];
  selectedQuarter: number;

  constructor(public apiService: ApiService, private toastMsg: ToastrService) { }

  ngOnInit(): void {
  }

  onChangeQuarter(quarter: any) {
    this.selectedQuarter = quarter.target.value;
    }


    onSubmit() {
      if (!(this.selectedYear > 2000 && this.selectedYear < 2099)) {
          this.toastMsg.error('Please enter valid year number between 2000 and 2099');
          return;
      }
  
      if(this.selectedQuarter === undefined){
          this.toastMsg.error('Please select a Quarter from the dropdown.');
          return;
      }
  
      var body = {
          Quarter: this.selectedQuarter, 
          Year: this.selectedYear
      }
  
      this.apiService.post('RoofEmployeeMonthlyOverrideRate/QuarterlyRoofingOverride', body)
          .subscribe(data => {
              this.toastMsg.success('Roofing quarterly override snapshot taken successfully. Please check dynamic report to see the snapshot.');
              }, (err: any) => {
              this.toastMsg.error(err.message, 'Server Error!');
          });
  }

}
