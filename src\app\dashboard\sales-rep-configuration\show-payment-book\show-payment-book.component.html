<div class="card" *ngIf="paymentBook">
  <div class="card-header-info p-3">
    <div class="row">
      <div class="col-sm-6">
        <label>Name: </label> {{paymentBook.ruleName}}
      </div>
      <div class="col-sm-6">
        <label>Description:</label> {{paymentBook.ruleDescription}}
      </div>
    </div>

    <div class="row">
      <div class="col-md-6">
        <div class="row">
          <label class="col-sm-5">Book Type</label>

          <div class="col-sm-7">
            <input type="text" class="custom-input" value="{{paymentBook.pbType}}" readonly>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="row">
          <label class="col-sm-5">Overdraw Limit ( - )</label>

          <div class="col-sm-7">
            <input type="number" class="custom-input" value="{{paymentBook.overdrawLimit}}" readonly>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="row">
          <label class="col-sm-5">Weekly Pay</label>

          <div class="col-sm-7">
            <input type="number" class="custom-input" value="{{paymentBook.weeklyPay}}" readonly>
          </div>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-md-12">
        <ng-container *ngIf="apiService.checkPermission('AssignPlan')">
          <button class="btn btn-primary float-right" (click)="allowChange()"><i class="fas fa-edit"></i>
            Change</button>
        </ng-container>
      </div>
    </div>
    <div class="row" *ngIf="showPreview">
      <div class="col-md-6">
        <div class="row">
          <label class="col-sm-5">Payment Book</label>
          <div class="col-sm-7">
            <select class="custom-select" [(ngModel)]="previewPaymentBookId">
              <ng-container *ngFor="let pb of paymentBooks">
                <option [value]="pb.ruleId" *ngIf="pb.ruleId != paymentBookId">{{pb.ruleName}}</option>
              </ng-container>
            </select>
          </div>
        </div>
      </div>
    </div>
    <div class="row" *ngIf="showPreview && previewPaymentBook">
      <div class="col-md-6">
        <div class="row">
          <label class="col-sm-5">Book Type</label>

          <div class="col-sm-7">
            <input type="text" class="custom-input" value="{{previewPaymentBook.pbType}}" readonly>
          </div>

        </div>
      </div>
      <div class="col-md-6">
        <div class="row">
          <label class="col-sm-5">Overdraw Limit ( - )</label>

          <div class="col-sm-7">
            <input type="number" class="custom-input" value="{{previewPaymentBook.overdrawLimit}}" readonly>
          </div>

        </div>
      </div>
      <div class="col-md-6">
        <div class="row">
          <label class="col-sm-5">Weekly Pay</label>

          <div class="col-sm-7">
            <input type="number" class="custom-input" value="{{previewPaymentBook.weeklyPay}}" readonly>
          </div>

        </div>
      </div>

    </div>
    <div class="row justify-content-end" *ngIf="showPreview && previewPaymentBook">
      <button class="btn btn-primary" (click)="confirm()"><i class="fas fa-save"></i> Save</button>
    </div>
  </div>
</div>
<!-- <app-confirmation-modal [message]="'Are you sure you want to assign a new Payment Book?'" (confirmed)="assignPaymentBook($event)"></app-confirmation-modal> -->

  <div class="card">
    <div class="card-header-info">
      <h4 class="card-title no-hover-effect">Payment Book Schedule</h4>
    </div>
    <div class="card-body">
      <table class="my-table mat-table w-100">
        <thead>
          <tr>
            <th class="mat-header-cell">Payment Book Type</th>
            <th class="mat-header-cell">Commission Rule</th>
            <th class="mat-header-cell">Effective Start Date</th>
            <th class="mat-header-cell">Request Completed On</th>
            <th class="mat-header-cell">Valid Indicator</th>
            <th class="mat-header-cell">Activation Note</th>
          </tr>
        </thead>
        <tr class="mat-row" *ngFor="let item of paymentBookSchedule">
          <td data-td-head="Payment Book" class="mat-cell">{{item.paymentBook}}</td>
          <td data-td-head="Commission Rule" class="mat-cell">{{item.commissionRule}}</td>
          <td data-td-head="Effective Start Date" class="mat-cell">{{item.effectiveStartDate | date}}</td>
          <td data-td-head="Request Completed On" class="mat-cell">{{item.requestCompletedOn | date}}</td>
          <td data-td-head="Valid Indicator" class="mat-cell">{{item.validIndicator ? 'True': 'False'}}</td>
          <td data-td-head="Activation Note" class="mat-cell">{{item.activationNote}}</td>
        </tr>
      </table>

    </div>
  </div>
  