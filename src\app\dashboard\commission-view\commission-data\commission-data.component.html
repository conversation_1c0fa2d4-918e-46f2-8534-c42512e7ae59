<div class="slide-toggle-btn tab-light-bg mb-3">
    <button [className]="showCommissionData ? 'tab-toggle tab-expanded' : 'tab-toggle tab-collapsed'" 
        mat-flat-button
        color="primary" 
        (click)="showData()">
        <i class="fas fa-history"></i>
        Commission Data
        <i class="material-icons tab-icons">{{showCommissionData ? 'remove_circle_outline' : 'add_circle_outline'}}</i>
    </button>
    <div [hidden]="!showCommissionData" class="toggle-container">
        
        <div class="col-md-12">
            <div class="row">
                <div class="col-md-4  gray-bg">
                    <ng-container *ngIf="baseformulasList && baseformulasList.length > 0">
                
                        <table mat-table [dataSource]="commissionDataElements" matSort class="my-table w-100">
                            <ng-container matColumnDef="baseFormulaName">
                                <th class="col-7 formula-name" mat-header-cell *matHeaderCellDef mat-sort-header> Base Formula Name
                                </th>
                                <td class="col-7 formula-content" data-td-head="Base Formula Name" mat-cell *matCellDef="let element"
                                    (click)="findBaseFormula(element.baseFormulaName)"> {{element.baseFormulaName}}
                                </td>
                            </ng-container>
                
                            <ng-container matColumnDef="commissionAmount">
                                <th style="white-space: nowrap;" class="col-5 formula-name" mat-header-cell *matHeaderCellDef
                                    mat-sort-header> Commission Amount </th>
                                <td class="col-5 formula-content" data-td-head="Commission Amount" mat-cell *matCellDef="let element"
                                    (click)="findBaseFormula(element.baseFormulaName)"> {{element.commissionAmount}} </td>
                            </ng-container>
                
                            <tr mat-header-row *matHeaderRowDef="commissionDataColumns"></tr>
                            <tr mat-row *matRowDef="let row; columns: commissionDataColumns;"></tr>
                        </table>
                      
                    </ng-container>
                </div>
                <div class="col-md-8 card-inner" [hidden]="baseFormulaBreakdown.length == 0">


                    <ng-container *ngIf="baseFormulaBreakdown.length">
                        <div class="row section-head">
                            <div class="col-md-6">
                                <h4>Base Formula: {{baseFormulaBreakdown[0].baseFormulaName}}</h4>
                            </div>
                            <div class="col-md-6">
                                <h4>Result: {{baseFormulaBreakdown[0].commissionAmount}}</h4>
                            </div>
                        </div>

                        <!-- TO DO: REWORK FOR ONE NGFOR RIGHT AFTER ROW -->
                        <table class="w-100 formula-table table-hover my-table mat-table">
                            <thead>
                                <tr class="mat-header-row">
                                    <th class="mat-header-cell">Step</th>
                                    <th class="mat-header-cell">Evaluation</th>
                                    <th class="mat-header-cell">Criteria</th>
                                    <th class="mat-header-cell">Action</th>
                                    <th class="mat-header-cell">Action Evaluated</th>
                                    <th class="mat-header-cell">Action Value</th>
                                </tr>
                            </thead>
                            <tbody>
                                <ng-container *ngFor="let step of baseFormulaBreakdown[0].steps">
                                    <tr class="hover" (click)="setConditions(step.stepName)"
                                    [ngStyle]="{'background-color':step.isTrue ? '#daf3c1' : '#fff' }">
                                        <td data-td-head="Step" class="mat-cell">{{step.stepName}}</td>
                                        <td data-td-head="Evaluation" class="mat-cell">{{step.isTrue}}</td>
                                        <td data-td-head="Crieria" class="mat-cell">
                                            <p *ngIf="step.criteria == '1'">All Conditions Should Meet</p>
                                            <p *ngIf="step.criteria == '2'">One or More Conditions Should Meet</p>
                                            <p *ngIf="step.criteria != '1' && step.criteria != '2' && step.criteria">
                                                Advanced</p>
                                        </td>
                                        <td data-td-head="Action" class="mat-cell">{{step.actionString}}</td>
                                        <td data-td-head="Action Evaluated" class="mat-cell">
                                            {{step.isTrue == true ? step.actionIdentifierWithValues : 'NA' }}</td>
                                        <td data-td-head="Action Value" class="mat-cell">
                                            {{step.isTrue == true ? step.actionValue : 'NA'}}</td>
                                    </tr>
                                </ng-container>
                            </tbody>
                        </table>
                        <br>
                        <ng-container *ngIf="baseFormulaConditions.length">
                            <div data-toggle="collapse" aria-expanded="false" aria-controls="collapseExample">
                                <div class="row">
                                    <span class="col-md-3 base-formula-condition-title">Condition ID</span>
                                    <span class="col-md-5 base-formula-condition-title">Condition</span>
                                    <span class="col-md-2 base-formula-condition-title"
                                        style="text-align: left">Condition
                                        Value</span>
                                    <span class="col-md-2 base-formula-condition-title"
                                        style="text-align: right">Evaluation</span>
                                </div>
                                <hr>
                                <div class="base-formula-conditions">
                                    <ng-container *ngFor="let conditionRow of baseFormulaConditions; let k = index">
                                        <div class="row">
                                            <span class="col-md-3">{{k + 1}}.</span>
                                            <span class="col-md-5">{{conditionRow.leftString}} {{conditionRow.operator}}
                                                {{conditionRow.rightString}}</span>
                                            <span class="col-md-2" style="text-align: left">{{conditionRow.leftValue}}
                                                {{conditionRow.operator}} {{conditionRow.rightValue}}</span>
                                            <span class="col-md-2"
                                                style="text-align: right">{{conditionRow.isTrue}}</span>
                                        </div>
                                        <br>
                                    </ng-container>
                                </div>
                            </div>
                        </ng-container>
                    </ng-container>

                </div>
            </div>
        </div>
        <h4 class="mt-3">Metadata</h4>
        <hr>
        <div class="row">
            <ng-container *ngIf="metadata && metadata.length > 0">
                <ng-container *ngFor="let data of metadata">
                    <ng-container *ngIf="data.displayName !== data.value">
                        <div class="col-md-6">
                            <div class="row">
                                <label class="col-7 formula-name">
                                    {{data.displayName ? data.displayName : '-'}}
                                </label>
                                <div class="col-5 formula-content">
                                    {{data.value}}
                                </div>
                            </div>
                        </div>
                    </ng-container>
                </ng-container>
            </ng-container>
        </div>
    </div>
</div>