import { Component, OnInit, ViewChild, Input } from '@angular/core';
import { MatLegacyPaginator } from '@angular/material/legacy-paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { Observable, Subscriber, OperatorFunction } from 'rxjs';
import { UntypedFormBuilder, UntypedFormGroup, Validators} from "@angular/forms";
import { ApiService } from "../../../services/api.service";
import { ToastrService } from 'ngx-toastr';
import { DatePipe, DecimalPipe } from '@angular/common';
import { TableFilterPipe } from '../../../pipe/table-filter.pipe';
import { IContact } from 'src/app/model/one-time-payment.model';
import { debounceTime, distinctUntilChanged, map, filter } from 'rxjs/operators';
import { MatLegacyDialog } from '@angular/material/legacy-dialog';
import { EmployeeOverrideRateDialogComponent } from './employee-override-rate-dialog/employee-override-rate-dialog.component';
import { ConfirmationModalComponent } from 'src/app/confirmation-modal/confirmation-modal.component';


@Component({
  selector: 'app-employee-override-rate',
  templateUrl: './employee-override-rate.component.html',
  styleUrls: ['./employee-override-rate.component.css']
})
export class EmployeeOverrideRateComponent implements OnInit {
  @Input() editedRate: string;
  base64Output : any;
  tableArr: EmployeeOverrideModel[] = [];
  employeeOverrideRate: any;
  employeeOverrideRateEdit;
  employeeOverrideRateAdd;
  employeeOverrideFormAdd: UntypedFormGroup;
  employeeOverrideFormEdit: UntypedFormGroup;
  editRow: boolean = false;
  addRow: boolean = false;
  originalDataSource: any;
  dataSource: any;
  searchText: string = "";
  displayedColumns = [];
  dropdowns: any;
  fileUpload: File | null = null;
  contacts: IContact[] = [];
  hideEmployeeTitle: boolean = false;
  maxStartDateEdit: Date;
  minEndDateEdit: Date;
  maxStartDateAdd: Date;
  minEndDateAdd: Date;
  errorEntries: any;
  salesDivisions = ["Sales- Direct", "Sales- Traditional", "Sales- Outreach"];
  teamsData = ["All","Roofing","Non-Roofing"];
  salesOffices: string[] = [];
  employeeTitles = ["District Manager (DM)", "Regional Manager (RM)", "Assistant District Manager (ADM)"];
  columnNames = [{
      id: "contactName",
      value: "Contact Name"
    },
    {
    id: "paycomId",
    value: "Paycom Id"

  }, {
    id: "salesDivision",
    value: "Sales Division"
  },
  {
    id: "salesOffice",
    value: "Sales Office"
  },
  {
    id: "effectiveStartDate",
    value: "Effective Start Date"
  },
  {
    id: "effectiveEndDate",
    value: "Effective End Date"
  },
  {
    id: "rate",
    value: "Rate"
  },
  {
    id: "employeeTitle",
    value: "Employee Title"
  },
  {
    id: "team",
    value: "Team"
  }];
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatLegacyPaginator, { static: true }) paginator: MatLegacyPaginator;

  formatter = (c: IContact) => c.contactName;
  search: OperatorFunction<string, readonly { contactId, contactName }[]> = (text$: Observable<string>) => text$.pipe(
    debounceTime(0),
    distinctUntilChanged(),
    filter(term => term.length >= 2),
    map(term => this.contacts.filter(c => new RegExp(term, 'mi').test(c.contactName)).slice(0, 10))
  )

  constructor(private formBuilder: UntypedFormBuilder, public apiService: ApiService, private toastMsg: ToastrService, private datePipe: DatePipe,
     private decimalPipe: DecimalPipe, private pipe: TableFilterPipe, private dialog: MatLegacyDialog) { }

  ngOnInit(): void {
    this.displayedColumns = this.columnNames.map(x => x.id);
    this.GetAllEmployeeOverrideRates();

    this.employeeOverrideFormEdit = this.formBuilder.group({
      employeeOverrideRateId: [0, [Validators.required]],
      salesDivision: ["", [Validators.required]],
      salesOffice: ["", [Validators.required]],
      effectiveStartDate: ["", [Validators.required]],
      effectiveEndDate: [""],
      rate: [""],
      employeeTitle: [""],
      contactId: [""],
      contactName:[""],
      team: [""],
    });

    this.employeeOverrideFormAdd = this.formBuilder.group({
      salesDivision: ["", [Validators.required]],
      salesOffice: ["", [Validators.required]],
      effectiveStartDate: ["", [Validators.required]],
      effectiveEndDate: [""],
      rate: [""],
      employeeTitle: [""],
      contactId: ["", [Validators.required]],
      team: [""],
    });

    this.getContacts();
    this.getSalesOffices();
    this.onChanges();
  }

  onChanges() {
    this.employeeOverrideFormAdd.get("salesDivision").valueChanges.subscribe(val => {
      if(val === this.salesDivisions[2]){
        this.employeeOverrideFormAdd.controls['employeeTitle'].enable();
        this.hideEmployeeTitle = true;
        }
        else{
          this.employeeOverrideFormAdd.controls['employeeTitle'].reset();
          this.employeeOverrideFormAdd.controls['employeeTitle'].disable({ onlySelf: true });
          this.hideEmployeeTitle = false;
        }
      }
    );
    this.employeeOverrideFormEdit.get("salesDivision").valueChanges.subscribe(val => {
      if(val === this.salesDivisions[2]){
          this.employeeOverrideFormEdit.controls['employeeTitle'].enable();
          this.hideEmployeeTitle = true;
        }
        else{
          this.employeeOverrideFormEdit.controls['employeeTitle'].reset();
          this.employeeOverrideFormEdit.controls['employeeTitle'].disable({ onlySelf: true });
          this.hideEmployeeTitle = false;
        }
      }
    );

    // this.employeeOverrideFormAdd.get("employeeTitle").valueChanges.subscribe(val => {
    //   if(val === this.employeeTitles[0]){
    //     this.employeeOverrideFormAdd.controls['rate'].reset();
    //     this.employeeOverrideFormAdd.controls['rate'].setValue("0.00");
    //     this.employeeOverrideFormAdd.controls['rate'].disable({ onlySelf: true });
    //     }
    //     else{
    //       this.employeeOverrideFormAdd.controls['rate'].enable();
    //     }
    //   }
    // );

    // this.employeeOverrideFormEdit.get("employeeTitle").valueChanges.subscribe(val => {
    //   if(val === this.employeeTitles[0]){
    //     this.employeeOverrideFormEdit.controls['rate'].reset();
    //     this.employeeOverrideFormEdit.controls['rate'].setValue("0.00");
    //     this.employeeOverrideFormEdit.controls['rate'].disable({ onlySelf: true });
    //     }
    //     else{
    //       this.employeeOverrideFormEdit.controls['rate'].enable();
    //     }
    //   }
    // );

  }

  GetAllEmployeeOverrideRates() {
    this.apiService.get('EmployeeOverrideRate')
      .subscribe(data => {
        this.employeeOverrideRate = data;
        this.displayedColumns = this.columnNames.map(x => x.id);
        this.createTable();
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }

  rowClick(employeeoverriderate: any) {
    this.editRow = !this.editRow;
    var overrideRate = this.employeeOverrideRate.filter(x => x.contactId === employeeoverriderate.contactId && x.salesDivision === employeeoverriderate.salesDivision && x.salesOffice === employeeoverriderate.salesOffice
     && this.decimalPipe.transform(x.rate, '1.2-2') === employeeoverriderate.rate && x.employeeTitle === employeeoverriderate.employeeTitle && this.datePipe.transform(x.effectiveStartDate, 'yyyy-MM-dd') === this.datePipe.transform(employeeoverriderate.effectiveStartDate, 'yyyy-MM-dd'));
    this.employeeOverrideRateEdit = overrideRate;

    this.employeeOverrideFormEdit.controls['employeeOverrideRateId'].setValue(this.employeeOverrideRateEdit[0].employeeOverrideRateId);
    this.employeeOverrideFormEdit.controls['salesDivision'].setValue(this.employeeOverrideRateEdit[0].salesDivision);
    this.employeeOverrideFormEdit.controls['salesOffice'].setValue(this.employeeOverrideRateEdit[0].salesOffice);
    this.employeeOverrideFormEdit.controls['effectiveStartDate'].setValue(this.datePipe.transform(this.employeeOverrideRateEdit[0].effectiveStartDate, 'yyyy-MM-dd'));
    this.employeeOverrideFormEdit.controls['effectiveEndDate'].setValue(this.datePipe.transform(this.employeeOverrideRateEdit[0].effectiveEndDate, 'yyyy-MM-dd'));
    this.employeeOverrideFormEdit.controls['rate'].setValue(this.employeeOverrideRateEdit[0].rate);
    this.employeeOverrideFormEdit.controls['employeeTitle'].setValue(this.employeeOverrideRateEdit[0].employeeTitle);
    this.employeeOverrideFormEdit.controls['team'].setValue(this.employeeOverrideRateEdit[0].team);
    this.employeeOverrideFormEdit.controls['contactId'].setValue(this.employeeOverrideRateEdit[0].contactId);
    this.employeeOverrideFormEdit.controls['contactName'].setValue(this.employeeOverrideRateEdit[0].contactName);
    this.employeeOverrideFormEdit.controls['contactId'].disable();

    this.employeeOverrideFormAdd.controls['salesDivision'].setValue(this.employeeOverrideRateEdit[0].salesDivision);
    this.employeeOverrideFormAdd.controls['salesOffice'].setValue(this.employeeOverrideRateEdit[0].salesOffice);
    this.employeeOverrideFormAdd.controls['effectiveStartDate'].setValue(this.datePipe.transform(this.employeeOverrideRateEdit[0].effectiveStartDate, 'yyyy-MM-dd'));
    //this.employeeOverrideFormAdd.controls['effectiveEndDate'].setValue(this.datePipe.transform(this.employeeOverrideRateEdit[0].effectiveEndDate, 'yyyy-MM-dd'));
    this.employeeOverrideFormAdd.controls['rate'].setValue(this.decimalPipe.transform(this.employeeOverrideRateEdit[0].rate, '1.2-2'));
    this.employeeOverrideFormAdd.controls['employeeTitle'].setValue(this.employeeOverrideRateEdit[0].employeeTitle);
    this.employeeOverrideFormAdd.controls['contactId'].setValue(this.employeeOverrideRateEdit[0].contactId);
    this.employeeOverrideFormAdd.controls['team'].setValue(this.employeeOverrideRateEdit[0].team);

    if(!this.employeeOverrideFormEdit.controls['employeeTitle'].disabled){
      this.hideEmployeeTitle = true;
    }
    else{
      this.hideEmployeeTitle = false;
    }

  }

  Add() {
    this.employeeOverrideRateAdd = this.tableArr;
    this.addRow = !this.addRow;

    this.employeeOverrideFormAdd.controls['salesDivision'].setValue(this.employeeOverrideRateAdd[0].salesDivision);
    this.employeeOverrideFormAdd.controls['salesOffice'].setValue(this.employeeOverrideRateAdd[0].salesOffice);
    this.employeeOverrideFormAdd.controls['effectiveStartDate'].setValue(this.datePipe.transform(this.employeeOverrideRateAdd[0].effectiveStartDate, 'yyyy-MM-dd'));
    //this.employeeOverrideFormAdd.controls['effectiveEndDate'].setValue(this.datePipe.transform(this.employeeOverrideRateAdd[0].effectiveEndDate, 'yyyy-MM-dd'));
    this.employeeOverrideFormAdd.controls['rate'].setValue(this.employeeOverrideRateAdd[0].rate);
    this.employeeOverrideFormAdd.controls['employeeTitle'].setValue(this.employeeOverrideRateAdd[0].employeeTitle);

    if(!this.employeeOverrideFormAdd.controls['employeeTitle'].disabled){
      this.hideEmployeeTitle = true;
    }
    else{
      this.hideEmployeeTitle = false;
    }
  }

  createTable() {
    let tableArr: EmployeeOverrideModel[] = [];
    for(let i:number = 0; i <= this.employeeOverrideRate.length - 1; i++) {
      let currentRow = this.employeeOverrideRate[i];
      if(i==0)
      {
        this.tableArr[0] =this.employeeOverrideRate[0];
      }
      tableArr.push({employeeOverrideRateId: currentRow.employeeOverrideRateId, paycomId: currentRow.paycomId, salesDivision: currentRow.salesDivision, salesOffice: currentRow.salesOffice, effectiveStartDate: this.datePipe.transform(currentRow.effectiveStartDate), effectiveEndDate: this.datePipe.transform(currentRow.effectiveEndDate),
        rate: this.decimalPipe.transform(currentRow.rate, '1.2-2') , employeeTitle: currentRow.employeeTitle, contactName: currentRow.contactName, contactId: currentRow.contactId,team:currentRow.team});
    }
    this.dataSource = new MatTableDataSource(tableArr);
    this.originalDataSource = tableArr;
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }

  getSalesOffices() {
    this.apiService.get('GetData/SalesOffices')
      .subscribe(data => {
        if (data && data.result) {
          this.salesOffices = data.result.map(office => { return <string>office });
          this.salesOffices.unshift('All');
        }
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      });
  }

  getContacts() {
    this.apiService.get('GetData/GetActiveContacts')
      .subscribe(data => {
        if (data && data.result) {
          this.contacts = data.result.map(type => { return <IContact>{ contactId: type.id, contactName: type.name } })
        }
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      })
  }

  onEditSubmit() {
      var values = {
        employeeOverrideRateId: this.employeeOverrideFormEdit.controls.employeeOverrideRateId.value,
        salesDivision: this.employeeOverrideFormEdit.controls.salesDivision.value,
        salesOffice: this.employeeOverrideFormEdit.controls.salesOffice.value,
        effectiveStartDate: this.employeeOverrideFormEdit.controls.effectiveStartDate.value,
        effectiveEndDate: this.employeeOverrideFormEdit.controls.effectiveEndDate.value,
        rate: this.employeeOverrideFormEdit.controls.rate.value,
        employeeTitle: this.employeeOverrideFormEdit.controls.employeeTitle.value == null ? "" : this.formatEmployeeTitle(this.employeeOverrideFormEdit.controls.employeeTitle.value),
        contactId: this.employeeOverrideFormEdit.controls.contactId.value,
        team: this.employeeOverrideFormEdit.controls.team.value,
      }

      var sDate = new Date(values.effectiveStartDate);
      sDate.setDate(sDate.getDate() + 1);
      var fom = new Date(sDate.getFullYear(), sDate.getMonth(), 1);
      var convertedEOM = null;
      var valid = true;

      if(values.effectiveEndDate){
        var eDate = new Date(values.effectiveEndDate);
        var eom = new Date(eDate.getFullYear(), eDate.getMonth()+1, 0);
        convertedEOM = this.datePipe.transform(eom, 'yyyy-MM-dd');
        valid = values.effectiveStartDate < values.effectiveEndDate ? true : false;
      }
      else {
        values.effectiveEndDate = null;
      }

      if (values.salesOffice === 'All') {
        const exists = this.originalDataSource.some((item) => item.contactId === values.contactId && item.salesDivision === values.salesDivision);
        if (exists) {
          let dialogRef = this.dialog.open(ConfirmationModalComponent, {
            data: {
              message: "Selecting 'ALL' will close out any existing Sales Office-specific assignments for the Override Contact. Do you want to proceed?"
            }
          });
          dialogRef.afterClosed().subscribe((res: boolean) => {
            if (!res) {
              return; 
            }
            this.callUpdateOverride(values, fom, convertedEOM, valid);
          })
          return
        }
      }
    if (values.salesOffice?.length == 2) {
      const salesDivisionExist = this.originalDataSource.some((item) => item.contactId === values.contactId && item.salesOffice.toLowerCase().includes(values.salesOffice.toLowerCase()) &&
        item.salesOffice.toLowerCase() !== values.salesOffice.toLowerCase() && item.team === values.team &&
        (
          item.effectiveEndDate === null ||
          new Date(item.effectiveEndDate) > new Date(values.effectiveStartDate)
        ));
      if (salesDivisionExist) {
        let dialogRef = this.dialog.open(ConfirmationModalComponent, {
          data: {
            message: "This user has active Sales Office-level overrides in the selected State.Proceeding will add the State-level override and automatically deactivate those Sales Office-level entries effective the day before the new State override’s start date. Do you want to continue?"
          }
        });
        dialogRef.afterClosed().subscribe((res: boolean) => {
          if (!res) {
            return;
          }
          this.callUpdateOverride(values, fom, convertedEOM, valid);
        })
        return
      }

    }
      
      if(values.effectiveStartDate == this.datePipe.transform(fom,  'yyyy-MM-dd') && (!values.effectiveEndDate || values.effectiveEndDate == convertedEOM) && valid){
        this.apiService.post('EmployeeOverrideRate/UpdateOverrideRate', values)
          .subscribe(data => {
            this.toastMsg.success('Employee Override Rate Updated Successfully');
            this.GetAllEmployeeOverrideRates();
            this.editRow = !this.editRow;
          }, 
          (err: any) => {
          if (err?.err?.message)
            this.toastMsg.error(err.message, "Server Error!");
          if (typeof err === "string") {
            let isJsonObject = false;            
            let parsedErr: any;
            try {
              parsedErr = JSON.parse(err);
              isJsonObject =parsedErr && typeof parsedErr === "object" &&!Array.isArray(parsedErr);
            } catch (e) {
              isJsonObject = false;
            }
            if (isJsonObject) {
              this.toastMsg.error(parsedErr.result, "Error!");
            } else this.toastMsg.error(err, "Error!");
          }
          if (err?.result) {
            this.toastMsg.error(err.result, "Error!");
          }
        }
      );
        }
        else{
          this.toastMsg.error('Invalid Date Range, Submission must be first and last day of the month with end date effective after start date.');
        }
    }

    callUpdateOverride(values: any, fom: Date, convertedEOM: any, valid: boolean){
      if (values.effectiveStartDate == this.datePipe.transform(fom, 'yyyy-MM-dd') && (!values.effectiveEndDate || values.effectiveEndDate == convertedEOM) && valid) {
        this.apiService.post('EmployeeOverrideRate/UpdateOverrideRate', values)
          .subscribe(data => {
            this.toastMsg.success('Employee Override Rate Updated Successfully');
            this.GetAllEmployeeOverrideRates();
            this.editRow = !this.editRow;
          }, 
          (err: any) => {
          if (err?.err?.message)
            this.toastMsg.error(err.message, "Server Error!");
          if (typeof err === "string") {
            let isJsonObject = false;            
            let parsedErr: any;
            try {
              parsedErr = JSON.parse(err);
              isJsonObject =parsedErr && typeof parsedErr === "object" &&!Array.isArray(parsedErr);
            } catch (e) {
              isJsonObject = false;
            }
            if (isJsonObject) {
              this.toastMsg.error(parsedErr.result, "Error!");
            } else this.toastMsg.error(err, "Error!");
          }
          if (err?.result) {
            this.toastMsg.error(err.result, "Error!");
          }
        });
      }
      else {
        this.toastMsg.error('Invalid Date Range, Submission must be first and last day of the month with end date effective after start date.');
      }
    }

    onAddSubmit() {
      var values = {
        salesDivision: this.employeeOverrideFormAdd.controls.salesDivision.value,
        salesOffice: this.employeeOverrideFormAdd.controls.salesOffice.value,
        effectiveStartDate: this.employeeOverrideFormAdd.controls.effectiveStartDate.value,
        effectiveEndDate: this.employeeOverrideFormAdd.controls.effectiveEndDate.value,
        rate: this.employeeOverrideFormAdd.controls.rate.value == null ? 0.00 : this.employeeOverrideFormAdd.controls.rate.value,
        employeeTitle: this.employeeOverrideFormAdd.controls.employeeTitle.value == null ? "" : this.formatEmployeeTitle(this.employeeOverrideFormAdd.controls.employeeTitle.value),
        contactId: this.employeeOverrideFormAdd.controls.contactId.value.contactId,
        team: this.employeeOverrideFormAdd.controls.team.value,
      }

      var sDate = new Date(values.effectiveStartDate);
      sDate.setDate(sDate.getDate() + 1);
      var fom = new Date(sDate.getFullYear(), sDate.getMonth(), 1);
      var convertedEOM = null;
      var valid = true;

      if(values.effectiveEndDate){
        var eDate = new Date(values.effectiveEndDate);
        var eom = new Date(eDate.getFullYear(), eDate.getMonth()+1, 0);
        convertedEOM = this.datePipe.transform(eom, 'yyyy-MM-dd');
        var valid = values.effectiveStartDate < values.effectiveEndDate ? true : false;
      }

      if (values.salesOffice === 'All') {
        const exists = this.originalDataSource.some((item) => item.contactId === values.contactId && item.salesDivision === values.salesDivision);
        if (exists) {
          let dialogRef = this.dialog.open(ConfirmationModalComponent, {
            data: {
              message: "Selecting 'ALL' will close out any existing Sales Office-specific assignments for the Override Contact. Do you want to proceed?"
            }
          });
          dialogRef.afterClosed().subscribe((res: boolean) => {
            if (!res) {
              return; 
            }
            this.callAddOverride(values, fom, convertedEOM, valid);
          })
          return
        }
      }
      if (values.salesOffice?.length == 2) {
      const salesDivisionExist = this.originalDataSource.some((item) => item.contactId === values.contactId && item.salesOffice.toLowerCase().includes(values.salesOffice.toLowerCase()) &&
        item.salesOffice.toLowerCase() !== values.salesOffice.toLowerCase() && item.team === values.team &&
        (
          item.effectiveEndDate === null ||
          new Date(item.effectiveEndDate) > new Date(values.effectiveStartDate)
        ));
      if (salesDivisionExist) {
        let dialogRef = this.dialog.open(ConfirmationModalComponent, {
          data: {
            message: "This user has active Sales Office-level overrides in the selected State.Proceeding will add the State-level override and automatically deactivate those Sales Office-level entries effective the day before the new State override’s start date. Do you want to continue?"
          }
        });
        dialogRef.afterClosed().subscribe((res: boolean) => {
          if (!res) {
            return;
          }
          this.callAddOverride(values, fom, convertedEOM, valid);
        })
        return
      }

    }

      if(values.effectiveStartDate == this.datePipe.transform(fom,  'yyyy-MM-dd') && (!values.effectiveEndDate || values.effectiveEndDate == convertedEOM) && valid){
        this.apiService.post('EmployeeOverrideRate/AddOverrideRate', values)
          .subscribe(data => {
            this.toastMsg.success('Employee Override Rate Added Successfully');
            this.GetAllEmployeeOverrideRates();
            this.addRow = !this.addRow;
          }, 
          (err: any) => {
          if (err?.err?.message)
            this.toastMsg.error(err.message, "Server Error!");
          if (typeof err === "string") {
            let isJsonObject = false;            
            let parsedErr: any;
            try {
              parsedErr = JSON.parse(err);
              isJsonObject =parsedErr && typeof parsedErr === "object" &&!Array.isArray(parsedErr);
            } catch (e) {
              isJsonObject = false;
            }
            if (isJsonObject) {
              this.toastMsg.error(parsedErr.result, "Error!");
            } else this.toastMsg.error(err, "Error!");
          }
          if (err?.result) {
            this.toastMsg.error(err.result, "Error!");
          }
        });
      }
      else{
        this.toastMsg.error('Invalid Date Range, Submission must be first and last day of the month with end date effective after start date.');
      }


    }
    callAddOverride(values: any, fom: Date, convertedEOM: any, valid: boolean) {
      if (values.effectiveStartDate == this.datePipe.transform(fom, 'yyyy-MM-dd') &&
        (!values.effectiveEndDate || values.effectiveEndDate == convertedEOM) &&
        valid) {
        this.apiService.post('EmployeeOverrideRate/AddOverrideRate', values)
          .subscribe(data => {
            this.toastMsg.success('Employee Override Rate Added Successfully');
            this.GetAllEmployeeOverrideRates();
            this.addRow = !this.addRow;
          }, 
          (err: any) => {
          if (err?.err?.message)
            this.toastMsg.error(err.message, "Server Error!");
          if (typeof err === "string") {
            let isJsonObject = false;            
            let parsedErr: any;
            try {
              parsedErr = JSON.parse(err);
              isJsonObject =parsedErr && typeof parsedErr === "object" &&!Array.isArray(parsedErr);
            } catch (e) {
              isJsonObject = false;
            }
            if (isJsonObject) {
              this.toastMsg.error(parsedErr.result, "Error!");
            } else this.toastMsg.error(err, "Error!");
          }
          if (err?.result) {
            this.toastMsg.error(err.result, "Error!");
          }
        });  
      } else {
        this.toastMsg.error('Invalid Date Range, Submission must be first and last day of the month with end date effective after start date.');
      }
    }
    formatEmployeeTitle(title: string){
      var result = "";
      if(title == this.employeeTitles[0]){
        result = "DM";
      }
      else if(title == this.employeeTitles[1]) {
        result = "RM";
      }
      else if (title == this.employeeTitles[2]){
        result = "ADM"
      }

      return result;
    }

    deleteRate(){
      var values = {
        employeeOverrideRateId: this.employeeOverrideFormEdit.controls.employeeOverrideRateId.value,
        salesDivision: this.employeeOverrideFormEdit.controls.salesDivision.value,
        salesOffice: this.employeeOverrideFormEdit.controls.salesOffice.value,
        effectiveStartDate: this.employeeOverrideFormEdit.controls.effectiveStartDate.value,
        effectiveEndDate: this.employeeOverrideFormEdit.controls.effectiveEndDate.value,
        rate: this.employeeOverrideFormEdit.controls.rate.value,
        employeeTitle: this.employeeOverrideFormEdit.controls.employeeTitle.value == null ? "" : this.formatEmployeeTitle(this.employeeOverrideFormEdit.controls.employeeTitle.value),
        contactId: this.employeeOverrideFormEdit.controls.contactId.value
      }

      this.apiService.post('EmployeeOverrideRate/DeleteOverrideRate', values)
        .subscribe(data => {
          this.toastMsg.success('Employee Override Rate Removed Successfully');
          this.GetAllEmployeeOverrideRates();
          this.editRow = !this.editRow;
        }, (err: any) => {
          if (err?.err?.message)
            this.toastMsg.error(err.message, "Server Error!");
          if (typeof err === "string") {
            let isJsonObject = false;            
            let parsedErr: any;
            try {
              parsedErr = JSON.parse(err);
              isJsonObject =parsedErr && typeof parsedErr === "object" &&!Array.isArray(parsedErr);
            } catch (e) {
              isJsonObject = false;
            }
            if (isJsonObject) {
              this.toastMsg.error(parsedErr.result, "Error!");
            } else this.toastMsg.error(err, "Error!");
          }
          if (err?.result) {
            this.toastMsg.error(err.result, "Error!");
          }
        }
      );
    }

    searchForItem():void {
      let filteredResults: EmployeeOverrideModel[] = [];
      if (this.searchText == '') {
        this.dataSource = new MatTableDataSource(this.originalDataSource);
        this.dataSource.sort = this.sort;
      this.dataSource.paginator = this.paginator;
      } else {
        filteredResults = this.pipe.transform(this.originalDataSource, this.searchText);
        this.dataSource = new MatTableDataSource(filteredResults);
        this.dataSource.sort = this.sort;
        this.dataSource.paginator = this.paginator;
      }
    }

    clearEditStartDate(date: HTMLInputElement) {
      date.value = "";
      this.employeeOverrideFormEdit.controls.effectiveStartDate.setValue('');
    }
    clearEditEndDate(date: HTMLInputElement) {
      date.value = "";
      this.employeeOverrideFormEdit.controls.effectiveEndDate.setValue('');
    }
    clearAddStartDate(date: HTMLInputElement) {
      date.value = "";
      this.employeeOverrideFormAdd.controls.effectiveStartDate.setValue('');
    }
    clearAddEndDate(date: HTMLInputElement) {
      date.value = "";
      this.employeeOverrideFormAdd.controls.effectiveEndDate.setValue('');
    }

    handleFileInput($event: Event) {
      var file = ($event.target as HTMLInputElement).files[0];
      this.convertFile(file);
      ($event.target as HTMLInputElement).value = "";
    }

    convertFile(file: File) {
      this.base64Output = null;
      var observe = new Observable((subscriber: Subscriber<any>) => {
        this.readFile(file, subscriber);
      });
      observe.subscribe((d) => {
        this.base64Output = d;
      });
    }

    readFile(file: File, subscriber: Subscriber<any>){
      var reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload=()=>{
        subscriber.next(reader.result);
        subscriber.complete();
      }
      reader.onerror=(error)=>{
        subscriber.error(error);
        subscriber.complete();
      }
    }
    
    upload(){
    if(!this.base64Output){
      this.toastMsg.error('File has not been uploaded or processed!');
    }
    else{
      this.apiService.post('EmployeeOverrideRate/UpdateFromExcel', {"output": this.base64Output.split(';base64,')[1]})
      .subscribe((data) => {
        var res: any = data;
        if(res.length == 0){
          this.GetAllEmployeeOverrideRates();
          this.toastMsg.success('Employee Override Rate Updated Successfully');
          this.base64Output = null;
        }
        else{
          this.GetAllEmployeeOverrideRates();
          this.errorEntries = data;
          this.dialog.open(EmployeeOverrideRateDialogComponent, {
            width: '800px',
            data: {
              errors: this.errorEntries
            },
          });
          this.base64Output = null;
        }

      }, (err: any) => {
          if (err?.err?.message)
            this.toastMsg.error(err.message, "Server Error!");
          if (typeof err === "string") {
            let isJsonObject = false;            
            let parsedErr: any;
            try {
              parsedErr = JSON.parse(err);
              isJsonObject =parsedErr && typeof parsedErr === "object" &&!Array.isArray(parsedErr);
            } catch (e) {
              isJsonObject = false;
            }
            if (isJsonObject) {
              this.toastMsg.error(parsedErr.result, "Error!");
            } else this.toastMsg.error(err, "Error!");
          }
          if (err?.result) {
            this.toastMsg.error(err.result, "Error!");
          }
        });
    }
  }

}

export interface EmployeeOverrideModel{
  employeeOverrideRateId: number;
  paycomId: string;
  salesDivision: string;
  salesOffice: string;
  effectiveStartDate: string;
  effectiveEndDate: string;
  rate: string;
  employeeTitle: string;
  contactName: string;
  contactId: string;  
  team:string;
}