import { Component, OnInit } from '@angular/core';
import { ApiService } from "../../../services/api.service";
import { Router, ActivatedRoute } from "@angular/router";
import { ToastrService } from 'ngx-toastr';
declare var $: any;

@Component({
  selector: 'app-version-rule',
  templateUrl: './version-rule.component.html',
  styleUrls: ['./version-rule.component.css']
})
export class VersionRuleComponent implements OnInit {

  ruleId: number;
  versionNo: number;
  ruleObj: any
  cloneRule: any;
  loadPage: boolean = false
  constructor(public apiService: ApiService, private router: Router, private activatedRoute: ActivatedRoute, private toastMsg: ToastrService) {
  }

  ngOnInit() {
    if (!this.apiService.checkPermission('CloneRule')) {
      this.apiService.goBack();
      this.toastMsg.error("Insufficient Permissions. Please make sure your account can access this information.", "Permissions");
    }
    this.activatedRoute.params.subscribe(params => {
      this.ruleId = parseInt(params.rule_id);
      this.versionNo = parseInt(params.version_no);

      if (localStorage.getItem('LastRuleDetails')) {
        this.ruleObj = JSON.parse(localStorage.getItem('LastRuleDetails'));
        if (this.ruleObj.rule_id != this.ruleId) {
          this.apiService.get('Rule/' + this.ruleId + '/' + this.versionNo)
            .subscribe(data => {
              if (data.statusCode === "200" || data.statusCode === "201") {
                this.loadPage = true;
                localStorage.setItem('LastRuleDetails', JSON.stringify(data.result))
                this.ruleObj = JSON.parse(localStorage.getItem('LastRuleDetails'));
                this.ruleObj.description = this.ruleObj.description;
                this.cloneRule = this.ruleObj;
                this.cloneRule.newVersion = true;
              }
              else {
                this.toastMsg.error("Server", 'Error!')
              }
            }, (err: any) => {
              this.toastMsg.error(err.message, 'Error!')
            });
        } else {
          this.ruleObj.description = this.ruleObj.description;
          this.cloneRule = this.ruleObj;
          this.cloneRule.newVersion = true;
          this.loadPage = true;
        }
      }
      })
  }
}
