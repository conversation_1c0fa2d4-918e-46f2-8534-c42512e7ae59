import { Component, OnInit, ViewChild } from '@angular/core';
import { ApiService } from 'src/app/services/api.service';
import { environment } from "src/environments/environment";
import { ToastrService } from 'ngx-toastr';
import { IOutreachOpportunity, OutreachOpportunity } from 'src/app/model/outreach-opportunity.model';
import { HttpClient, HttpParams } from '@angular/common/http';
import { ApiResponse } from 'src/app/services/api.response';
import * as FileSaver from 'file-saver';
import { MatLegacyPaginator } from '@angular/material/legacy-paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';

@Component({
  selector: 'app-outreach-opportunities',
  templateUrl: './outreach-opportunities.component.html',
  styleUrls: ['./outreach-opportunities.component.css']
})
export class OutreachOpportunitiesComponent implements OnInit {
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatLegacyPaginator, { static: true }) paginator: MatLegacyPaginator;
  outreachOpportunities: MatTableDataSource<IOutreachOpportunity> = new MatTableDataSource([]);
  pageSizeOptions: number[] = [10, 20, 50];
  pageSize: number = 50;
  outreachColumns: string[] = ["opportunityName", "demoDate", "hireDate", "monthlyDemoNumber", "sumofPayments", "leadGenerator", "leadGeneratorPlanName", "employeeStatus"];
  searchText: string = "";
  withdrawalPage: number = 1;
  public date: Date;
  DemoDateRangeStart: Date = null;
  DemoDateRangeEnd: Date = null;
  HireDatestart: Date = null;
  HireDateEnd: Date = null;
  LeadGenerator: string = "";
  PlanName: string = "";
  start: string = "";
  end: string = "";
  
  IsProcess: boolean = false;
  filter: boolean = false;

  constructor(private apiService: ApiService, private toastMsg: ToastrService, private http: HttpClient) { }

  ngOnInit() {
	if (!this.apiService.checkPermission('ExtractToPaycom')) {
      this.apiService.goBack();
      this.toastMsg.error("Insufficient Permissions. Please make sure your account can access this information.", "Permissions");
    }
	this.getAllOutreachOpportunities();
  }

  getAllOutreachOpportunities() {
    let params = new HttpParams();
	  params = params.append('DemoDateRangeStart', this.DemoDateRangeStart ? this.DemoDateRangeStart.toString() : '');
    params = params.append('DemoDateRangeEnd', this.DemoDateRangeEnd ? this.DemoDateRangeEnd.toString() : '');
    params = params.append('ContactName', this.LeadGenerator ? this.LeadGenerator.toString() : '');
    params = params.append('PlanName', this.PlanName ? this.PlanName.toString() : '');
    params = params.append('HireDatestart', this.HireDatestart ? this.HireDatestart.toString() : '');
    params = params.append('HireDateEnd', this.HireDateEnd ? this.HireDateEnd.toString() : '');
    
    this.http.get<ApiResponse>(`${environment.apiBaseUrl}Opportunities/OutreachOpportunities`, { params: params }) 
      .subscribe(data => {
        if (data && data.result) {
          var oo = data.result.map((bal: IOutreachOpportunity) => { return bal });
          this.outreachOpportunities = new MatTableDataSource(oo);
          this.outreachOpportunities.paginator = this.paginator;
          this.outreachOpportunities.sort = this.sort;
          this.applyFilter(this.searchText);

          console.log("Outreach Opportunities", this.outreachOpportunities);
        }
      }, (err: any) => {
        this.toastMsg.error(err.message, "Error!");
      })
  }

  onChangeFilter() {
    this.getAllOutreachOpportunities();
  }

  // METHOD OVERLOAD
  applyFilter(input: string): void;

  applyFilter(input: Event): void;

  applyFilter(input: any): any {
    var filterValue: string;
    if (typeof input === "string") {
      filterValue = input;
    } else {
      filterValue = (input.target as HTMLInputElement).value;
    }
    this.outreachOpportunities.filter = filterValue.trim().toLowerCase();
  }
   
 getOpportunityWorksheet() {
	 this.start  = this.DemoDateRangeStart ? this.DemoDateRangeStart.toString() : '';
   this.end = this.DemoDateRangeEnd ? this.DemoDateRangeEnd.toString() : '';

   this.http.get(`${environment.apiBaseUrl}Opportunities/OutreachRexport/OutreachOpportunities?DemoDateRangeStart=${this.start}&DemoDateRangeEnd=${this.end}&ContactName=${this.LeadGenerator}&PlanName=${this.PlanName}`, { responseType: 'blob' })
      .subscribe(data => {
        this.downLoadFile(data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      });
  }

  /**
   * Method is use to download file.
   * @param data - Array Buffer data
   * @param type - type of the document.
   */
  downLoadFile(data: any, type: string) {
    let blob = new Blob([data], { type: type });
    let date: Date = new Date();

    FileSaver.saveAs(blob, `Lead_Generator_Demos_${date}.xlsx`);
  }

  openSalesrep(id) {
    const url = `#/ui/commissions/salesrep/${id}`;
    window.open(url, '_blank');
  }
  
  
  openOpportunity(id) {
    const url = `#/ui/commissions/opportunitydetails/${id}`;
    window.open(url, '_blank');
  }


}
