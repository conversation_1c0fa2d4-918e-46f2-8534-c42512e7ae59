import { Component, OnInit } from '@angular/core';
import { ApiService } from 'src/app/services/api.service';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-home-depot-employee-override',
  templateUrl: './home-depot-employee-override.component.html',
  styleUrls: ['./home-depot-employee-override.component.css']
})
export class HomeDepotEmployeeOverrideComponent implements OnInit {
  selectedYear: number;
  selectedMonth: number;

  constructor(public apiService: ApiService, private toastMsg: ToastrService) { }

  ngOnInit(): void {
    if (!this.apiService.checkPermission('ViewRateTables')) {
      this.apiService.goBack();
      this.toastMsg.error("Insufficient Permissions. Please make sure your account can access this information.", "Permissions");
    }
  }

  onSubmit() {

    if (!(this.selectedYear > 2000 && this.selectedYear < 2099)) {
        this.toastMsg.error('Please enter valid year number between 2000 and 2099');
        return;
    }
   if (!(this.selectedMonth > 0 && this.selectedMonth < 13)) {
    this.toastMsg.error('Please enter valid month number between 1 and 12');
    return;
   }
   
    var body = {
        Year: this.selectedYear,
        Month: this.selectedMonth
    }
    this.apiService.post('EmployeeOverride/HomeDepot', body)
        .subscribe(data => {
            this.toastMsg.success('Home Depot override snapshot taken successfully. Please check dynamic report to see the snapshot.');
            }, (err: any) => {
            this.toastMsg.error(err.message, 'Server Error!');
        });
  }
}
