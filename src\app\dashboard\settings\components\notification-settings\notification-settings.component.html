<div class="page-title col-md-12 ">
    <h1>Notification Preference</h1>
  </div>
  <div class="content">
    <div class="card">
      <div class="card-header-info">
        <h4 class="card-title no-hover-effect"><i class="fas fa-dollar-sign"></i> Notification Preference</h4>
      </div>
      <div class="card-body">
          <form [formGroup]="notificationPreferenceForm"  class="w-100">
            <div class="row">
              <div class="form-group col-md-6"> <div class="row">
                <label class="col-sm-5">Notification Types</label>
                <div class="col-sm-7" *ngIf="notificationTypeList">
                    <div class="scrollable-list">
                        <ng-container *ngFor="let item of notificationTypeList">
                            <mat-checkbox (change)="onCheckboxChange(item)" [checked]="selectedItems?.includes(item.notificationTypeId)">
                                {{ item?.notificationTypeName }}
                            </mat-checkbox>
                        </ng-container>
                    </div>
              </div>
              </div>
            </div>
            </div>
            <div class="row align-button-right" style="padding-top: 7px;">
              <button type="submit" class="btn btn-primary" (click)="onSave()"><i class="fas fa-plus"></i> Save</button>
            </div>
          </form>
      </div>
    </div>
  </div>