<div class="page-title col-md-12 ">
  <div class="row">
    <div class="col-5">
      <h1>Business User Dashboard</h1>
    </div>
    <div class="col-7 d-flex justify-content-end pt-2">
      <button class="button-dashboard cursor-default" *ngIf="selectedTimeRange !='week'">{{selectedTimeRange =='month'? month:''}} {{selectedTimeRange =='month'? '-':''}} {{year}}</button>
      <button class="button-dashboard width-200 cursor-default" style="pointer-events: none;" *ngIf="selectedTimeRange =='week'" role="button">{{selectedDateRange.startDate | date:'MMM dd yyyy'}} To {{selectedDateRange.endDate | date:'MMM dd yyyy'}}</button>
      <button class="button-dashboard width-35 mx-1" role="button" title="Previous" (click)="previous()"><i class="fa fa-angle-double-left p-2" aria-hidden="true"></i></button>
      <button class="button-dashboard" [ngClass]="{'button-dashboard-custom':isSelectedMonth}" role="button" (click)="fetchData('month')">Month</button>
      <button class="button-dashboard mx-1" [ngClass]="{'button-dashboard-custom':isSelectedYear}" role="button" (click)="fetchData('year')">Year</button>      
      <button class="button-dashboard" [ngClass]="{'button-dashboard-custom':isSelectedWeek}" role="button" (click)="fetchData('week')">Week</button>
      <button class="button-dashboard width-35 mx-1" role="button" [disabled]="isDateRangeDisabled()" title="Next" (click)="next()"><i class="fa fa-angle-double-right p-2" aria-hidden="true"></i></button>
    </div>
  </div>
</div>

<div class="content">
  <div class="row">
    <div class="col-md-12 mt-2">
      <div class="card" style='min-height:800px'>
        <mat-tab-group animationDuration="500ms" (selectedTabChange)="OnChangeTab($event)">
          <mat-tab label="Summary">
            <app-all-charts [dateRange]="selectedDateRange" [tabNumber]="selectedTab"></app-all-charts>
          </mat-tab>
          <mat-tab label="Scheduled Job Status">
            <app-scheduled-jobs [dateRange]="selectedDateRange" [tabNumber]="selectedTab"></app-scheduled-jobs>
          </mat-tab>
          <mat-tab label="Reps with No Plans">
            <app-reps-without-plans [dateRange]="selectedDateRange" [tabNumber]="selectedTab"></app-reps-without-plans>
          </mat-tab>
          <mat-tab label="Pending Payments">
            <app-pending-payments [dateRange]="selectedDateRange" [tabNumber]="selectedTab"></app-pending-payments>
          </mat-tab>
          <mat-tab label="Processed Payments">
            <app-processed-payment [dateRange]="selectedDateRange" [tabNumber]="selectedTab"></app-processed-payment>
          </mat-tab>
          <mat-tab label="Break Down by Product Type">
            <app-breakdown-product [dateRange]="selectedDateRange" [tabNumber]="selectedTab"></app-breakdown-product>
          </mat-tab>
          <mat-tab label="Payments On Hold">
            <app-payment-hold [dateRange]="selectedDateRange" [tabNumber]="selectedTab"></app-payment-hold>
          </mat-tab>
          <mat-tab label="Recent Commission History">
            <app-recent-commission-history [dateRange]="selectedDateRange" [tabNumber]="selectedTab"></app-recent-commission-history>
          </mat-tab>
          <mat-tab label="Kilowatts by Division and States">
            <app-kilowatt [dateRange]="selectedDateRange" [tabNumber]="selectedTab"></app-kilowatt>
          </mat-tab>
          <mat-tab label="Recent EOM Executions History">
            <app-recent-eom [dateRange]="selectedDateRange" [tabNumber]="selectedTab"></app-recent-eom>
          </mat-tab>
          <mat-tab label="Aging Payments on Hold">
            <app-aging-payments [dateRange]="selectedDateRange" [tabNumber]="selectedTab"></app-aging-payments>
          </mat-tab>
          <mat-tab label="Dynamic Report Weekly Status">
            <app-dynamic-report [dateRange]="selectedDateRange" [tabNumber]="selectedTab"></app-dynamic-report>
          </mat-tab>

        </mat-tab-group>
      </div>
    </div>
  </div>
</div>