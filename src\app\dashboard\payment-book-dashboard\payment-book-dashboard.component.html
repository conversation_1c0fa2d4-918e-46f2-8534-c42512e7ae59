<div class="page-title col-md-12 ">
  <h1>Payment Book</h1>
  <div class="breadcrumbs"><a href="#">Home</a>/<a [routerLink]="['/ui/commissions/salesrep', contactId]">Contact Details</a>/<span>Payment Book</span>
  </div>
</div>
<div class=" w-100">
  <div class="content">
    <div class="container-fluid">
      <div class="card">
        <div class="card-header-info">

          <h4 class="card-title">Payment Book</h4>

        </div>
        <div class="card-body">

          <div class="row">
            <div class="col-md-12">
              <div class="float-right ">
                <button class="btn btn-primary" (click)="downLoadTransactionFile()"><i class="material-icons">save_alt</i>
                  Download</button>
              </div>
              <div class="float-right col-md-3 text-right pr-2">
                <div class="form-group input-group ">
          
                  <input class="custom-input ng-pristine ng-valid ng-touched" type="text" id="searchTextId"
                    [(ngModel)]="searchText" (keyup)="applyFilter($event)" name="searchText" placeholder="Search">
                  <span class="input-group-icon">
                    <i class="fas fa-search"></i>
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div class="row mt-4">
            <div class="col-md-4">
              <div class="row">
                <label class="col-5">Name</label>
                <span class="col-7"><a [routerLink]="['/ui/commissions/salesrep', contactId]">{{contactPaymentBook.contactName}}</a></span>

              </div>
            </div>
            <div class="col-md-4">
              <div class="row">
                <label class="col-5">Payment Book Type</label>
                <span class="col-7">{{contactPaymentBook.paymentBookTypeName}}</span>
              </div>
            </div>
            <div class="col-md-4">
              <div class="row">
                <label class="col-5">Weekly Pay</label>
                <span class="col-7">{{contactPaymentBook.weeklyPay | currency}}</span>
              </div>
            </div>
            <div class="col-md-4">
              <div class="row">

                <label class="col-5">Overdraw Limit</label>
                <span class="col-7">{{contactPaymentBook.overdrawLimit | currency}}</span>
              </div>
            </div>
            <div class="col-md-4">
              <div class="row">
                <label class="col-5">Current Balance</label>
                <span class="col-7">{{getCurrentBalance() | currency}}</span>
              </div>
            </div>


          </div>
          <table mat-table [dataSource]="transactions" matSort class="mt-3 my-table w-100">
            <ng-container matColumnDef="selected">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Select </th>
              <td mat-cell *matCellDef="let element">
                <section class="checkbox-section" *ngIf="checkboxDisabled(element) == false">
                  <mat-checkbox [(ngModel)]="element.selected" (change)="onSelectionChange()">
                  </mat-checkbox>
                </section>
                <section class="checkbox-section" *ngIf="checkboxDisabled(element) == true">
                  <mat-checkbox [(ngModel)]="element.selected" (change)="onSelectionChange()"
                    disabled="isCheckboxDisabled">
                  </mat-checkbox>
                </section>
              </td>
              </ng-container>
            <ng-container matColumnDef="dateProcessed">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> Date Processed </th>
              <td data-td-head="Date Processed" mat-cell *matCellDef="let element"> {{element.dateProcessed | date}}
              </td>
            </ng-container>

            <ng-container matColumnDef="opportunityName">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> Opportunity </th>
              <td data-td-head="Opportunity" mat-cell *matCellDef="let element"> <a
                  [routerLink]="['/ui/commissions/opportunitydetails', element.opportunityId ? element.opportunityId : 0]">{{element.opportunityName ? element.opportunityName : "-"}}</a>
              </td>
            </ng-container>

            <ng-container matColumnDef="commissionTransactionTypeName">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> Transaction Type </th>
              <td data-td-head="Transaction Type" mat-cell *matCellDef="let element">
                {{element.commissionTransactionTypeName}} </td>
            </ng-container>

            <ng-container matColumnDef="paymentTypeName">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> Payment Type </th>
              <td data-td-head="Payment Type" mat-cell *matCellDef="let element">
                {{element.paymentTypeName ? element.paymentTypeName : "-"}}
              </td>
            </ng-container>

            <ng-container matColumnDef="credit">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> Credit </th>
              <td data-td-head="Credit" mat-cell *matCellDef="let element">
                {{element.debitCredit == "C" ? (element.amount | currency) : "-"}} </td>
            </ng-container>

            <ng-container matColumnDef="debit">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> Debit </th>
              <td data-td-head="Debit" mat-cell *matCellDef="let element">
                {{element.debitCredit == "D" ? (element.amount | currency) : "-"}} </td>
            </ng-container>
            <ng-container matColumnDef="transactionName">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> Transaction Name </th>
              <td data-td-head="transactionName" mat-cell *matCellDef="let element">
                {{element.transactionName}} </td>
            </ng-container>
            <ng-container matColumnDef="paymentNote">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> Payment Note </th>
              <td data-td-head="paymentNote" mat-cell *matCellDef="let element">
                {{element.paymentNote}} </td>
            </ng-container>
            <ng-container matColumnDef="paymentReversalNote">
              <th mat-header-cell *matHeaderCellDef mat-sort-header> Payment Reversal Note </th>
              <td data-td-head="paymentReversalNote" mat-cell *matCellDef="let element">
                {{element.paymentReversalNote}} </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="columns"></tr>
            <tr mat-row *matRowDef="let row; columns: columns;"></tr>
          </table>
          <mat-paginator [pageSize]="5" [pageSizeOptions]="pageSizeOptions" style="margin-top: 2%;">
          </mat-paginator>
          
          <div>
            <div class="col-md-12">
              <div class="col-md-4 float-right">
                <span>
                  <mat-checkbox [(ngModel)]="AllowUsers"> Allowing user to enter a withdrawal Amount</mat-checkbox>
                </span>          
                <div>          
                  <input class="custom-input" id="withdrawalAmount" [(ngModel)]="withdrawalAmount" currencyMask
                    [options]="{ allowNegative: true, align: 'left' }" placeholder="Enter a Withdrawal Amount" required>
                </div>
                <p>Please enter a name for the withdrawal:</p>

                <div>
                  <input class="custom-input" id="withdrawalName" [(ngModel)]="withdrawalName"
                    placeholder="Name for the withdrawal">
                </div>
                <button class="btn float-right btn-primary" (click)="submitPaymentBookWithdrawalsPayBook()"
                  [disabled]="!checkSelected()"><i class="far fa-money-bill-alt"></i> withdraw </button>
              </div>   
              <div class="col-md-4 float-left rev-note-area">                
                <div>
                  <p>Notes</p>
                  <input class="custom-input" id="paymentReverseNote" [(ngModel)]="paymentReversalNotes"
                    placeholder="Enter Reversal Notes">
                </div>
                <button class="btn float-left btn-danger" (click)="ReversePayment()" [disabled]="!checkReverseSelected()"><i
                  class="far fa-money-bill-alt"></i> Reverse Selected Payment(s)</button>
              </div>
            </div>          
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
