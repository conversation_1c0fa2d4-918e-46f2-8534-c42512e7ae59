import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { ProductPurchaseMethodDeductionMaintenanceDialogComponent } from './product-purchase-method-deduction-maintenance-dialog.component';

describe('ProductPurchaseMethodDeductionMaintenanceDialogComponent', () => {
  let component: ProductPurchaseMethodDeductionMaintenanceDialogComponent;
  let fixture: ComponentFixture<ProductPurchaseMethodDeductionMaintenanceDialogComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ ProductPurchaseMethodDeductionMaintenanceDialogComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ProductPurchaseMethodDeductionMaintenanceDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
