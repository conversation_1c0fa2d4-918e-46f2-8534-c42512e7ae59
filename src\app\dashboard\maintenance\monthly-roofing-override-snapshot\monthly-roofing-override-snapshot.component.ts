import { Component, OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { ApiService } from 'src/app/services/api.service';

@Component({
  selector: 'app-monthly-roofing-override-snapshot',
  templateUrl: './monthly-roofing-override-snapshot.component.html',
  styleUrls: ['./monthly-roofing-override-snapshot.component.css']
})
export class MonthlyRoofingOverrideSnapshotComponent implements OnInit {
  selectedYear: number;
  selectedMonth: number;
  selectedDivision: string;
  salesDivisions: string[] = ["Sales- Direct", "Sales- Traditional", "Sales- Outreach"];
  constructor(public apiService: ApiService, private toastMsg: ToastrService,) { }

  ngOnInit() {
    if (!this.apiService.checkPermission('ViewRateTables')) {
      this.apiService.goBack();
      this.toastMsg.error("Insufficient Permissions. Please make sure your account can access this information.", "Permissions");
    }
  }
  onSubmit() {
    if (!(this.selectedYear > 2000 && this.selectedYear < 2099)) {
      this.toastMsg.error('Please enter valid year number between 2000 and 2099');
      return;
    }
    if (!(this.selectedMonth > 0 && this.selectedMonth < 13)) {
      this.toastMsg.error('Please enter valid month number between 1 and 12');
      return;
    }
    if (this.selectedDivision == "") {
      this.toastMsg.error('Please select sales division');
      return;
    }

    var body = {
      Year: this.selectedYear,
      Month: this.selectedMonth
    }
    this.apiService.post('RoofEmployeeMonthlyOverrideRate/MonthlyRoofingOverride', body)
      .subscribe(data => {
        this.toastMsg.success('Monthly roofing override snapshot taken successfully. Please check dynamic report to see the snapshot.');
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }

}
