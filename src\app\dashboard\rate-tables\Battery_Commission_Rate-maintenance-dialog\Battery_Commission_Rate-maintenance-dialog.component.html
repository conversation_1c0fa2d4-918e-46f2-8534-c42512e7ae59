<div class="dailog-title-bg">
  <div class="dailog-title"><i class="fas fa-history"></i> History<button class="dailog-close"
      [mat-dialog-close]><span>X</span></button>
  </div>
</div>
<div  *ngIf="BatteryCommissionRatesGroup"> 


  <div class="row">
    <div class="col-md-6">
      <div class="row">
        <label class="col-sm-5">State Code</label>
        <span class="col-sm-7">{{BatteryCommissionRatesGroup[0][0].stateCode1}}</span>
      </div>
    </div>
    <div class="col-md-6">
      <div class="row">
        <label class="col-sm-5">Flat Fee Ind</label>
        <span class="col-sm-7">{{BatteryCommissionRatesGroup[0][0].flatFeeInd}}</span>
      </div>
    </div>
    <div class="col-md-6">
      <div class="row">
        <label class="col-sm-5">Quantity</label>
        <span class="col-sm-7">{{BatteryCommissionRatesGroup[0][0].quantity}}</span>
      </div>
    </div>
  <div class="col-md-6">
    <div class="row">
      <label class="col-sm-5">Flat Fee Amount</label>
      <span class="col-sm-7">{{BatteryCommissionRatesGroup[0][0].flatFeeAmount| currency:'USD':true:'1.2-3'}}</span>
    </div>
  </div>
    <div class="col-md-6">
      <div class="row">
        <label class="col-sm-5">Battery Type</label>
        <span class="col-sm-7">{{BatteryCommissionRatesGroup[0][0].batteryTypeName}}</span>
      </div>
    </div>
    <div class="col-md-6">
      <div class="row">
        <label class="col-sm-5">Purchase Method</label>
        <span class="col-sm-7">{{BatteryCommissionRatesGroup[0][0].purchaseMethod1}}</span> 
      </div>
    </div>
  </div>

  <div class="col-md-12">
    <table class="my-table mat-table w-100 mt-3"> 
      <thead>
        <tr class="mat-header-row">
          <th class="mat-header-cell" scope="col">Effective Start Date</th>
          <th class="mat-header-cell" scope="col">Effective End Date</th>

          <!-- <th scope="col">Battery Sales Metric</th>
              <th scope="col">Battery Commission Amount</th> -->
        </tr>
      </thead>
      <tbody>
        <ng-container *ngFor="let tr of BatteryCommissionRatesGroup">
          <tr class="mat-row" (click)="groupClick(tr[0].batteryCommissionMappingId)">
            <td data-td-head="Effective Start Date" class="mat-cell">{{tr[0].effectiveStartDate | date}}</td>
            <td data-td-head="Effective End Date" class="mat-cell">{{tr[0].effectiveEndDate | date}}</td>
            
          </tr>
          <td colspan="4" style="background-color: #FFF;"
            *ngIf="batteryCommissionRateSelectedGroup && tr[0].batteryCommissionMappingId == batteryCommissionRateSelectedGroup[0].batteryCommissionMappingId">
            
            <table class="my-table mat-table w-100 mt-3">
              <thead>
                <tr class="mat-header-row">
                  <th class="mat-header-cell" scope="col">Battery Sales Metric</th>
                  <th class="mat-header-cell" scope="col">Battery Commission Amount</th>

                  <!-- <th scope="col">Battery Sales Metric</th>
              <th scope="col">Battery Commission Amount</th> -->
                </tr>
              </thead>
              <tbody>

                <tr class="mat-row" *ngFor="let tr of batteryCommissionRateSelectedGroup">
                  <td data-td-head="Battery Sales Metric" class="mat-cell"> {{tr.batterySalesMetric }}</td>
                  <td data-td-head="Battery Commission Amount" class="mat-cell"> {{tr.batteryCommissionAmount| currency:'USD':true:'1.2-3'}}</td>

                </tr>

              </tbody>
            </table>
          </td>
        </ng-container>
      </tbody>
    </table>
  </div>
</div>