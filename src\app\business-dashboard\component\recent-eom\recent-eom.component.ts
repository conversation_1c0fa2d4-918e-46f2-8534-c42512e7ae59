import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { MatLegacyPaginator } from '@angular/material/legacy-paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { Chart } from 'angular-highcharts';
import { IPendingPayment, IdateRange } from '../../models/models';
import { DatePipe, CurrencyPipe } from '@angular/common';
import { ToastrService } from 'ngx-toastr';
import { ApiService } from 'src/app/services/api.service';
import { DatezonePipe } from 'src/app/pipe/datezone.pipe';


@Component({
  selector: 'app-recent-eom',
  templateUrl: './recent-eom.component.html',
  styleUrls: ['./recent-eom.component.css']
})
export class RecentEomComponent  {
  displayedColumns = [];
  recentEomData: any;  
  originalDataSource: any;
  eomDataSource: any;
  @ViewChild('table', { read: MatSort, static: true }) sort: MatSort;
  @ViewChild('paginator', { static: true }) paginator: MatLegacyPaginator;
  salesTerittoryChart: Chart;
  salesDivisionChart: Chart;
  @Input() dateRange: IdateRange | null = null;
  @Input() tabNumber: number | null = null;
  previousDateRange: IdateRange | null = null;

  constructor(public apiService: ApiService, private toastMsg: ToastrService, private datePipe: DatePipe,private currencyPipe: CurrencyPipe,private dateZonePipe: DatezonePipe) { }
  columnNames = [
    {
      id: "commissionRuleName",
      value: "Commission Rule Name"
    },
    {
      id: "commissionRuleTypeName",
      value: "Commission Rule Type Name"
    },
    {
      id: "lastExecutedOn",
      value: "Last Executed On",
      dataType:'Date'
    },
    {
      id: "ruleCreatedOn",
      value: "Rule Created On",
      dataType:'Date'
    },    
  ];

  ngOnChanges(){
    if (this.tabNumber === 9) {
      if (this.dateRange) {
        if (this.previousDateRange === null || this.previousDateRange !== this.dateRange) {
          this.previousDateRange = this.dateRange;
          this.getEomData();
        }       
      }
    }
  }

  getEomData() {
    this.apiService.get(`BusinessDashboard/RecentEOMHistory?toDate=${this.dateRange.endDate}&fromDate=${this.dateRange.startDate}`)
      .subscribe((res: any) => {
        this.recentEomData = res;               
        this.displayedColumns = this.columnNames.map(x => x.id);
        this.createTable();       
        
        
      }, (err: any) => {
        this.toastMsg.error(err.message, "Server Error!");
      });
  }

  createTable() {    
    let tableArr: any[] = [];
    for (let i: number = 0; i <= this.recentEomData.length - 1; i++) {
      let currentRow = this.recentEomData[i];
      tableArr.push({
        commissionRuleId: currentRow.commissionRuleId, lastExecutedOn: this.dateZonePipe.transform(currentRow.lastExecutedOn), commissionRuleName: currentRow.commissionRuleName,
        commissionRuleTypeName: currentRow.commissionRuleTypeName, ruleCreatedOn: this.dateZonePipe.transform(currentRow.ruleCreatedOn),
      });
    }
    this.eomDataSource = new MatTableDataSource(tableArr);
    this.originalDataSource = tableArr;
    this.eomDataSource.sort = this.sort;
    this.eomDataSource.paginator = this.paginator;
  }
  onSortChange(event:any){
    let dateFieldData = this.columnNames.filter(s=>s.dataType === 'Date');
    let dateField = dateFieldData && dateFieldData.length > 0 ? dateFieldData.filter(s=>s.id === event.active).map(c=> c.id).toString():'';
    if(dateField){
      this.eomDataSource.sortingDataAccessor = (item, property) => {
        switch (property) {
          case dateField:
            return new Date(item[dateField]).toISOString();
          default:
            return item[property];
        }
      };

      this.eomDataSource.sortingFn = (a: any, b: any, active: string, direction: string) => {
        if (active === dateField) {
          const dateA = new Date(a);
          const dateB = new Date(b);
          if (direction === 'asc') {
            return dateA.getTime() - dateB.getTime();
          } else {
            return dateB.getTime() - dateA.getTime();
          }
        } else {
          return this.eomDataSource.sortingDataAccessor(a, active) > this.eomDataSource.sortingDataAccessor(b, active) ? 1 : -1;
        }
      };
    }
    
    
  }

}
