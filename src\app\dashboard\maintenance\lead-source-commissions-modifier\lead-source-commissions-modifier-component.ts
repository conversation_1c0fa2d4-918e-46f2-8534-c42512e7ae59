import { Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from "@angular/forms";
import { ApiService } from "../../../services/api.service";
import { ToastrService } from 'ngx-toastr';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { DataSource } from '@angular/cdk/table';
import { MatLegacyPaginator } from '@angular/material/legacy-paginator';

import { TableFilterPipe } from '../../../pipe/table-filter.pipe';
import { DatePipe, PercentPipe, CurrencyPipe } from '@angular/common';
import { MatLegacyDialog } from '@angular/material/legacy-dialog';
import { HttpParams, HttpClient } from '@angular/common/http';
import { ApiResponse } from '../../../services/api.response';
import { environment } from '../../../../environments/environment';
import { ILeadSourceCommisionModfier } from '../../../model/lead-source-commissions-modifier';

@Component({
    selector: 'app-lead-source-commissions-modifier',
    templateUrl: './lead-source-commissions-modifier-component.html',
    styleUrls: ['./lead-source-commissions-modifier-component.css']
})
export class LeadsourceconfigurationComponent implements OnInit {
    dropdowns: any;
    leadSourceForm: UntypedFormGroup;
    addInd: boolean = false;
    pageSizeOptions: number[] = [10, 20, 50];
    pageSize: number = 10;
    allSelected: boolean = false;
    leadSource: string = null;
    leadSourceModifierType: string = null;
    selectedLeadSourceId: number = 0;
    selectedLeadSourceModifierTypeId: number = 0;
    searchText: string = "";
    originalDataSource: any;
    dataSource: any;
    leadSourceCommissionsModifiers: MatTableDataSource<ILeadSourceCommisionModfier> = new MatTableDataSource([]);
    leadSourceCommissionModifiers: string[] = ["selected", "modifierType", "leadSource"];
    filter: boolean = false;
    @ViewChild(MatSort, { static: true }) sort: MatSort;
    @ViewChild(MatLegacyPaginator, { static: true }) paginator: MatLegacyPaginator;

    constructor(public apiService: ApiService, private toastMsg: ToastrService, private formBuilder: UntypedFormBuilder, private pipe: TableFilterPipe, private http: HttpClient,
        private datePipe: DatePipe, private percentPipe: PercentPipe, private currencyPipe: CurrencyPipe, private dialog: MatLegacyDialog) {
    }

    ngOnInit() {
        this.getLeadSourceCommissionsModifiers();
        if (!this.apiService.checkPermission('ViewRateTables')) {
            this.apiService.goBack();
            this.toastMsg.error("Insufficient Permissions. Please make sure your account can access this information.", "Permissions");
        }
        this.getDropdowns();
        this.leadSourceForm = this.formBuilder.group({
            leadSource: [this.leadSource, [Validators.required]],
            leadSourceModifierType: [this.leadSourceModifierType, [Validators.required]]
        });
    }

    onChangeFilter() {
        this.getLeadSourceCommissionsModifiers();
    }

    checkSelected() {
        return this.leadSourceCommissionsModifiers && this.leadSourceCommissionsModifiers.data.filter(record => record.selected).length > 0;
    }

    getNumberSelected(): number {
        if (this.leadSourceCommissionsModifiers) return this.leadSourceCommissionsModifiers.data.filter(record => record.selected).length;
    }

    onSelectionChange() {
        let balances = this.leadSourceCommissionsModifiers.filteredData.filter(bal => bal.selected);
        if (balances.length == this.leadSourceCommissionsModifiers.filteredData.length) {
            this.allSelected = true;
        } else {
            this.allSelected = false;
        }
    }

    onBulkSelectionChange() {
        this.leadSourceCommissionsModifiers.filteredData.map(bal => bal.selected = this.allSelected);
    }

    deleteSelected() {
        let leadSourceCommissionsModifiers = this.leadSourceCommissionsModifiers.data.filter(record => record.selected);
        var ids = new Array();
        for (var i = 0; i < leadSourceCommissionsModifiers.length; i++) {
            ids.push(leadSourceCommissionsModifiers[i].leadSourceCommissionsModifierId)
        }
        this.apiService.post('leadSourceCommission/deleteRows', ids)
            .subscribe(data => {
                this.toastMsg.success("Deleted selected Records successfully.");
                this.getLeadSourceCommissionsModifiers();
                this.applyFilter(this.searchText);
            }, err => {
                this.toastMsg.error(err.message, "Error!");
            })
    }


    getLeadSourceCommissionsModifiers() {
        let params = new HttpParams();

        params = params.append('leadSourceId', this.selectedLeadSourceId + "");
        params = params.append('modifierTypeId', this.selectedLeadSourceModifierTypeId + "");
        this.http.get<ApiResponse>(`${environment.apiBaseUrl}leadSourceCommission/RetrieveAll`, { params: params })
            .subscribe(data => {
                if (data && data.result) {
                    var leadSourceCommissionsModifiers = data.result.map((rows: ILeadSourceCommisionModfier) => { return rows });
                    this.leadSourceCommissionsModifiers = new MatTableDataSource<ILeadSourceCommisionModfier>(leadSourceCommissionsModifiers);
                    this.leadSourceCommissionsModifiers.paginator = this.paginator;
                    this.leadSourceCommissionsModifiers.sort = this.sort;
                    this.applyFilter(this.searchText);
                }
            }, err => {
                this.toastMsg.error(err.message, "Error!");
            })
    }

    applyFilter(input: string): void;

    applyFilter(input: Event): void;

    applyFilter(input: any): any {
        var filterValue: string;
        if (typeof input === "string") {
            filterValue = input;
        } else {
            filterValue = (input.target as HTMLInputElement).value;
        }
        this.leadSourceCommissionsModifiers.filter = filterValue.trim().toLowerCase();
    }


    onSubmit() {
        if (!this.leadSourceForm.invalid) {
            var body = {
                ModifiertypeId: this.leadSourceForm.controls.leadSourceModifierType.value,
                LeadsourceId: this.leadSourceForm.controls.leadSource.value,
            }

            this.apiService.post('Leadsourcecommission', body)
                .subscribe(data => {
                    this.toastMsg.success('Lead source commissions modifier added successfully');
                    this.getLeadSourceCommissionsModifiers()
                    this.addInd = !this.addInd;
                }, (err: any) => {
                    this.toastMsg.error(err.message, 'Server Error!');
                });
        }
    }

    getDropdowns() {
        this.apiService.get('leadsourcecommission/dropdowns')
            .subscribe(data => {
                this.dropdowns = data;
            }, (err: any) => {
                this.toastMsg.error(err.message, 'Server Error!');
            });
    }

    searchForItem(): void {
        let filteredResults: Element[] = [];
        if (this.searchText == '') {
            this.dataSource = new MatTableDataSource(this.originalDataSource);
            this.dataSource.sort = this.sort;
            this.dataSource.paginator = this.paginator;
        } else {
            filteredResults = this.pipe.transform(this.originalDataSource, this.searchText);
            this.dataSource = new MatTableDataSource(filteredResults);
            this.dataSource.sort = this.sort;
            this.dataSource.paginator = this.paginator;
        }
    }
}

export interface Element {
    leadsource: string,
    modifierType: string
}

