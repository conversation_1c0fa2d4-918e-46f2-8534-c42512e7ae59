export interface IOpportunity {
    opportunityId?: number;
    opportunityName?: string;
    leadSource?: string;
    salesForceId?: string;
    eventName?: string;
    projectStatus?: string;
    permitPayer?: string;
    salesTerritory?: string;
    utilityCompany?: string;
    stage?: string;
    stageStatus?: string;
    opportunityType?: string;
    appointmentTerritory?: string;
    directSalesOffice?: string;
    territorySalesOffice?: string;
    county?: string;
    leadGeneratorOffice?: string;
    iosTrack?: string;
    appointmentCompleted?: boolean;
    appointmentConfirmed?:boolean;
    insideSalesCampaignId?: number;
    insideSalesCampaignName?: string;
    opportunityFinalized?: boolean;
    monthlyDemoNumber?: number;
    
    trinitySalespersonName?: string;
    trinitySalespersonId?: number;
    leadGeneratorName?: string;
    leadGeneratorId?: number;
    sdrInsideSalesName?: string;
    sdrInsideSalesId?: number;
    salesSuccessRepresentativeName?: string;
    secondarySalesPersonName?: string;
    secondarySalesPersonId?: number;
    salesSuccessRepresentativeId?: number;
    primaryContactName?: string;
    primaryContactId?: number;
    previousTrinitySalesperson?: string;
    accountExecutiveName?: string;
    accountExecutiveId?: number;

    roofingPurchaseMethod?: string;
    roofingManufacturer?: string;
    roofingSaleAmount?: number;
    roofAdderAmount?: number;
    roofingInstallDate?: Date;
    roofingContractDate?: Date;
    roofingInstallationStage?: string;
    roofSquares?:number;
    roofingSalesPersonId?: number;
    roofSalesChargebackAmount?: number;
    roofingSalesPersonName?: string;
    roofingFinancePartnerName?: string;
    roofingContractId?:string;
    roofDateQuoted?: Date;

    batterySalesPersonId?: number;    
    batterySalesPersonName?: string;

    
    dateOfFirstAppointment?: Date;
    dateContractSigned?: Date;
    actualInstallDate?: Date;
    dateLoiSigned?: Date;
    winBackDate?: Date;
    actualInstallCompleteDate?: Date;
    scheduledInstallDate?: Date;
    demoDate?: Date;
    opportunityCreatedDate?: Date;
    dateQuoted?: Date;

    systemSizeKWdc?: number;
    moduleType?: string;
    inverterType?: string;
    inverter2Type?: string;
    inverter3Type?: string;
    inverter4Type?: string;
    installationType?: string;


    opportunityAmount?: number;
    ppaRate?: number;
    ppaRateEscalator?: number;
    pricePerWatt?: number;
    purchaseMethod?: string;
    partner?: string;
    salesChargeBackAmount?: number;
    amountForReduction?: number;
    permitAmount?: number;

    salesOpsRole?:string;
    salesOpsContactName?:string;
    salesOpsId?:number;

    contractRebateAmount?:number;
    contractDepositAmount?:number;

    batteryInstallDate?: Date;
    batteryContractDate?: Date;
    batteryPurchaseMethod?: string;
    batterySaleAmount?: number;
    batteryType?: string;
    batteryQuantity?: number;
    batteryAmount?: number;
    batteryinstallcompletedate?: Date;
    batteryPTODate?: Date;
    batteryAdderAmount?: number;
    batteryContractID?: string;
    batteryFinancePartner?: string;
    batteryDateQuoted?: Date;
    lowSlope?: boolean;
    normalSlope?: boolean;
    steepSlope?: boolean;
    componentsNeeded?: string;
    batterySalesChargebackAmount?: number;

    rrSalespersonId?: number;
    rrSalespersonName?: string;
    rrInstallationStage?: string;
    rrSystemSize?: number;
    rrAmount?: number;
    rrChargebackAmount?: number;
    rrContractId?: string;
    rrContractDate?: Date;
    rrPurchaseMethod?: string;
    reinstallDate?: Date;
    amountDetails?: string;
    secondarySalespersonRole?:string;
    brLeadGeneratorId?: number;
    brLeadGeneratorName?: string;
    ptoDate?: Date;
    flip?: boolean;
    nonflipSolarContractDate?: Date;
    nonflipRoofContractDate?: Date;
    nonflipBatteryContractDate?: Date;
    appointmentType?: string;
    confirmationFormula?: string;
    InsideSalesCampaignId?: number;
}
export interface SlopeType {
    lowSlope?: boolean;
    normalSlope?: boolean;
    steepSlope?: boolean;
  }
