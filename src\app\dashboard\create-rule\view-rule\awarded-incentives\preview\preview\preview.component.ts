import { Component, Input, OnInit, Output, SimpleChanges, ViewChild, EventEmitter } from '@angular/core';
import { IStep } from 'src/app/model/step.model';
import { MatLegacyDialog } from '@angular/material/legacy-dialog';
import { MatLegacyPaginator, LegacyPageEvent } from '@angular/material/legacy-paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { IOnDemandJson, IOnDemandJsonMetadata } from 'src/app/model/on-demand-json.model';
import { ApiService } from 'src/app/services/api.service';
import { ToastrService } from 'ngx-toastr';
import { IncentiveComponent } from '../../incentive/incentive.component';
import { DatePipe } from '@angular/common';

@Component({
  selector: 'app-preview',
  templateUrl: './preview.component.html',
  styleUrls: ['./preview.component.css']
})
export class PreviewComponent implements OnInit {
  @ViewChild(MatSort) sort: MatSort;
  @ViewChild('awardedPaginator') paginator: MatLegacyPaginator;
  @Input() ruleId: number;
  @Input() steps: IStep[];
  @Input() worksheetSelectedPreviewAttempt: number; 
  @Output() updateSelectedWorksheet = new EventEmitter<number>();
  awardedIncentivesDataPreview: MatTableDataSource<IOnDemandJson> = new MatTableDataSource([]);
  previewColumns: string[] = ["contactId", "ruleName", "contactName", "salesDivision", "employementStatus", "stepName", "actionValue"];
  pageSizeOptions: number[] = [5, 10];
  pageSize: number = 5;
  pageEvent: LegacyPageEvent;
  selectedPreview: number = 0;
  previewAttempts: number[] = [];
  previewAttemptsData: any[] = [];
  previewRuleId: number = 0;
  attemptExecutedBy: string;
  attemptExecutedAt: string;
  constructor(private apiService: ApiService, private toastMsg: ToastrService, public dialog: MatLegacyDialog, private datePipe: DatePipe) { }

  ngOnInit() {
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.ruleId) {
      this.previewRuleId = changes.ruleId.currentValue
      this.getAwardIncentivesPreview(changes.ruleId.currentValue);
      this.getPreviewAttempts(changes.ruleId.currentValue);
    }
  }

  getAwardIncentivesPreview(ruleId: number): void {
    if (ruleId) {
      this.apiService.get(`OnDemandJson/Preview/${ruleId}/${this.selectedPreview}`)
      .subscribe(data => {
        if (data["statusCode"] === "201" && data.result) {
          console.log(data.result)
          var previews = data.result.map(x => {return <IOnDemandJson>x});
          this.awardedIncentivesDataPreview = new MatTableDataSource(previews);
          this.awardedIncentivesDataPreview.paginator = this.paginator;
          this.awardedIncentivesDataPreview.sort = this.sort;
        }
      }, (err: any) => {
        this.toastMsg.error(err.message || err, 'Server Error!')
      })
    }
  }

  getPreviewAttempts(ruleId: number): void {
    if (ruleId) {
      this.apiService.get('OnDemandJson/PreviewAttempts/' + ruleId)
      .subscribe(data => {
          if (data["statusCode"] === "201" && data.result) {
            this.previewAttempts = data.result.attempts;
            this.previewAttemptsData = data.result.attemptData;
            this.selectedPreview = Math.max(...this.previewAttempts);
            if (this.previewAttempts.length > 0) {
              let execution = this.previewAttemptsData.find(x => x.attemptNumber == this.selectedPreview);
              this.attemptExecutedBy = execution.userCreatedId
              this.attemptExecutedAt = this.datePipe.transform(execution.userCreatedTimestamp)
              this.updateSelectedWorksheet.emit(this.selectedPreview);
            }
          }
      }, (err: any) => {
        this.toastMsg.error(err.message || err, 'Server Error!')
      })
    }
  }

  onClick(row: IOnDemandJson): void {
    var steps = this.steps.filter(s => s.stepId == row.stepId);

    if (steps.length > 0) {
      var step = steps[0];
    } else {
      this.toastMsg.error("There was an error retrieving the awarded step.", "Error");
      return;
    }

    var metaDatas: MatTableDataSource<IOnDemandJsonMetadata> = new MatTableDataSource(row.metaDatas);

    const dialogRef = this.dialog.open(IncentiveComponent, {
      width: '85%',
      height: '85%',
      data: {step: step, metaDatas: metaDatas}
    });
  }

  onPreviewAttemptChange() {
    this.getAwardIncentivesPreview(this.previewRuleId)
    let execution = this.previewAttemptsData.find(x => x.attemptNumber == this.selectedPreview);
    this.attemptExecutedBy = execution.userCreatedId;
    this.attemptExecutedAt = this.datePipe.transform(execution.userCreatedTimestamp);
    this.updateSelectedWorksheet.emit(this.selectedPreview);
  }

}
