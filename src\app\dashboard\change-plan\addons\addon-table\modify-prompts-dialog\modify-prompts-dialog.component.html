<div class="dailog-title-bg">
    <div class="dailog-title">Edit<button class="dailog-close" [mat-dialog-close]><span>X</span></button></div>
</div>
<!-- Rule Preview -->
<!-- Prompts -->

<!-- <ng-container *ngFor="let prompt of prompts"> -->
<!-- <ng-container
                        *ngIf="prompt.ruleTypeName == 'Base Pay Structure'; else elseBlock">
                        <app-base-pay-structure-prompt [rule]="prompt"
                            (rulePrompt)="onRulePromptChange($event)">
                        </app-base-pay-structure-prompt>
                    </ng-container>
                    <ng-template #elseBlock> -->
<ng-container *ngIf="data.rule && data.rule.ruleTypeName == 'Base Pay Structure'">
    <app-base-pay-structure-prompt [(rule)]="data.rule" (rulePrompt)="onRulePromptChange($event)">
    </app-base-pay-structure-prompt>
</ng-container>
<ng-container *ngIf="data.rule && data.rule.ruleTypeName == 'Employee Incentive'">
    <app-employee-incentive-prompt [(rule)]="data.rule" (rulePrompt)="onRulePromptChange($event)">
    </app-employee-incentive-prompt>
</ng-container>
<ng-container *ngIf="data.rule && data.rule.ruleTypeName == 'Payment Book Schedule'">
    <app-payment-book-schedule-prompt [(rule)]="data.rule" (rulePrompt)="onRulePromptChange($event)">
    </app-payment-book-schedule-prompt>
</ng-container>
<!-- </ng-template> -->
<!-- </ng-container> -->
<div class="col-md-12 text-right">
    <p style="color: red" *ngIf="!datesValid">Dates must be in chronological order</p>
    <p style="color: red" *ngIf="!promptsFilled">All prompts must be filled</p>
</div>
<div class="col-md-12 text-right">
    <button class="btn btn-primary" [mat-dialog-close]="addOn" [disabled]="!datesValid || !promptsFilled"><i class="fas fa-check"></i> OK</button>
</div>