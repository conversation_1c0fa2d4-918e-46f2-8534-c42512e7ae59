<div class="page-title col-md-12 ">
  <h1> Roof Commission Rates</h1>
  <div class="breadcrumbs"><a href="#">Home</a>/<span>Roof Commission Rates</span>
  </div>
</div>

<div class="content">

  <div class="card">
    <div class="card-header-info">
      <h4 class="card-title no-hover-effect">Roof Commission Rates</h4>
    </div>
    <div class="card-body">
      <div class="row">
        <div class="col-md-12">
          <div class="input-group float-right table-search">
            <input class="custom-input" type="text" id="searchTextId" [(ngModel)]="searchText" name="searchText"
              placeholder="Search" (input)="searchForItem()">
            <span class="input-group-icon">
              <i class="fas fa-search"></i>
            </span>
          </div>

        </div>
      </div>
      <mat-table #table [dataSource]="dataSource" matSort>
        <ng-container matColumnDef="{{column.id}}" *ngFor="let column of columnNames">
          <mat-header-cell *matHeaderCellDef mat-sort-header class="mat-sort-header-content-override"> {{column.value}} </mat-header-cell>
          <mat-cell [attr.data-td-head]="column.value" *matCellDef="let element"> {{element[column.id]}} </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns;" class="pointer table-content"
          (click)="rowClick(row)"></mat-row>
      </mat-table>
      <mat-paginator [pageSizeOptions]="[5, 10, 20, 50]" showFirstLastButtons></mat-paginator>
      <div *ngIf="apiService.checkPermission('CreateRateTables')">
        <a class="btn  btn-primary float-right" *ngIf="!addInd" (click)="Add()"><i
            class="material-icons pointer">add_circle</i> Add</a>
        <a class="btn  btn-primary float-right" *ngIf="addInd" (click)="addInd = !addInd"><i
            class="material-icons pointer">remove_circle</i> Hide</a>
      </div>
    </div>
  </div>
  <div class="card" *ngIf="addInd">
    <div class="card-header-info">
      <h4 class="card-title no-hover-effect"><i class="fas fa-plus"></i> Add Roof Commission Rate</h4>
    </div>
    <div class="card-body">
      <div>
        <form [formGroup]="roofCommissionRateForm" (ngSubmit)="onSubmit()" class="w-100">
          <div class="row" *ngIf="activeRoofCommissionRate">
            <div class="form-group col-md-4">
              <div class="row">
                <label class="col-sm-5">Pricing Type</label>
                <div class="col-sm-7">
                  <select class="custom-select" name="state_code_dropdown" formControlName="pricingType"
                    data-style="btn btn-link" id="state_code_dropdown">
                    <option *ngFor="let pt of pricingType" value="{{pt.value}}">
                      {{pt.value}}</option>
                  </select>
                </div>
              </div>
            </div>
            <div class="form-group col-md-4">
              <div class="row">
                <label class="col-sm-5">Sales Rep Type</label>
                <div class="col-sm-7">
                  <select class="custom-select" name="utility_company_dropdown" formControlName="salesRepType"
                    data-style="btn btn-link" id="utility_company_dropdown">
                    <option *ngFor="let sr of salesRepType" value="{{sr.value}}">
                      {{sr.value}}</option>
                  </select>
                </div>
              </div>
            </div>
            <div class="form-group col-md-4">
              <div class="row">
                <label class="col-sm-5">Effective Start Date</label> 
                <!-- {{effectiveStartDate |json}} -->
                <div class="col-sm-7 ">
                  <div class="date-picker w-100">
                    <input #StartDatePicker type="date" name="start_date" id="start_date" class="custom-input"
                      formControlName="effectiveStartDate" placeholder="">
                    <span *ngIf="StartDatePicker.value.length > 0" class="mat-icon cal-reset"
                      (click)="clearDate(StartDatePicker)"><i class="far fa-calendar-times"></i></span>
                    <span *ngIf="StartDatePicker.value.length <= 0" class="mat-icon cal-open"><i
                        class="far fa-calendar-alt"></i></span>

                    <div *ngIf="roofCommissionRateForm.errors && roofCommissionRateForm.errors.maxDate">
                      <p style="color: red;">New Effecting Start Date should be greater than any previous start dates
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="form-group col-md-4">
              <div class="row">
                <label class="col-sm-5">Floor Rate</label>
                <div class="col-sm-7">
                  <input currencyMask formControlName="floorRate"
                    [options]="{ allowNegative: false, align: 'left', precision: 2 }" [value]="" class="custom-input">
                </div>
              </div>
            </div>

            <div class="form-group col-md-4">
              <div class="row">
                <label class="col-sm-5">Base Rate</label>
                <div class="col-sm-7">
                  <input currencyMask formControlName="baseRate"
                    [options]="{ allowNegative: false, align: 'left', precision: 2 }" [value]="" class="custom-input">
                </div>
              </div>
            </div>
            <div class="form-group col-md-4">
              <div class="row">
                <label class="col-sm-5">Bonus Tier Rate</label>
                <div class="col-sm-7">
                  <input currencyMask formControlName="bonusTierRate"
                    [options]="{ allowNegative: false, align: 'left', precision: 2 }" [value]="" class="custom-input">
                </div>
              </div>
            </div>
            <div class="form-group col-md-4">
              <div class="row">
                <label class="col-sm-5">Floor Rate Low Slope</label>
                <div class="col-sm-7">
                  <input currencyMask formControlName="floorRateLowSlope"
                    [options]="{ allowNegative: false, align: 'left', precision: 2 }" [value]="" class="custom-input">
                </div>
              </div>
            </div>

            <div class="form-group col-md-4">
              <div class="row">
                <label class="col-sm-5">Lead Source Type</label>
                <div class="col-sm-7">
                  <select class="custom-select" name="lead_source_type" formControlName="leadSourceType"
                  data-style="btn btn-link" id="lead_source_type">
                    <option *ngFor="let t of leadSourceTypes" value="{{t}}">
                      {{t}}
                    </option>
                  </select>
                </div>
              </div>
            </div>

            <div class="form-group col-md-4">
              <div class="row">
                <label class="col-sm-5">Lead Fee</label>
                <div class="col-sm-7">
                  <input currencyMask formControlName="leadFee"
                    [options]="{ allowNegative: false, align: 'left', precision: 2 }" [value]="" class="custom-input">
                </div>
              </div>
            </div>
            <div class="form-group col-md-4">
              <div class="row">
                <label class="col-sm-5">Base Commission Amount</label>
                <div class="col-sm-7">
                  <input currencyMask formControlName="baseCommissionAmount"
                    [options]="{ allowNegative: false, align: 'left', precision: 2 }" [value]="" class="custom-input">
                </div>
              </div>
            </div>

            <div class="form-group col-md-4">
              <div class="row">
              </div>
            </div>
          </div>
          <div class="row" *ngFor="let group of roofGroup; index as i">
            <div class="gray-bg col-md-12 pt-3 mb-1">
              <div class="row">
                <div class="form-group col-md-4">
                  <div class="row">
                    <label class="col-sm-5">Sales Metric</label> 
                    <div class="col-sm-7">
                      <input currencyMask [options]="{ allowNegative: false, align: 'left', precision: 2 }"
                        [value]="roofCommissionRate[0].salesMetric" name="{{getControlName(group[0])}}"
                        formControlName="{{getControlName(group[0])}}" class="custom-input">
                    </div>
                  </div>
                </div>
                <div class="form-group col-md-4">
                  <div class="row">
                    <label class="col-sm-5">Low Slope Sales Metric</label>
                    <div class="col-sm-7">
                      <input currencyMask [options]="{ allowNegative: false, align: 'left' }"
                        name="{{getControlName(group[1])}}" [value]="roofCommissionRate[0].lowSlopeMetric"
                        formControlName="{{getControlName(group[1])}}" class="custom-input">
                    </div>
                  </div>
                </div>
                <div class="form-group col-md-4">
                  <div class="row">
                    <label class="col-sm-5">Commission Amount</label> 
                    <div class="col-sm-7">
                      <input currencyMask [options]="{ allowNegative: false, align: 'left' }"
                        name="{{getControlName(group[2])}}" formControlName="{{getControlName(group[2])}}"
                        class="custom-input">
                    </div>
                  </div>
                </div>
                <div class="form-group col-md-4">
                  <div class="row">
                    <label class="col-sm-5">Self Gen Commission Amount</label> 
                    <div class="col-sm-7">
                      <input currencyMask [options]="{ allowNegative: false, align: 'left' }"
                        name="{{getControlName(group[3])}}" formControlName="{{getControlName(group[3])}}"
                        class="custom-input">
                    </div>
                  </div>
                </div>
                <a class="text-info"><i class="material-icons hover" (click)="removeFormRow(i)">delete</i></a>
                <a class="text-info hover"><i *ngIf="i == roofGroup.length - 1" class="material-icons"
                    (click)="addFormRow()">add_box</i></a>
              </div>
            </div>
          </div>
          <div class="row align-button-right">
            <button type="submit" class="btn btn-primary" [disabled]="roofCommissionRateForm.invalid"><i class="fas fa-plus"></i> Add Roof Commission Rate</button>
          </div>
        </form>
      </div>
    </div>
  </div>
  <div class="card" *ngIf="roofCommissionRateGroup">
    <div class="card-header-info">
      <h4 class="card-title"><i class="fas fa-history"></i> History</h4>
    </div>
    <div class="card-body">
      <div class="row">
        <div class="col-md-12">
          <a class="text-info"><i class="material-icons float-right blue-icon"
              (click)="roofCommissionRateGroup = null">cancel</i></a>
        </div>      


       
      </div>
    </div>