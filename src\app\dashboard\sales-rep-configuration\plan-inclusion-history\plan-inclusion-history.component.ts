import { Component, OnInit, Inject, ViewChild } from '@angular/core';
import { MAT_LEGACY_DIALOG_DATA } from '@angular/material/legacy-dialog';
import { MatLegacyPaginator } from '@angular/material/legacy-paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { DatePipe } from '@angular/common';

@Component({
  selector: 'app-plan-inclusion-history',
  templateUrl: './plan-inclusion-history.component.html',
  styleUrls: ['./plan-inclusion-history.component.css']
})
export class PlanInclusionHistoryComponent implements OnInit {
displayedColumns = [];
columnNames = [{
  id: "commissionRuleName",
  value: "Rule Name"
}, 
{
  id: "effectiveStartDate",
  value: "Effective Start Date"
},
{
  id: "effectiveEndDate",
  value: "Effective End Date"
},
{
  id: "userCreatedId",
  value: "Created By"
},
{
  id: "userCreatedTimestamp",
  value: "Created Timestamp"
}];
tableArr: any[] = [];
dataSource;
originalDataSource;
@ViewChild(MatSort, { static: true }) sort: MatSort;
@ViewChild(MatLegacyPaginator, {static: true}) paginator: MatLegacyPaginator;
inclusionsHistory: any[];

  constructor(@Inject(MAT_LEGACY_DIALOG_DATA) public data: any, private datePipe: DatePipe) { this.inclusionsHistory = this.data.inclusionHistory }

  ngOnInit() {
    console.log(this.data);
    if (this.inclusionsHistory[0].isRuleType) {
      this.columnNames[0].value = "Incentive"
    }
    this.displayedColumns = this.columnNames.map(x => x.id);
    this.createTable();
  }

  createTable() {
    let tableArr: any[] = [];
    for (let i: number = 0; i <= this.inclusionsHistory.length - 1; i++) {
      let currentRow = this.inclusionsHistory[i];
      if(i==0)
      {
        this.tableArr[0] =this.inclusionsHistory[0];
      }
      tableArr.push({
        commissionRuleName: currentRow.commissionRuleName, effectiveStartDate: this.datePipe.transform(currentRow.effectiveStartDate, 'yyyy-MM-dd'),
        effectiveEndDate: this.datePipe.transform(currentRow.effectiveEndDate, 'yyyy-MM-dd') , userCreatedId: currentRow.userCreatedId, userCreatedTimestamp: this.datePipe.transform(currentRow.userCreatedTimestamp, 'yyyy-MM-dd')
      });
    }
    this.dataSource = new MatTableDataSource(tableArr);
    this.originalDataSource = tableArr;
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }

}
