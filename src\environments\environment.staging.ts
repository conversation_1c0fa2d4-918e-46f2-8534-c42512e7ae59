export const environment = {
  production: true,
  ssoBaseUrl: 'https://onepay_uat.trinity-solar.com/api/api/',
  apiBaseUrl: 'https://onepay_uat.trinity-solar.com/api/api/',
  workflowBaseUrl: 'https://testopsii.trinity-solar.com:5004/api/',
  secretKey: "STAGING-M@%$#*!21@$&#%$*#61",
  applicationId:7,

  // baseUrl:'http://localhost:58980/',
  baseUrl:'https://onepay_uat.trinity-solar.com/api/',
  scopeUri: ['api://a82a939e-61d2-4675-82cf-122b5fd91169/onePAY'],
  authority:'https://login.microsoftonline.com/f1006ee5-f888-4308-92ea-fcaebe1c0b5e',
  tenantId: 'f1006ee5-f888-4308-92ea-fcaebe1c0b5e',
  uiClienId: 'a82a939e-61d2-4675-82cf-122b5fd91169',
  redirectUrl: 'https://onepay_uat.trinity-solar.com',
  oneReportUrl:'https://testapps.trinity-solar.com/oneReport',
};
