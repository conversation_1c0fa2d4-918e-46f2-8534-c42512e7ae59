import { Component, OnInit, Inject } from '@angular/core';
import { MatLegacyDialogRef, MAT_LEGACY_DIALOG_DATA } from '@angular/material/legacy-dialog';
import { ApiService } from '../../../services/api.service';
import { ToastrService } from 'ngx-toastr';
//import { Subject } from "rxjs";

@Component({
  selector: 'app-Battery_Commission_Rate-maintenance-dialog.component',
  templateUrl: './Battery_Commission_Rate-maintenance-dialog.component.html',
  styleUrls: ['./Battery_Commission_Rate-maintenance-dialog.component.css']
})
export class BatteryRateMaintenanceDialogComponent implements OnInit {
  BatteryCommissionRatesGroup: Element[] = [];
  batteryCommissionRateSelectedGroup: any;
  cnt: Number = 1;
  constructor(public dialogRef: MatLegacyDialogRef<BatteryRateMaintenanceDialogComponent>,
    private apiService: ApiService, private toastMsg: ToastrService, @Inject(MAT_LEGACY_DIALOG_DATA) public data: any) { }

  ngOnInit() {
    this.BatteryCommissionRatesGroup = this.data.batteryCommissionRate;
    this.BatteryCommissionRatesGroup.sort((a, b) => {
      // return <any>new (b[0].batteryCommissionMappingId) - <any>new (a[0].batteryCommissionMappingId);  
      return <any>(b[0].batteryCommissionMappingId) - <any>(a[0].batteryCommissionMappingId);  
    });
  }

  isObject(val) {
    if (val === null) { return false; }
    return ((typeof val === 'function') || (typeof val === 'object'));
  }

  isObjectEmpty(obj) {
    return Object.keys(obj).length === 0;
  }

  groupClick(group: any) {
    var batteryCommissionList = this.BatteryCommissionRatesGroup.forEach(element => {
      var selectedGroup = element.filter(x => x.batteryCommissionMappingId === group)
      if (typeof selectedGroup === 'object') {
        if (!this.isObjectEmpty(selectedGroup)) {
          this.batteryCommissionRateSelectedGroup = selectedGroup;
        }
      }
    });
  }
}

export interface Element {
  [x: string]: any;
  effectiveStartDate: string,
  effectiveEndDate: string,
  batteryCommissionMappingId: number;
}

