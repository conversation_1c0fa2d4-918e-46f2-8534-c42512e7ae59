import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { PPABonusFlatRateMaintenanceComponent } from './ppa-bonus-flat-rate-maintenance.component';

describe('PPABonusFlatRateMaintenanceComponent', () => {
  let component: PPABonusFlatRateMaintenanceComponent;
  let fixture: ComponentFixture<PPABonusFlatRateMaintenanceComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ PPABonusFlatRateMaintenanceComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PPABonusFlatRateMaintenanceComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
