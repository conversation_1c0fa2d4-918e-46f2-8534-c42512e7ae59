import { <PERSON>mpo<PERSON>, OnIni<PERSON>, <PERSON><PERSON>hil<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild, ViewEncapsulation} from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { ApiService } from "../../services/api.service";
import { ToastrService } from 'ngx-toastr';
declare var $: any;
import { DatePipe } from '@angular/common';
import { IRateAndException } from 'src/app/model/rate-and-exception.model';
import { IRateAndExceptionType } from 'src/app/model/rate-and-exception-type.model';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { maxRateAndExceptionDate, dateLessThanDate, maxEditRateAndExceptionDate } from 'src/app/shared/validators';
import { IControlTable, IControlTableRow } from 'src/app/model/control-table.model';
import { IOpportunity } from 'src/app/model/opportunity.model';
import { ICommissionRuleTrigger } from 'src/app/model/commission-rule-trigger.model';
import { ICommission } from 'src/app/model/commission.model';
import { IPlan, IPlanDetail } from 'src/app/model/plan.model';
import { IRule } from 'src/app/model/rule.model';
import { MatLegacyPaginator } from '@angular/material/legacy-paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { IContactRateIncentiveModifier } from 'src/app/model/contact-rate-incentive-modifier.model';
import { IContactPlan } from 'src/app/model/contact-plan.model';
import { HttpClient, HttpParams } from '@angular/common/http';
import { ApiResponse } from 'src/app/services/api.response';
import { IContact, IRuleType, ICommissionProcessingOneTimePayment } from 'src/app/model/one-time-payment.model'
import { IPayment } from 'src/app/model/payment.model';
import { environment } from "src/environments/environment";
import * as FileSaver from 'file-saver';
import { Observable, OperatorFunction } from 'rxjs';
import { debounceTime, distinctUntilChanged, map, filter } from 'rxjs/operators';
import { MatLegacyDialog } from '@angular/material/legacy-dialog';
import { ConfirmationModalComponent } from 'src/app/confirmation-modal/confirmation-modal.component';
import { DatezonePipe } from 'src/app/pipe/datezone.pipe';
@Component({
  selector: 'app-sales-rep',
  templateUrl: './sales-rep.component.html',
  encapsulation: ViewEncapsulation.None,
  styleUrls: ['./sales-rep.component.css']
})
export class SalesRepComponent implements OnInit {
  @ViewChildren(MatLegacyPaginator) paginator = new QueryList<MatLegacyPaginator>();
  @ViewChildren(MatSort) sort = new QueryList<MatSort>();
  rateAndExceptionTypes: IRateAndExceptionType[];
  selectedRateAndExceptionType: IRateAndExceptionType = <IRateAndExceptionType>{};
  selectedRateAndExceptionSalesTerritory: number;
  contactsList: any;
  plan: IPlan;
  contact_id: any;
  contact_plan_id: any;
  plan_id: any;
  addInd: boolean = false;
  updateInd: boolean = false;
  rateAndExceptionFormGroup: UntypedFormGroup;
  oneTimePaymentFormGroup: UntypedFormGroup;
  dropdowns = {};
  p: number = 1;
  p2: number = 1;
  p3: number = 1;
  opportunities: {} = {};
  commissionRuleTriggers: ICommissionRuleTrigger[];
  contactSize = 5;
  recentSPOSize = 5;
  commissionSize = 5;
  contactPaymentBook: boolean = false;
  newPlanEndDate: Date;
  record: any;
  //disabled: boolean;
  updateRateAndException: boolean;
  contactRateAndExceptionTypeId: number;
  salesTerritories: any;
  anySalesTerritory: any;
  selectedSalesTerritory: any;
  commissionOnWattsSoldTypeId: any;

  contacts: IContact[] = [];

  public model: IContact;

  formatter = (c: IContact) => c.contactName;
  search: OperatorFunction<string, readonly { contactId, contactName }[]> = (text$: Observable<string>) => text$.pipe(
    debounceTime(0),
    distinctUntilChanged(),
    filter(term => term.length >= 2),
    map(term => this.contacts.filter(c => new RegExp(term, 'mi').test(c.contactName)).slice(0, 10))
  )

  // Contact Rate Incentive Modifiers
  contactRateIncentiveModifiers: MatTableDataSource<IContactRateIncentiveModifier> = new MatTableDataSource([]);
  columns: string[] = ["commissionRuleName", "amountEligible","amountGained", "percentGained", "effectiveStartDate", "effectiveEndDate", "activeInd"];

  // Contact Plan Hitsory
  contactPlanHistory: MatTableDataSource<IContactPlan> = new MatTableDataSource([]);
  contactPlanHistoryColumns: string[] = ["planName", "contactPlanStartDate", "contactPlanEndDate", "contactPlanComments", "userCreatedTimestamp", "userCreatedId"];


  // Rate and Exceptions
  rateAndExceptions: IRateAndException[] = [];
  selectedRateAndExceptions: MatTableDataSource<IRateAndException> = new MatTableDataSource([]);
  rateAndExceptionColumns: string[] = ["contactRateAndExceptionTypeName", "effectiveStartDate", "effectiveEndDate", "value", "salesTerritory"];

  // Opportunities
  opportunityColumns: string[] = ["opportunityName", "salesTerritory", "stage", "dateContractSigned", "actualInstallDate", "opportunityFinalized"];

  // Recent Commissions
  recentCommissionsList: MatTableDataSource<ICommission> = new MatTableDataSource([]);
  recentCommissionColumns: string[] = ["commissionId", "ruleName", "opportunityName", "commissionAmount", "createdDate", "opportunityFinalized"];

  // otp list
  paymentColumns: string[] = ["contactName", "opportunityName", "paymentTypeName", "commissionTypeName", "commissionRuleName", "amount", "paymentStatusName", "processedDate", "paymentDueDate", "commissionForContact", "PaymentNote", "PaymentReversalNote"];
  payments: MatTableDataSource<IPayment> = new MatTableDataSource([]);
  //paymentBookBalances: MatTableDataSource<IPaymentBookBalance> = new MatTableDataSource([]);
  allSelected: boolean = false;
  pageSizeOptions: number[] = [5, 10, 20];
  pageSize = this.pageSizeOptions[0];
  public date: Date;

  // One Time Payment
  commissionRuleTypeId: number = null;
  commissionRuleTypes: IRuleType[] = [];
  commissionProcessingOneTimePayment: ICommissionProcessingOneTimePayment[] = [];

  // Contact Rates And Overrides Show Inactive
  contactRatesAndOverridesShowInactive:boolean = false

  constructor(private router: Router, public apiService: ApiService, private toastMsg: ToastrService, private datePipe: DatePipe, private activatedRoute: ActivatedRoute, private formBuilder: UntypedFormBuilder, private http: HttpClient, private dialog: MatLegacyDialog,private dateZonePipe: DatezonePipe) {
    // this.contact_id = this.activatedRoute.snapshot.params.contact_id;
    this.router.routeReuseStrategy.shouldReuseRoute = function () {
      return false;
    };
  }

  ngOnInit() {
    this.activatedRoute.params.subscribe(params => {
      this.contact_id = params.contact_id;

      if (!this.apiService.checkPermission('ViewSalesRepDetail')) {
        this.apiService.goBack();
        this.toastMsg.error("Insufficient Permissions. Please make sure your account can access this information.", "Permissions");
      }
      this.rateAndExceptionFormGroup = this.formBuilder.group({
        contactRateAndExceptionTypeId: [null],
        effectiveStartDate: ['', [Validators.required]],
        effectiveEndDate: [''],
        amount: [null],
        percentage: [null],
        number: [null],
        salesTerritory: [null]
      }, { validators: [dateLessThanDate] });

      this.oneTimePaymentFormGroup = this.formBuilder.group({
        commissionRuleTypeId: ["0", [Validators.required]],
        contactId: ["0"],
        amount: ["0", [Validators.required]],
        notes: [null]
      });
      this.getSalesTerritories();
      if (this.contact_id) {
        this.getContactsDetails();
        this.getOpportunities();
        this.getCommissionRuleTriggers();
        this.getRecentCommissions();
        this.getContactPaymentBook();
        this.getPlanDetails(this.contact_id);
        this.getContactRateIncentiveModifiers();
        this.getContactPlanHistory();
        this.getPayments();
        this.getContacts();
      }
      this.getCommissionRulesOneTimePayment();
    });
  }

  // getAllPaymentBookBalances() {
  //   // this.http.get<ApiResponse>(`${environment.apiBaseUrl}Payments/ContactPaymentBookBalances${this.salesDivision || this.salesOffice ? '?' : ''}${this.salesDivision != null && this.salesDivision != 'null' ? 'salesDivision=' + this.salesDivision + '&' : ''}${this.salesOffice != null && this.salesOffice != 'null' ? 'salesOffice=' + this.salesOffice + '&' : ''}`)
  //   let params = new HttpParams();
  //   params = params.append('IsZeroWithdrawalAmount', 'false');
  //   params = params.append('ContactId', this.contact_id);
  //   this.http.get<ApiResponse>(`${environment.apiBaseUrl}Payments/ContactPaymentBookBalances`, { params: params })
  //     .subscribe(data => {
  //       if (data && data.result) {
  //         var paymentBooks = data.result.map((bal: IPaymentBookBalance) => { return bal });

  //         this.paymentBookBalances = new MatTableDataSource(paymentBooks);
  //         this.paymentBookBalances.sort = this.sort.toArray()[this.sort.toArray().length - 1];
  //         this.paymentBookBalances.paginator = this.paginator.toArray()[this.paginator.toArray().length - 1];

  //       }
  //     }, (err: any) => {
  //       this.toastMsg.error(err.message, "Error!");
  //     })
  // }

  getContacts() {
    this.apiService.get('GetData/GetActiveContacts')
      .subscribe(data => {
        if (data && data.result) {
          this.contacts = data.result.map(type => { return <IContact>{ contactId: type.id, contactName: type.name } })
          this.oneTimePaymentFormGroup.controls.contactId.setValue("0");
        }
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      })
  }

  getPayments() {
    let params = new HttpParams();
    params = params.append('exclude0Payment', 'true');
    params = params.append('contactId', this.contact_id);
    this.http.get<ApiResponse>(`${environment.apiBaseUrl}Payments/GetOneTimePayments`, { params: params })
      .subscribe(data => {
        if (data && data.result) {
          var payments = data.result.map((payment: IPayment) => { return payment });
          this.payments = new MatTableDataSource<IPayment>(payments);

          this.payments.sort = this.sort.toArray()[this.sort.toArray().length - 1];
          this.payments.paginator = this.paginator.toArray()[this.paginator.toArray().length - 1];
        }
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      })
  }

  onBulkSelectionChange() {
    this.payments.filteredData.map(bal => bal.selected = this.allSelected);
  }

  onSelectionChange() {
    let balances = this.payments.filteredData.filter(bal => bal.selected);
    if (balances.length == this.payments.filteredData.length) {
      this.allSelected = true;
    } else {
      this.allSelected = false;
    }
  }

  checkSelected() {
    return this.payments && this.payments.data.filter(payment => payment.selected).length > 0;
  }

  getPaymentsWorksheet() {

    let p = this.payments.filteredData.filter(bal => bal.selected);
    var paymentIds = p.map(p => { return p.paymentId });
    var body = {
      paymentIds: paymentIds
    }

    this.http.post(`${environment.apiBaseUrl}Payments/PaymentsWorksheet`, body, { responseType: 'blob' })
      .subscribe(data => {
        this.downLoadFile(data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;");
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      });
  }

  /**
   * Method is use to download file.
   * @param data - Array Buffer data
   * @param type - type of the document.
   */
  downLoadFile(data: any, type: string) {
    let blob = new Blob([data], { type: type });

    FileSaver.saveAs(blob, 'Payments_Export.xlsx');
  }

  /**
   * Get contact details
   */
  getContactsDetails() {
    this.contactsList = null;
    this.apiService.get('Contacts/' + this.contact_id)
      .subscribe(data => {
        if (data["statusCode"] == "201" && data.result) {
          this.contactsList = data.result[0]
          //if(data.result[0].data[0] && data.result[0].data[0].planID){

          //}
        } else {
          this.toastMsg.error("No contacts found.", 'Server Error!')
        }
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!')
      });
  }

  /**
   * Get plan details
   */
  getPlanDetails(planId: number) {
    this.plan = null;
    this.apiService.get('PlanAssign/' + planId)
      .subscribe(data => {
        if (data["statusCode"] === "201" && data.result) {

          this.contact_plan_id = data.result.contactPlanId;
          this.plan_id = data.result.planId;

          this.plan = <IPlan>{
            contactPlanId: data.result.contactPlanId,
            planId: data.result.planId,
            planName: data.result.planName,
            description: data.result.description,
            startDate: data.result.startDate,
            endDate: data.result.endDate,
            planDetails: data.result.planDetails.map(planDetail => {
              return <IPlanDetail>{
                ruleTypeName: planDetail.ruleTypeName,
                rules: planDetail.rules.map(rule => {
                  return <IRule>{
                    ruleId: rule.ruleId,
                    ruleName: rule.ruleName,
                    promptAssignPlan: rule.promptAssignPlan
                  }
                })
              }
            })
          }
        }
      }, (err: any) => {
        //this.toastMsg.error(err.message, 'Server Error!')
      });
  }

  getRateAndExceptionTypes() {
    this.apiService.get(`RateAndExceptions/RateAndExceptionTypes?Active_Ind=${!this.contactRatesAndOverridesShowInactive}`)
      .subscribe(data => {
        this.rateAndExceptionTypes = [];
        if (data["statusCode"] === "201" && data.result) {
          this.rateAndExceptionTypes = data.result.map(res => {
            return <IRateAndExceptionType>{
              contactRateAndExceptionTypeId: res.contactRateAndExceptionTypeId,
              contactRateAndExceptionTypeName: res.contactRateAndExceptionTypeName,
              inputType: res.inputType,
              activeInd: res.activeInd,
              hasControlTable: res.hasControlTable
            }
          });

          this.commissionOnWattsSoldTypeId = this.rateAndExceptionTypes.filter(x => x.contactRateAndExceptionTypeName == "Commission Amount On Watt Sold")[0].contactRateAndExceptionTypeId;
          this.selectedRateAndExceptionType = this.rateAndExceptionTypes[0];

          this.initializeForm();

          this.rateAndExceptionFormGroup.controls.contactRateAndExceptionTypeId.setValue(this.selectedRateAndExceptionType.contactRateAndExceptionTypeId);

          this.getRateAndExceptions();

          this.getDropdowns();
        }
      }, (err: any) => {
        this.toastMsg.error(err.message, "Server Error!");
      });
  }

  getDropdowns() {
    this.rateAndExceptionTypes.forEach(type => {
      if (type.hasControlTable) {
        this.apiService.get(`RateAndExceptions/Dropdown/${type.contactRateAndExceptionTypeId}`)
          .subscribe(data => {
            if (data["statusCode"] === "201" && data.result) {
              let controlTable = <IControlTable>{};
              controlTable.rows = data.result.rows.map(row => { return <IControlTableRow>{ displayName: row.displayName, value: row.value } });

              this.dropdowns[type.contactRateAndExceptionTypeId] = controlTable;

            }
          }, (err: any) => {
            this.toastMsg.error(err.Message, "Server Error!");
          });
      }
    });
  }

  getSalesTerritories() {
    this.apiService.get('RateAndExceptions/GetSalesTerritories')
      .subscribe(data => {
        if (data && data.result) {
          // this.salesTerritories = data.result.map(territory => { return <string>territory });
          this.salesTerritories = data.result;
          this.anySalesTerritory = this.salesTerritories.filter(x => x.salesTerritory1 == "Any")[0];
          this.getRateAndExceptionTypes();
        }
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      });
  }

  getRateAndExceptions() {
    this.rateAndExceptions = [];
    this.apiService.get(`RateAndExceptions/${this.contact_id}?Active_Ind=${!this.contactRatesAndOverridesShowInactive}`)
      .subscribe(data => {
        if (data["statusCode"] === "201" && data.result) {
          if (data.result.length > 0) {
            this.rateAndExceptions = data.result.map(res => {
              return <IRateAndException>{
                contactRateAndExceptionId: res.contactRateAndExceptionId,
                contactRateAndExceptionTypeId: res.contactRateAndExceptionTypeId,
                contactRateAndExceptionTypeName: res.contactRateAndExceptionTypeName,
                rateOrExceptionAmount: res.rateOrExceptionAmount,
                rateOrExceptionPercentage: res.rateOrExceptionPercentage,
                rateOrExceptionNumber: res.rateOrExceptionNumber,
                effectiveStartDate: res.effectiveStartDate,
                effectiveEndDate: res.effectiveEndDate,
                inputType: res.inputType,
                salesTerritory: res.salesTerritory ? res.salesTerritory : this.anySalesTerritory.salesTerritory1
              }
            });

            this.getSelectedRateAndExceptions();
          } else {
            this.rateAndExceptions = [];
            this.selectedRateAndExceptions = new MatTableDataSource();
          }
        }
      }, (err: any) => {
        this.toastMsg.error(err.message, "Server Error!");
      });
  }

  getOpportunities() {
    this.opportunities = {};
    this.apiService.get(`Contacts/Opportunities/${this.contact_id}`)
      .subscribe(data => {
        if (data["statusCode"] === "201" && data.result) {
          Object.keys(data.result).forEach((ruleTriggerId, i) => {
            this.opportunities[ruleTriggerId] = this.opportunities[ruleTriggerId] || [];
            this.opportunities[ruleTriggerId] = new MatTableDataSource(data.result[ruleTriggerId].map(row => {
              return <IOpportunity>{
                opportunityId: row.opportunityId,
                opportunityName: row.opportunityName,
                trinitySalespersonName: row.trinitySalespersonName,
                systemSizeKwdc: row.systemSizeKwdc,
                opportunityAmount: row.opportunityAmount,
                purchaseMethod: row.purchaseMethod,
                salesTerritory: row.salesTerritory,
                dateContractSigned: row.dateContractSigned,
                utilityCompany: row.utilityCompany,
                stage: row.stage,
                actualInstallDate: row.actualInstallDate,
                opportunityFinalized: row.opportunityFinalized
              }
            }));

            // must be changed if more tables added to page
            this.opportunities[ruleTriggerId].sort = this.sort.toArray()[3 + i];
            this.opportunities[ruleTriggerId].paginator = this.paginator.toArray()[3 + i];
          });
        }

      }, (err: any) => {
        this.toastMsg.error(err.message, "Server Error!");
      });
  }

  getRecentCommissions() {
    this.recentCommissionsList = new MatTableDataSource([]);
    this.apiService.get(`Commissions/RecentByContact/${this.contact_id}`)
      .subscribe(data => {
        if (data["statusCode"] === "201" && data.result) {
          Object.keys(data.result).forEach(row => {
            this.recentCommissionsList = new MatTableDataSource(data.result.map(row => <ICommission>{
              commissionId: row.commissionId,
              contactId: row.contactId,
              planName: row.planName,
              ruleName: row.ruleName,
              ruleId: row.ruleId,
              paidTo: row.paidTo,
              rateOnWattSold: row.rateOnWattSold,
              totalReductions: row.totalReductions,
              commissionAmount: row.commissionAmount,
              commissionablePPW: row.commissionablePPW,
              commissionWatts: row.commissionWatts,
              solarProDeduction: row.solarProDeduction,
              opportunityId: row.opportunityId,
              opportunityName: row.opportunityName,
              salesDivision: row.salesDivision,
              salesOffice: row.salesOffice,
              actualInstallDate: row.actualInstallDate,
              dateContractSigned: row.dateContractSigned,
              commissionFinalized: row.commissionFinalized,
              commissionOverridden: row.commissionOverridden,
              createdDate: row.createdDate,
              opportunityFinalized: row.opportunityFinalized
            }));

            this.recentCommissionsList.sort = this.sort.toArray()[this.sort.toArray().length - 2];
            this.recentCommissionsList.paginator = this.paginator.toArray()[this.paginator.toArray().length - 2];
          });
        }

      }, (err: any) => {
        this.toastMsg.error(err.message, "Server Error!");
      });
  }

  getCommissionRuleTriggers() {
    this.apiService.get('CommissionRuleTriggers')
      .subscribe(data => {
        if (data["statusCode"] === "201" && data.result) {
          this.commissionRuleTriggers = data.result.map(ruleTrigger => <ICommissionRuleTrigger>{
            commissionRuleTriggerId: ruleTrigger.commissionRuleTriggerId,
            commissionRuleTriggerName: ruleTrigger.commissionRuleTriggerName,
            activeInd: ruleTrigger.activeInd
          });
        }
      }, (err: any) => {
        this.toastMsg.error(err.message, "Server Error!");
      });
  }

  getContactPaymentBook() {
    if (this.contact_id) {
      this.apiService.get(`PaymentBook/GetContactPaymentBook/${this.contact_id}`)
        .subscribe(data => {
          // if (data && data.result) {
          // dilip UAM issues
          if (data && data.result && this.apiService.checkPermission('ContactPaymentBook')) {
            this.contactPaymentBook = true;

          }
        }, err => {
        })
    }
  }

  getContactRateIncentiveModifiers() {
    if (this.contact_id) {
      this.apiService.get(`GetData/ContactRateIncentiveModifiers/${this.contact_id}`)
        .subscribe(data => {
          if (data && data.result) {
            var crim = data.result.map(x => { return <IContactRateIncentiveModifier>x });
            crim.sort((a, b) => {
              return <any>new Date(b.userModifiedTimestamp) - <any>new Date(a.userModifiedTimestamp);
            });
            this.contactRateIncentiveModifiers = new MatTableDataSource(crim);
            this.contactRateIncentiveModifiers.paginator = this.paginator.toArray()[2];
            this.contactRateIncentiveModifiers.sort = this.sort.toArray()[2];
          }
        }, err => {

        });
    }
  }

  getContactPlanHistory() {
    if (this.contact_id) {
      this.apiService.get(`ContactPlan/History/${this.contact_id}`)
        .subscribe(data => {
          if (data && data.result) {
            var cph = data.result.map(x => { return <IContactPlan>x });

            this.contactPlanHistory = new MatTableDataSource(cph);
            this.contactPlanHistory.paginator = this.paginator.toArray()[0];
            this.contactPlanHistory.sort = this.sort.toArray()[0];
          }
        }, err => {
          console.log("ERROR: getContactPlanHistory()", err);
        });
    }
  }

  onTypeChange(event: any) {
    let selected = this.rateAndExceptionTypes.filter(x => x.contactRateAndExceptionTypeId == event.target.value);
    if (selected.length > 0) {
      this.selectedRateAndExceptionType = selected[0];
    }

    this.rateAndExceptionFormGroup.controls.contactRateAndExceptionTypeId.setValue(event.target.value);

    // this.getSelectedRateAndExceptions();

    this.initializeForm();
  }

  onTerritoryChange() {
    var territory = this.salesTerritories.filter(x => x.salesTerritoryId == this.rateAndExceptionFormGroup.controls.salesTerritory.value);
    var territoryName = territory.length > 0 ? territory[0].salesTerritory1 : this.anySalesTerritory.salesTerritory1;

    this.rateAndExceptionFormGroup.controls.effectiveStartDate.setValue(null);
    this.rateAndExceptionFormGroup.controls.effectiveEndDate.setValue(null);

    this.selectedSalesTerritory = territoryName;

    this.rateAndExceptionFormGroup.setValidators([dateLessThanDate(), maxRateAndExceptionDate(this.selectedRateAndExceptions.filteredData, this.selectedSalesTerritory)]);
  }

  addClick() {
    this.initializeForm();
    this.rateAndExceptionFormGroup.controls.contactRateAndExceptionTypeId.setValue(this.selectedRateAndExceptionType.contactRateAndExceptionTypeId);
    this.updateRateAndException = false;
    this.addInd = true;
    this.rateAndExceptionFormGroup.controls.effectiveStartDate.setValue(null);
    this.rateAndExceptionFormGroup.controls.effectiveEndDate.setValue(null);
    this.rateAndExceptionFormGroup.controls.amount.setValue(0);
    this.rateAndExceptionFormGroup.controls.effectiveStartDate.enable();
    this.rateAndExceptionFormGroup.controls.amount.enable();
    this.rateAndExceptionFormGroup.controls.percentage.enable();
    this.rateAndExceptionFormGroup.controls.salesTerritory.setValue(this.anySalesTerritory.salesTerritoryId);
  }
  hideClick() {
    this.addInd = false;
  }
  rowClick(val: any) {
    var record = val.target.value;
    if (record == undefined || record.effectiveEndDate != null) {
      this.updateRateAndException = false;
      return;
    }
    let selected = this.rateAndExceptionTypes.filter(x => x.contactRateAndExceptionTypeId == record.contactRateAndExceptionTypeId);
    if (selected.length > 0) {
      this.selectedRateAndExceptionType = selected[0];
    }

    this.updateRateAndException = true;
    this.addInd = false;
    this.record = record;
    this.updateInd = true;
    this.rateAndExceptionFormGroup.clearValidators();
    this.rateAndExceptionFormGroup.reset();

    this.rateAndExceptionFormGroup.controls.effectiveStartDate.setValue(this.datePipe.transform(record.effectiveStartDate, 'yyyy-MM-dd'));
    this.rateAndExceptionFormGroup.controls.effectiveEndDate.setValue(null);
    this.contactRateAndExceptionTypeId = this.record.contactRateAndExceptionTypeId;
    this.rateAndExceptionFormGroup.controls.contactRateAndExceptionTypeId.setValue(this.selectedRateAndExceptionType.contactRateAndExceptionTypeId);
    this.rateAndExceptionFormGroup.controls.effectiveStartDate.disable();
    this.rateAndExceptionFormGroup.controls.amount.disable();
    this.rateAndExceptionFormGroup.controls.percentage.disable();

    var territory = this.salesTerritories.filter(x => x.salesTerritory1 == record.salesTerritory);
    var territoryId = territory.length > 0 ? territory[0].salesTerritoryId : this.anySalesTerritory.salesTerritoryId;

    this.rateAndExceptionFormGroup.controls.salesTerritory.setValue(territoryId);
    this.rateAndExceptionFormGroup.controls.salesTerritory.disable();
    this.selectedSalesTerritory = record.salesTerritory;

    if (record.rateOrExceptionAmount != null) {
      this.selectedRateAndExceptionType.inputType = "Amount"
    }
    if (record.rateOrExceptionPercentage != null) {
      this.selectedRateAndExceptionType.inputType = "Percentage"
    }
    if (record.rateOrExceptionNumber != null) {
      this.selectedRateAndExceptionType.inputType = "Number"
    }

    switch (this.selectedRateAndExceptionType.inputType) {
      case 'Amount':
        this.rateAndExceptionFormGroup.controls.amount.setValue(record.rateOrExceptionAmount);
        break;
      case 'Percentage':
        this.rateAndExceptionFormGroup.controls.percentage.setValue(record.rateOrExceptionPercentage);
        break;
      case 'Number':
        this.rateAndExceptionFormGroup.controls.amount.setValue(record.rateOrExceptionNumber);
        break;
    }

    this.rateAndExceptionFormGroup.setValidators([dateLessThanDate(), maxEditRateAndExceptionDate(this.selectedRateAndExceptions.filteredData, this.selectedSalesTerritory)]);
  }


  confirm() {
    if (this.selectedRateAndExceptionType.contactRateAndExceptionTypeId == this.commissionOnWattsSoldTypeId) {
        var fd = this.rateAndExceptionFormGroup.controls.effectiveStartDate.value + "T00:00:00";
        var foundFromDate = [];
        foundFromDate = this.contactRateIncentiveModifiers.filteredData.filter(rec=> new Date(fd) >= new Date(rec.effectiveStartDate) && rec.effectiveEndDate == null && rec.activeInd == true);
        if (foundFromDate.length == 0) {
          foundFromDate = this.contactRateIncentiveModifiers.filteredData.filter(rec=> new Date(fd) >= new Date(rec.effectiveStartDate) && new Date(fd) <= new Date(rec.effectiveEndDate) && rec.activeInd == true);
        }
        if (foundFromDate.length > 0){
          let dialogRef = this.dialog.open(ConfirmationModalComponent, {
            data: {
              message: "There is already an active rate incentive modifier, are you sure you want to add this incentive?"
            }
          });
      
          dialogRef.afterClosed().subscribe((res: boolean) => {
            if (res) {
              this.onSubmit();
            } else {
              return false;
            }
          })
        } else {
          this.onSubmit();
        }
    } else {
      this.onSubmit();
    }    
  }

  onSubmit() {
    if ((this.rateAndExceptionFormGroup.controls.effectiveEndDate.value == null) &&
      (this.addInd == false) &&
      (this.updateInd == true)
    ) {
      this.toastMsg.error("Effective end date is less than effective start date.");
      return;
    }
    let perc = this.rateAndExceptionFormGroup.controls.percentage.value ? this.rateAndExceptionFormGroup.controls.percentage.value : null;

    let form = <IRateAndException>{
      contactId: this.contact_id,
      contactRateAndExceptionTypeId: this.selectedRateAndExceptionType.contactRateAndExceptionTypeId,
      rateOrExceptionAmount: this.rateAndExceptionFormGroup.controls.amount.value,
      rateOrExceptionPercentage: perc,
      rateOrExceptionNumber: this.rateAndExceptionFormGroup.controls.number.value,
      effectiveStartDate: this.rateAndExceptionFormGroup.controls.effectiveStartDate.value,
      effectiveEndDate: this.rateAndExceptionFormGroup.controls.effectiveEndDate.value,
      salesTerritory: this.rateAndExceptionFormGroup.controls.salesTerritory.value == this.anySalesTerritory.salesTerritoryId ? null : this.rateAndExceptionFormGroup.controls.salesTerritory.value
    }
    if (this.record != undefined) {
      form.contactRateAndExceptionId = this.record.contactRateAndExceptionId;
    }

    this.apiService.post('RateAndExceptions', form)
      .subscribe(data => {
        this.toastMsg.success("Rate Override Successfully Created!");
        this.addInd = false;
        this.updateInd = false;
        this.updateRateAndException = false;
        this.initializeForm();
        this.getRateAndExceptions();
        this.getDropdowns();
        this.record = null;
      }, (err: any) => {
        this.toastMsg.error(err.message, "Server Error!");
      });
      this.rateAndExceptionFormGroup.reset();
      this.getRateAndExceptions();
  }

  initializeForm() {
    this.initializeType();

    this.rateAndExceptionFormGroup.controls.effectiveStartDate.setValue(null);
    this.rateAndExceptionFormGroup.controls.effectiveEndDate.setValue(null);

    this.rateAndExceptionFormGroup.updateValueAndValidity();

  }

  setUrl() {
    let url = '/ui/commissions/salesrep/' + this.contact_id;
    localStorage.setItem('opportunity', url);
  }

  initializeType() {
    switch (this.selectedRateAndExceptionType.inputType) {
      case 'Amount':
        this.rateAndExceptionFormGroup.controls.amount.setValue(0);
        this.rateAndExceptionFormGroup.controls.amount.setValidators([Validators.required, Validators.min(0.001)]);
        this.rateAndExceptionFormGroup.controls.amount.updateValueAndValidity();
        this.rateAndExceptionFormGroup.controls.percentage.setValue(null);
        this.rateAndExceptionFormGroup.controls.percentage.clearValidators();
        this.rateAndExceptionFormGroup.controls.percentage.updateValueAndValidity();
        this.rateAndExceptionFormGroup.controls.number.setValue(null);
        this.rateAndExceptionFormGroup.controls.salesTerritory.setValue(this.anySalesTerritory.salesTerritoryId);
        this.rateAndExceptionFormGroup.controls.number.clearValidators();
        this.rateAndExceptionFormGroup.controls.number.updateValueAndValidity();
        if(this.selectedRateAndExceptionType.contactRateAndExceptionTypeId == this.commissionOnWattsSoldTypeId){
          this.rateAndExceptionFormGroup.controls.salesTerritory.enable();
        }
        break;

      case 'Percentage':
        this.rateAndExceptionFormGroup.controls.amount.setValue(null);
        this.rateAndExceptionFormGroup.controls.amount.clearValidators();
        this.rateAndExceptionFormGroup.controls.amount.updateValueAndValidity();
        this.rateAndExceptionFormGroup.controls.percentage.setValue(0);
        this.rateAndExceptionFormGroup.controls.percentage.setValidators([Validators.required, Validators.min(0.001)]);
        this.rateAndExceptionFormGroup.controls.percentage.updateValueAndValidity();
        this.rateAndExceptionFormGroup.controls.number.setValue(null);
        this.rateAndExceptionFormGroup.controls.salesTerritory.setValue(this.anySalesTerritory.salesTerritoryId);
        this.rateAndExceptionFormGroup.controls.number.clearValidators();
        this.rateAndExceptionFormGroup.controls.number.updateValueAndValidity();
        break;

      case 'Number':
        this.rateAndExceptionFormGroup.controls.amount.setValue(null);
        this.rateAndExceptionFormGroup.controls.amount.clearValidators();
        this.rateAndExceptionFormGroup.controls.amount.updateValueAndValidity();
        this.rateAndExceptionFormGroup.controls.percentage.setValue(null);
        this.rateAndExceptionFormGroup.controls.percentage.clearValidators();
        this.rateAndExceptionFormGroup.controls.percentage.updateValueAndValidity();
        this.rateAndExceptionFormGroup.controls.number.setValue(0);
        this.rateAndExceptionFormGroup.controls.salesTerritory.setValue(this.anySalesTerritory.salesTerritoryId);
        this.rateAndExceptionFormGroup.controls.number.setValidators([Validators.required, Validators.min(1)]);
        this.rateAndExceptionFormGroup.controls.number.updateValueAndValidity();
        break;
    }
    this.rateAndExceptionFormGroup.setValidators([dateLessThanDate(), maxRateAndExceptionDate(this.selectedRateAndExceptions.filteredData, this.anySalesTerritory.salesTerritory1)]);
    this.rateAndExceptionFormGroup.updateValueAndValidity();
    this.oneTimePaymentFormGroup.controls.amount.setValue(0);
    this.oneTimePaymentFormGroup.controls.contactId.setValue(0);
    this.oneTimePaymentFormGroup.controls.commissionRuleTypeId.setValue(0);
    this.oneTimePaymentFormGroup.controls.amount.setValidators([Validators.required]);
    this.oneTimePaymentFormGroup.controls.amount.updateValueAndValidity();
    this.oneTimePaymentFormGroup.controls.commissionRuleTypeId.updateValueAndValidity();
  }

  getSelectedRateAndExceptions() {
    let ratesAndExceptions = this.rateAndExceptions.map(x => {
      if (x.rateOrExceptionAmount != null) {
        x.value = x.rateOrExceptionAmount;
      } else if (x.rateOrExceptionNumber != null) {
        x.value = x.rateOrExceptionNumber;
      } else if (x.rateOrExceptionPercentage != null) {
        x.value = x.rateOrExceptionPercentage;
      }

      return x;
    });

    this.selectedRateAndExceptions = new MatTableDataSource(ratesAndExceptions);

    this.selectedRateAndExceptions.sort = this.sort.toArray()[1];
    this.selectedRateAndExceptions.paginator = this.paginator.toArray()[1];

    this.rateAndExceptionFormGroup.setValidators([dateLessThanDate(), maxRateAndExceptionDate(this.selectedRateAndExceptions.filteredData, this.anySalesTerritory.salesTerritory1)]);
    this.rateAndExceptionFormGroup.updateValueAndValidity();

  }

  checkStartDate(): boolean {
    if (this.rateAndExceptionFormGroup.controls.effectiveStartDate.value == null || this.selectedRateAndExceptions.filteredData.length == 0 || this.selectedRateAndExceptions[this.selectedRateAndExceptions.filteredData.length - 1].effectiveEndDate == null) {
      return false;
    }

    return new Date(this.rateAndExceptionFormGroup.controls.effectiveStartDate.value) < new Date(this.selectedRateAndExceptions[this.selectedRateAndExceptions.filteredData.length - 1].effectiveEndDate)
  }

  checkAssignPlanCapability() {
    return this.apiService.checkPermission('AssignPlan');
  }

  checkCanViewRule() {
    return this.apiService.checkPermission('ViewRule');
  }

  checkCanViewComm() {
    return this.apiService.checkPermission('ViewCommissions');
  }

  checkCanViewOpp() {
    return this.apiService.checkPermission('ViewOpportunityDetail');
  }

  myFilter = (d: Date | null): boolean => {
    const day = (d || new Date(1900, 1, 1));
    // Prevent Saturday and Sunday from being selected.
    return day > new Date(this.plan.startDate.toString());
  }

  getMinDate() {
    return new Date(this.plan.startDate.toString());
  }

  updatePlanEndDate() {
    if (this.newPlanEndDate) {
      if (this.newPlanEndDate < this.plan.endDate) {
        this.toastMsg.warning("Plan's end date cannot be before the plan's start date.");
        return;
      }

      var body = {
        contactPlanId: this.plan.contactPlanId,
        newEndDate: this.newPlanEndDate
      }

      this.apiService.post('ContactPlan/UpdateEndDate', body)
        .subscribe(data => {
          if (data && data.result) {
            this.plan.endDate = data.result.contactPlanEndDate;
            this.newPlanEndDate = null;
          }
        }, err => {
          this.toastMsg.error(err.message, "Error!");
        })
    }
  }

  checkCanViewSalesRepDashboard(): boolean {
    if (this.apiService.checkPermission('ViewSalesRepDashboard')) {
      return true;
    } else {
      return false;
    }
  }

  checkCanCalcCommissions(): boolean {
    if (this.apiService.checkPermission('CalculateCommissionsOpportunityLevel')) {
      return true;
    } else {
      return false;
    }
  }

  checkNewPlanEndDate() {
    return this.newPlanEndDate && this.newPlanEndDate > this.plan.startDate
  }

  calculateCommissions() {
    if (this.contact_id) {
      var body = {
        "PlanExecutionJobTypeName": "Runtime_Automation",
        "Plans": [],
        "SalesDivision": "",
        "Opportunities": [],
        "Contacts": [parseInt(this.contact_id)]
      }

      this.http.post(`${this.apiService.workflowBaseUrl}Workflow/RunPlanExecutionV2Async`, body)
        .subscribe((data: any) => {
          if (data && data.result) {
            this.toastMsg.success(data.result);
          }
        }, err => {
          this.toastMsg.error(err.message, "Error!");
        })
    }
  }

  getRateAndExceptionColumns() {
    switch (this.selectedRateAndExceptionType.inputType) {
      case "Amount":
        var arr = this.rateAndExceptionColumns.slice();
        arr.push("rateOrExceptionAmount");
        return arr;

      case "Percentage":
        var arr = this.rateAndExceptionColumns.slice();
        arr.push("rateOrExceptionPercentage");
        return arr;

      case "Number":
        var arr = this.rateAndExceptionColumns.slice();
        arr.push("rateOrExceptionNumber");
        return arr;
    }
  }

  getMinEndDate() {
    if (this.plan && this.plan.startDate) {
      let date = new Date(this.plan.startDate);
      date.setDate(date.getDate() + 1);
      return date;
    }
  }

  getMinRateStartDate() {
    if (this.rateAndExceptions) {

      var territories = this.salesTerritories.filter(x => x.salesTerritoryId == this.rateAndExceptionFormGroup.controls.salesTerritory.value);
      var filteredTerritory = territories.length > 0 ? territories[0].salesTerritory1 : this.anySalesTerritory.salesTerritory1;
      
      var dates = this.selectedRateAndExceptions.filteredData.map(x => { if (x.effectiveStartDate && x.contactRateAndExceptionTypeName == this.selectedRateAndExceptionType.contactRateAndExceptionTypeName && x.salesTerritory == filteredTerritory) return new Date(x.effectiveStartDate) }).filter(x => x != null);
      //var date = new Date(Math.max.apply(null, dates));
      var date = new Date(Math.max(...dates.map(date => date.getTime())));
      if (date) date.setDate(date.getDate() + 1);
      if (date && !isNaN(date.getDate())) {
        return date;
      } else {
        return null;
      }
    }
  }

  clearDateStart(date: HTMLInputElement) {
    date.value = "";
    this.date = null;
    this.rateAndExceptionFormGroup.controls.effectiveStartDate.setValue('');
        event.stopPropagation();
      }

  clearDateEnd(date: HTMLInputElement) {
    date.value = "";
    this.date = null;
    this.rateAndExceptionFormGroup.controls.effectiveEndDate.setValue('');
        event.stopPropagation();
  }

  getCommissionRulesOneTimePayment() {
    this.apiService.get('GetData/GetCommissionRulesOneTimePayment')
      .subscribe(data => {
        if (data && data.result) {
          this.commissionRuleTypes = data.result.map(type => { return <IRuleType>{ commissionRuleTypeId: type.id, commissionRuleTypeName: type.name } })
        }
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      })
  }
  clearOneTimePaymentsFields(){
    this.oneTimePaymentFormGroup.controls.contactId.setValue('0');
    this.oneTimePaymentFormGroup.controls.commissionRuleTypeId.setValue('0');
    this.oneTimePaymentFormGroup.controls.amount.setValue('0');
    this.oneTimePaymentFormGroup.controls.notes.setValue(null);
  }

  onSubmitOneTimePayment() {

    if (this.oneTimePaymentFormGroup.controls.commissionRuleTypeId.value == "0")  {
      this.toastMsg.error("Select Commission Rule.");
      return;
    }

    let form = <ICommissionProcessingOneTimePayment>{
      contactId: this.contact_id,
      commissionRuleId: this.oneTimePaymentFormGroup.controls.commissionRuleTypeId.value,
      commissionForContactId: this.oneTimePaymentFormGroup.controls.contactId.value.contactId,
      amount: this.oneTimePaymentFormGroup.controls.amount.value,
      notes: this.oneTimePaymentFormGroup.controls.notes.value
    }

    this.apiService.post('Commissions/OneTimePayment', form)
      .subscribe(data => {
        this.toastMsg.success("One Time Payment Successfully Created!");
        this.clearOneTimePaymentsFields();
        this.getRecentCommissions();
        this.getPayments();
      }, (err: any) => {
        this.toastMsg.error(err.message, "Server Error!");
      });
  }

  openSalesrep(id){
    const url = `#/ui/commissions/salesrepconfiguration/${id}`;
    window.open(url, '_blank');
  }


  openOpportunity(opportunityId){
    const url = `#/ui/commissions/opportunitydetails/${opportunityId}`;
    window.open(url, '_blank');
  }

  get effectiveStartDate() { return this.rateAndExceptionFormGroup.get('effectiveStartDate'); }
  get amount() { return this.rateAndExceptionFormGroup.get('amount'); }
  get percentage() { return this.rateAndExceptionFormGroup.get('percentage'); }
  get number() { return this.rateAndExceptionFormGroup.get('number'); }
}
