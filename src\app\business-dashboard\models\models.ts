export interface IdateRange {
    startDate: string,
    endDate: string

}

export interface IPendingPayment {
    contactId: number,
    amount: string,
    contactLegalName: string,
    opportunityId: number
    opportunityName: string,
    commissionId: number,
    paymentStatus: string
    paymentType: string,
    salesDivision: string,
    salesTerritoryStateCode: string,
    modifiedDate: string
  }

  export interface IRecentCommissionHistory {
    contactId: number,
    amount: string,
    contactLegalName: string,
    commissionType:string;
    commissionId: number,
    opportunityId: number
    opportunityName: string,
    modifiedDate: string,
    createdDate: string
  }
  export interface IPaymentHold {
    contactId: number,
    amount: string,
    contactLegalName: string,
    opportunityId: number,
    commissionId: number,
    opportunityName: string,
    paymentStatus: string
    paymentType: string,
    salesDivision: string,
    modifiedDate: string
  }

  export interface IYaxis {
    type: string
    name: string
    data: number[]
  }