import { Component, OnInit, ViewChild, Input } from '@angular/core';
import { MatLegacyPaginator } from '@angular/material/legacy-paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { ApiService } from 'src/app/services/api.service';
import { ToastrService } from 'ngx-toastr';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { ICommissionAudit } from 'src/app/model/audit-commission-finalize';


@Component({
    selector: 'app-commission-audit',
    templateUrl: './audit-commission-finalize.component.html',
    styleUrls: ['./audit-commission-finalize.component.css']
  })

  export class CommissionAuditComponent implements OnInit {
    @ViewChild(MatSort, { static: true }) sort: MatSort;
    @ViewChild(MatLegacyPaginator, {static: true}) paginator: MatLegacyPaginator;
    @Input() commissionId: number;
    showCommissionAudit: boolean = false;
    pageSizeOptions: number[] = [5, 10, 25, 100];
    commissionAuditElements: MatTableDataSource<ICommissionAudit> = new MatTableDataSource();
    commissionAuditColumns: string[] = ["commissionId", "finalized", "reasonForChange", "userCreatedTimestamp", "userCreatedId"];

    constructor(public apiService: ApiService, private toastMsg: ToastrService, private http: HttpClient) { }

    ngOnInit() {
      this.commissionId = 0;
      this.commissionAuditElements = new MatTableDataSource();
    }

    ngOnChanges() {
      this.getCommissionAudit(this.commissionId);
    }

    getCommissionAudit(commissionId: number) {
        this.http.get<ICommissionAudit[]>(`${environment.apiBaseUrl}AuditCommissionFinalize/${commissionId}`)
          .subscribe(data => {
            if (data) {
              this.commissionAuditElements = new MatTableDataSource(data);
              this.commissionAuditElements.sort = this.sort;
              this.commissionAuditElements.paginator = this.paginator;
            }
          }, err => {
            this.toastMsg.error(err.message, "Error!");
          });
      }

    }