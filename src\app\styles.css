/* @import '~@angular/material/prebuilt-themes/indigo-pink.css'; */
@import "./custom-md-theme.css";
/* @import './custom-boostrap-md.css'; */
/* =============================================================================
   HTML5 CSS Reset Minified - Eric Meyer
   ========================================================================== */

/* =============================================================================
   My CSS
   ========================================================================== */

/* ---- base ---- */

canvas {
  display: block;
  vertical-align: bottom;
}

/* ---- stats.js ---- */

.count-particles {
  background: #000022;
  position: absolute;
  top: 48px;
  left: 0;
  width: 80px;
  color: #fff;
  font-size: 0.8em;
  text-align: left;
  text-indent: 4px;
  line-height: 14px;
  padding-bottom: 2px;
  font-family: Helvetica, Arial, sans-serif;
  font-weight: bold;
}

.js-count-particles {
  font-size: 1.1em;
}

#stats,
.count-particles {
  -webkit-user-select: none;
  margin-top: 5px;
  margin-left: 5px;
}

#stats {
  border-radius: 3px 3px 0 0;
  overflow: hidden;
}

.count-particles {
  border-radius: 0 0 3px 3px;
}

/* ---- particles.js container ---- */

#particles-js {
  width: 100%;
  height: 350px;
  /*background-image: url("");*/
  background-size: cover;
  background-position: 50% 50%;
  background-repeat: no-repeat;
}
/*Client logos*/
.client {
  /* filter: url(filters.svg#grayscale); Firefox 3.5+ */

  max-width: 100%;
  max-height: 100%;
}

.client:hover {
  filter: none;
  -webkit-filter: grayscale(0);
  -webkit-transform: scale(1.01);
  opacity: 1 !important;
}
.client-text {
  width: 150px;
  height: 150px;
  padding: 10px;
}
.overlay {
  top: 75%;
  bottom: 0;
  left: 0;
  right: 0;
  transition: 0.5s ease;
  opacity: 0;
}
.client-text:hover .overlay {
  opacity: 1;
}
.client-logo-div {
  border: solid 1px #f7f7f7 !important;
  box-sizing: border-box;
  text-align: -webkit-center !important;
  filter: gray; /* IE5+ */
  -webkit-filter: grayscale(1); /* Webkit Nightlies & Chrome Canary */
  -webkit-transition: all 0.2s ease-in-out;
  opacity: 0.3 !important;
}
.client-logo-div:hover {
  filter: none;
  -webkit-filter: grayscale(0);
  -webkit-transform: scale(1.01);
  opacity: 1 !important;
}
.content {
  height: -webkit-fill-available !important;
}

.custom-font-weight {
  font-weight: 600;
}

.card-font-weight {
  font-weight: 500;
}

.dropdown-menu .dropdown-item:hover,
.dropdown-menu .dropdown-item:focus,
.dropdown-menu a:hover,
.dropdown-menu a:focus,
.dropdown-menu a:active {
  background-color: #26c6da !important;
}

.form-control,
.is-focused .form-control {
  background-image: linear-gradient(
      to top,
      #6cca98 2px,
      rgba(156, 39, 176, 0) 2px
    ),
    linear-gradient(to top, #d2d2d2 1px, rgba(210, 210, 210, 0) 1px);
}

.form-check .form-check-input:checked + .form-check-sign .check {
  background: #26c6da;
}
.form-check-radio .form-check-input:checked + .circle .check {
  background: #26c6da;
}
.error {
  color: #ff0000;
}
/* .main-head{
    padding: 4px 0px 0px 75px; height: 80px; background: linear-gradient(60deg, #26c6da, #00acc1);
  } */
.custom-nav-header {
  /* position: fixed; background: linear-gradient(60deg, #26c6da, #00acc1); */
  background-color: #369dd6;
  color: white;
  height: 80px !important;
}

.mat-progress-spinner circle,
.mat-spinner circle {
  stroke: #00acc1;
}
div.sticky {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
}

.custom-input {
  display: inline-block;
  height: calc(2.4375rem + 2px);
  padding: 0.375rem 1.75rem 0.375rem 0.75rem;
  line-height: 1.5;
  color: #495057;
  vertical-align: middle;
  background-size: 8px 10px;
  border: 1px solid #d2d2d2;
  border-radius: 0.25rem;
  appearance: none;
}
.paymentwithdrawal .custom-input{width: 100%;;}
.outreachopportunities .custom-input{width: 100%;;}
/* button {
  background-color: #369DD6 !important;
} */

.btn {
  background-color: #369dd6 !important;
}

.btn-success {
  background-color: #4caf50 !important;
}

.btn.btn-success.btn-link {
  background-color: transparent !important;
}

.btn-warning {
  background-color: #ff9800 !important;
}

.btn-danger {
  background-color: #f44336 !important;
}

.btn-secondary {
  background-color: inherit !important;
}

/* .material-icons {
  color: #369DD6 !important;
} */

.fa-plus-circle:before {
  color: #369dd6 !important;
}

.blue-icon {
  color: #369dd6 !important;
}

.background-trinity-green {
  background-color: #6cca98 !important;
}

i.material-icons.pointer.ng-star-inserted {
  color: #369dd6 !important;
}
.hover:hover {
  cursor: pointer;
}

.no-hover-effect:hover {
  cursor: default;
}

.hover-move:hover {
  cursor: move;
}

.mat-column-25 {
  word-wrap: break-word !important;
  white-space: unset !important;
  flex: 0 0 28% !important;
  width: 25% !important;
  overflow-wrap: break-word;
  word-wrap: break-word;

  word-break: break-word;

  -ms-hyphens: auto;
  -moz-hyphens: auto;
  -webkit-hyphens: auto;
  hyphens: auto;
}

.align-button-right {
  display:flex !important;
  justify-content: flex-end !important;
}
