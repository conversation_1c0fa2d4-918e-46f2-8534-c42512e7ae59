import { Component, Input, OnInit, ViewChild } from '@angular/core';
import Highcharts from 'highcharts/highmaps';
// import worldMap from '@highcharts/map-collection/custom/north-america.geo.json';
import topology from '@highcharts/map-collection/countries/us/us-all.geo.json';

@Component({
  selector: 'app-map',
  templateUrl: './map.component.html',
  styleUrls: ['./map.component.css']
})
export class MapComponent implements OnInit {
  @ViewChild('globalChart') globalChart;
  Highcharts: typeof Highcharts = Highcharts;
  chartConstructor = 'mapChart';
  mapData: any;
  @Input() data:any;  
  chartInstance: Highcharts.Chart | any;
  chartOptions: Highcharts.Options; 

  //  public var fetchRes:any;


  getChartInstance(e: Highcharts.Chart) {
    this.chartInstance = e;
    this.chartInstance.reflow();
  }

  constructor() { }

  ngOnInit() {    
    this.kiloWattChartLoad(); 
    setTimeout(() => {
      if(this.data){
        this.chartOptions = {
          series: [{
            type: 'map',
            data: this.data,
            joinBy: ['postal-code', 'code'],
            dataLabels: {
              enabled: true,
              color: '#EFF9FE',
              format: '{point.code}'
            },
            name: 'KiloWatts Sold',
          }] 
        }
      } 
    },500)
  }

  ngAfterViewInit() {
    
  }

  ngOnChanges(){
    if(this.data){
      this.chartOptions = {
        series: [{
          type: 'map',
          data: this.data,
          joinBy: ['postal-code', 'code'],
          dataLabels: {
            enabled: true,
            color: '#EFF9FE',
            format: '{point.code}'
          },
          name: 'KiloWatts Sold',
        }] 
      }
    }    
  }

  kiloWattChartLoad() {
    
    
    this.chartOptions = {
      chart: {
        borderWidth: 1,
        map: topology,
        borderColor: 'transparent',
      },
      exporting: {
        sourceWidth: 600,
        sourceHeight: 500
      },
      colorAxis: {
        min: 1,
        type: 'logarithmic',
        minColor: '#EFF9FE',
        maxColor: '#2a78c2',
        stops: [
          [0, '#EFF9FE'],
          [0.67, '#8BC1E2'],
          [1, '#2a78c2']
        ]
      },
      credits: {
        enabled: false
      },
      title: {
        text: 'Kilowatts Sold by State',
      },
      subtitle: {
        text: "State wise data of system size kWDC sold with actual install date on the date range"
      },

      legend: {
        layout: 'horizontal',
        borderWidth: 0,
        backgroundColor: 'rgba(255,255,255,0.85)',
        floating: true,
        verticalAlign: 'bottom',
        y: 25
      },

      mapNavigation: {
        enabled: true
      },
      
      tooltip: {
        pointFormat: '{point.code}: {point.value}KW'
      },
      series: [{
        type: 'map',
        data: this.mapData,
        joinBy: ['postal-code', 'code'],
        dataLabels: {
          enabled: true,
          color: '#EFF9FE',
          format: '{point.code}'
        },
        name: 'KiloWatts Sold',
      }]     
    };
  }
}
