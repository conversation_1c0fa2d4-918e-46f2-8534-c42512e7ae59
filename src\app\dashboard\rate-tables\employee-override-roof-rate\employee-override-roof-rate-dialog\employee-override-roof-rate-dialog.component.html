<p>The following submitted rates had errors</p>

  <mat-table [dataSource]="errors" matSort>
    <ng-container matColumnDef="reason">
      <th mat-header-cell *matHeaderCellDef> Reason </th>
      <td mat-cell *matCellDef="let element"> {{element.reason}} </td>
    </ng-container>

    <ng-container matColumnDef="effectiveStartDate">
      <th mat-header-cell *matHeaderCellDef> Effective Start Date </th>
      <td mat-cell *matCellDef="let element"> {{element.rate.effectiveStartDate | date: 'MM-dd-yyyy'}} </td>
    </ng-container>

    <ng-container matColumnDef="effectiveEndDate">
      <th mat-header-cell *matHeaderCellDef> Effective End Date </th>
      <td mat-cell *matCellDef="let element"> {{element.rate.effectiveEndDate | date: 'MM-dd-yyyy'}} </td>
    </ng-container>


    <tr mat-header-row *matHeaderRowDef="columnsToDisplay"></tr>
    <tr mat-row *matRowDef="let myRowData; columns: columnsToDisplay"></tr>

  </mat-table>


