import { Component, OnInit, ViewChild } from '@angular/core';
import { StatusOverviewComponent } from './status-overview/status-overview.component'
import { IJob } from 'src/app/model/job.model';
import { ApiService } from 'src/app/services/api.service';

@Component({
  selector: 'app-data-integration',
  templateUrl: './data-integration.component.html',
  styleUrls: ['./data-integration.component.css']
})
export class DataIntegrationComponent implements OnInit {
  @ViewChild(StatusOverviewComponent) private statusOverviewComponent: StatusOverviewComponent;
  startButtonEnabled: boolean = true;
  jobsStatus;

  constructor(public apiService: ApiService) { }

  ngOnInit() {
    // this.getJobs();
  }

  onJobStarted(submitted: boolean){
    this.statusOverviewComponent.getStatusOverview();
  }

  getJobs(jobsStatus) {
    this.jobsStatus = jobsStatus;
  }
}
