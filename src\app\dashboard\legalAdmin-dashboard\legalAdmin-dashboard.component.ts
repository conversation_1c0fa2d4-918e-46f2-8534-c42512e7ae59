import { Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, FormGroup, Validators } from "@angular/forms";
import { ApiService } from '../../services/api.service';
import { Router, ActivatedRoute } from '@angular/router';
import { InfoBox } from './model/infobox.model';
import { ToastrService } from 'ngx-toastr';
// import { stringify } from 'querystring';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { DataSource } from '@angular/cdk/table';
import { MatLegacyPaginator } from '@angular/material/legacy-paginator';
import { TableFilterPipe } from '../../pipe/table-filter.pipe';
import { DatePipe, PercentPipe, CurrencyPipe } from '@angular/common';
import { ExportService } from 'src/app/services/export.service';

@Component({
  selector: 'app-legalAdmin-dashboard',
  templateUrl: './legalAdmin-dashboard.component.html',
  styleUrls: ['./legalAdmin-dashboard.component.css']
})
export class LegalAdminDashboardComponent implements OnInit {
  @ViewChild('table1', { read: MatSort, static: true }) sort1: MatSort;
  @ViewChild('table2', { read: MatSort, static: true }) sort2: MatSort;
  @ViewChild('table3', { read: MatSort, static: true }) sort3: MatSort;
  @ViewChild('table4', { read: MatSort, static: true }) sort4: MatSort;
  @ViewChild('table5', { read: MatSort, static: true }) sort5: MatSort;

  @ViewChild('paginator1', { static: true }) paginator1: MatLegacyPaginator;
  @ViewChild('paginator2', { static: true }) paginator2: MatLegacyPaginator;
  @ViewChild('paginator3', { static: true }) paginator3: MatLegacyPaginator;
  @ViewChild('paginator4', { static: true }) paginator4: MatLegacyPaginator;
  @ViewChild('paginator5', { static: true }) paginator5: MatLegacyPaginator;
  infoBox: InfoBox = {
    blueInfo: { name: "", number: 0 },
    greenInfo: { name: "", number: 0 },
    greyInfo: { name: "", number: 0 }
  };
  tabPosition: number = 0;
  tabName: string = "Reps With Plans";
  getRepsWithPlans: any;
  getRepsWithoutPlans: any;
  getPlans: any;
  originalRepsWithPlans: any;
  originalRepsWithoutPlans: any;
  originalPlans: any;
  dataSourceRepsWithPlans;
  dataSourceRepsWithoutPlans;
  dataSourcePlans;
  displayedColumnsRepsWithPlans = [];
  displayedColumnsRepsWithoutPlans = [];
  displayedColumnsPlans = [];

  columnNames = [{
    id: "Contact_Name",
    value: "Contact Name"
  },{
    id: "Paycom_Employee_Number",
    value: "Paycom/Referral No."
  },{
    id: "Payment_Book",
    value: "Payment Book"
  },{
    id: "Plan_Name",
    value: "Plan Name"
  },
  {
    id: "Sales_Office",
    value: "Sales Office"
  },
  {
    id: "Sales_Division",
    value: "Sales Division"
  }, {
    id: "Title",
    value: "Title"
  },
  {
    id: "Contact_Plan_Start_Date",
    value: "Plan Start Date"
  },
  {
    id: "Contact_Plan_End_Date",
    value: "Plan End Date"
  },
  {
    id: "Contact_Phone",
    value: "Contact Phone"
  },
  {
    id: "Contact_Email",
    value: "Contact Email"
  }];

  columnNamesAlt = [{
    id: "Contact_Name",
    value: "Contact Name"
  },{
    id: "Paycom_Employee_Number",
    value: "Paycom/Referral No."
  },{
    id: "Payment_Book",
    value: "Payment Book"
  },  
  {
    id: "Sales_Office",
    value: "Sales Office"
  },
  {
    id: "Sales_Division",
    value: "Sales Division"
  },
  {
  id: "Start_Date",
  value: "Start Date"
  },
  {
    id: "End_Date",
    value: "End Date"
  },
  {
    id: "Contact_Phone",
    value: "Contact Phone"
  },
  {
    id: "Contact_Email",
    value: "Contact Email"
  }];
  columnNamesPlans = [{
    id: "Plan_Name",
    value: "Plan Name"
  }];


  constructor(public apiService: ApiService, private toastMsg: ToastrService, private router: Router, private activatedRoute: ActivatedRoute, private formBuilder: UntypedFormBuilder, private pipe: TableFilterPipe,
    private datePipe: DatePipe, private percentPipe: PercentPipe, private currencyPipe: CurrencyPipe, private exportService: ExportService) {

    this.apiService.hideLoader = true;
    localStorage.setItem('href', window.location.href);

    this.infoBox = {
      blueInfo: { name: "", number: 0 },
      greenInfo: { name: "", number: 0 },
      greyInfo: { name: "", number: 0 }
    };
  }

  ngOnInit() {
    if (!this.apiService.checkPermission('ViewLegalDashboard')) {
      this.router.navigate(['/unauthorized']);
    }
    this.getData();
    this.getStaticBoxes();
    this.dataSourceRepsWithPlans = new MatTableDataSource(this.dataSourceRepsWithPlans);
    this.dataSourceRepsWithPlans.sort = this.sort2;
  }

  getStaticBoxes() {
    this.apiService.get('Dashboard/GetRepsWithPlansCount')
      .subscribe(data => {
        this.infoBox.blueInfo = { name: "Reps With Plans", number: data };
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
    this.apiService.get('Dashboard/GetRepsWithoutAPlanCount')
      .subscribe(data => {
        this.infoBox.greenInfo = { name: "Reps Without Plans", number: data };
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
    this.apiService.get('Dashboard/GetPlansCount')
      .subscribe(data => {
        this.infoBox.greyInfo = { name: "List Of Plans", number: data };
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }

  getData() {
    this.apiService.get('Dashboard/GetRepsWithPlans')
      .subscribe(data => {
        console.log("withplans", data);
        this.getRepsWithPlans = data.result;
        this.displayedColumnsRepsWithPlans = this.columnNames.map(x => x.id);
        this.createTableRepsWithPlans();
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });

    this.apiService.get('Dashboard/GetSalesWithoutActive')
      .subscribe(data => {
        console.log("without", data);
        this.getRepsWithoutPlans = data.result;
        this.displayedColumnsRepsWithoutPlans = this.columnNamesAlt.map(x => x.id);
        this.createTableRepsWithoutPlans();
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
    this.apiService.get('Dashboard/GetPlans')
      .subscribe(data => {
        console.log("plans");
        this.getPlans = data.result;
        this.displayedColumnsPlans = this.columnNamesPlans.map(x => x.id);
        this.createTablePlans();
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }
  createTableRepsWithPlans() {
    let tableArr: Element[] = [];
    for (let i: number = 0; i <= this.getRepsWithPlans.length - 1; i++) {
      let currentRow = this.getRepsWithPlans[i];
      tableArr.push({
        Contact_Id: currentRow.contact_Id, Contact_Name: currentRow.contact_Name,Paycom_Employee_Number:currentRow.paycom_Employee_Number, Plan_Name: currentRow.plan_Name, Plan_Header_Id: currentRow.plan_Header_Id, Sales_Office: currentRow.sales_Office, Sales_Division: currentRow.sales_Division,
        Title: currentRow.title, Employee_Start_Date: this.datePipe.transform(currentRow.employee_Start_Date), Contact_Plan_Start_Date: this.datePipe.transform(currentRow.contact_Plan_Start_Date), Contact_Plan_End_Date: this.datePipe.transform(currentRow.contact_Plan_End_Date), Contact_Phone: currentRow.contact_Phone, Contact_Email: currentRow.contact_Email
      });
    }
    this.dataSourceRepsWithPlans = new MatTableDataSource(tableArr);
    this.originalRepsWithPlans = tableArr;
    this.dataSourceRepsWithPlans.sort = this.sort1;
    this.dataSourceRepsWithPlans.paginator = this.paginator1;
  }
  createTableRepsWithoutPlans() {
    let tableArr: ElementWithoutPlans[] = [];
    for (let i: number = 0; i <= this.getRepsWithoutPlans.length - 1; i++) {
      let currentRow = this.getRepsWithoutPlans[i];
      tableArr.push({
        Contact_Id: currentRow.contact_Id, Contact_Name: currentRow.contact_Name,Paycom_Employee_Number:currentRow.paycom_Employee_Number, Sales_Office: currentRow.sales_Office, Sales_Division: currentRow.sales_Division,
        Start_Date: this.datePipe.transform(currentRow.start_Date), End_Date: this.datePipe.transform(currentRow.end_Date), Contact_Phone: currentRow.contact_Phone, Contact_Email: currentRow.contact_Email
      });
    }
    this.dataSourceRepsWithoutPlans = new MatTableDataSource(tableArr);
    this.originalRepsWithoutPlans = tableArr;
    this.dataSourceRepsWithoutPlans.sort = this.sort2;
    this.dataSourceRepsWithoutPlans.paginator = this.paginator2;
  }
  createTablePlans() {
    let tableArr: ElementAlt[] = [];
    for (let i: number = 0; i <= this.getPlans.length - 1; i++) {
      let currentRow = this.getPlans[i];
      tableArr.push({ Plan_Header_Id: currentRow.plan_Header_Id, Plan_Name: currentRow.plan_Name });
    }
    this.dataSourcePlans = new MatTableDataSource(tableArr);
    this.originalPlans = tableArr;
    this.dataSourcePlans.sort = this.sort3;
    this.dataSourcePlans.paginator = this.paginator3;
  }

  moveToSelectedTab(tabName: string) {
    this.tabName = tabName;
    for (let i = 0; i < document.querySelectorAll('.mat-tab-label-content').length; i++) {
      if ((<HTMLElement>document.querySelectorAll('.mat-tab-label-content')[i]).innerText == tabName) {
        (<HTMLElement>document.querySelectorAll('.mat-tab-label')[i]).click();
      }
    }
  }

  exportTable(tabName: string) {
    var data : any[][];
    var headers: string[];
    switch (tabName) {
      case "Reps With Plans":
        headers = [];
        data = [];
        let adjustedColumnNames = this.columnNames;
        adjustedColumnNames.push({
          id: "Employee_Start_Date",
          value: "Employee Start Date"
        });
        adjustedColumnNames.forEach(x => {
          headers.push(x.value);
        });
        headers = adjustedColumnNames.map(x => x.value);
        data = this.dataSourceRepsWithPlans.filteredData.map(x => {
          var keys = adjustedColumnNames.map(y => y.id);
          var values = keys.map(y => {return x[y]});
          return values;
        });
        break;
      
      case "Reps Without Plans":
        headers = this.columnNamesAlt.map(x => x.value);
        data = this.dataSourceRepsWithoutPlans.filteredData.map(x => {
          var keys = this.columnNamesAlt.map(y => y.id);
          var values = keys.map(y => {return x[y]});
          return values;
        });
        break;

      case "List of Plans":
        headers = this.columnNamesPlans.map(x => x.value);
        data = this.dataSourcePlans.filteredData.map(x => {
          var keys = this.columnNamesPlans.map(y => y.id);
          var values = keys.map(y => {return x[y]});
          return values;
        });
        break;
    }

    // export
    this.exportService.excel(headers, data, tabName);
  }
}

export interface Element {
  Contact_Id: string;
  Contact_Name: string,
  Paycom_Employee_Number:string,
  Plan_Name: string,
  Plan_Header_Id: string,
  Sales_Office: string,
  Sales_Division: string,
  Title: string,
  Employee_Start_Date: string,
  Contact_Plan_Start_Date: string,
  Contact_Plan_End_Date: string,
  Contact_Phone: string,
  Contact_Email: string
}

export interface ElementAlt {
  Plan_Header_Id: string,
  Plan_Name: string
}
export interface ElementWithoutPlans {
  Contact_Id: string,
  Contact_Name: string,
  Paycom_Employee_Number:string,
  Sales_Office: string,
  Sales_Division: string,
  Start_Date: string,
  End_Date: string,
  Contact_Phone: string,
  Contact_Email: string
}
