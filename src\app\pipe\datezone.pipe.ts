import { DatePipe } from '@angular/common';
import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'datezone'
})
export class DatezonePipe implements PipeTransform {
  constructor(private datePipe: DatePipe) { }

  transform(timestamp: string, timezone: string = 'America/New_York'): string {
    if (timestamp !== null && timestamp !== undefined && timestamp !== '') {
      const utcDate = new Date(timestamp + "Z");
      const formattedDate = utcDate.toLocaleString('en-US', { timeZone: timezone });
      let finalFormat = this.datePipe.transform(formattedDate, 'MMM d, y');
      return finalFormat;
    }
    else{
      return '';;
    }

  }

}
