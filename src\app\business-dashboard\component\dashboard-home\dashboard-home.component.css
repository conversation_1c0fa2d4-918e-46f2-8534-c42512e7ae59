.info-icon {
    font-size: 50px;
    line-height: 100%;
    opacity: .6;
}
.text-uppercase {
    text-transform: uppercase !important;
}
.info-card-black{
    color: #000;
    font-size: 18px;
    min-height: 147px;
    
}
#chart-width {
    width: 100%;
    max-width: 100%; /* Optionally set a max-width */
    height: auto;    /* Automatically adjust height */
}

/* CSS */
.button-dashboard {
  background-color: #fff;
  border: 1px solid #d5d9d9;
  border-radius: 8px;
  box-shadow: rgba(213, 217, 217, .5) 0 2px 5px 0;
  box-sizing: border-box;
  color: #0f1111;
  cursor: pointer;
  display: inline-block;
  font-family: "Amazon Ember",sans-serif;
  font-size: 13px;
  line-height: 29px;
  padding: 0 10px 0 11px;
  position: relative;
  text-align: center;
  text-decoration: none;
  user-select: none;
  -webkit-user-select: none;
  touch-action: manipulation;
  vertical-align: middle;
  width: 100px;
  height:35px;
}
.button-dashboard:hover {
  background-color: #f7fafa;
}
.button-dashboard:focus {
  border: 2px solid #008296;
  box-shadow: rgba(213, 217, 217, .5) 0 2px 5px 0;
  outline: 0;
}
.button-dashboard-custom {
  border: 2px solid #008296;
  box-shadow: rgba(213, 217, 217, .5) 0 2px 5px 0;
  outline: 0;
}
.width-35{
  width: 35px !important;
  padding: 0px;
}
.width-200{
  width: 200px !important;
}
.cursor-default{
  cursor: default!important;
}