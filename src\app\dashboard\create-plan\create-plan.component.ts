import { Component, OnInit } from '@angular/core';
import { ApiService } from '../../services/api.service';
import { ToastrService } from 'ngx-toastr';
import { Router } from "@angular/router";
declare var $: any;
@Component({
  selector: 'app-create-plan',
  templateUrl: './create-plan.component.html',
  styleUrls: ['./create-plan.component.css']
})
export class CreatePlanComponent implements OnInit {
  basicPlan: any[] = [];
  savedPlan = [];
  dummyPlan = [];
  disableSaveFlag: boolean = true;
  // AddPlan:any[] = [{"parent":"Base Pay","child":[]},{"parent":"Bonus","child":[]},{"parent":"Bonus Incentives","child":[]},
  // {"parent":"Rate Incentives","child":[]},{"parent":"Payment Books","child":[]},{"parent":"Base Pay Structure","child":[]}];
  rules: any[] = [];
  item: string = "no";
  requiredIndex: number = 100;
  removedIndex: number = 100;
  removedChildIndex: number = 100;
  child: boolean = false;
  childIndex: number = 100;
  planName: string;
  grandChild1: boolean = false;
  grandChild2: boolean = false;
  grandChild3: boolean = false;
  grandChildIndex: number = 100;
  grandChildItem: string = "no";
  grandChildrequiredIndex: number = 100;
  grandChildRemovedIndex: number = 100;
  removedGrandChildIndex: number = 100;
  grandChildRemove: boolean = false;
  removegrandChild1: boolean = false;
  removegrandChild2: boolean = false;
  removegrandChild3: boolean = false;
  rulesData: any[] = [];
  clone: boolean = false;
  planId = JSON.parse(localStorage.getItem("planId"));
  highlighted: string = "";
  showChildren: {} = {};

  //test
  testObject: { parent: string, child: string[] }[] = [{ parent: "parent one", child: ["child one", "child two", "child three"] },
  { parent: "parent two", child: ["child four", "child five", "child six"] }, { parent: "parent three", child: ["child seven", "child eight", "child nine"] }];
  //end test

  constructor(public apiService: ApiService, private toastMsg: ToastrService, private router: Router) {
    // console.log(this.planId);
    if (localStorage.getItem("clone") == "true") {
      this.clone = true;
    } else {
      this.clone = false;
    }
    this.getRuleTypes();
    this.validate();
  }

  clear() {
    // localStorage.setItem("clone","false");
    this.basicPlan = [];
    this.savedPlan = [];
    this.getRuleTypes();
  }

  getRuleTypes() {
    this.apiService.get('getdata/GetRuleType')
      .subscribe(data => {
        if (data.statusCode === "201" && data.result) {
          data.result.forEach((row: any) => {
            if (["Employee Incentive", "Base Pay Structure", "Payment Book"].includes(row.ruleCd)) return;
            this.basicPlan.push({ "parent": row.ruleCd, "child": [], "planLimitation": row.planLimitation });
            this.savedPlan.push({ "parent": row.ruleCd, "child": [], "planLimitation": row.planLimitation });
            this.dummyPlan.push({ "parent": row.ruleCd, "child": [], "planLimitation": row.planLimitation });
          });
          this.getRules();
        }
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Error!')
      });
  }

  getRules() {
    this.apiService.get('getdata/GetRules')
      .subscribe(data => {
        var tempThis = this;
        if (data.statusCode === "201" && data.result) {
          this.rulesData = data.result;
          data.result.forEach((row: any) => {
            if (row.children == null) {
              tempThis.rules = tempThis.rules.concat(row.data);
              let ruleArray = this.basicPlan.filter(item => item.parent == row.ruleType);
              if (ruleArray && ruleArray.length > 0) {
                ruleArray[0].child = [];
                row.data.forEach((row1: any) => {
                  if (row1.ruleName)
                    ruleArray[0].child.push(row1.ruleName);
                });
              }
            } else {
              tempThis.rules = tempThis.rules.concat(row.data);
              let ruleArray = this.basicPlan.filter(item => item.parent == row.ruleType);
              if (ruleArray && ruleArray.length > 0) {
                ruleArray[0].child = [];
                row.children.forEach((row2: any, rowIndex: any) => {
                  ruleArray[0].child.push(JSON.parse(JSON.stringify(row2.ruleType)));
                  tempThis.rules = tempThis.rules.concat(row2);
                });
              }
            }
          });
        }
        if (localStorage.getItem("clone") == "true") {
          this.assignPlan();
        } else {
        }
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Error!')
      });
  }

  assignPlan() {
    let planId = JSON.parse(localStorage.getItem("planId"));
    this.apiService.get('Plans/' + planId)
      .subscribe(data => {
        if (data.statusCode === "200" || data.statusCode === "201") {
          this.planName = data.result.planName;
          data.result.planDetails.forEach((row: any) => {
            let ruletype: any[] = [];
            for (let i = 0; i < row.rules.length; i++) {
              ruletype = this.rulesData.filter(item1 => item1.ruleType == row.ruleTypeName);
              if (ruletype) {
                if (ruletype[0]) {
                  let rule;
                  let child = false;
                  if (ruletype[0].children != null) {
                    rule = ruletype[0].children.filter(rule => rule.ruleTypeId == row.rules[i].ruleId);
                    child = true;
                  } else {
                    rule = ruletype[0].data.filter(rule => rule.ruleId == row.rules[i].ruleId);
                    child = false;
                  }
                  this.savedPlan.forEach((row1: any) => {
                    if (rule[0]) {
                      if (row1.parent == row.ruleTypeName) {
                        if (child == false) {
                          row1.child.push(rule[0].ruleName);
                        } else {
                          row1.child.push(rule[0].ruleType);
                        }
                      }
                    }
                  });
                }
              }
            }
          });
          this.savedPlan.forEach((row4: any) => {
            row4.child.forEach((row5: any) => {
              let rowArray = this.basicPlan.filter(item => item.parent == row4.parent);
              if (rowArray && rowArray.length > 0) {
                if (row5 != "") {
                  let childItem = rowArray[0].child.filter(item => item == row5);
                  let index = rowArray[0].child.indexOf(childItem[0]);
                  rowArray[0].child.splice(index, 1);
                }
              }
            });
          });
          this.validate();
          this.toastMsg.success("Plan fetched successfully", '!');
        } else {
          this.toastMsg.error("Server", 'Error!')
        }
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Error!')
      });

  }

  ngOnInit() {
    if (!this.apiService.checkPermission('CreatePlan')) {
      // this.router.navigate(['/ui/dashboard'])
      this.apiService.goBack();
      this.toastMsg.error("Insufficient Permissions. Please make sure your account can access this information.", "Permissions");
    }
    $.fn.extend({
      treed: function (o) {

        var openedClass = 'fa-minus-circle';
        var closedClass = 'fa-plus-circle ';

        if (typeof o != 'undefined') {
          if (typeof o.openedClass != 'undefined') {
            openedClass = o.openedClass;
          }
          if (typeof o.closedClass != 'undefined') {
            closedClass = o.closedClass;
          }
        };

        //initialize each of the top levels
        var tree = $(this);
        tree.addClass("treeview");
        $('#tree1').find('li').has("ul").each(function () {
          var branch = $(this); //li with children ul
          branch.prepend("<i class='indicator fa " + closedClass + "'></i>");
          branch.addClass('branch');
          branch.on('click', function (e) {
            if (this == e.target) {
              var icon = $(this).children('i:first');
              icon.toggleClass(openedClass + " " + closedClass);
              $(this).children().children().toggle();
            }
          })
          branch.children().children().toggle();
        });
        //fire event from the dynamically added icon
        tree.find('.branch .indicator').each(function () {
          $(this).on('click', function () {
            $(this).closest('li').click();
          });
        });
        //fire event to open branch if the li contains an anchor instead of text
        tree.find('.branch>a').each(function () {
          $(this).on('click', function (e) {
            $(this).closest('li').click();
            e.preventDefault();
          });
        });
        //fire event to open branch if the li contains a button instead of text
        tree.find('.branch>button').each(function () {
          $(this).on('click', function (e) {
            $(this).closest('li').click();
            e.preventDefault();
          });
        });
      }
    });

    //Initialization of treeviews
    $('#tree1').treed();
    $('#tree2').treed();
  }

  addChild(item: any, index: any, childIndex: any) {
    this.highlighted = item;
    $('*').removeClass('highlightBackground');
    $('#tree1 #' + index).find(".child" + childIndex).addClass('highlightBackground');
    this.item = item;
    this.requiredIndex = index;
    this.childIndex = childIndex;
    this.child = true;
  }


  removeChild(item: any, index: any, childIndex: any) {
    this.highlighted = item;
    $('*').removeClass('highlightBackground');
    $('#tree2 #' + index).find(".child" + childIndex).addClass('highlightBackground');
    this.removedIndex = index;
    this.removedChildIndex = childIndex;
    this.grandChildRemove = false;;
  }


  add() {
    this.highlighted = "";
    $('*').removeClass('highlightBackground');
    if (this.child == true) {
      if (this.basicPlan[this.requiredIndex].planLimitation == 1 && this.savedPlan[this.requiredIndex].child.length >= 1) {
        this.toastMsg.error("Base Pay is already added. Only one Base Pay is allowed.", "Only One Base Pay");
      } else {
        if (this.child == true) {
          if (this.child == true && this.item != "no" && this.requiredIndex != 100) {
            this.dummyPlan[this.requiredIndex].child = [];
            this.dummyPlan[this.requiredIndex].child.push(this.item);
            this.savedPlan[this.requiredIndex].child.push(JSON.parse(JSON.stringify(this.dummyPlan[this.requiredIndex].child[0])));
            this.item = "no";
          }
          if (this.childIndex != 100) {
            this.basicPlan[this.requiredIndex].child.splice(this.childIndex, 1);
            this.childIndex = 100;
          }
        }
      }
      this.validate();
    }
  }



  Remove() {
    this.highlighted = "";
    $('*').removeClass('highlightBackground');
    if (this.grandChildRemove == false) {
      if (this.removedChildIndex != 100 && this.removedIndex != 100) {
        this.basicPlan[this.removedIndex].child.push(this.savedPlan[this.removedIndex].child[this.removedChildIndex]);
        // this.savedPlan[this.removedIndex].child=[""];
        this.savedPlan[this.removedIndex].child.splice(this.removedChildIndex, 1);
      }
    }
    this.validate();
  }

  validate() {
    let array = this.savedPlan.filter(item => item.child.length > 0);
    if (array && array.length > 0) {
      this.disableSaveFlag = false;
    } else {
      this.disableSaveFlag = true;
    }
  }

  checkCanSave() {
    return this.savedPlan.filter(item => item.child.length > 0).length > 0;
  }

  savePlan() {
    let planDetails: any[] = [];
    let uniqueRuleIds = [];
    this.savedPlan.forEach((row: any) => {
      if (row.child.length > 0) {
        row.child.forEach((row1: any) => {
          if (row1) {
            let rules: any = {} = this.rules.filter(item => item.ruleName == row1);
            if (rules != null && rules.length > 0 && !uniqueRuleIds.includes(rules[0].ruleId)) {
              planDetails.push({ "ruleId": parseInt(rules[0].ruleId) });
              uniqueRuleIds.push(rules[0].ruleId)
            }
            else {
              let childRule = this.rules.filter(item => item.ruleType == row1);
              if (childRule != null && childRule.length > 0) {
                planDetails.push({ "ruleTypeId": parseInt(childRule[0].ruleTypeId) });
              }
            }
          }
        })
      }
    });
    if (this.planName !== null && this.planName !== undefined) {
      const createPlanPayLoad = {
        planId: 0,
        planName: this.planName,
        description: "plan",
        startDate: new Date(),
        endDate: new Date(),
        planDetails: planDetails
      }
      this.apiService.post('Plans', createPlanPayLoad)
        .subscribe(data => {
          if (data.statusCode === "200" || data.statusCode === "201") {
            localStorage.setItem("clone", "false");
            this.getRules();
            this.savedPlan = [{ "parent": "Base Pay", "child": [""] }, { "parent": "Bonus", "child": [""] }, { "parent": "Bonus Incentives", "child": [""] },
            { "parent": "Rate Incentives", "child": [""] }, { "parent": "Payment Books", "child": [""] }, { "parent": "Base Pay Structure", "child": [""] }];
            this.toastMsg.success("Plan has been created successfully", 'Success!')
            this.router.navigate(["ui/commissions/viewPlan", data.result.planId]);
          } else {
            this.toastMsg.error("Server", 'Error!')
          }
        }, (err: any) => {
          this.toastMsg.error(err.message, 'Error!')
        });
    } else {
      this.toastMsg.error("Please enter in a name for your plan", "Plan Name Not Set");
    }
  }

  ngOnDestroy() {
    localStorage.setItem("clone", "false");
    localStorage.removeItem("search");
    localStorage.removeItem("type");
    localStorage.removeItem("searchedParam");
  }

  onGoBack() {
    if (confirm("Your unsaved progress will be deleted, do you wish to continue?")) {
      if (localStorage.getItem("clone") == "true") {
        this.router.navigate(['/ui/commissions/viewPlan', this.planId]);
      } else {
        this.apiService.goBack();
      }
    }
  }

  toggleContainer(parentId) {
    if (!this.showChildren[parentId]) this.showChildren[parentId] = false;

    this.showChildren[parentId] = !this.showChildren[parentId];
  }
}
