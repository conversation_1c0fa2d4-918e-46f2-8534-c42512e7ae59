import { Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators, UntypedFormControl, AbstractControl } from "@angular/forms";
import { ApiService } from "../../../services/api.service";
import { ToastrService } from 'ngx-toastr';
import { maxPermitDeductionDate, rateNotExisting } from '../../../shared/validators';
import { groupBy } from '../../../shared/group-by';
import { getControlName } from '../../../shared/get-control-name';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { DataSource } from '@angular/cdk/table';
import { MatLegacyPaginator } from '@angular/material/legacy-paginator';
import { TableFilterPipe } from '../../../pipe/table-filter.pipe';
import { DatePipe, PercentPipe, CurrencyPipe } from '@angular/common';
// import { StringMap } from '@angular/compiler/src/compiler_facade_interface';
import { MatLegacyDialog } from '@angular/material/legacy-dialog';
import { PPABonusFlatRateMaintenanceDialogComponent } from '../ppa-bonus-flat-rate-maintenance-dialog/ppa-bonus-flat-rate-maintenance-dialog.component';
import { HttpClient } from '@angular/common/http';

@Component({
  selector: 'app-ppa-bonus-flat-rate-maintenance',
  templateUrl: './ppa-bonus-flat-rate-maintenance.component.html',
  styleUrls: ['./ppa-bonus-flat-rate-maintenance.component.css']
})
export class PPABonusFlatRateMaintenanceComponent implements OnInit {
  allPPABonusFlatRates: any;
  activePPABonusFlatRates: any;
  PPABonusFlatRateGroup: any;
  PPABonusFlatRateSelectedGroup: any;
  dropdowns: any;
  PPABonusFlatRateForm: UntypedFormGroup;
  ppaPpkwGroup: AbstractControl[][];
  addInd: boolean = false;
  isReloading :boolean = false ;
  stateCodeDefault: number = 1;
  financePartnerDefault: number = 1;
  purchaseMethodDefault: number = 1;
  utilityCompanyDefault: number = 1;
  p: number = 1;
  tableArr: Element[] = [];
  PPABonusFlatRate; 
  isPPABonusFlatRateRateSelected : boolean = false;
  searchText: string = "";
  public date: Date;
  originalDataSource;
  dataSource;
  displayedColumns = [];
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatLegacyPaginator, { static: true }) paginator: MatLegacyPaginator;

  columnNames = [{
    id: "stateCode",
    value: "State Code"

  }, {
    id: "utilityCompany",
    value: "Utility Company"
  },
  {
    id: "financePartner",
    value: "Finance Partner"
  },
  {
    id: "purchaseMethod",
    value: "Purchase Method"
  },
  {
    id: "effectiveStartDate",
    value: "Effective Start Date"
  },
  {
    id: "effectiveEndDate",
    value: "Effective End Date"
  }];

  constructor(public apiService: ApiService, private toastMsg: ToastrService, private formBuilder: UntypedFormBuilder, private pipe: TableFilterPipe,
    private datePipe: DatePipe, private percentPipe: PercentPipe, private currencyPipe: CurrencyPipe, private dialog: MatLegacyDialog,private http:HttpClient) {

  }

  ngOnInit() {
    if (!this.apiService.checkPermission('ViewRateTables')) {
      // this.router.navigate(['/ui/dashboard'])
      this.apiService.goBack();
      this.toastMsg.error("Insufficient Permissions. Please make sure your account can access this information.", "Permissions");
    }
    this.PPABonusFlatRateForm = this.formBuilder.group({
      stateCode: [this.stateCodeDefault, [Validators.required]],
      financePartner: [this.financePartnerDefault, [Validators.required]],
      purchaseMethod: [this.purchaseMethodDefault, [Validators.required]],
      utilityCompany: [this.utilityCompanyDefault, [Validators.required]],
      effectiveStartDate: ['', [Validators.required]],
      ppaRate: [0, []],
      pricePerKw: [0, [Validators.required]],
    });

    this.getDropdowns();

    this.ppaPpkwGroup = [[this.PPABonusFlatRateForm.controls.ppaRate, this.PPABonusFlatRateForm.controls.pricePerKw]];

    this.onChanges();
  }

  onChanges() {
    this.PPABonusFlatRateForm.valueChanges.subscribe(val => {
      // console.log(this.PPABonusFlatRateForm.errors);
    });
  }

  clearDate(date: HTMLInputElement) {
    date.value = "";
    this.date = null;
    this.PPABonusFlatRateForm.controls.effectiveStartDate.setValue('');
    event.stopPropagation();
  }

  onSubmit() {
    if (!this.PPABonusFlatRateForm.invalid) {
      var groupArr = [];
      this.ppaPpkwGroup.forEach(x => {
        groupArr.push(
          {
            "PpaRate": x[0].value,
            "PricePerKw": x[1].value
          }
        );
      });

      // console.log(groupArr);

      var body = {
        stateCodeId: this.PPABonusFlatRateForm.controls.stateCode.value,
        financePartnerId: this.PPABonusFlatRateForm.controls.financePartner.value,
        purchaseMethodId: this.PPABonusFlatRateForm.controls.purchaseMethod.value,
        utilityCompanyId: this.PPABonusFlatRateForm.controls.utilityCompany.value,
        effectiveStartDate: this.PPABonusFlatRateForm.controls.effectiveStartDate.value,
        ppaPpwGroups: groupArr
      }
      // console.log("Body  136 => "+JSON.stringify(body));

      this.apiService.post('PPABonusFlatRateMaintenance', body)
      // this.http.post('https://onepay.citrusinformatics.com/api/PPABonusFlatRateMaintenance',body)
        .subscribe(data => {
          this.toastMsg.success('Flat PPA Bonus Successfully Added');
          this.isReloading = true ;
          this.getAllPPABonusFlatRates();
          this.getActivePPABonusFlatRates();
          this.addInd = !this.addInd;
        }, (err: any) => {
          if (err?.err?.message)
            this.toastMsg.error(err.message, "Server Error!");
          if (typeof err === "string") {
            let isJsonObject = false;            
            let parsedErr: any;
            try {
              parsedErr = JSON.parse(err);
              isJsonObject =parsedErr && typeof parsedErr === "object" &&!Array.isArray(parsedErr);
            } catch (e) {
              isJsonObject = false;
            }
            if (isJsonObject) {
              this.toastMsg.error(parsedErr.result, "Error!");
            } else this.toastMsg.error(err, "Error!");
          }
          if (err?.result) {
            this.toastMsg.error(err.result, "Error!");
          }
        });
    }
  }

  getAllPPABonusFlatRates() {
    this.apiService.get('PPABonusFlatRateMaintenance/retrieveall')    
    // this.http.get('https://onepay.citrusinformatics.com/api/PPABonusFlatRateMaintenance/retrieveall')
      .subscribe(data => {
        this.allPPABonusFlatRates = data;
        //console.log(this.allPPABonusFlatRates);
        if(!this.isReloading)
        {
           if (this.PPABonusFlatRateForm.controls.ppaRate) this.PPABonusFlatRateForm.controls.ppaRate.setValidators([Validators.required, rateNotExisting(this.allPPABonusFlatRates)]);
        } else {
          this.PPABonusFlatRateForm.clearValidators();
        }
        for(let i:number = 0; i <= this.allPPABonusFlatRates.length - 1; i++) {
          this.allPPABonusFlatRates[i].effectiveStartDate = this.datePipe.transform(this.allPPABonusFlatRates[i].effectiveStartDate);
          this.allPPABonusFlatRates[i].effectiveEndDate = this.datePipe.transform(this.allPPABonusFlatRates[i].effectiveEndDate);
        }
        if (this.PPABonusFlatRateGroup) {
          this.getPPABonusFlatRateGroup(this.PPABonusFlatRateGroup[0][0]);
          this.PPABonusFlatRateSelectedGroup = this.PPABonusFlatRateGroup;
        }
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }

  getActivePPABonusFlatRates() {
    this.apiService.get('PPABonusFlatRateMaintenance/retrieveactive')  
    // this.http.get('https://onepay.citrusinformatics.com/api/PPABonusFlatRateMaintenance/retrieveactive')
      .subscribe(data => {
        // console.log('ppa', data);
        this.activePPABonusFlatRates = data;
        this.displayedColumns = this.columnNames.map(x => x.id);
        this.createTable();
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }

  getDropdowns() {
    this.apiService.get('PPABonusFlatRateMaintenance/dropdowns') 
    // this.http.get('https://onepay.citrusinformatics.com/api/PPABonusFlatRateMaintenance/dropdowns')
      .subscribe(data => {
        this.dropdowns = data;
        this.getAllPPABonusFlatRates();
        this.getActivePPABonusFlatRates();
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }

  getPPABonusFlatRateGroup(PPABonusFlatRate: any) {
    var PPABonusFlatRates = this.allPPABonusFlatRates.filter(x => x.financePartnerId === PPABonusFlatRate.financePartnerId && x.purchaseMethodId === PPABonusFlatRate.purchaseMethodId && x.stateCodeId === PPABonusFlatRate.stateCodeId && x.utilityCompanyId === PPABonusFlatRate.utilityCompanyId);
    PPABonusFlatRates = Object.values(groupBy(PPABonusFlatRates, 'effectiveStartDate'));
    // console.log(PPABonusFlatRates);
    this.PPABonusFlatRateGroup = PPABonusFlatRates;
    this.PPABonusFlatRateSelectedGroup = null;
  }

  get ppaRate() { return this.PPABonusFlatRateForm.get('ppaRate'); }

  get pricePerKw() { return this.PPABonusFlatRateForm.get('pricePerKw'); }

  rowClick(ev: any) {    
    var PPABonusFlatRate = ev;
    PPABonusFlatRate = this.allPPABonusFlatRates.filter(x => x.financePartnerId === PPABonusFlatRate.financePartnerId && x.purchaseMethodId === PPABonusFlatRate.purchaseMethodId && x.stateCodeId === PPABonusFlatRate.stateCodeId && x.utilityCompanyId === PPABonusFlatRate.utilityCompanyId);
    this.PPABonusFlatRate = PPABonusFlatRate;
    this.isPPABonusFlatRateRateSelected = true;
    // console.log("PPABonusFlatRate rowClick 209= >" + JSON.stringify(this.PPABonusFlatRate));
    // console.log("this.PPABonusFlatRate  = >" + JSON.stringify(this.PPABonusFlatRate));
    PPABonusFlatRate = Object.values(groupBy(PPABonusFlatRate, 'effectiveStartDate'));
    const dialogRef = this.dialog.open(PPABonusFlatRateMaintenanceDialogComponent, {
      width: '80%', data: { PPABonusFlatRate }
    });
    this.PPABonusFlatRateForm.controls['stateCode'].setValue(this.PPABonusFlatRate[0].stateCodeId);
    this.PPABonusFlatRateForm.controls['financePartner'].setValue(this.PPABonusFlatRate[0].financePartnerId);
    this.PPABonusFlatRateForm.controls['purchaseMethod'].setValue(this.PPABonusFlatRate[0].purchaseMethodId);
    this.PPABonusFlatRateForm.controls['utilityCompany'].setValue(this.PPABonusFlatRate[0].utilityCompanyId);
   
    dialogRef.afterClosed().subscribe(result => {
      // console.log(result);
    });
  }

  
Add(){
  this.addInd = !this.addInd;
  this.isReloading = true ;
  let PPABonusFlatRate = this.activePPABonusFlatRates[0];
  PPABonusFlatRate = this.allPPABonusFlatRates.filter(x => x.financePartnerId === PPABonusFlatRate.financePartnerId && x.purchaseMethodId === PPABonusFlatRate.purchaseMethodId && x.stateCodeId === PPABonusFlatRate.stateCodeId && x.utilityCompanyId === PPABonusFlatRate.utilityCompanyId);
    this.PPABonusFlatRate = PPABonusFlatRate;
  if(!this.isReloading)
  {
     if (this.PPABonusFlatRateForm.controls.ppaRate) this.PPABonusFlatRateForm.controls.ppaRate.setValidators([Validators.required, rateNotExisting(this.allPPABonusFlatRates)]);
  } else {
    this.PPABonusFlatRateForm.clearValidators();
  }
  // console.log("Add this.PPABonusFlatRate => 225   " +JSON.stringify(this.PPABonusFlatRate));
  this.PPABonusFlatRateForm.controls['financePartner'].setValue(this.PPABonusFlatRate[0].stateCodeId);
  this.PPABonusFlatRateForm.controls['financePartner'].setValue(this.PPABonusFlatRate[0].financePartnerId);
  this.PPABonusFlatRateForm.controls['purchaseMethod'].setValue(this.PPABonusFlatRate[0].purchaseMethodId);
  this.PPABonusFlatRateForm.controls['utilityCompany'].setValue(this.PPABonusFlatRate[0].utilityCompanyId);
}



  groupClick(group: any) {
    this.PPABonusFlatRateSelectedGroup = group;
  }

  addFormRow() {
    this.PPABonusFlatRateForm.addControl(`ppaRate${this.ppaPpkwGroup.length}`, new UntypedFormControl(0, []));
    this.PPABonusFlatRateForm.addControl(`pricePerKw${this.ppaPpkwGroup.length}`, new UntypedFormControl(0, []));
    var c1 = this.PPABonusFlatRateForm.get(`ppaRate${this.ppaPpkwGroup.length}`);
    var c2 = this.PPABonusFlatRateForm.get(`pricePerKw${this.ppaPpkwGroup.length}`);
    c1.setValidators([Validators.required, rateNotExisting(this.allPPABonusFlatRates)]);
    c2.setValidators([Validators.required]);
    this.ppaPpkwGroup.push([c1, c2]);
  }

  removeFormRow(index: number) {
    if (this.ppaPpkwGroup.length == 1) return;

    this.ppaPpkwGroup[index].slice(0).forEach(x => {
      this.PPABonusFlatRateForm.removeControl(getControlName(x));
    });

    this.ppaPpkwGroup.splice(index, 1);
  }

  getControlName(control: AbstractControl) {
    return getControlName(control);
  }

  createTable() {
    let tableArr: Element[] = [];
    for (let i: number = 0; i <= this.activePPABonusFlatRates.length - 1; i++) {
      let currentRow = this.activePPABonusFlatRates[i];
      if(i==0)
      {
        this.tableArr[0] =this.activePPABonusFlatRates[0];
        // console.log(" this.tableArr = 264 > "+ JSON.stringify(this.tableArr));
      }
      tableArr.push({
        effectiveEndDate: this.datePipe.transform(currentRow.effectiveEndDate), effectiveStartDate: this.datePipe.transform(currentRow.effectiveStartDate),
        financePartner: currentRow.financePartner, financePartnerId: currentRow.financePartnerId, ppaRate: this.currencyPipe.transform(currentRow.ppaRate),
        pricePerKw: this.currencyPipe.transform(currentRow.pricePerKw), purchaseMethod: currentRow.purchaseMethod, purchaseMethodId: currentRow.purchaseMethodId,
        purchaseMethodPpaBonusMappingId: currentRow.purchaseMethodPpaBonusMappingId, stateCode: currentRow.stateCode, stateCodeId: currentRow.stateCodeId,
        utilityCompany: currentRow.utilityCompany, utilityCompanyId: currentRow.utilityCompanyId, ppwBonusPricePerKw: currentRow.ppwBonusPricePerKw, ppwBonusMetric: currentRow.ppwBonusMetric
      });
    }
    this.dataSource = new MatTableDataSource(tableArr);
    this.originalDataSource = tableArr;
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }

  searchForItem(): void {
    let filteredResults: Element[] = [];
    if (this.searchText == '') {
      this.dataSource = new MatTableDataSource(this.originalDataSource);
      this.dataSource.sort = this.sort;
      this.dataSource.paginator = this.paginator;
    } else {
      // filteredResults = this.originalDataSource.filter(option => option.stateCode.toLowerCase().includes(this.searchText));
      filteredResults = this.pipe.transform(this.originalDataSource, this.searchText);
      this.dataSource = new MatTableDataSource(filteredResults);
      this.dataSource.sort = this.sort;
      this.dataSource.paginator = this.paginator;
    }
  }
}

export interface Element {
  effectiveEndDate: string,
  effectiveStartDate: string,
  financePartner: string,
  financePartnerId: number,
  ppaRate: string,
  pricePerKw: string,
  purchaseMethod: string,
  purchaseMethodId: number,
  purchaseMethodPpaBonusMappingId: number,
  stateCode: string,
  stateCodeId: number,
  utilityCompany: string,
  utilityCompanyId: number,
  ppwBonusPricePerKw: number,
  ppwBonusMetric: number
}
