<div class="page-title col-md-12 ">
  <h1>Dashboard</h1>

</div>

<div class="content">


  <div class="row">

    <div class="col-md-4">
      <div class="card bg-purple-dark info-card hover" (click)='moveToSelectedTab("Reps with Direct Plans")'>
        <div class="card-body">
          <div class="row">
            <div class="col-8 ">
              <div class="h4 mt-0">{{infoBox.blueInfo.name}}</div>
              <div class="text-uppercase">
                <h1>{{infoBox.blueInfo.number}}</h1>
              </div>
            </div>
            <div class="col-4 text-right info-icon"><i class="fas fa-user-tie"></i></div>

          </div>

        </div>
      </div>

    </div>
    <div class="col-md-4">
      <div class="card bg-green-dark info-card hover" (click)='moveToSelectedTab("Reps with Traditional Plans")'>
        <div class="card-body">
          <div class="row">
            <div class="col-8 ">
              <div class="h4 mt-0">{{infoBox.greenInfo.name}}</div>
              <div class="text-uppercase">
                <h1>{{infoBox.greenInfo.number}}</h1>
              </div>
            </div>
            <div class="col-4 text-right info-icon"><i class="fas fa-user"></i></div>

          </div>

        </div>
      </div>

    </div>
    <div class="col-md-4">
      <div class="card bg-gray-dark info-card hover" (click)='moveToSelectedTab("Reps with Outreach Plans")'>
        <div class="card-body">
          <div class="row">
            <div class="col-8 ">
              <div class="h4 mt-0">{{infoBox.greyInfo.name}}</div>
              <div class="text-uppercase">
                <h1>{{infoBox.greyInfo.number}}</h1>
              </div>
            </div>
            <div class="col-4 text-right info-icon"><i class="fas fa-user-alt"></i></div>

          </div>

        </div>
      </div>

    </div>
  </div>

  <div class="row">
    <div class="col-md-12 mt-2">
      <div class="card" style='min-height:400px'>
        <mat-tab-group animationDuration="2000ms">
          <mat-tab label="Reps with Direct Plans">
            <div class="w-100 text-right">
              <button class="btn btn-primary" (click)="exportTable('Reps with Direct Plans')"><i
                  class="fas fa-download"></i> Download</button>
            </div>
            <mat-table #table1 [dataSource]="dataSourceDirect" matSort>
              <ng-container matColumnDef="{{column.id}}" *ngFor="let column of columnNames">
                <mat-header-cell *matHeaderCellDef mat-sort-header class="table-header"> {{column.value}}
                </mat-header-cell>
                <mat-cell [attr.data-td-head]="column.value" *matCellDef="let element">
                  <ng-container *ngIf="column.value == 'Contact Name'; else noLinkProcessed">
                    <a [routerLink]="['/ui/commissions/salesrep', element.Contact_Id]">{{element[column.id]}}</a>
                  </ng-container>
                  <ng-template #noLinkProcessed>
                    <ng-container *ngIf="column.value == 'Plan Name'; else noLinkProcessed2">
                      <a [routerLink]="['/ui/commissions/viewPlan', element.Plan_Header_Id]">{{element[column.id]}}</a>
                    </ng-container>
                  </ng-template>
                  <ng-template #noLinkProcessed2>
                    {{element[column.id]}}
                  </ng-template>
                </mat-cell>
              </ng-container>
              <mat-header-row *matHeaderRowDef="displayedColumnsDirect"></mat-header-row>
              <mat-row *matRowDef="let row; columns: displayedColumnsDirect;" class="pointer table-content"
                (click)="rowClick(row)"></mat-row>
            </mat-table>
            <mat-paginator #paginator1 [pageSizeOptions]="[5, 10, 20, 50]" showFirstLastButtons></mat-paginator>
          </mat-tab>
          <mat-tab label="Reps with Traditional Plans">
            <div class="w-100 text-right">
              <button class="btn btn-primary" (click)="exportTable('Reps with Traditional Plans')"><i
                  class="fas fa-download"></i> Download</button>
            </div>
            <mat-table #table2 [dataSource]="dataSource" matSort #sort2="matSort">
              <ng-container matColumnDef="{{column.id}}" *ngFor="let column of columnNames">
                <mat-header-cell *matHeaderCellDef mat-sort-header class="table-header"> {{column.value}}
                </mat-header-cell>
                <mat-cell [attr.data-td-head]="column.value" *matCellDef="let element">
                  <ng-container *ngIf="column.value == 'Contact Name'; else noLinkPending">
                    <a [routerLink]="['/ui/commissions/salesrep', element.Contact_Id]">{{element[column.id]}}</a>
                  </ng-container>
                  <ng-template #noLinkPending>
                    <ng-container *ngIf="column.value == 'Plan Name'; else noLinkPending2">
                      <a [routerLink]="['/ui/commissions/viewPlan', element.Plan_Header_Id]">{{element[column.id]}}</a>
                    </ng-container>
                  </ng-template>
                  <ng-template #noLinkPending2>
                    {{element[column.id]}}
                  </ng-template>
                </mat-cell>
              </ng-container>
              <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
              <mat-row *matRowDef="let row; columns: displayedColumns;" class="pointer table-content"
                (click)="rowClick(row)"></mat-row>
            </mat-table>
            <mat-paginator #paginator2 [pageSizeOptions]="[5, 10, 20, 50]" showFirstLastButtons></mat-paginator>
          </mat-tab>
          <mat-tab label="Reps with Outreach Plans">
            <div class="w-100 text-right">
              <button class="btn btn-primary" (click)="exportTable('Reps with Outreach Plans')"><i
                  class="fas fa-download"></i> Download</button>
            </div>
            <mat-table #table3 [dataSource]="dataSourceOutreach" matSort #sort3="matSort">
              <ng-container matColumnDef="{{column.id}}" *ngFor="let column of columnNames">
                <mat-header-cell *matHeaderCellDef mat-sort-header class="table-header"> {{column.value}}
                </mat-header-cell>
                <mat-cell [attr.data-td-head]="column.value" *matCellDef="let element">
                  <ng-container *ngIf="column.value == 'Contact Name'; else noLinkReclaims">
                    <a [routerLink]="['/ui/commissions/salesrep', element.Contact_Id]">{{element[column.id]}}</a>
                  </ng-container>
                  <ng-template #noLinkReclaims>
                    <ng-container *ngIf="column.value == 'Plan Name'; else noLinkReclaims2">
                      <a [routerLink]="['/ui/commissions/viewPlan', element.Plan_Header_Id]">{{element[column.id]}}</a>
                    </ng-container>
                  </ng-template>
                  <ng-template #noLinkReclaims2>
                    {{element[column.id]}}
                  </ng-template>
                </mat-cell>
              </ng-container>
              <mat-header-row *matHeaderRowDef="displayedColumnsOutreach"></mat-header-row>
              <mat-row *matRowDef="let row; columns: displayedColumnsOutreach;" class="pointer table-content"
                (click)="rowClick(row)"></mat-row>
            </mat-table>
            <mat-paginator #paginator3 [pageSizeOptions]="[5, 10, 20, 50]" showFirstLastButtons></mat-paginator>
          </mat-tab>
        </mat-tab-group>
      </div>
    </div>
  </div>
</div>