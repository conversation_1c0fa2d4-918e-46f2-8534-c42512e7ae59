
<div class="page-title col-md-12 "><h1 class="">Salesrep Dashboard</h1>
	<div class="breadcrumbs" *ngIf="IsShowBreadcrumbs == true"><a href="#">Home</a>/<span>Sales Rep Dashboard</span>
	</div></div>
 
	<div class="content">
	 
	 
			<div class="row">
				<div class="col-md-6 d-flex">
					<div class="card">
						<div class="card-header-info">
							<h4 class="card-title"><i class="fas fa-user-tie"></i> Employee Information</h4>
						</div>
						<div class="card-body">
							<div class="col-md-12" *ngIf="contactsDetails">
								 <div class="row">
									 <div class="col-md-12">
										 <div class="row">
										<label class="col-sm-5">Name</label>
										<div class="col-sm-7">{{ contactsDetails.contactName }}</div>
									 </div> </div>
									 <div class="col-md-12">
										<div class="row">
										<label class="col-sm-5">Title</label>
										<div class="col-sm-7">{{ contactsDetails.title }}</div>
										</div></div>
										<div class="col-md-12">
											<div class="row">
										<label class="col-sm-5">Sales Office</label>
										<div class="col-sm-7">{{ contactsDetails.salesOffice }}</div>
											</div></div>
											<div class="col-md-12">
												<div class="row">
										<label class="col-sm-5">Division</label>
										<div class="col-sm-7">{{ contactsDetails.salesDivision }}</div>
												</div></div>
									<!-- <tr>
										<td >Advance%</td>
										<td >50 %</td>
									</tr> -->
									<div class="col-md-12">
										<div class="row">
										<label class="col-sm-5">Eligibility End Date</label>
										<div class="col-sm-7">{{ contactsDetails.lastDayOfEmployement | date }}</div>
										</div> </div>

								</div>
							</div>
							<div *ngIf="this.overrides.length > 0"  class="align-button-right override-button"><button class="btn btn-primary" (click)="openDialog()">
								Overrides
							  </button></div>
							<div *ngIf="this.outreachOverrides.length > 0" class="align-button-right override-button"><button class="btn btn-primary" (click)="openOutreachDialog()">
							    Overrides
							</button></div>
						</div>
					</div>
				</div>
				<div class="col-md-6  d-flex">
					<div class="card">
						<div class="card-header-info">
							<h4 class="card-title"><i class="fas fa-search-dollar"></i> Search Commissions</h4>
						</div>
						<div class="card-body">
							<div class="row">
								<div class="col-md">
									<form action="">
										<div class="row">
											<div class="col-md-12">
												<label for="username">Opportunity Name :</label>
												<input type="text" [(ngModel)]="OpportunityName" class="form-control"
													id="usr" name="username">
											</div>
											<div class="col-md-4">
												<!-- <button type="button" (click)="getSalesRepDashBoard('opportunity')"
													class="btn btn-outline-warning">Search</button> -->
											</div>
										</div>
									</form>
								</div>
							</div>
							<br/>
							<div class="row">
								<div class="col-md">
									<div class="row">
										<div class="col-md-6">
											<label for="BasedOn">Search Based On</label>
											<select class="custom-select hover" (change)="onChangeBasedOn($event)">
												<option value="ModifiedDate">Modified date</option>
												<option value="DemoDate">Demo Date</option>
												<option value="InstallDate">Actual Install Date</option>
												<option value="ContractSignedDate">Contract Signed Date</option>
												<option value="PaymentDate">Payment Date</option>
											</select>
										</div>
										<div class="col-md-6">
											<label for="dateRange">Date Range</label>
											<select class="custom-select hover" (change)="onChangeDateRange($event)">
												<option value="CustomRange">Custom Range</option>
												<option value="ThisWeek">This Week</option>
												<option value="LastWeek">Last Week</option>
												<option value="ThisMonth">This Month</option>
												<option value="LastMonth">Last Month</option>
												<option value="ThisYear">This Year</option>
											</select>
										</div>
									</div>
								</div>
							</div>
							<div class="row mt-4" *ngIf="dateRange =='Custom Range'">
								<div class="col-md">
									<form action="">
										<div class="row">
											<div class="col-md-6">
												<div class="form-group">
													<label>Start Date</label>
													<div class="input-group date-picker">
														<input #datepickerFromDate type="date" [(ngModel)]="FromDate" name="FromDate" class="custom-input">
														<span *ngIf="datepickerFromDate.value.length > 0" class="mat-icon cal-reset" (click)="this.FromDate = null; "><i
																class="far fa-calendar-times"></i></span>
														<span *ngIf="datepickerFromDate.value.length <= 0" class="mat-icon cal-open"><i
																class="far fa-calendar-alt"></i></span>
													</div>
												</div>
											</div>
											<div class="col-md-6">
												<div class="form-group">
													<label>End Date</label>
													<div class="input-group date-picker">
														<input #datepickerToDate type="date" [(ngModel)]="ToDate" name="ToDate" class="custom-input">
														<span *ngIf="datepickerToDate.value.length > 0" class="mat-icon cal-reset" (click)="this.ToDate = null; "><i
																class="far fa-calendar-times"></i></span>
														<span *ngIf="datepickerToDate.value.length <= 0" class="mat-icon cal-open"><i
																class="far fa-calendar-alt"></i></span>
													</div>
												</div>
											</div>
											<div class="col-md-4 my-auto">
												<!-- <button type="button" (click)="getSalesRepDashBoard('date')"
													class="btn btn-outline-warning justify-content-center">Search</button> -->
											</div>
										</div>
									</form>
								</div>
							</div>
							<div class="row">
								<div class="col-md">
									<p *ngIf="FromDate == ''" style="color:#B4B6B6; size:10px;">*Date filters based on {{basedOn}} test</p>
									<p *ngIf="FromDate != ''" style="color:#B4B6B6; size:10px;">*Date filters based on {{basedOn}} with From Date: {{FromDate | date}} to {{ToDate | date}} </p>
									<form action="">
										<div class="text-sm-center align-button-right">
											<button type="button" (click)="resetData()"
												class="btn btn-primary "><i class="fas fa-sync"></i> Reset </button>
												<button type="button" (click)="search()"
													class="btn btn-primary"><i class="fas fa-search"></i> Search</button>
										</div>
									</form>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="container-fluid ">
					<div class="row">
						<div class="col">
							<!-- Start payment book -->
							<div class="row">
								<div class="col-md">
									<div class="card">
										<div class="card-header-info">
											<h4 class="card-title"><i class="fas fa-dollar-sign"></i> Payment Book</h4>
										</div>
										<div class="card-body">
											<div class="row">
												<div class="col-md">
													<div>
															<div class="row mt-4">
																<div class="col-md-4">
																	<div class="row">
																		<label class="col-5">Payment Book Type</label>
																		<span class="col-7">{{contactPaymentBook.paymentBookTypeName}}</span>
																	</div>
																</div>
																<div class="col-md-4">
																	<div class="row">
																		<label class="col-5">Weekly Pay</label>
																		<span class="col-7">{{contactPaymentBook.weeklyPay | currency}}</span>
																	</div>
																</div>
																
																<div class="col-md-4">
																	<div class="row">
																		<label class="col-5">Current Balance</label>
																		<span class="col-7">{{getCurrentBalance() | currency}}</span>
																	</div>
																</div>
															</div>
															
											<table mat-table [dataSource]="transactions" matSort class="mt-3 my-table w-100">
												<ng-container matColumnDef="paymentTransactionId">
													<th mat-header-cell *matHeaderCellDef mat-sort-header> View Details</th>
													<td data-td-head="paymentTransactionId" mat-cell *matCellDef="let element"> <a *ngIf="this.showProcessedPaymentsLink == true" class="hover" (click)="getProcessedPayments(element.dateProcessed)"><mat-icon>open_in_new</mat-icon></a>
														<a *ngIf="showProcessedPaymentsLink == false">loading...</a>
													</td>
												</ng-container>
												<ng-container matColumnDef="dateProcessed">
													<th mat-header-cell *matHeaderCellDef mat-sort-header> Date</th>
													<td data-td-head="Date Processed" mat-cell *matCellDef="let element"> {{element.dateProcessed | date}}
													</td>
												</ng-container>
											
												<ng-container matColumnDef="opportunityName">
													<th mat-header-cell *matHeaderCellDef mat-sort-header> Opportunity </th>
													<td data-td-head="Opportunity" mat-cell *matCellDef="let element">
														{{element.opportunityName
														? element.opportunityName : "-"}} </td>
												</ng-container>
											
												<ng-container matColumnDef="commissionTransactionTypeName">
													<th mat-header-cell *matHeaderCellDef mat-sort-header> Transaction Type </th>
													<td data-td-head="Transaction Type" mat-cell *matCellDef="let element">
														{{element.commissionTransactionTypeName}} </td>
												</ng-container>
											
												<ng-container matColumnDef="paymentTypeName">
													<th mat-header-cell *matHeaderCellDef mat-sort-header> Payment Type </th>
													<td data-td-head="Payment Type" mat-cell *matCellDef="let element">
														{{element.paymentTypeName ? element.paymentTypeName : "-"}}
													</td>
												</ng-container>
											
												<ng-container matColumnDef="credit">
													<th mat-header-cell *matHeaderCellDef mat-sort-header> Credit </th>
													<td data-td-head="Credit" mat-cell *matCellDef="let element">
														{{element.debitCredit == "C" ? (element.amount | currency) : "-"}} </td>
												</ng-container>
											
												<ng-container matColumnDef="debit">
													<th mat-header-cell *matHeaderCellDef mat-sort-header> Debit </th>
													<td data-td-head="Debit" mat-cell *matCellDef="let element">
														{{element.debitCredit == "D" ? (element.amount | currency) : "-"}} </td>
												</ng-container>
											
												<tr mat-header-row *matHeaderRowDef="colTransations"></tr>
												<tr mat-row *matRowDef="let row; columns: colTransations;"></tr>
											</table>
											<mat-paginator [pageSizeOptions]="pageSizeOptions" style="margin-top: 2%;">
											</mat-paginator>
													</div>
												</div>
											</div>
											
										</div>
									</div>
								</div>
							</div>
							<!-- End payment book -->						
							<div class="row">
								<div class="col-md">
									<div class="card">
										<div class="card-header-info">
											<h4 class="card-title"><i class="fas fa-dollar-sign"></i> Processed Payments</h4>
										</div>
										<div class="card-body">
											<div class="row">
												<div class="col-md">
													<!-- <h4 style="font-weight: 500;">Payments processed for the Selected
														Date Range, Opportunity, or DL # </h4> -->
													<div class="dashboard-table">
														<table mat-table [dataSource]="ProcessedPayments" matSort
															class="w-100 my-table">
															<ng-container matColumnDef="paymentFor">
																<th mat-header-cell *matHeaderCellDef mat-sort-header>
																	Payment For </th>
																<td data-td-head="Payment For" mat-cell *matCellDef="let element">
																	{{element.paymentFor}} </td>
																<td mat-footer-cell *matFooterCellDef> Total </td>
															</ng-container>

															<ng-container matColumnDef="paymentType">
																<th mat-header-cell *matHeaderCellDef mat-sort-header>
																	Payment Type </th>
																<td data-td-head="Payment Type"  mat-cell *matCellDef="let element">
																	{{element.paymentType}} </td>
																<td mat-footer-cell *matFooterCellDef></td>
															</ng-container>

															<ng-container matColumnDef="dateProcessed">
																<th mat-header-cell *matHeaderCellDef mat-sort-header>
																	Date </th>
																<td  data-td-head="Date Processed "   mat-cell *matCellDef="let element">
																	{{element.dateProcessed | date}} </td>
																<td mat-footer-cell *matFooterCellDef></td>
															</ng-container>

															<ng-container matColumnDef="transactionType">
																<th mat-header-cell *matHeaderCellDef mat-sort-header>
																	Transaction Type </th>
																<td data-td-head="Transaction Type"  mat-cell *matCellDef="let element">
																	{{element.transactionType}} </td>
																<td mat-footer-cell *matFooterCellDef></td>
															</ng-container>

															<ng-container matColumnDef="systemSize">
																<th mat-header-cell *matHeaderCellDef mat-sort-header>
																	System Size </th>
																<td  data-td-head="System Size "   mat-cell *matCellDef="let element">
																	{{element.systemSize}} </td>
																<td mat-footer-cell *matFooterCellDef></td>
															</ng-container>

															<ng-container matColumnDef="cppw">
																<th mat-header-cell *matHeaderCellDef mat-sort-header>
																	CPPW </th>
																<td  data-td-head="CPPW"   mat-cell *matCellDef="let element">
																	{{element.cppw}} </td>
																<td mat-footer-cell *matFooterCellDef></td>
															</ng-container>

															<ng-container matColumnDef="amount">
																<th mat-header-cell *matHeaderCellDef mat-sort-header>
																	Amount </th>
																<td  data-td-head="Amount "  mat-cell *matCellDef="let element">
																	{{element.amount | currency}} </td>
																<td mat-footer-cell *matFooterCellDef>
																	{{ProcessedPaymentsTotal | currency}} </td>
															</ng-container>

															<ng-container matColumnDef="actualInstallDate">
																<th mat-header-cell *matHeaderCellDef mat-sort-header>
																	Actual Install Date </th>
																<td  data-td-head="Actual Install Date "   mat-cell *matCellDef="let element">
																	{{element.actualInstallDate | date}} </td>
																<td mat-footer-cell *matFooterCellDef></td>
															</ng-container>

															<tr mat-header-row *matHeaderRowDef="processedPaymentColumns"></tr>
															<tr mat-row *matRowDef="let row; columns: processedPaymentColumns;"></tr>
															<tr mat-footer-row *matFooterRowDef="processedPaymentColumns"></tr>
														</table>
														<mat-paginator [pageSizeOptions]="pageSizeOptions"
															style="margin-top: 2%;">
														</mat-paginator>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
							<div class="row">
								<div class="col-md">
									<div class="card">
										<div class="card-header-info">
											<h4 class="card-title"><i class="fas fa-dollar-sign"></i> Pending Payments</h4>
										</div>
										<div class="card-body">
											<div class="row">
												<mat-radio-group [(ngModel)]="selectedDealerType" (click)="$event.stopPropagation()" (change)="onChangeDealType($event)">
													<mat-radio-button class="mr-2" value="All">All</mat-radio-button>
													<mat-radio-button class="mr-2"value="Solar">Solar</mat-radio-button>
													<mat-radio-button class="mr-2" value="Roof">Roof</mat-radio-button>	
													<mat-radio-button class="mr-2" value="Battery">Battery</mat-radio-button>												
												  </mat-radio-group>
											</div>
										 
													<!-- <h4 style="font-weight: 500;">Upcoming Potential Status (30 days)
													</h4> -->
												 
														<table mat-table [dataSource]="PendingPayments" matSort
															class="w-100 my-table">
															<ng-container matColumnDef="paymentFor">
																<th mat-header-cell *matHeaderCellDef mat-sort-header>
																	Payment For </th>
																<td  data-td-head="Payment For" mat-cell *matCellDef="let element">
																	{{element.paymentFor}} </td>
																<td mat-footer-cell *matFooterCellDef> Total </td>
															</ng-container>

															<ng-container matColumnDef="paymentType">
																<th mat-header-cell *matHeaderCellDef mat-sort-header>
																	Payment Type </th>
																<td  data-td-head="Payment Type"  mat-cell *matCellDef="let element">
																	{{element.paymentType}} </td>
																<td mat-footer-cell *matFooterCellDef></td>
															</ng-container>

															<ng-container matColumnDef="dateProcessed">
																<th mat-header-cell *matHeaderCellDef mat-sort-header>
																	Date </th>
																<td   data-td-head="Date Processed"  mat-cell *matCellDef="let element">
																	{{element.dateProcessed | date}} </td>
																<td mat-footer-cell *matFooterCellDef></td>
															</ng-container>

															<ng-container matColumnDef="transactionType">
																<th mat-header-cell *matHeaderCellDef mat-sort-header>
																	Transaction Type </th>
																<td data-td-head="Transaction Type"  mat-cell *matCellDef="let element">
																	{{element.transactionType}} </td>
																<td mat-footer-cell *matFooterCellDef></td>
															</ng-container>

															<ng-container matColumnDef="systemSize">
																<th mat-header-cell [hidden]="selectedDealerType=='Roof'" *matHeaderCellDef mat-sort-header>
																	System Size </th>
																<td  data-td-head="System Size" [hidden]="selectedDealerType=='Roof'" mat-cell *matCellDef="let element">
																	{{element.systemSize}} </td>
																<td mat-footer-cell [hidden]="selectedDealerType=='Roof'" *matFooterCellDef></td>
															</ng-container>

															<ng-container matColumnDef="amount">
																<th mat-header-cell *matHeaderCellDef mat-sort-header>
																	Amount </th>
																<td  data-td-head="Amount"  mat-cell *matCellDef="let element">
																	{{element.amount | currency}} </td>
																<td mat-footer-cell *matFooterCellDef>
																	{{PendingPaymentsTotal | currency}}
																 </td>
															</ng-container>

															<ng-container matColumnDef="actualInstallDate">
																<th mat-header-cell [hidden]="selectedDealerType=='Roof'" *matHeaderCellDef mat-sort-header>
																	Actual Install Date </th>
																<td  data-td-head="Actual Install Date " [hidden]="selectedDealerType=='Roof'" mat-cell *matCellDef="let element">
																	{{element.actualInstallDate | date}} </td>
																<td mat-footer-cell *matFooterCellDef></td>
															</ng-container>

															<ng-container matColumnDef="roofInstallDate">
																<th mat-header-cell [hidden]="selectedDealerType=='Solar'" *matHeaderCellDef mat-sort-header>
																	Roof Install Date </th>
																<td  data-td-head="Roof Install Date" [hidden]="selectedDealerType=='Solar'"  mat-cell *matCellDef="let element">
																	{{element.roofInstallDate | date}} </td>
																<td mat-footer-cell *matFooterCellDef></td>
															</ng-container>
															<ng-container  matColumnDef="roofSquares">
																<th mat-header-cell [hidden]="selectedDealerType=='Solar'" *matHeaderCellDef mat-sort-header >
																	Roof Squares</th>
																<td  data-td-head="Roof Squares" [hidden]="selectedDealerType=='Solar'"  mat-cell *matCellDef="let element">
																	{{element.roofSquares}} </td>
																<td mat-footer-cell *matFooterCellDef></td>
															</ng-container>
															<ng-container matColumnDef="dealType">
																<th mat-header-cell *matHeaderCellDef mat-sort-header>
																	Deal Type</th>
																<td  data-td-head="Deal Type"   mat-cell *matCellDef="let element">
																	{{element.dealType}} </td>
																<td mat-footer-cell *matFooterCellDef></td>
															</ng-container>
															
															<tr mat-header-row *matHeaderRowDef="pendingPaymentsColumn"></tr>
															<tr mat-row *matRowDef="let row; columns: pendingPaymentsColumn"></tr>
															<tr mat-footer-row *matFooterRowDef="pendingPaymentsColumn"></tr>
														</table>
														<mat-paginator [pageSizeOptions]="pageSizeOptions"
															style="margin-top: 2%;">
														</mat-paginator>
													 
										</div>
									</div>
								</div>
							</div>
							<div class="row">
								<div class="col-md">
									<div class="card">
										<div class="card-header-info">
											<h4 class="card-title"><i class="fas fa-dollar-sign"></i> Approved-On Hold Payments</h4>
											<p style="display: inline; vertical-align: middle; size:14px;"><b>*The book balance does not include any payments marked as "Approved-On Hold." These commissions will be included once Accounting marks the opportunity as paid in full.</b></p>
										</div>
										<div class="card-body">
											<div class="row">
												<mat-radio-group [(ngModel)]="selectedApprovedOnHoldType" (click)="$event.stopPropagation()" (change)="onChangeApprovedOnHoldDealType()">
													<mat-radio-button class="mr-2" value="All">All</mat-radio-button>
													<mat-radio-button class="mr-2"value="Solar">Solar</mat-radio-button>
													<mat-radio-button class="mr-2" value="Roof">Roof</mat-radio-button>	
													<mat-radio-button class="mr-2" value="Battery">Battery</mat-radio-button>												
												  </mat-radio-group>
											</div>
													<!-- <h4 style="font-weight: 500;">Upcoming Potential Status (30 days)
													</h4> -->
												 
														<table mat-table [dataSource]="ApprovedOnHoldPayments" matSort
															class="w-100 my-table">
															<ng-container matColumnDef="paymentFor">
																<th mat-header-cell *matHeaderCellDef mat-sort-header>
																	Payment For </th>
																<td  data-td-head="Payment For" mat-cell *matCellDef="let element">
																	{{element.paymentFor}} </td>
																<td mat-footer-cell *matFooterCellDef> Total </td>
															</ng-container>

															<ng-container matColumnDef="paymentType">
																<th mat-header-cell *matHeaderCellDef mat-sort-header>
																	Payment Type </th>
																<td  data-td-head="Payment Type"  mat-cell *matCellDef="let element">
																	{{element.paymentType}} </td>
																<td mat-footer-cell *matFooterCellDef></td>
															</ng-container>

															<ng-container matColumnDef="dateProcessed">
																<th mat-header-cell *matHeaderCellDef mat-sort-header>
																	Date </th>
																<td   data-td-head="Date Processed"  mat-cell *matCellDef="let element">
																	{{element.dateProcessed | date}} </td>
																<td mat-footer-cell *matFooterCellDef></td>
															</ng-container>

															<ng-container matColumnDef="transactionType">
																<th mat-header-cell *matHeaderCellDef mat-sort-header>
																	Transaction Type </th>
																<td data-td-head="Transaction Type"  mat-cell *matCellDef="let element">
																	{{element.transactionType}} </td>
																<td mat-footer-cell *matFooterCellDef></td>
															</ng-container>

															<ng-container matColumnDef="systemSize">
																<th mat-header-cell [hidden]="selectedDealerType=='Roof'" *matHeaderCellDef mat-sort-header>
																	System Size </th>
																<td  data-td-head="System Size" [hidden]="selectedDealerType=='Roof'" mat-cell *matCellDef="let element">
																	{{element.systemSize}} </td>
																<td mat-footer-cell [hidden]="selectedDealerType=='Roof'" *matFooterCellDef></td>
															</ng-container>

															<ng-container matColumnDef="amount">
																<th mat-header-cell *matHeaderCellDef mat-sort-header>
																	Amount </th>
																<td  data-td-head="Amount"  mat-cell *matCellDef="let element">
																	{{element.amount | currency}} </td>
																<td mat-footer-cell *matFooterCellDef>
																	{{ApprovedOnHoldPaymentsTotal | currency}}
																 </td>
															</ng-container>

															<ng-container matColumnDef="actualInstallDate">
																<th mat-header-cell [hidden]="selectedDealerType=='Roof'" *matHeaderCellDef mat-sort-header>
																	Actual Install Date </th>
																<td  data-td-head="Actual Install Date " [hidden]="selectedDealerType=='Roof'" mat-cell *matCellDef="let element">
																	{{element.actualInstallDate | date}} </td>
																<td mat-footer-cell *matFooterCellDef></td>
															</ng-container>

															<ng-container matColumnDef="roofInstallDate">
																<th mat-header-cell [hidden]="selectedDealerType=='Solar'" *matHeaderCellDef mat-sort-header>
																	Roof Install Date </th>
																<td  data-td-head="Roof Install Date" [hidden]="selectedDealerType=='Solar'"  mat-cell *matCellDef="let element">
																	{{element.roofInstallDate | date}} </td>
																<td mat-footer-cell *matFooterCellDef></td>
															</ng-container>
															<ng-container  matColumnDef="roofSquares">
																<th mat-header-cell [hidden]="selectedDealerType=='Solar'" *matHeaderCellDef mat-sort-header >
																	Roof Squares</th>
																<td  data-td-head="Roof Squares" [hidden]="selectedDealerType=='Solar'"  mat-cell *matCellDef="let element">
																	{{element.roofSquares}} </td>
																<td mat-footer-cell *matFooterCellDef></td>
															</ng-container>
															<ng-container matColumnDef="dealType">
																<th mat-header-cell *matHeaderCellDef mat-sort-header>
																	Deal Type</th>
																<td  data-td-head="Deal Type"   mat-cell *matCellDef="let element">
																	{{element.dealType}} </td>
																<td mat-footer-cell *matFooterCellDef></td>
															</ng-container>
															
															<tr mat-header-row *matHeaderRowDef="pendingPaymentsColumn"></tr>
															<tr mat-row *matRowDef="let row; columns: pendingPaymentsColumn"></tr>
															<tr mat-footer-row *matFooterRowDef="pendingPaymentsColumn"></tr>
														</table>
														<mat-paginator [pageSizeOptions]="pageSizeOptions"
															style="margin-top: 2%;">
														</mat-paginator>
													 
										</div>
									</div>
								</div>
							</div>
							<div class="row">
								<div class="col-md">
									<div class="card">
										<div class="card-header-info">
											<h4 class="card-title"><i class="fas fa-dollar-sign"></i> Pending Reclaims</h4>
										</div>
										<div class="card-body">
											 
													<!-- <h4 style="font-weight: 500;">Remaining amount to be reclaimed</h4> -->
												 
														<table mat-table [dataSource]="PendingReclaims" matSort
															class="my-table w-100">
															<ng-container matColumnDef="paymentFor">
																<th mat-header-cell *matHeaderCellDef mat-sort-header>
																	Payment For </th>
																<td  data-td-head="Payment For  "  mat-cell *matCellDef="let element">
																	{{element.paymentFor}} </td>
																<td mat-footer-cell *matFooterCellDef> Total </td>
															</ng-container>

															<ng-container matColumnDef="paymentType">
																<th mat-header-cell *matHeaderCellDef mat-sort-header>
																	Payment Type </th>
																<td   data-td-head="Payment Type"  mat-cell *matCellDef="let element">
																	{{element.paymentType}} </td>
																<td mat-footer-cell *matFooterCellDef></td>
															</ng-container>

															<ng-container matColumnDef="dateProcessed">
																<th mat-header-cell *matHeaderCellDef mat-sort-header>
																	Date </th>
																<td   data-td-head="Date Processed" mat-cell *matCellDef="let element">
																	{{element.dateProcessed | date}} </td>
																<td mat-footer-cell *matFooterCellDef></td>
															</ng-container>

															<ng-container matColumnDef="transactionType">
																<th mat-header-cell *matHeaderCellDef mat-sort-header>
																	Transaction Type </th>
																<td data-td-head="Transaction Type"  mat-cell *matCellDef="let element">
																	{{element.transactionType}} </td>
																<td mat-footer-cell *matFooterCellDef></td>
															</ng-container>
															
															<ng-container matColumnDef="systemSize">
																<th mat-header-cell *matHeaderCellDef mat-sort-header>
																	System Size </th>
																<td  data-td-head="System Size" mat-cell *matCellDef="let element">
																	{{element.systemSize}} </td>
																<td mat-footer-cell *matFooterCellDef></td>
															</ng-container>

															<ng-container matColumnDef="amount">
																<th mat-header-cell *matHeaderCellDef mat-sort-header>
																	Amount </th>
																<td  data-td-head="Amount"  mat-cell *matCellDef="let element">
																	{{element.amount | currency}} </td>
																<td mat-footer-cell *matFooterCellDef>
																	{{PendingReclaimsTotal | currency}} </td>
															</ng-container>
															<ng-container matColumnDef="actualInstallDate">
																<th mat-header-cell *matHeaderCellDef mat-sort-header>
																	Actual Install Date </th>
																<td  data-td-head="Actual Install Date "   mat-cell *matCellDef="let element">
																	{{element.actualInstallDate | date}} </td>
																<td mat-footer-cell *matFooterCellDef></td>
															</ng-container>

															<tr mat-header-row *matHeaderRowDef="columns"></tr>
															<tr mat-row *matRowDef="let row; columns: columns;"></tr>
															<tr mat-footer-row *matFooterRowDef="columns"></tr>
														</table>
														<mat-paginator [pageSizeOptions]="pageSizeOptions"
															style="margin-top: 2%;">
														</mat-paginator>
													 
										</div>
									</div>
								</div>
							</div>
							<div class="row">
								<div class="col-md">
									<div class="card">
										<div class="card-header-info">
											<h4 class="card-title"><i class="fas fa-dollar-sign"></i> My Opportunities</h4>
										</div>
										<div class="card-body">
												<mat-tab-group>
													<mat-tab *ngIf="isTrinitySalesPersonOpp">
														<ng-template matTabLabel>
															<span>Trinity Salesperson</span>
														</ng-template>
														<app-salesrep-opportunity-table [opportunities]="trinitySalesPersonOpportunities" ></app-salesrep-opportunity-table>
													</mat-tab>
													<mat-tab *ngIf="isLeadGeneratorOpp">
														<ng-template matTabLabel>
															<span>Lead Generator</span>
														</ng-template>
														<app-salesrep-opportunity-table [opportunities]="leadGeneratorOpportunities"></app-salesrep-opportunity-table>
													</mat-tab>
													<mat-tab *ngIf="isSdrInsideSalesOpp">
														<ng-template matTabLabel>
															<span>SDR Inside Sales</span>
														</ng-template>
														<app-salesrep-opportunity-table [opportunities]="sdrInsideSalesOpportunities"></app-salesrep-opportunity-table>
													</mat-tab>
													<mat-tab *ngIf="isSalesSuccessRepresentativeOpp">
														<ng-template matTabLabel>
															<span>Sales Success Representative</span>
														</ng-template>
														<app-salesrep-opportunity-table [opportunities]="salesSuccessRepresentativeOpportunities"></app-salesrep-opportunity-table>
													</mat-tab>
													<mat-tab *ngIf="isAccountExecutiveOpp">
														<ng-template matTabLabel>
															<span>Account Executive</span>
														</ng-template>
														<app-salesrep-opportunity-table [opportunities]="accountExecutiveOpportunities"></app-salesrep-opportunity-table>
													</mat-tab>
													<mat-tab *ngIf="isBatterySalesPersonOpp">
														<ng-template matTabLabel>
															<span>Battery Sales Person</span>
														</ng-template>
														<app-salesrep-opportunity-table [opportunities]="batterySalespersonOpportunities"></app-salesrep-opportunity-table>
													</mat-tab>
													<mat-tab *ngIf="isRoofingSalesPersonOpp">
														<ng-template matTabLabel>
															<span>Roofing Sales Person</span>
														</ng-template>
														<app-salesrep-opportunity-table [opportunities]="roofingSalespersonOpportunities"></app-salesrep-opportunity-table>
													</mat-tab>
												
												</mat-tab-group>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
		 