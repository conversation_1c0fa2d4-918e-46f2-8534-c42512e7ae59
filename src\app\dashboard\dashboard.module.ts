import { Component, Directive, NgModule, Pipe, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DashboardRoutingModule } from './dashboard-routing.module';
import { DashboardComponent } from '../dashboard/dashboard.component';
import { CommissionsComponent } from './commissions/commissions.component';
import { CreatePlanComponent } from './create-plan/create-plan.component';
import { ChangePlanComponent } from './change-plan/change-plan.component';
import { OpportunityDetailsComponent } from './opportunity-details/opportunity-details.component';
import { PaymentsComponent } from './payments/payments.component';
import { UserDashboardComponent } from './user-dashboard/user-dashboard.component';
import { EmpHistoryComponent } from './emp-history/emp-history.component';
import { EmpHistoryDetailsComponent } from './emp-history-details/emp-history-details.component';
import { SalesRepConfigurationComponent } from './sales-rep-configuration/sales-rep-configuration.component';
import { PaybooksComponent } from './paybooks/paybooks.component';
import { ReportComponent } from './report/report.component';
import { SalesRepComponent } from './sales-rep/sales-rep.component';
import { OpportunitiesListComponent } from './opportunities-list/opportunities-list.component';
import { SalesRepListComponent } from './sales-rep-list/sales-rep-list.component';
import { SearchComponent } from './search/search.component';
import { CreateRuleModule } from '../dashboard/create-rule/create-rule.module';
import { ShowPaymentBookComponent } from './sales-rep-configuration/show-payment-book/show-payment-book.component';
import { NgxCurrencyDirective } from 'ngx-currency';
import { SharedModule } from '../shared-module/shared.module';
import { fromEventPattern } from 'rxjs';
import { RateTablesModule } from './rate-tables/rate-tables.module';
import { OpportunityComponent } from './opportunity-details/opportunity/opportunity.component';
import { CommissionComponent } from './opportunity-details/commission/commission.component';
import { OneTimePaymentComponent } from './opportunity-details/onetimepayment/onetimepayment.component';
import { LeadsourceconfigurationComponent } from "./maintenance/lead-source-commissions-modifier/lead-source-commissions-modifier-component"
import { LeadSourceComponent } from "./maintenance/lead-source/lead-source-component"
// import { SunnovaLoanComponent } from './opportunity-details/sunnova-loan/sunnova-loan.component';
import { DataIntegrationComponent } from './data-integration/data-integration.component';
import { StarterComponent } from './data-integration/starter/starter.component';
import { StatusOverviewComponent } from './data-integration/status-overview/status-overview.component';
import { AddUserComponent } from './usermanagment/add-user/add-user.component';
import { EditUserComponent } from './usermanagment/edit-user/edit-user.component';
import { UsersListComponent } from './usermanagment/users-list/users-list.component';
import { BasePayStructurePromptComponent } from './change-plan/base-pay-structure-prompt/base-pay-structure-prompt.component';
import { EmployeeIncentivePromptComponent } from './change-plan/employee-incentive-prompt/employee-incentive-prompt.component';
import { ShowBasePayStructureComponent } from './sales-rep-configuration/show-base-pay-structure/show-base-pay-structure.component';
import { MatBadgeModule } from '@angular/material/badge';
import { MatLegacyButtonModule } from '@angular/material/legacy-button';
import { MatLegacyCheckboxModule } from '@angular/material/legacy-checkbox';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatLegacyDialogModule } from '@angular/material/legacy-dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatLegacyFormFieldModule  } from '@angular/material/legacy-form-field';
import { MatGridListModule } from '@angular/material/grid-list';
import { MatIconModule } from '@angular/material/icon';
import { MatLegacyInputModule  } from '@angular/material/legacy-input';
import { MatLegacyListModule } from '@angular/material/legacy-list';
import { MatLegacyRadioModule } from '@angular/material/legacy-radio';
import { MatLegacySelectModule } from '@angular/material/legacy-select';
import { MatSort } from '@angular/material/sort';
import { MatLegacyTabsModule } from '@angular/material/legacy-tabs';
import { MatLegacyTooltipModule } from '@angular/material/legacy-tooltip';
// import { ShowRuleComponent } from './sales-rep-configuration/show-rule/show-rule.component';
// import { ShowRuleTypeComponent } from './sales-rep-configuration/show-rule-type/show-rule-type.component';

import { PaymentReviewComponent } from './payments/payment-review/payment-review.component';
import { PaymentBookWithdrawalComponent } from './payments/payment-book-withdrawal/payment-book-withdrawal.component';
// import { OutreachOpportunitiesComponent } from './reports/outreach-opportunities/outreach-opportunities.component';
import { MainPipeModule } from '../pipe/main-pipe.module';
import { DynamicReportComponent } from './reports/dynamic-report/dynamic-report.component';
import { SalesrepDashboardComponent } from './salesrep-dashboard/salesrep-dashboard.component';
import { PaymentBookDashboardComponent } from './payment-book-dashboard/payment-book-dashboard.component';
import { CommissionViewComponent } from './commission-view/commission-view.component';
import { MatLegacyTableModule } from '@angular/material/legacy-table';
import { MatSortModule } from '@angular/material/sort';
import { CdkTableModule } from '@angular/cdk/table';
import { MatLegacyPaginatorModule } from '@angular/material/legacy-paginator';
import { CurrencyPipe } from '@angular/common';
// import { CreateStaticComponent } from './create-static/create-static.component';
//import { AdminAccessGuard } from '../guards/admin-access.guard';

import { NgxPaginationModule } from 'ngx-pagination';
import { CommissionFilters } from './commission-view/service/filters.service';
import { OpportunityDetailListComponent } from './opportunity-details/opportunity/opportunity-detail-list/opportunity-detail-list.component';
import { CommissionHistoryComponent } from './commission-view/commission-history/commission-history.component';
import { CommissionAuditComponent } from './commission-view/audit-commission-finalize/audit-commission-finalize.component';
import { CommissionPaymentsComponent } from './commission-view/payments/payments.component';
import { PaymentHistoryDialogComponent } from './commission-view/payments/payment-history-dialog/payment-history-dialog.component';
import { CommissionIdentifierChangeDialogComponent } from './commission-view/commission-identifier-change-dialog/commission-identifier-change-dialog.component';
import { EmployeeIncentiveDialogComponent } from './change-plan/employee-incentive-dialog/employee-incentive-dialog.component';
import { BasePayStructureDialogComponent } from './change-plan/base-pay-structure-dialog/base-pay-structure-dialog.component';
import { PaymentBookDialogComponent } from './change-plan/payment-book-dialog/payment-book-dialog.component';
import { PaymentBookScheduleDialogComponent } from './change-plan/payment-book-schedule-dialog/payment-book-schedule-dialog.component';
import { PaymentBookSchedulePromptComponent } from './change-plan/payment-book-schedule-prompt/payment-book-schedule-prompt.component';
import { AuditOpportunityFinalizeComponent } from './opportunity-details/audit-opportunity-finalize/audit-opportunity-finalize.component';

import { AddonsComponent } from './change-plan/addons/addons.component';
import { AddonTableComponent } from './change-plan/addons/addon-table/addon-table.component';
import { ModifyPromptsDialogComponent } from './change-plan/addons/addon-table/modify-prompts-dialog/modify-prompts-dialog.component';
// import { ConfirmationModalComponent } from '../confirmation-modal/confirmation-modal.component';
import { InnerDashboardComponent } from './inner-dashboard/inner-dashboard.component';
import { FinanceAdminDashboardComponent } from './financeAdmin-dashboard/financeAdmin-dashboard.component';
import { SalesAdminDashboardComponent } from './salesadmin-dashboard/salesAdmin-dashboard.component';
import { LegalAdminDashboardComponent } from './legalAdmin-dashboard/legalAdmin-dashboard.component';
import { SidenavService } from './../services/sidenav.service';
import { SharedSidebarService } from './../shared/sidebar-icon';
import { ConfirmationDialogComponent } from '../confirmation-dialog/confirmation-dialog.component';
import { CommissionDataComponent } from './commission-view/commission-data/commission-data.component';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { PaymentNoteModelComponent } from './commission-view/payments/payment-note-model/payment-note-model.component';
import { SearchResultComponent } from './search-result/search-result.component';
import { SalesrepOpportunityTableComponent } from './salesrep-opportunity-table/salesrep-opportunity-table.component';
import { SalesRepOverrideDetailsPopupComponent } from './sales-rep-override-details-popup/sales-rep-override-details-popup.component';

import { SalesRepOutreachOverrideDetailsPopupComponent } from './sales-rep-outreach-override-details-popup/sales-rep-outreach-override-details-popup.component';
import { EmployeeOverrideComponent } from './maintenance/employee-override/employee-override-component';
import { EmployeeOverrideRoofingComponent } from './maintenance/employee-override-roofing/employee-override-roofing-component';

import { SalesRepProcessedPaymentPopupComponent } from './sales-rep-processed-payment-popup/sales-rep-processed-payment-popup.component';
import { PlanExecutionComponent } from './plan-execution/plan-execution.component';
import { PlanStatusOverviewComponent } from './plan-execution/plan-status-overview/plan-status-overview.component';
import { PlanStarterComponent } from './plan-execution/plan-starter/plan-starter.component';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { CustomReportComponent } from './custom-report/custom-report.component';
import { AddPlanInclusionDialogComponent } from './sales-rep-configuration/add-plan-inclusion-dialog/add-plan-inclusion-dialog/add-plan-inclusion-dialog.component';
import { PlanInclusionHistoryComponent } from './sales-rep-configuration/plan-inclusion-history/plan-inclusion-history.component';
import { TimezoneDatePipe } from '../pipe/timezone-date.pipe';
import { DatezonePipe } from '../pipe/datezone.pipe';
import { HomeDepotEmployeeOverrideComponent } from './maintenance/home-depot-employee-override/home-depot-employee-override/home-depot-employee-override.component';
import { MonthlyRoofingOverrideSnapshotComponent } from './maintenance/monthly-roofing-override-snapshot/monthly-roofing-override-snapshot.component';
import { BatteryQuarterlyOverrideComponent } from './maintenance/battery-quarterly-override/battery-quarterly-override.component';
import { QuaterlyRoofingOverrideComponent } from './maintenance/quaterly-roofing-override/quaterly-roofing-override.component';
import { ConfirmDialogComponent } from './confirm-dialog/confirm-dialog.component';
import { CommissionableDivisionContactInclusionComponent } from './maintenance/commissionable-division-contact-inclusion/commissionable-division-contact-inclusion.component';
import { EvaluateCommissionComponent } from './opportunity-details/evaluate-commission/evaluate-commission.component';
import { EvaluateCommissionViewComponent } from './evaluate-commission-view/evaluate-commission-view.component';
import { PrepaidPaymentsDashboardComponent } from './payments/prepaid-payments/prepaid-payments-dashboard/prepaid-payments-dashboard.component';

@NgModule({
    declarations: [
        DashboardComponent,
        CommissionsComponent,
        CreatePlanComponent,
        ChangePlanComponent,
        OpportunityDetailsComponent,
        PaymentsComponent,
        UserDashboardComponent,
        EmpHistoryComponent,
        EmpHistoryDetailsComponent,
        SalesRepConfigurationComponent,
        PaybooksComponent,
        ReportComponent,
        SalesRepComponent,
        OpportunitiesListComponent,
        SalesRepListComponent,
        SearchComponent,
        ShowPaymentBookComponent,
        OpportunityComponent,
        CommissionComponent,
        OneTimePaymentComponent,
        AuditOpportunityFinalizeComponent,
        LeadsourceconfigurationComponent,
        LeadSourceComponent,
        EmployeeOverrideComponent,
        EmployeeOverrideRoofingComponent,
        // SunnovaLoanComponent,
        DataIntegrationComponent,
        StarterComponent,
        StatusOverviewComponent,
        AddUserComponent,
        EditUserComponent,
        UsersListComponent,
        BasePayStructurePromptComponent,
        EmployeeIncentivePromptComponent,
        ShowBasePayStructureComponent,
        // // ShowRuleComponent,
        PaymentReviewComponent,
        PaymentBookWithdrawalComponent,
        // OutreachOpportunitiesComponent,
        SalesrepDashboardComponent,
        DynamicReportComponent,
        PaymentBookDashboardComponent,
        CommissionViewComponent,
        OpportunityDetailListComponent,
        CommissionHistoryComponent,
        CommissionAuditComponent,
        CommissionPaymentsComponent,
        PaymentHistoryDialogComponent,
        CommissionIdentifierChangeDialogComponent,
        EmployeeIncentiveDialogComponent,
        BasePayStructureDialogComponent,
        PaymentBookDialogComponent,
        PaymentBookScheduleDialogComponent,
        PaymentBookSchedulePromptComponent,
        AddonsComponent,
        AddonTableComponent,
        ModifyPromptsDialogComponent,
        InnerDashboardComponent,
        FinanceAdminDashboardComponent,
        LegalAdminDashboardComponent,
        SalesAdminDashboardComponent,
        ConfirmationDialogComponent,
        CommissionDataComponent,
        SearchResultComponent,
        PaymentNoteModelComponent,
        SalesrepOpportunityTableComponent,
        SalesRepOverrideDetailsPopupComponent,
        SalesRepProcessedPaymentPopupComponent,
        SalesRepOutreachOverrideDetailsPopupComponent,
        PlanStatusOverviewComponent,
        PlanExecutionComponent,
        PlanStarterComponent,
        CustomReportComponent,
        AddPlanInclusionDialogComponent,
        PlanInclusionHistoryComponent,
        HomeDepotEmployeeOverrideComponent,
        MonthlyRoofingOverrideSnapshotComponent,
        BatteryQuarterlyOverrideComponent,
        QuaterlyRoofingOverrideComponent,
        ConfirmDialogComponent,
        CommissionableDivisionContactInclusionComponent,
        EvaluateCommissionComponent,
        EvaluateCommissionViewComponent,
        PrepaidPaymentsDashboardComponent
    ],
    imports: [
        CommonModule,
        DashboardRoutingModule,
        FormsModule,
        NgMultiSelectDropDownModule.forRoot(),
        ReactiveFormsModule.withConfig({callSetDisabledState: 'whenDisabledForLegacyCode'}),
        NgxPaginationModule,
        CreateRuleModule,
        NgxCurrencyDirective,
        SharedModule,
        RateTablesModule,
        MatLegacyRadioModule,
        MainPipeModule,
        MatLegacyCheckboxModule,
        MatLegacyTableModule,
        MatSortModule,
        CdkTableModule,
        MatLegacyPaginatorModule,
        MatGridListModule,
        MatLegacyListModule,
        MatDividerModule,
        MatLegacyButtonModule,
        MatIconModule,
        MatLegacyFormFieldModule,
        MatLegacyInputModule,
        MatLegacySelectModule,
        MatLegacyDialogModule,
        MatLegacyTabsModule,
        MatDatepickerModule,
        MatNativeDateModule,
        MatBadgeModule,
        MatLegacyTooltipModule,
        NgbModule,
        MainPipeModule
    ],
    providers: [
        CurrencyPipe,
        CommissionFilters,
        DatezonePipe,
        TimezoneDatePipe,
        DatePipe,
        MatDatepickerModule,
        SidenavService,
        SharedSidebarService,        
    ],
    schemas:[CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class DashboardModule { }
