import { Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from "@angular/forms";
import { ApiService } from "../../../services/api.service";
import { ToastrService } from 'ngx-toastr';
import { dateLessThanDate, maxTerritoryRateDate } from '../../../shared/validators';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { DataSource } from '@angular/cdk/table';
import { MatLegacyPaginator } from '@angular/material/legacy-paginator';
import { TableFilterPipe } from '../../../pipe/table-filter.pipe';
import { DatePipe, PercentPipe, CurrencyPipe } from '@angular/common';
import { MatLegacyDialog } from '@angular/material/legacy-dialog';
import { OutReachConfigurationMaintenanceDialogComponent } from '../out-reach-configuration-maintenance-dialog/out-reach-configuration-maintenance-dialog.component';

@Component({
  selector: 'app-out-reach-configuration',
  templateUrl: './out-reach-configuration-maintenance.component.html',
  styleUrls: ['./out-reach-configuration-maintenance.component.css']
})
export class OutReachConfigurationComponent implements OnInit {
  allOutreachPayConfigurationType: any;
  activeOutreachPayConfigurationType: any;
  OutreachPayConfigurationTypeGroup: any;
  dropdowns: any;
  isReloading :boolean = false ;
  outReachConfigForm: UntypedFormGroup;
  addInd: boolean = false;
  outreachPayConfigurationTypeNameDefault: number = 1;
  payAfterThresholdIndDefault: number = 1;
  p: number = 1;
  tableArr: Element[] = [];
  outreachConfigurationRate1; 
   isoutreachConfigurationRateSelected : boolean = false;
  searchText: string = "";
  public date: Date;
  originalDataSource;
  dataSource;
  displayedColumns = [];
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatLegacyPaginator, { static: true }) paginator: MatLegacyPaginator;

  columnNames = [{
    id: "outreachPayConfigurationTypeName",
    value: "Configuration Name"

  },/* {
    id: "outreachPayConfigurationId",
    value: "Outreach Pay ConfigurationId"
  },
  {
    id: "outreachPayConfigurationTypeId",
    value: "Outreach Pay Configuration TypeId"
  },*/
  {
    id: "payAfterThresholdInd",
    value: "Pay Before or After Threshold is Reached"
  },
  {
    id: "payBasedOn",
    value: "Pay Based On"
  },
  // {
  //   id: "activeInd",
  //   value: "ActiveInd"
  // },
  {
    id: "configurationAmount",
    value: "Awarded Amount"
  },
  {
    id: "configurationThreshold",
    value: "Threshold to Reach"
  },
  {
    id: "effectiveStartDate",
    value: "Effective Start Date"
  }
    ,
  {
    id: "effectiveEndDate",
    value: "Effective End Date"
  }
  ];

  constructor(public apiService: ApiService, private toastMsg: ToastrService, private formBuilder: UntypedFormBuilder, private pipe: TableFilterPipe,
    private datePipe: DatePipe, private percentPipe: PercentPipe, private currencyPipe: CurrencyPipe, private dialog: MatLegacyDialog) {

  }

  ngOnInit() {
    if (!this.apiService.checkPermission('ViewRateTables')) {
      // this.router.navigate(['/ui/dashboard'])
      this.apiService.goBack();
      this.toastMsg.error("Insufficient Permissions. Please make sure your account can access this information.", "Permissions");
    }
    this.getDropdowns();

    this.outReachConfigForm = this.formBuilder.group({
      outreachPayConfigurationTypeName: [this.outreachPayConfigurationTypeNameDefault, [Validators.required]],
      //outreachPayConfigurationId: [0, [Validators.required]],
      //outreachPayConfigurationTypeId: [0, [Validators.required]],
      payAfterThresholdInd: [false, [Validators.required]],
      payBasedOn: ['DEMO', [Validators.required]],
      // activeInd: ['false', [Validators.required]],
      configurationAmount: [0, [Validators.required]],
      configurationThreshold: [0, [Validators.required]],
      effectiveEndDate: ['', [Validators.required]],
      effectiveStartDate: ['', [Validators.required]]
    });

    this.onChanges();
  }

  onChanges() {
    this.outReachConfigForm.valueChanges.subscribe(val => {
      // console.log(this.territoryRateForm.errors);
    });
  }

  clearStartDate(date: HTMLInputElement) {    
    date.value = "";
    this.date = null;
    this.outReachConfigForm.controls.effectiveStartDate.setValue('');
    event.stopPropagation();
  }

  clearEndDate(date: HTMLInputElement) {    
    date.value = "";
    this.date = null;
    this.outReachConfigForm.controls.effectiveEndDate.setValue('');
    event.stopPropagation();
  }

  onSubmit() {
    if (!this.outReachConfigForm.invalid) {
      var values = {
        outreachPayConfigurationTypeId: parseInt(this.outReachConfigForm.controls.outreachPayConfigurationTypeName.value),
        //outreachPayConfigurationId: this.outReachConfigForm.controls.outreachPayConfigurationId.value,
        //outreachPayConfigurationTypeId: this.outReachConfigForm.controls.outreachPayConfigurationTypeId.value,
        payAfterThresholdInd: this.outReachConfigForm.controls.payAfterThresholdInd.value,
        payBasedOn: this.outReachConfigForm.controls.payBasedOn.value,
        // activeInd: this.outReachConfigForm.controls.activeInd.value,
        configurationAmount: this.outReachConfigForm.controls.configurationAmount.value,
        configurationThreshold: this.outReachConfigForm.controls.configurationThreshold.value,
        effectiveEndDate: this.outReachConfigForm.controls.effectiveEndDate.value,
        effectiveStartDate: this.outReachConfigForm.controls.effectiveStartDate.value
        
      }
      this.apiService.post('OutReachConfiguration', values)
      .subscribe(data => {
        this.toastMsg.success('OutReach Configuration Successfully Added');
        this.isReloading = true ;
        this.getAllOutReachConfiguration();
        this.getActiveOutReachConfiguration();
        this.addInd = !this.addInd;
      }, (err: any) => {
          if (err?.err?.message)
            this.toastMsg.error(err.message, "Server Error!");
          if (typeof err === "string") {
            let isJsonObject = false;            
            let parsedErr: any;
            try {
              parsedErr = JSON.parse(err);
              isJsonObject =parsedErr && typeof parsedErr === "object" &&!Array.isArray(parsedErr);
            } catch (e) {
              isJsonObject = false;
            }
            if (isJsonObject) {
              this.toastMsg.error(parsedErr.result, "Error!");
            } else this.toastMsg.error(err, "Error!");
          }
          if (err?.result) {
            this.toastMsg.error(err.result, "Error!");
          }
        });
    }
  }

  getAllOutReachConfiguration() {
    this.apiService.get('OutReachConfiguration/RetrieveAll')
      .subscribe(data => {
        this.allOutreachPayConfigurationType = data;
        if(!this.isReloading)
        {
          this.outReachConfigForm.setValidators([maxTerritoryRateDate(this.allOutreachPayConfigurationType)]);
        }else{
          this.outReachConfigForm.clearValidators();
        }
        if (this.OutreachPayConfigurationTypeGroup) this.getOutreachPayConfigurationTypeGroup(this.OutreachPayConfigurationTypeGroup[0]);
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }

  getActiveOutReachConfiguration() {
    this.apiService.get('OutReachConfiguration/RetrieveActive')
      .subscribe(data => {
        this.activeOutreachPayConfigurationType = data;
        this.displayedColumns = this.columnNames.map(x => x.id);
        this.createTable();

      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }

  getDropdowns() {
    this.apiService.get('OutReachConfiguration/dropdowns')
      .subscribe(data => {
        this.dropdowns = data;
        //console.log(this.dropdowns);
        this.getAllOutReachConfiguration();
        this.getActiveOutReachConfiguration();
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }



  getOutreachPayConfigurationTypeGroup(outreachPayConfigurationType: any) {
    //console.log(outreachPayConfigurationType);
    var outreachPayConfigurationTypes = this.allOutreachPayConfigurationType.filter(x => x.outreachPayConfigurationTypeId === outreachPayConfigurationType.outreachPayConfigurationTypeId);

    this.OutreachPayConfigurationTypeGroup = outreachPayConfigurationTypes;
  }

  get configurationAmount() { return this.outReachConfigForm.get('configurationAmount'); }
  get payAfterThresholdInd() { return this.outReachConfigForm.get('payAfterThresholdInd'); }

  rowClick(outreachPayConfigurationType: any) {
    var outreachPayConfigurationType = this.allOutreachPayConfigurationType.filter(x => x.outreachPayConfigurationTypeId === outreachPayConfigurationType.outreachPayConfigurationTypeId);
    this.outreachConfigurationRate1 = outreachPayConfigurationType;
    this.isoutreachConfigurationRateSelected = true;
    const dialogRef = this.dialog.open(OutReachConfigurationMaintenanceDialogComponent, {
      width: '80%', data: { outreachPayConfigurationType }
    });
    // console.log("outreachConfigurationRate1 = >" + JSON.stringify(this.outreachConfigurationRate1));
    // console.log("this.outreachConfigurationRate1  = >" + JSON.stringify(this.outreachConfigurationRate1));

    this.outReachConfigForm.controls['outreachPayConfigurationTypeName'].setValue(this.outreachConfigurationRate1[0].outreachPayConfigurationTypeId);
    this.outReachConfigForm.controls['payAfterThresholdInd'].setValue(this.outreachConfigurationRate1[0].payAfterThresholdInd);
    this.outReachConfigForm.controls['payBasedOn'].setValue(this.outreachConfigurationRate1[0].payBasedOn);
    this.outReachConfigForm.controls['configurationAmount'].setValue(this.outreachConfigurationRate1[0].configurationAmount);
    this.outReachConfigForm.controls['configurationThreshold'].setValue(this.outreachConfigurationRate1[0].configurationThreshold);
    // this.outReachConfigForm.controls['outreachPayConfigurationTypeId'].setValue(this.outreachConfigurationRate1[0].outreachPayConfigurationTypeId);
    // this.outReachConfigForm.controls['activeInd'].setValue(this.outreachConfigurationRate1[0].activeInd);

    dialogRef.afterClosed().subscribe(result => {
      // console.log(result);
    });
  }

  Add(){
    this.outreachConfigurationRate1 = this.tableArr;
    this.addInd = !this.addInd;
    this.isReloading= true ;
    if(!this.isReloading)
    {
      this.outReachConfigForm.setValidators([maxTerritoryRateDate(this.allOutreachPayConfigurationType)]);
    }else{
      this.outReachConfigForm.clearValidators();
    }
      // console.log("Add this.outreachConfigurationRate1 => 292   " +JSON.stringify(this.outreachConfigurationRate1));

      this.outReachConfigForm.controls['outreachPayConfigurationTypeName'].setValue(this.outreachConfigurationRate1[0].outreachPayConfigurationTypeId);
      this.outReachConfigForm.controls['payAfterThresholdInd'].setValue(this.outreachConfigurationRate1[0].payAfterThresholdInd);
      this.outReachConfigForm.controls['payBasedOn'].setValue(this.outreachConfigurationRate1[0].payBasedOn);
      this.outReachConfigForm.controls['configurationAmount'].setValue(this.outreachConfigurationRate1[0].configurationAmount);
      this.outReachConfigForm.controls['configurationThreshold'].setValue(this.outreachConfigurationRate1[0].configurationThreshold);
      // this.outReachConfigForm.controls['outreachPayConfigurationTypeId'].setValue(this.outreachConfigurationRate1[0].outreachPayConfigurationTypeId);
      // this.outReachConfigForm.controls['activeInd'].setValue(this.outreachConfigurationRate1[0].activeInd);
  }


  createTable() {
    let tableArr: Element[] = [];
    for (let i: number = 0; i <= this.activeOutreachPayConfigurationType.length - 1; i++) {
      let currentRow = this.activeOutreachPayConfigurationType[i];
      if(i==0){
      this.tableArr[0] =this.activeOutreachPayConfigurationType[0];
      }
      tableArr.push({
        outreachPayConfigurationTypeName: currentRow.outreachPayConfigurationTypeName,
        outreachPayConfigurationId: currentRow.outreachPayConfigurationId,
        outreachPayConfigurationTypeId: currentRow.outreachPayConfigurationTypeId,
        payAfterThresholdInd: currentRow.payAfterThresholdInd ? "After" : "Before",//currentRow.payAfterThresholdInd,
        payBasedOn: currentRow.payBasedOn,
        configurationAmount: this.currencyPipe.transform(currentRow.configurationAmount, "USD", true, "1.2-2"),
        //  configurationAmount: currentRow.configurationAmount,
        configurationThreshold: currentRow.configurationThreshold,
        effectiveEndDate: this.datePipe.transform(currentRow.effectiveEndDate),
        effectiveStartDate: this.datePipe.transform(currentRow.effectiveStartDate)
      });
    }
    this.dataSource = new MatTableDataSource(tableArr);
    this.originalDataSource = tableArr;
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }

  searchForItem(): void {
    let filteredResults: Element[] = [];
    if (this.searchText == '') {
      this.dataSource = new MatTableDataSource(this.originalDataSource);
      this.dataSource.sort = this.sort;
      this.dataSource.paginator = this.paginator;
    } else {
      // filteredResults = this.originalDataSource.filter(option => option.salesTerritory.toLowerCase().includes(this.searchText));
      filteredResults = this.pipe.transform(this.originalDataSource, this.searchText);
      this.dataSource = new MatTableDataSource(filteredResults);
      this.dataSource.sort = this.sort;
      this.dataSource.paginator = this.paginator;
    }
  }
}


export interface Element {
  outreachPayConfigurationTypeName: string,
  outreachPayConfigurationId: number,
  outreachPayConfigurationTypeId: number,
  payAfterThresholdInd: string;
  payBasedOn: string,
  // activeInd: string,
  configurationAmount: string,
  configurationThreshold: number,
  effectiveEndDate: string,
  effectiveStartDate: string
}

