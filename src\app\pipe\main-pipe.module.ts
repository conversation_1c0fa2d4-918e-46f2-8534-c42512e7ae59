import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TableFilterPipe } from './table-filter.pipe';
import { TimezoneDatePipe } from './timezone-date.pipe';
import { DatezonePipe } from './datezone.pipe';

@NgModule({
  declarations: [
    TableFilterPipe,
    TimezoneDatePipe,
    DatezonePipe
  ],
  imports: [
    CommonModule
  ],
  exports: [
    TableFilterPipe,
    TimezoneDatePipe,
    DatezonePipe
  ]
})
export class MainPipeModule { }
