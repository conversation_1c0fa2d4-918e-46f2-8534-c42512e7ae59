<div class="dailog-title-bg">
    <div class="dailog-title">Add Base Pay Structure<button class="dailog-close"
            [mat-dialog-close]><span>X</span></button></div>
</div>

<div class="form-group row">
<div class="col-md-4">
    <div class="row">
        <div class="col-10">
            <select class="custom-select hover" [(ngModel)]="selectedRuleId" (change)="onSelect()">
                <option [value]="null">Base Pay Structure</option>
                <option *ngFor="let rule of data.basePayStructureRules[0].rules" [value]="rule.ruleId">
                    {{rule.ruleName}}</option>
            </select>
        </div>
    </div>
</div>

<div class="col-md-6">
    <div class="row">
        <label class="col-4" style="margin-top: 5px;">Base Pay Rule Mapping</label>
        <div class="col-7">
            <select class="custom-select hover" [(ngModel)]="selectedBasePayRuleId" (change)="onSelect()">
                <option *ngFor="let rule of data.basePayRules" [value]="rule.ruleId">
                    {{rule.ruleName}}</option>
            </select>
        </div>
    </div>
</div>
</div>

<!-- Rule Preview -->
<!-- Prompts -->
<div class="row mt-3" *ngIf="prompts">
    <div class="col-md-12">
        <!-- <ng-container *ngFor="let prompt of prompts"> -->
        <!-- <ng-container
                        *ngIf="prompt.ruleTypeName == 'Base Pay Structure'; else elseBlock"> -->
        <ng-container
            *ngIf="data.basePayStructureRules && data.basePayStructureRules[0].ruleTypeName == 'Base Pay Structure'">
            <app-base-pay-structure-prompt [(rule)]="prompts" (rulePrompt)="onRulePromptChange($event)">
            </app-base-pay-structure-prompt>
        </ng-container>
        <!-- </ng-container>
                    <ng-template #elseBlock>
                <ng-container
                    *ngIf="data.employeeIncentiveRules && data.employeeIncentiveRules[0].ruleTypeName == 'Employee Incentive'">
                    <app-employee-incentive-prompt [(rule)]="prompts" (rulePrompt)="onRulePromptChange($event)">
                    </app-employee-incentive-prompt>
                </ng-container>
                </ng-template> -->
        <!-- </ng-container> -->
    </div>
</div>
<div class="text-right">
    <p style="color: red" *ngIf="basePayStructurePrompt != null && basePayStructurePrompt != undefined && !checkValidDates()">Dates must be in chronological order</p>
    <p style="color: red" *ngIf="basePayStructurePrompt != null && basePayStructurePrompt != undefined && !checkAllPromptsEntered()">All prompts must be filled</p>
    <p style="color: red" *ngIf="!checkBasePayRuleMappingProvided()">Base Pay Rule Mapping must be provided</p>
</div>
<div class="row justify-content-end">
    <button class="btn btn-primary" [mat-dialog-close]="addOn"
        [disabled]="!checkAllPromptsEntered() || !checkValidDates() || !checkBasePayRuleMappingProvided()"><i class="fas fa-check"></i> OK</button>
</div>