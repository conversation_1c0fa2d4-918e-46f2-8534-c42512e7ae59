<div class="opportunity-list-container tab-light-bg slide-toggle-btn">
    <button mat-flat-button [className]="show ? 'tab-toggle tab-expanded' : 'tab-toggle tab-collapsed'" color="primary" class="slide-toggle-btn" (click)="onClick()">{{header}}<mat-icon>{{icon}}</mat-icon></button>
    <ng-container *ngIf="rows && show">
        <div class="toggle-container">
        <div class="opportunity-grid-list row">
            <div class="opportunity-grid-tile col-md-6" *ngFor="let row of rows">
                <div class="row ">
                    <label class="col-5">{{row.key}}: </label>
                    <!-- <ng-container *ngIf="row.link; else elseBlock">
                        <a class="opportunity-row-item" [routerLink]="[row.route]">{{row.value}}</a>
                    </ng-container> -->
                    <!-- <ng-template #elseBlock>
                        <p class="opportunity-row-item no-hover-effect">{{row.value}}</p>
                    </ng-template> -->

                    <ng-container *ngIf="row.link;else second">
                        <div class="col-7">
                            <a *ngIf="row.key=='SFDC Opp ID';else others" [href]="[row.route]" target="_blank">{{row.value}}</a>
                            <ng-template #others>
                                <a *ngIf="!enableEdit; else contacts" [routerLink]="[row.route]">{{row.value}}</a>
                            </ng-template>
                        </div>
                    </ng-container>
                    <ng-template #second>
                        <ng-container *ngIf="row.key==='Opportunity Finalized' || row.key==='Appointment Completed' || row.key == 'Appointment Confirmed';else third">                            
                            <ng-container *ngIf="!enableEdit || row.key === 'Opportunity Finalized'; else editBool">
                                <div class="col-7">
                                    <mat-checkbox  [(ngModel)]="row.value" [disabled]="true"> </mat-checkbox>
                                </div>
                            </ng-container>
                        </ng-container>
                    </ng-template>
                    <ng-template #third>
                        <div class="col-7" *ngIf="!enableEdit || row.key === 'Opportunity Name'; else edit">{{row.value}}</div>
                    </ng-template>
                    <ng-template #contacts>
                        <input id="typeahead-prevent-manual-entry" type="text" class="custom-select" [ngModel]="getContactObject(row.editValue)"
                            (selectItem)="editFieldValue(row.key, $event, 'dropdown')" [ngbTypeahead]="searchContacts" [inputFormatter]="formatter"
                            [editable]="false" [resultTemplate]="rt" placeholder="Type to search" />
                    </ng-template>     
                    <ng-template #rt let-r="result">
                        <div class="col-sm-12" style="width: 80%">
                            {{ r.contactName }}
                        </div>
                    </ng-template>
                    <ng-template #editBool>
                        <div class="col-7">
                            <mat-checkbox [(ngModel)]="row.value" (change)="editFieldValue(row.key, $event)"> </mat-checkbox>
                        </div>
                    </ng-template>                    
                    <ng-template #edit>
                        <div class="col-7">
                            <!-- Inside Sales Campaign Dropdown -->                            
                            <select class="custom-select" *ngIf="row.key === 'Inside Sales Campaign'"
                                [value]="row.editValue ? row.editValue : -1" (change)="editFieldValue(row.key, $event, 'dropdown')">
                                <option disabled value="-1">Not Selected</option>
                                <option disabled value="-1">{{row?.editValue}}</option>                                
                                <option *ngFor="let option of insideSalesCampaignOptions" [value]="option.campaignId"
                                    [selected]="option.campaignId === row?.editValue">
                                    {{ option.campaignName }}
                                </option>
                            </select>
                    
                            <!-- Roofing Finance Partner Dropdown -->
                            <select class="custom-select" *ngIf="row.key === 'Roofing Finance Partner'"
                                [value]="row.editValue ? row.editValue : -1" (change)="editFieldValue(row.key, $event, 'dropdown')">
                                <option disabled value="-1">Not Selected</option>
                                <option *ngFor="let option of roofingFinancePartnerOptions" [value]="option.financePartner1"
                                    [selected]="option.financePartner1 === row.value">
                                    {{ option.financePartner1 }}
                                </option>
                            </select>
                    
                            <!-- Fallback: Text or Date Input -->
                            <input type="date"
                                *ngIf="row.isDate && row.key !== 'Inside Sales Campaign' && row.key !== 'Roofing Finance Partner'"
                                class="custom-input" [(ngModel)]="row.editValue" (input)="editFieldValue(row.key, $event)">
                            <input type="text"
                                *ngIf="!row.isDate && row.key !== 'Inside Sales Campaign' && row.key !== 'Roofing Finance Partner'"
                                class="custom-input" (input)="editFieldValue(row.key, $event)" [disabled]="row.key === 'Previous Trinity Salesperson' || row.key === 'Date Quoted' || row.key === 'Roof Date Quoted' || row.key === 'Battery Date Quoted' || row.key === 'Battery Adder Amount'"
                                [value]="row.editValue ? row.editValue : (row.value ? row.value : '')" />
                        </div>
                    </ng-template>
                </div>
            </div>
            <div class="opportunity-grid-tile col-md-6" *ngIf="isRoof">
                <div class="row ">
                    <label class="col-5">Slope Type: </label>                    
                        <div class="col-2">Low:<mat-checkbox class="chk-bx" [disabled]="true" [(ngModel)]="slopeType.lowSlope"> </mat-checkbox>
                        </div>
                        <div class="col-3 lbl-chk">Standard:<mat-checkbox class="chk-bx" [disabled]="true" [(ngModel)]="slopeType.normalSlope"> </mat-checkbox>
                        </div>
                </div>                
            </div>
        </div></div>
    </ng-container>
</div>