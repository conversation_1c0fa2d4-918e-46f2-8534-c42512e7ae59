<div class="container-fluid">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header-info">
                    <h4 class="card-title">Kilowatts by Divisions and States</h4>
                </div>
                <div class="row">
                  <ul class="pl-5 pt-2">
                    <li class="text-info">Data of the opportunities, system size with actual install date during the selected date range</li>
                  </ul>
              </div>
                <div class="card-body">
                    <mat-table #table [dataSource]="paymentDataSource" matSort #sort="matSort" (matSortChange)="onSortChange($event)">
                        <ng-container matColumnDef="{{column.id}}" *ngFor="let column of columnNames">
                          <mat-header-cell *matHeaderCellDef mat-sort-header class="table-header"> {{column.value}}
                          </mat-header-cell>
                          <mat-cell [attr.data-td-head]="column.value" *matCellDef="let element">
                            <ng-container *ngIf="column.value == 'Opportunity Name'; else contactName">
                              <a [routerLink]="['/ui/commissions/opportunitydetails', element.opportunityId]">{{element[column.id]}}</a>
                            </ng-container>
                            <ng-template #contactName>
                              <ng-container *ngIf="column.value == 'Contact Legal Name'; else amount">
                                <a [routerLink]="['/ui/commissions/salesrep', element.contactId]">{{element[column.id]}}</a>
                              </ng-container>
                            </ng-template>
                            <ng-template #amount>
                              <ng-container *ngIf="column.value == 'Amount'; else common">
                                {{element[column.id] |currency:'USD':'symbol':'1.2-2'}}
                              </ng-container>
                            </ng-template>
                            <ng-template #common>
                              {{element[column.id]}}
                            </ng-template>
                          </mat-cell>
                        </ng-container>
                        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
                        <mat-row *matRowDef="let row; columns: displayedColumns;" class="pointer table-content"
                          ></mat-row>
                      </mat-table>
                      <div class="text-center p-2" *ngIf="originalDataSource && originalDataSource.length === 0">
                        <h5>No Data Found</h5>
                    </div>
                      <mat-paginator #paginator [pageSizeOptions]="[10, 20]" showFirstLastButtons></mat-paginator>                
                </div>
            </div>
        </div>
        <div class="col-md-4">
          <ng-container *ngIf="salesDivisionChartData?.length !==0">
            <div class="row justify-content-center">    
              <div [chart]="salesDivisionChart"></div>
            </div>
          </ng-container> 
          <ng-container *ngIf="salesDivisionChartData?.length ==0">
            <div class="row justify-content-center h-50">
              <div >
                    <h5 class="business-chart-title">Sales Division</h5> 
                    <p>No Chart to Display</p>
                  </div>
            </div>
          </ng-container>
          <ng-container *ngIf="salesTerittoryChartData?.length !==0">
            <div class="row justify-content-center">        
              <div [chart]="salesTerittoryChart"></div>
            </div>
          </ng-container>
          <ng-container *ngIf="salesTerittoryChartData?.length == 0">
            <div class="row justify-content-center">
              <div>
              <h5 class="business-chart-title">States</h5> 
              <p>No Chart to Display</p>
            </div> 
            </div>
          </ng-container>        
        </div>
    </div>
  </div>