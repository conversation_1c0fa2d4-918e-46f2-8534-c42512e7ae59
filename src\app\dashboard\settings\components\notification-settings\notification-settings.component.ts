import { Component, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { ApiService } from 'src/app/services/api.service';

@Component({
  selector: 'app-notification-settings',
  templateUrl: './notification-settings.component.html',
  styleUrls: ['./notification-settings.component.css']
})
export class NotificationSettingsComponent implements OnInit {
  notificationPreferenceForm: UntypedFormGroup;
  frequencyList =[
    {value:0,name:'On Log In'},
    {value:5,name:'Every 5 minutes'},
    {value:30,name:'Every 30 minutes'},
    {value:60,name:'Every 60 minutes'},
  ]
  notificationTypeList:any;
  selectedItems:any[] = [];
  notificationData:any[] =[];
  currentUser:any;
  constructor(public apiService: ApiService,private toastMsg: ToastrService,private formBuilder: UntypedFormBuilder) { }

  ngOnInit() {
    this.currentUser= +JSON.parse(localStorage.getItem('currentUser')).empId;   
    this.apiService.show(); 
    this.createForm();
    this.getPreferenceData();
    this.getNotificationType();
  }

  createForm(){
    this.notificationPreferenceForm = this.formBuilder.group({
      Preferences:[],
      notificationfrequency:[0],
      EmpId:this.currentUser
    });
  }
  getNotificationType(){
    this.apiService.get('Notifications/GetNotificationsTypes')
      .subscribe(data => {
        this.notificationTypeList = data.result;
      }, (err: any) => {
        this.toastMsg.error(err, 'Server Error!');
      });
  }
  getPreferenceData(){    
    this.apiService.get('Notifications/GetNotificationsPreferences?EmpId='+this.currentUser+'')
    .subscribe(data => {      
      if(data.result && data.result.length > 0){
        this.selectedItems = data.result[0].notificationTypeIds;
        this.notificationPreferenceForm.patchValue({
          notificationfrequency:0
        });
      }
      this.apiService.hide(); 
    }, (err: any) => {
      this.apiService.hide(); 
      this.toastMsg.error(err, 'Server Error!');
    });
  }
  onSave(){
    this.apiService.show(); 
    let preferenceData:any[] =[];
    this.notificationTypeList.forEach(item=>{
      preferenceData.push({notificationtypeid:item.notificationTypeId,activeInd:this.selectedItems.includes(item.notificationTypeId)?1:0})
    })
    this.notificationPreferenceForm.patchValue({
      Preferences: preferenceData,
      notificationfrequency:Number(this.notificationPreferenceForm.controls.notificationfrequency.value)
    });    
    this.apiService.post('Notifications/SavePreferences', this.notificationPreferenceForm.value)
        .subscribe(data => {
          this.apiService.hide(); 
          if (data["statusCode"] === "201" && data.result) {
            this.toastMsg.success("Preference Saved Successfully.", 'Success!') ;

          } else {
            this.toastMsg.error("Update.", 'Server Error!')
          }
        }, (err: any) => {       
          this.apiService.hide();    
          this.toastMsg.error(err.message, 'Server Error!')
        });
  }
  onCheckboxChange(item:any){
    const index = this.selectedItems.indexOf(item.notificationTypeId);
    if (index === -1) {
      this.selectedItems.push(item.notificationTypeId);
    } else {
      this.selectedItems.splice(index, 1);
    }
  }
  

}
