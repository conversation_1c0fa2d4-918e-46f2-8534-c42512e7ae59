<!-- Commission History -->
<div class="slide-toggle-btn tab-light-bg mb-3">
        <button [className]="showCommissionHistory ? 'tab-toggle tab-expanded' : 'tab-toggle tab-collapsed'"   mat-flat-button color="primary"  (click)="showCommissionHistory = !showCommissionHistory"><i class="fas fa-history"></i> Commission
            History<i
                class="material-icons tab-icons">{{showCommissionHistory ? 'remove_circle_outline' : 'add_circle_outline'}}</i></button>
        <div [hidden]="!showCommissionHistory"  class="toggle-container">
            <table mat-table [dataSource]="commissionHistoryElements" matSort class="my-table w-100">
                <ng-container matColumnDef="userModifiedTimestamp">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header> Date </th>
                    <td mat-cell data-td-head="Date" *matCellDef="let element"> {{element.userModifiedTimestamp | date:'short'}} </td>
                </ng-container>

                <ng-container matColumnDef="commissionAmount">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header> Amount </th>
                    <td data-td-head="Amount"  mat-cell *matCellDef="let element"> {{element.commissionAmount | currency}} </td>
                </ng-container>

                <ng-container matColumnDef="commissionFinalized">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header> Finalized </th>
                    <td data-td-head="Finalized"  mat-cell *matCellDef="let element"> {{element.commissionFinalized}} </td>
                </ng-container>

                <ng-container matColumnDef="commissionOverridden">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header> Overridden </th>
                    <td data-td-head="Overridden"  mat-cell *matCellDef="let element"> {{element.commissionOverridden}} </td>
                </ng-container>

                <ng-container matColumnDef="commissionNote">
                    <th class="mat-column-25" mat-header-cell *matHeaderCellDef mat-sort-header> Commission Note </th>
                    <td data-td-head="Commission Note"  mat-cell *matCellDef="let element"> {{element.commissionNote}} </td>
                </ng-container>

                <tr mat-header-row *matHeaderRowDef="commissionHistoryColumns"></tr>
                <tr mat-row *matRowDef="let row; columns: commissionHistoryColumns;"></tr>
            </table>
            <mat-paginator [pageSizeOptions]="pageSizeOptions">
            </mat-paginator>
        </div>
   