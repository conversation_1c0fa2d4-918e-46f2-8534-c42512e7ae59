<div class="dailog-title-bg">
  <div class="dailog-title"><i class="fas fa-dollar-sign"></i> Monthly Overrides<button class="dailog-close"
          [mat-dialog-close]><span>X</span></button>
  </div>
</div>
    <div class="col-md-12">
      <div class="input-group float-left date-picker table-filters">
        <input #monthPicker1 class="custom-input" type="month" (change)="onMonthChange(monthPicker1.value)" />
        <span *ngIf="monthPicker1.value.length > 0" class="mat-icon cal-reset"  (click)="clearDate(monthPicker1)"><i class="far fa-calendar-times"></i></span> 
        <span *ngIf="monthPicker1.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span> 
      </div>
      <div class="input-group float-right table-filters">
        <input class="custom-input" type="text" id="searchTextId" [(ngModel)]="searchText" name="searchText" placeholder="Search" (input)="searchForItem(monthPicker1)">
        <span class="input-group-icon">
            <i class="fas fa-search"></i>
        </span>
      </div>
    </div>
    <div class="float-left"><b><u>Employee Name</u>: {{employeeName}}</b></div>
    <div style="margin-top: 85px;">
        <mat-table #table [dataSource]="dataSource" matSort>
            <ng-container matColumnDef="{{column.id}}" *ngFor="let column of columnNames">
            <mat-header-cell *matHeaderCellDef mat-sort-header class="table-header"> {{column.value}} </mat-header-cell>
            <mat-cell  [attr.data-td-head]="column.value"   *matCellDef="let element"> {{element[column.id]}} </mat-cell>
            </ng-container>
            <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
            <mat-row *matRowDef="let row; columns: displayedColumns;" class="pointer table-content"></mat-row>
        </mat-table>
        <div class="float-right"><b><u>Total</u>: {{totalAmount}}</b></div>
        <div style="margin-top: 30px;"><mat-paginator [pageSizeOptions]="[5, 10, 20, 50]" showFirstLastButtons></mat-paginator></div>
    </div>