.checkbox-section {
  display: flex;
  align-content: center;
  align-items: center;
}

.checkbox-margin {
    margin: 0 10px;
}

.checkbox-all-margin {
  margin-left: 25px;
  margin-right: 10px;
  margin-top: 3px;
}
.pre-extract-save {
  display: flex;
  justify-content: flex-end;
}
.pay-selected {
 width:100%; float:left;

}
.naming-column {
  width: 35%;
}

.mat-column-width {
  width: 10%;
}
.hover-withdrawal:hover {
  cursor: pointer;
  color: #369DD6;
}
::ng-deep.cdk-focused, .cdk-mouse-focused {   
  outline: none; /* Remove the outline */
  box-shadow: none; /* Remove any box-shadow or other styles */    
}
.fa-arrow-up , .fa-arrow-down{ 
  margin-right: 20px;
  font-size: 10px;
  color: gray;
  margin-bottom: 2px;
}
::ng-deep.mat-header-cell{
  cursor: pointer;
}
.overflow_scroll{
  overflow-x: scroll;
}