import { Component, OnInit, Inject } from '@angular/core';
import { ApiService } from '../../../services/api.service';
import { ToastrService } from 'ngx-toastr';
import { MatLegacyDialogRef, MAT_LEGACY_DIALOG_DATA } from '@angular/material/legacy-dialog';

@Component({
  selector: 'app-product-rate-dialog',
  templateUrl: './product-rate-dialog.component.html',
  styleUrls: ['./product-rate-dialog.component.css']
})
export class ProductRateDialogComponent implements OnInit {
  productRateGroup: Element[] = [];
  constructor(public dialogRef: MatLegacyDialogRef<ProductRateDialogComponent>,
       private apiService: ApiService, private toastMsg: ToastrService, @Inject(MAT_LEGACY_DIALOG_DATA) public data: any) 
  {} 

  ngOnInit() {    
    this.productRateGroup = this.data.productRate;
    this.productRateGroup.sort((a, b) => {
      return <any>new Date(b.effectiveStartDate) - <any>new Date(a.effectiveStartDate);
    });
  }

}

export interface Element {
  salesTerritory: string,
  productType: string,
  effectiveStartDate: string,
  effectiveEndDate: string,
  minimumCommission: string,
  floorRate: string,
  baseRate: string,
}





