import { Component, Input, OnInit } from '@angular/core';
import { ApiService } from '../../../services/api.service';
import { ToastrService } from 'ngx-toastr';
import { DatePipe } from '@angular/common';
import { IdateRange } from '../../models/models';

interface CustomPoint extends Highcharts.Point {
  custom: any;
}
@Component({
  selector: 'app-scheduled-jobs',
  templateUrl: './scheduled-jobs.component.html',
  styleUrls: ['./scheduled-jobs.component.css']
})
export class ScheduledJobsComponent implements OnInit {

  @Input() dateRange: IdateRange | null = null;

  @Input() tabNumber: number | null = null;

  previousDateRange: IdateRange | null = null;

  jobData: any ;
//   {
//     "jobs": [
//         {
//             "jobName": "Data Integration",
//             "dayStatus": {
//                 "Friday": [
//                     2,
//                     1
//                 ],
//                 "Saturday": [
//                   0,
//                   2
//               ],
//                 "Sunday": [
//                     0,
//                     3
//                 ],
//                 "Monday": [
//                     0,
//                     1
//                 ],
//                 "Tuesday": [
//                     0,
//                     1
//                 ],
//                 "Wednesday": [
//                     0,
//                     1
//                 ],
//                 "Thursday": [
//                     0,
//                     1
//                 ]
//             }
//         }
//     ]
// }
  
  

  constructor(
    private apiService: ApiService,
    private toastMsg: ToastrService,
    private datePipe: DatePipe
  ) { }

  ngOnInit() { }

  ngOnChanges() {
    if (this.tabNumber === 1) {
      if (this.dateRange) {
        if (
          this.previousDateRange === null ||
          this.previousDateRange !== this.dateRange
        ) {
          this.previousDateRange = this.dateRange;
          const startDate: any = new Date(this.dateRange.startDate);
          const endDate: any = new Date(this.dateRange.endDate);
          const dateDifference = endDate - startDate;
          const daysDifference =
            dateDifference / (1000 * 60 * 60 * 24);
          if (daysDifference > 7) {
            this.toastMsg.warning(
              "Only Week range is supported On scheduled job status",
              "Warning"
            );
            return;
          }
          this.getJobs();
        }
      }
    }
  }

  getJobs() {
    this.apiService.get(`BusinessDashboard/ScheduledJobStatusSummary?toDate=${this.dateRange.endDate}&fromDate=${this.dateRange.startDate}`)
      .subscribe((res: any) => {
        this.jobData = res;        
      }, (err: any) => {
        this.toastMsg.error(err.message, "Server Error!");
      });
  }

  getDateRange(): Date[] {
    const startDate = new Date(this.dateRange.startDate);
    const endDate = new Date(this.dateRange.endDate);
    const dateRange: Date[] = [];
    for (
      let current = startDate;
      current <= endDate;
      current.setDate(current.getDate() + 1)
    ) {
      dateRange.push(new Date(current));
    }
    return dateRange;
  }
}

