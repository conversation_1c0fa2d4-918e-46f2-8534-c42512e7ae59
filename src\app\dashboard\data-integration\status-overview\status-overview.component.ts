import { Component, OnInit, Output, EventEmitter } from '@angular/core';
import { ApiService } from '../../../services/api.service';
import { ToastrService } from 'ngx-toastr';
import { IJob } from '../../../model/job.model';
import { IDataIntegrationOverview } from '../../../model/data-integration-overview.model';

@Component({
  selector: 'app-status-overview',
  templateUrl: './status-overview.component.html',
  styleUrls: ['./status-overview.component.css']
})
export class StatusOverviewComponent implements OnInit {
  jobs: IJob[];
  overview: object;
  selectedJobId: number;
  p: number = 1;
  @Output() jobsStatus = new EventEmitter();

  constructor(private apiService: ApiService, private toastMsg: ToastrService) { }

  ngOnInit() {
    this.getJobs();
    this.getStatusOverview();
  }

  refresh() {
    this.getJobs();
    this.getStatusOverview();
  }

  getJobs() {
    let jobsStatus = {};
    this.apiService.get('DataIntegration/Jobs')
      .subscribe((data: any) => {
        this.jobs = data.map(job => {
          return <IJob>{
            diJobId: job.diJobId,
            diJobTypeName: job.diJobTypeName,
            jobStartTimestamp: job.jobStartTimestamp,
            jobEndTimestamp: job.jobEndTimestamp,
            jobStatus: job.jobStatus
          };
        });

        this.jobs.forEach(job => {
          jobsStatus[job.diJobTypeName] = jobsStatus[job.diJobTypeName] || [];
          jobsStatus[job.diJobTypeName].push(job.jobStatus);
        });

        this.jobsStatus.emit(jobsStatus);
      }, (err: any) => {
        this.toastMsg.error(err.message, "Server Error!");
      });
  }

  getStatusOverview() {
    this.apiService.get('DataIntegration')
      .subscribe((data: any) => {
        this.overview = data.reduce((a, b) => {
          let ovr = <IDataIntegrationOverview>{
            diJobExtractComponentId: b.diJobExtractComponentId,
            diJobId: b.diJobId,
            diExtractComponentName: b.diExtractComponentName,
            jobComponentStartTimestamp: b.jobComponentStartTimestamp,
            jobComponentEndTimestamp: b.jobComponentEndTimestamp,
            jobComponentStatus: b.jobComponentStatus,
            batchCount: b.batchCount
          };
          (a[ovr.diJobId] = a[ovr.diJobId] || []).push(ovr);
          return a;
        }, {});
      }, (err: any) => {
        this.toastMsg.error(err.message, "Server Error!");
      });
  }

  onRowClick(jobId: number) {
    this.selectedJobId = jobId;
  }

}
