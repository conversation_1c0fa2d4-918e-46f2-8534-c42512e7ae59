<div class="dailog-title-bg">
    <div class="dailog-title"><i class="fas fa-history"></i> History<button class="dailog-close"
        [mat-dialog-close]><span>X</span></button>
    </div>
  </div>
  <div class="row" *ngIf="roleRateGroup">
  
  
    <div class="col-md-6">
      <div class="row">
        <label class="col-sm-5">Role</label>
        <span class="col-sm-7">{{roleRateGroup[0][0].role}}</span>
      </div>
    </div>
    <div class="col-md-6">
      <div class="row">
        <label class="col-sm-5">Tier</label>
        <span class="col-sm-5">{{roleRateGroup[0][0].tierName}}</span>
      </div>
    </div>
    <div class="col-md-6">
      <div class="row">
        <label class="col-sm-5">State</label>
        <span class="col-sm-7">{{roleRateGroup[0][0].stateCode}}</span>
      </div>
    </div>
    <div class="col-md-6">
      <div class="row">
        <label class="col-sm-5">Amount</label>
        <span class="col-sm-7">{{roleRateGroup[0][0].amount  | currency}}</span>
      </div>
    </div>
    <div class="col-md-12">
      <table class="my-table mat-table w-100 mt-3">
        <thead>
          <tr class="mat-header-row">
            <th class="mat-header-cell" scope="col">Effective Start Date</th>
            <th class="mat-header-cell" scope="col">Effective End Date</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let tr of roleRateGroup">
            <tr class="mat-row" (click)="groupClick(tr)">
              <td data-td-head="Effective Start Date" class="mat-cell">{{tr[0].effectiveStartDate | date}}</td>
              <td data-td-head="Effective End Date" class="mat-cell">{{tr[0].effectiveEndDate | date}}</td>
            </tr>
            <td colspan="2" style="background-color: #FFF;"
              *ngIf="roleSelectedGroup && tr[0].effectiveStartDate == roleSelectedGroup[0].effectiveStartDate">
  
              <table class="my-table mat-table w-100 mt-3">
                <thead>
                  <tr class="mat-header-row">
                    <th class="mat-header-cell" scope="col">Role</th>
                    <th class="mat-header-cell" scope="col">Tier</th>
                    <th class="mat-header-cell" scope="col">State</th>
                    <th class="mat-header-cell" scope="col">Amount</th>
                  </tr>
                </thead>
                <tbody>
                  <tr class="mat-row" *ngFor="let tr of roleSelectedGroup">
                    <td data-td-head="PPA Rate" class="mat-cell"> {{tr.role}}</td>
                    <td data-td-head="tierName" class="mat-cell"> {{tr.tierName}}</td>
                    <td data-td-head="stateCode" class="mat-cell"> {{tr.stateCode}}</td>
                    <td data-td-head="amount" class="mat-cell"> {{tr.amount | currency}}</td>
                    <td data-td-head="effectiveStartDate" class="mat-cell"> {{tr.effectiveStartDate | date}}</td>
                    <td data-td-head="effectiveEndDate" class="mat-cell"> {{tr.effectiveEndDate | date}}</td>
                  </tr>
                </tbody>
              </table>
            </td>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>