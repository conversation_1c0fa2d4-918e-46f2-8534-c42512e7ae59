.notification-list {
    width: 98% !important;
    /* margin: 20px auto; */
    border: 1px solid #ccc;
    background-color: white;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    margin-left: 1.5%;
  }

  .notification {
    padding: 10px;
    /* border-bottom: 1px solid #ccc; */
    display: flex;
    align-items: center;
  }

  .notification img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;
  }

  .notification-content {
    flex: 1;
  }

  .notification-title {
    font-weight: bold;
  }  

  ::ng-deep .mat-dialog-container{
    border-radius: 10px !important;
    overflow-x: hidden;
    overflow-y: hidden;
    max-height: 100vh;
    border: 1px solid #ccc;
    box-shadow: none;
  }
  .notification-pan{
    max-height: 70vh;
    overflow-y: scroll;
    overflow-x: hidden;
    height: 70vh;
  }
  .dots {
    height: 10px;
    width: 10px;
    background-color: #369dd6;
    border-radius: 50%;
    display: inline-block;
  }
  ::ng-deep .mat-progress-spinner{
    margin-left: 7.5rem;
    margin-top: 12rem;
  }
  .no-dispaly-msg{
    padding-top: 64%;
    padding-left: 17%;
    padding-bottom: 72%;
  }
  .setting-icon{
    cursor: pointer;
  }
  .loader-text {
    color: #408bc0;
  }
  .setting-icon{
    cursor: pointer;
  }
  .loader {
    border: 6px solid #0073b1; /* LinkedIn blue color */
    border-top: 6px solid transparent;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}