<div class="card">
    <div class="card-header-info">
        <h6 class="card-title"><i class="fas fa-puzzle-piece"></i> Add-Ons</h6>
    </div>
    <div class="card-body">

        <mat-tab-group>
            <mat-tab>
                <ng-template matTabLabel>
                    <span matBadge="{{employeeIncentives.length}}" matBadgeOverlap="false">Employee Incentives</span>
                </ng-template>
                <app-addon-table [addons]="employeeIncentives" [columns]="standardColumns" (addonsOutput)="onEiChange($event)"></app-addon-table>
                <!-- <ng-container *ngFor="let addon of employeeIncentives">
                    <p>{{addon.ruleName}}</p>
                </ng-container> -->
            </mat-tab>
            <mat-tab>
                <ng-template matTabLabel>
                    <span matBadge="{{basePayStructures.length}}" matBadgeOverlap="false">Base Pay Structure</span>
                </ng-template>
                <app-addon-table [addons]="basePayStructures" [columns]="basePayColumns" [basePayRules]="basePayRules" (addonsOutput)="onBpsChange($event)"></app-addon-table>
                <!-- <ng-container *ngFor="let addon of basePayStructures">
                    <p>{{addon.ruleName}}</p>
                </ng-container> -->
            </mat-tab>
            <mat-tab>
                <ng-template matTabLabel>
                    <span matBadge="{{paymentBooks.length}}" matBadgeOverlap="false">Payment Book</span>
                </ng-template>
                <app-addon-table [addons]="paymentBooks" [columns]="standardColumns" (addonsOutput)="onPbChange($event)"></app-addon-table>
                <!-- <ng-container *ngFor="let addon of paymentBooks">
                    <p>{{addon.ruleName}}</p>
                </ng-container> -->
            </mat-tab>
            <mat-tab>
                <ng-template matTabLabel>
                    <span matBadge="{{paymentBookSchedules.length}}" matBadgeOverlap="false">Payment Book Schedule</span>
                </ng-template>
                <app-addon-table [addons]="paymentBookSchedules" [columns]="standardColumns" (addonsOutput)="onPbsChange($event)"></app-addon-table>
            </mat-tab>
        </mat-tab-group>

    </div>
</div>