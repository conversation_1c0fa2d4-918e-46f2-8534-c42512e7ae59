.custom-table {

    width: 100%;
    background: #efefef;
    white-space: nowrap;
}

.custom-thead {
    font-size: 1.2em;
    font-weight: bolder;
    background-color: #F3F3F3;
    white-space: nowrap;
}

._ngcontent-vpg-c15 {
    white-space: nowrap;
    width: 40%;

}

.custom-tr {
    display: flex;
    justify-content: space-between;
    width: 80%;
    border-top: 1px solid lightgrey;
}

.custom-td {
    text-align: left;
    width: 100%;
    padding: 2px 12px;
    white-space: nowrap;
}

.pointer {
    cursor: pointer;
}

.table-header {
    font-weight: 800;
    color: #5f5f5f;
    /* align-content: flex-start; */
    white-space: nowrap;

}

mat-header-cell {
    display: flex;
    white-space: nowrap;
    /* justify-content:flex-start; */
    white-space: nowrap;


}