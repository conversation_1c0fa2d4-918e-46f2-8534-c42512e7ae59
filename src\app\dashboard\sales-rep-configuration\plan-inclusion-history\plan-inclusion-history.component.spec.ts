import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { PlanInclusionHistoryComponent } from './plan-inclusion-history.component';

describe('PlanInclusionHistoryComponent', () => {
  let component: PlanInclusionHistoryComponent;
  let fixture: ComponentFixture<PlanInclusionHistoryComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ PlanInclusionHistoryComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PlanInclusionHistoryComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
