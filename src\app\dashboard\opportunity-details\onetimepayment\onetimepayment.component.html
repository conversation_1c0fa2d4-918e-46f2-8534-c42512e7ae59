<ng-template #rt let-r="result" let-t="id">

  <div class="col-sm-12" style="width: 80%;">
    {{ r.contactName}}
  </div>
</ng-template>
<div class="card">
  <div class="card-header-info">
    <h4 class="card-title">One Time Payment</h4>
  </div>
  <div class="card-body">
    <form [formGroup]="oneTimePaymentFormGroup" (ngSubmit)="onSubmitOneTimePayment()" class="w-100">
      <div class="row" *ngIf="contacts">
        <div class="form-group col-md-4">
          <div class="row">
            <label class="col-sm-5">Contact</label>
            <div class="col-sm-7">
              <input id="typeahead-prevent-manual-entry" type="text" class="custom-select"
                formControlName="contactId" [ngbTypeahead]="search" [inputFormatter]="formatter" [editable]='false'
                [resultTemplate]="rt" placeholder="Type to search" autocomplete="new-contact" />
            </div>
          </div>
        </div>

        <div class="form-group col-md-4">
          <div class="row">
            <label class="col-sm-5">Commission Rule</label>
            <div class="col-sm-7">
              <select class="custom-select" [(ngModel)]="commissionRuleTypeId" formControlName="commissionRuleTypeId">
                <option [value]="0">--SELECT--</option>
                <option *ngFor="let type of commissionRuleTypes" [value]="type.commissionRuleTypeId">
                  {{type.commissionRuleTypeName}}
                </option>
              </select>
            </div>
          </div>
        </div>
        <div class="form-group col-md-4">
          <div class="row">
            <label class="col-sm-5">Amount</label>
            <div class="col-sm-7">
              <input currencyMask [options]="{ precision: 2 }" name="amount"
                formControlName="amount" class="custom-input" autocomplete="new-amount">
            </div>
          </div>
        </div>
        <div class="form-group col-md-4">
          <div class="row">
            <label class="col-sm-5">Notes</label>
            <div class="col-sm-7">
              <input name="notes"
                formControlName="notes" class="custom-input" placeholder="One Time Payment Notes" autocomplete="new-notes">
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-12 align-button-right">
          <button type="submit" class="btn btn-primary" [disabled]="oneTimePaymentFormGroup.invalid"><i
              class="fas fa-check"></i>
            Pay</button>
        </div>
      </div>
    </form>
  </div>
</div>