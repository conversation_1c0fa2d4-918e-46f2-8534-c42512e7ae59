<div class="container-fluid">
  <div class="row">
      <div class="col-md-8">
          <div class="card">
              <div class="card-header-info">
                  <h4 class="card-title">Reps Without Plans</h4>
              </div>
              <div class="row">
                <ul class="pl-5 pt-2">
                  <li class="text-info">Data of the sales Rep with no active plans assigned</li>
                </ul>
              </div>
              <div class="card-body">
                <app-grid-mat-table [gridData]="originalDataSource"  
                [columnData]="columnNamesAlt" 
                [tableWidth]="'1500px'"
                [displayColumnData]="displayedColumnsRepsWithoutPlans"
                [dateFields]="dateColumns">
            </app-grid-mat-table>
              </div>
          </div>
      </div>
      <div class="col-md-4">
          <div *ngIf="repsWithoutPlansChartData?.length ==0" class="text-center">
            <h5 class="business-chart-title">Reps by Sales Division</h5> 
            <p>No Chart to Display</p>
          </div> 
          <div *ngIf="repsWithoutPlansChartData?.length !=0" [chart]="repsWithoutPlansChart"></div>
      </div>
  </div>
</div>