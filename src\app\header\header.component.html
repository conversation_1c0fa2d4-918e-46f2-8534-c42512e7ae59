
<!-- <app-login-dialog></app-login-dialog> -->
<!-- End Navbar -->

<mat-toolbar class="commissions-navbar header">
  <!-- <a class="trinity-logo-sidebar"><img src="/assets/images/trinity-logo-white-2.png" alt=""></a>-->
  <!--<ng-container>
    <button type="button" mat-button (click)="toggleSidenav()" id="nav-toggle"  [ngClass]="{'toggle-color':sidebarToggle}"  class="menu-toggle"><i
        class="material-icons">{{icon}}</i></button>
        
  </ng-container>  -->


  <span class="spacer"></span>

  <ng-container *ngIf="showSearch">
    <app-search></app-search>
  </ng-container>

  <i class="header-icon material-icons hover" (click)="dashboard()" *ngIf="homeUrl">home</i>
  <span *ngIf="showNoticationIcon" href="javascript:void(0);" aria-haspopup="true" aria-expanded="false" style="display: flex; align-items: center;" [ngClass]="{'low-notifi': notificationCount >=10 && notificationCount < 100,'medium-notifi': notificationCount === 100,'highcount-notifi': notificationCount > 100}" class="hover header-link notification-margin" (click)="openNotificationPopup()">
    <i class="header-icon material-icons">notifications</i> 
    <span *ngIf="notificationCount > 0 && notificationCount <= 100" class="badge notifiy-numb rounded-pill badge-notification bg-danger">{{ notificationCount }}</span>   
    <span *ngIf="notificationCount > 100" class="badge notifiy-numb rounded-pill badge-notification bg-danger">100+</span>   
  </span>
  <span href="javascript:void(0);" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" style="display: flex; align-items: center;" class="hover header-link">
    <i class="header-icon material-icons">person</i>
    {{apiService.getUserName()}}
  </span>
  <span href="javascript:void(0);"    style="display: flex; align-items: center;" class="hover header-link">
    <a target="_blank" [href]="'https://' + outputpathQuestion + '/Pages/ResponsePage.aspx?id=5W4A8Yj4CEOS6vyuvhwLXjKaiBn9X0NDhTwETo8Cr35UQVpIRklSVTNZT0M3U01WMzhJSjFJMTE1Qi4u'" data-toggle="tooltip" data-placement="bottom" title="Support">    
    <mat-icon svgIcon="question-mark" aria-hidden="false" aria-label="Contact Support" class="header-icon material-icons guide-ico"></mat-icon>
    </a>    
  </span>
  <span href="javascript:void(0);"  style="display: flex; align-items: center;" class="hover header-link">
    <a target="_blank" [href]="'https://' + outputpathBook + '/:u:/r/teams/BTIntranet/SitePages/User%20Guides/onePAY/onePAY-User-Guide.aspx?csf=1&web=1&e=aJS7TU&mobileredirect=true&DefaultItemOpen=1'" data-toggle="tooltip" data-placement="bottom" title='User Guide'>      
      <mat-icon svgIcon="book-open" aria-hidden="false" aria-label="User Guide" class="header-icon material-icons guide-ico"></mat-icon>
    </a>  
  </span>
  <div class="dropdown-menu dropdown-menu-right" aria-labelledby="navbarDropdownProfile">
    <a class="dropdown-item" [routerLink]="['logout']">Logout</a>
    <a class="dropdown-item" (click)="openVersionDialog()">Version</a>
    <a class="dropdown-item" (click)="clearCache()">Clear cache</a>
  </div>
</mat-toolbar>