import { ComponentFixture, TestBed } from '@angular/core/testing';

import { RoofingMonthlyOverrideDialogComponent } from './roofing-monthly-override-dialog.component';

describe('RoofingMonthlyOverrideDialogComponent', () => {
  let component: RoofingMonthlyOverrideDialogComponent;
  let fixture: ComponentFixture<RoofingMonthlyOverrideDialogComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ RoofingMonthlyOverrideDialogComponent ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(RoofingMonthlyOverrideDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
