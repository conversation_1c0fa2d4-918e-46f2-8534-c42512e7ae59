<!-- <div class="logo main-head">
		<a [routerLink]="['/']" class="simple-text logo-normal"><img src="./assets/images/Trinity.png" width="100px"
				alt=""></a>
	</div> -->
<div class="page-title col-md-12 ">
	<h1> Create Plan</h1>
	<div class="breadcrumbs"><a href="#">Home</a>/<span>Create Plan</span></div>
</div>

<div class="content">

	<!-- <a *ngIf="clone" class="text-info nav-link" [routerLink] = "['/ui/commissions/viewPlan', planId]">
				<i class="material-icons">arrow_back</i> back</a>
        <a *ngIf="!clone" class="text-info nav-link" [routerLink] = "['/ui/commissions']">
          <i class="material-icons">arrow_back</i> back</a> -->

	<div class="row justify-content-center">
		<div class="col-md ">
			<div class="card">
				<div class="card-header card-header-info">
					<h4 class="card-title"><i class="fas fa-file-signature"></i> Create Plan</h4>
				</div>
				<form action="" method="post" accept-charset="utf-8" class="card-body">
					<div class="row">
						<div class="col-md-6 mb-3">
							<div class="row">
								<label class="col-5">Name : </label>
								<div class="col-7">
									<input type="text" name="rule_name" class="custom-input" value="Plan 1"
										[(ngModel)]="planName">
								</div>
							</div>
						</div>
					</div>
					<div class="row mt-2 ">
						<div class="col col-md col-xl">
							<div class="row">
								<div class="col-md-5">
									<div class="card">
										<div class="card-header-info">
											<h4 class="card-title no-hover-effect">Available</h4>
										</div>
										<div class="card-body scroll-area tooltip1">
											<span class="tooltiptext">Click on the Elements and click on Add
												to add rules to the Cart</span>
											<input id="bstree-data" type="hidden" name="bstree-data"
												data-ancestors="Teretory:Base">
											<div class="w-100">
												<ng-container *ngFor="let item of basicPlan; let i = index">
													<div class="row">
														<i
															class="material-icons tab-icons">{{showChildren[item.parent] ? 'remove_circle_outline' : 'add_circle_outline'}}</i>
														<h5 class="parent hover" id={{i}}
															[class.highlighted]="childItem === highlighted"
															(click)="toggleContainer(item.parent)">{{item.parent}}</h5>
													</div>
													<ng-container *ngIf="showChildren[item.parent]">
														<ng-container
															*ngFor="let childItem of item.child; let j = index">
															<p class="indented-child hover" id={{j}}
																(click)="addChild(childItem,i,j)"
																[class.highlighted]="childItem === highlighted"><i
																	class="fas fa-angle-right"></i> {{childItem}}</p>
														</ng-container>
													</ng-container>
												</ng-container>
												<!-- <ul id="tree1">
																<li *ngFor="let item of basicPlan;let i = index"
																	id={{i}}><a id={{i}} class="hover">{{item.parent}}</a>
																	<ul>
																		<li *ngFor="let childItem of item.child;let j =index"
																			id={{i}} class="hover"><a class=child{{j}}
																				(click)="addChild(childItem,i,j)">{{childItem}}</a>
																		</li>
																	</ul>
																</li>
															</ul> -->
											</div>
										</div>
									</div>
								</div>
								<div class="col-md-2 text-center my-auto">
									<button type="button" class="btn col-md btn-primary" (click)="add()"><span
											class="position_btn">Add</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<i
											class="fa fa-arrow-right"></i></button>
									<button type="button" class="btn col-md  btn-primary" (click)="Remove()"><span
											class="position_btn">Remove</span><i class="fa fa-arrow-left"></i></button>
								</div>
								<div class="col-md-5">
									<div class="card">
										<div class="card-header-info ">
											<h4 class="card-title no-hover-effect">Cart</h4>
										</div>
										<div class="card-body scroll-area tooltip1">
											<span class="tooltiptext">Click on the Elements and click on
												Remove to remove rules from Cart</span>
											<input id="bstree-data" type="hidden" name="bstree-data"
												data-ancestors="Teretory:Base">
											<div class="w-100">
												<ng-container *ngFor="let item of savedPlan; let i = index">
													<h5 class="parent no-hover-effect" id={{i}}
														[class.highlighted]="childItem === highlighted">{{item.parent}}
													</h5>
													<ng-container *ngFor="let childItem of item.child; let j = index">
														<p class="indented-child hover" id={{j}}
															(click)="removeChild(childItem,i,j)"
															[class.highlighted]="childItem === highlighted"><i
																class="fas fa-angle-right"></i> {{childItem}}</p>
													</ng-container>
												</ng-container>
												<!-- <ul id="tree2">
																<li *ngFor="let item of savedPlan;let i = index"
																	id={{i}}><a id={{i}} class="hover">{{item.parent}}</a>
																	<ul>
																		<li *ngFor="let childItem of item.child;let j=index"
																			id={{i}} class="hover"><a class=child{{j}}
																				(click)="removeChild(childItem,i,j)">{{childItem}}</a>
																			
																		</li>
																	</ul>
																</li>
															</ul> -->

											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col text-right">
							<button type="submit" class="btn btn-primary" (click)="onGoBack()"><i
									class="fas fa-times"></i> Cancel</button>
							<button type="submit" class="btn  btn-primary" (click)="clear()"><i
									class="fas fa-sync-alt"></i> Clear</button>
							<button type="submit" class="btn  btn-primary" [disabled]="!checkCanSave()"
								(click)="savePlan()"><i class="fas fa-save"></i> Save Plan</button>
						</div>
					</div>
				</form>

			</div>

		</div>
	</div>
</div>