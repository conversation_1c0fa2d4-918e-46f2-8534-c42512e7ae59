<div class="row">
    <div class="col-md-12">

        <div class="float-right col-md-3 text-right pr-2">
            <div class="form-group input-group ">

                <input class="custom-input ng-pristine ng-valid ng-touched" type="text" id="searchTextId"
                    [(ngModel)]="searchText" (keyup)="applyFilter($event)" name="searchText" placeholder="Search">
                <span class="input-group-icon">
                    <i class="fas fa-search"></i>
                </span>
            </div>
        </div>
    </div>
</div>

<table mat-table [dataSource]="dataSource" matSort class="my-table w-100" >
    <ng-container matColumnDef="opportunityName">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
            Opportunity Name</th>
        <td data-td-head="Opportunity Name" mat-cell *matCellDef="let element">
            {{element.opportunityName}} </td>
    
    </ng-container>
    
    <ng-container matColumnDef="demoDate">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
            Demo Date </th>
        <td data-td-head="Demo Date" mat-cell *matCellDef="let element">
            {{element.demoDate | datezone}} </td>
    
    </ng-container>
    
    <ng-container matColumnDef="dateContractSigned">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
            Date Contract Signed </th>
        <td data-td-head="Date Contract Signed" mat-cell *matCellDef="let element">
            {{element.dateContractSigned | date}} </td>
    
    </ng-container>
    
    <ng-container matColumnDef="actualInstallDate">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
            Actual Install Date </th>
        <td data-td-head="Actual Install Date" mat-cell *matCellDef="let element">
            {{element.actualInstallDate | date}} </td>
    
    </ng-container>
    
    <ng-container matColumnDef="stage">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
            Stage </th>
        <td data-td-head="Stage" mat-cell *matCellDef="let element">
            {{element.stage}} </td>
    
    </ng-container>
    
    <ng-container matColumnDef="systemSize">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
            System Size </th>
        <td data-td-head="System Size" mat-cell *matCellDef="let element">
            {{element.systemSize}} </td>
    </ng-container>
    
    <ng-container matColumnDef="appointmentConfirmed">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
            Appointment Confirmed </th>
        <td data-td-head="Appointment Confirmed  " mat-cell *matCellDef="let element">
           {{element.appointmentConfirmed ? 'Yes' : 'No'}} </td>
    
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="columns"></tr>
    <tr mat-row *matRowDef="let row; columns: columns;"></tr>
</table>
<mat-paginator [pageSizeOptions]="pageSizeOptions">
</mat-paginator>