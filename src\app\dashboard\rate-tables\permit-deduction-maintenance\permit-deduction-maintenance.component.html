<div class="page-title col-md-12 "><h1> Permit Deduction Maintenance</h1>
	<div class="breadcrumbs"><a  href="#">Home</a>/<span>Permit Deduction Maintenance</span>
	</div></div>
 
  <div class="content">
  
    <div class="card">
      <div class="card-header-info">
        <h4 class="card-title"><i class="fas fa-dollar-sign"></i> Permit Deduction</h4>
      </div>
      <div class="card-body">
          <div class="row">
            <div class="col-md-12">             
              <div class="input-group float-right table-search">
                <input class="custom-input" type="text" id="searchTextId" [(ngModel)]="searchText" name="searchText" placeholder="Search" (input)="searchForItem()">
                <span class="input-group-icon">
                <i class="fas fa-search"></i>
            </span>
          </div>
      
        </div>

            </div>
            <mat-table #table [dataSource]="dataSource" matSort>
              <ng-container matColumnDef="{{column.id}}" *ngFor="let column of columnNames">
                <mat-header-cell *matHeaderCellDef mat-sort-header class="table-header"> {{column.value}} </mat-header-cell>
                <mat-cell [attr.data-td-head]="column.value"  *matCellDef="let element"> {{element[column.id]}} </mat-cell>
              </ng-container>
              <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
              <mat-row *matRowDef="let row; columns: displayedColumns;" class="pointer table-content" (click)= "rowClick(row)"></mat-row>
            </mat-table>
            <mat-paginator [pageSizeOptions]="[5, 10, 20, 50]" showFirstLastButtons></mat-paginator>
            <div *ngIf="apiService.checkPermission('CreateRateTables')">
            <a class="btn  btn-primary float-right" *ngIf="!addInd" (click)="Add()"><i class="material-icons pointer">add_circle</i>Add</a>
            <a class="btn  btn-primary float-right" *ngIf="addInd" (click)="addInd = !addInd"><i class="material-icons pointer">remove_circle</i> Hide</a>
            </div>
        
      </div>
    </div>
    <div class="card" *ngIf="addInd">
      <div class="card-header-info">
        <h4 class="card-title no-hover-effect"><i class="fas fa-plus"></i> Add Permit Deduction</h4>
      </div>
      <div class="card-body">
        <div>
          <form [formGroup]="permitDeductionForm" (ngSubmit)="onSubmit()" class="w-100">
            <div class="row" *ngIf="activePermitDeductions">
              <div class="form-group col-md-4"> <div class="row">
                <label class="col-sm-5">Sales Territory</label>
                <div class="col-sm-7">
                <select class="custom-select" name="sales_territory_dropdown" formControlName="salesTerritory"
                  data-style="btn btn-link" id="sales_territory_dropdown">
                  <option *ngFor="let fp of dropdowns.salesTerritories" [selected]="fp.salesTerritoryId === permitDeductionRate1[0].salesTerritoryId"  value="{{fp.salesTerritoryId}}">
                    {{fp.salesTerritory1}}</option>
                </select>
              </div>
            </div>
            </div>
              <div class="form-group col-md-4"> <div class="row">
                <label class="col-sm-5">Finance Partner</label>
                <div class="col-sm-7">
                <select class="custom-select" name="finance_partner_dropdown" formControlName="financePartner"
                  data-style="btn btn-link" id="finance_partner_dropdown">
                  <option *ngFor="let fp of dropdowns.financePartners"
                  [selected]="fp.financePartnerId === permitDeductionRate1[0].financePartnerId" value="{{fp.financePartnerId}}">
                    {{fp.financePartner1}}</option>
                </select>
              </div>
              </div>
            </div>
              <div class="form-group col-md-4"> <div class="row">
                <label class="col-sm-5">Purchase Method</label>
                <div class="col-sm-7">
                <select class="custom-select" name="purchase_method_dropdown" formControlName="purchaseMethod"
                  data-style="btn btn-link" id="purchase_method_dropdown">
                  <option *ngFor="let fp of dropdowns.purchaseMethods"
                  [selected]="fp.purchaseMethodId == permitDeductionRate1[0].purchaseMethodId"  value="{{fp.purchaseMethodId}}">
                    {{fp.purchaseMethod1}}</option>
                </select>
              </div>
            </div>
            </div>
            <div class="form-group col-md-4"> <div class="row">
              <label class="col-sm-5">Utility Company</label>
              <div class="col-sm-7">
              <select class="custom-select" name="utility_company_dropdown" formControlName="utilityCompany"
                data-style="btn btn-link" id="utility_company_dropdown">
                <option *ngFor="let fp of dropdowns.utilityCompany"
                [selected]="fp.utilityCompanyId == permitDeductionRate1[0].utilityCompanyId"  value="{{fp.utilityCompanyId}}">
                  {{fp.utilityCompany1}}</option>
              </select>
            </div>
            </div>
            </div>
              <div class="form-group col-md-4"><div class="row">
                <label class="col-sm-5">Effective Start Date</label>
                <div class="col-sm-7 "><div class="date-picker w-100">
                <input #StartDatePicker type="date" name="start_date" id="start_date" class="custom-input"
                  formControlName="effectiveStartDate" placeholder=""> 
                  <span *ngIf="StartDatePicker.value.length > 0" class="mat-icon cal-reset"  (click)="clearDate(StartDatePicker)"><i class="far fa-calendar-times"></i></span> 
                <span *ngIf="StartDatePicker.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span>   
                
                <div *ngIf="permitDeductionForm.errors && permitDeductionForm.errors.maxDate">
                  <p style="color: red;">New Effecting Start Date should be greater than any previous start dates</p>
                </div>
              </div>
              </div>
              </div>
              </div>
              <div class="form-group col-md-4"> <div class="row">
                <label class="col-sm-5">Permit Deduction Rate</label>
                <div class="col-sm-7">
                <input currencyMask [options]="{ allowNegative: false, align: 'left', precision: 3 }" name="permitDeductionRate"
                [value]="permitDeductionRate1[0].permitDeductionRate" formControlName="permitDeductionRate" class="custom-input">
                <div *ngIf="permitDeductionRate.errors && permitDeductionRate.errors.max">
                  <p style="color: red;">Permit Deduction Rate cannot be higher than $20.00</p>
                </div>
              </div>
            </div>
            </div>

            <div class="form-group col-md-4"> 
              <div class="row">
                <label class="col-sm-5">Minimum PPW</label>
                <div class="col-sm-7">
                  <input currencyMask [options]="{ allowNegative: false, align: 'left', precision: 2 }" name="minimumPpw"
                  [value]="permitDeductionRate1[0].minimumPpw" formControlName="minimumPpw" class="custom-input">
                </div>
              </div>
            </div>

            </div>
            <div class="row">
            <div class="col-md-12 align-button-right">
              <button type="submit" class="btn btn-primary" [disabled]="permitDeductionForm.invalid"><i class="fas fa-plus"></i> Add Permit
                Deduction</button>
            </div>
          </div>
          </form>
        </div>
      </div>
    </div>
    <div class="card" *ngIf="permitDeductionGroup">
      <div class="card-header-info">
        <h4 class="card-title">History</h4>
      </div>
      <div class="card-body">
        <div class="row">
          <!-- <hr style="width: 95%; border-color: #26c6da;" /> -->
          <div class="w-100">
            <a class="text-info"><i class="material-icons float-right blue-icon"
                (click)="permitDeductionGroup = null">cancel</i></a>
          </div>
         <div class="col-md-12">
              <div class="row">
              
                 <div class="col-md-6">
                    <div class="row">
                      <label class="col-sm-5">Sales Territory</label>
                      <span class="col-sm-7">{{permitDeductionGroup[0].salesTerritory}}</span>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="row">
                      <label class="col-sm-5">Finance Partner</label>
                      <span class="col-sm-7">{{permitDeductionGroup[0].financePartner}}</span>
                    </div> </div>
                    <div class="col-md-6">
                      <div class="row">
                        <label class="col-sm-5">Purchase Method</label> 
                        <span class="col-sm-7">{{permitDeductionGroup[0].purchaseMethod}}</span>
                      </div> </div>
                  
          </div> 
          <table class="my-table mat-table col-md-12 mt-3">
            <thead>
              <tr  class="mat-header-row">
                <th class="mat-header-cell"   scope="col">Effective Start Date</th>
                <th class="mat-header-cell"   scope="col">Effective End Date</th>
                <th class="mat-header-cell"   scope="col">Permit Deduction Rate</th>
              </tr>
            </thead>
            <tbody>
              <tr class="mat-row "  *ngFor="let tr of permitDeductionGroup">
                <td class="mat-cell" data-td-head="Effective Start Date">{{tr.effectiveStartDate | date}}</td>
                <td class="mat-cell" data-td-head="Effective End Date">{{tr.effectiveEndDate | date}}</td>
                <!-- <td>{{tr.permitDeductionRate | currency:'USD':true:'1.2-3'}}</td> -->
                <!-- Dilip Rate table changes -->
                <td class="mat-cell" data-td-head="Permit Deduction Rate">{{tr.permitDeductionRate | currency:'USD':true:'1.3-3'}}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div> </div>
    