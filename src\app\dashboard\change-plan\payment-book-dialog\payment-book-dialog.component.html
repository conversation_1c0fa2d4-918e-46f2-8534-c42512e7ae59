<div class="dailog-title-bg">
    <div class="dailog-title">Add Payment Book<button class="dailog-close" [mat-dialog-close]><span>X</span></button></div>
    </div>
 
            <select class="custom-select hover w-50" [(ngModel)]="selectedRuleId" (change)="onSelect()">
                <option [value]="null">Payment Book</option>
                <option *ngFor="let rule of data.paymentBookRules[0].rules" [value]="rule.ruleId">
                    {{rule.ruleName}}</option>
            </select>
   
        
        <div class="w-100 text-right">
            <button class="btn btn-primary" [mat-dialog-close]="addOn"><i class="fas fa-check"></i> OK</button>
        </div>
   