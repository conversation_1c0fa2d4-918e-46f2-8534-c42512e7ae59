import { Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, FormGroup, Validators } from "@angular/forms";
import { ApiService } from '../../services/api.service';
import { Router, ActivatedRoute } from '@angular/router';
import { InfoBox } from './model/infobox.model';
import { ToastrService } from 'ngx-toastr';
// import { stringify } from 'querystring';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { DataSource } from '@angular/cdk/table';
import { MatLegacyPaginator } from '@angular/material/legacy-paginator';
import { TableFilterPipe } from '../../pipe/table-filter.pipe';
import { DatePipe, PercentPipe, CurrencyPipe } from '@angular/common';
import { ExportService } from 'src/app/services/export.service';

@Component({
  selector: 'app-salesAdmin-dashboard',
  templateUrl: './SalesAdmin-dashboard.component.html',
  styleUrls: ['./SalesAdmin-dashboard.component.css']
})
export class SalesAdminDashboardComponent implements OnInit {
  @ViewChild('table1', { read: MatSort, static: true }) sort1: MatSort;
  @ViewChild('table2', { read: MatSort, static: true }) sort2: MatSort;
  @ViewChild('table3', { read: MatSort, static: true }) sort3: MatSort;
  @ViewChild('table4', { read: MatSort, static: true }) sort4: MatSort;

  @ViewChild('paginator1', { static: true }) paginator1: MatLegacyPaginator;
  @ViewChild('paginator2', { static: true }) paginator2: MatLegacyPaginator;
  @ViewChild('paginator3', { static: true }) paginator3: MatLegacyPaginator;
  @ViewChild('paginator4', { static: true }) paginator4: MatLegacyPaginator;
  infoBox: InfoBox = {
    blueInfo: { name: "", number: 0 },
    greenInfo: { name: "", number: 0 },
    greyInfo: { name: "", number: 0 }
  };
  tabPosition: number = 0;
  getTraditional: any;
  getDirect: any;
  getOutreach: any;
  originalDataSource: any;
  originalDataSourceDirect: any;
  originalDataSourceOutreach: any;
  dataSource;
  dataSourceDirect;
  dataSourceOutreach;
  displayedColumns = [];
  displayedColumnsDirect = [];
  displayedColumnsOutreach = [];


  columnNames = [{
    id: "Contact_Name",
    value: "Contact Name"

  }, {
    id: "Plan_Name",
    value: "Plan Name"
  },
  {
    id: "Sales_Office",
    value: "Sales Office"
  }, {
    id: "Sales_Division",
    value: "Sales Division"
  },
  {
    id: "Title",
    value: "Title"
  },
  {
    id: "Start_Date",
    value: "Start Date"
  },
  {
    id: "Contact_Phone",
    value: "Contact Phone"
  },
  {
    id: "Contact_Email",
    value: "Contact Email"
  }];

  columnNamesAlt = [{
    id: "Contact_Name",
    value: "Contact Name"
  },
  {
    id: "Sales_Office",
    value: "Sales Office"
  },
  {
    id: "Title",
    value: "Title"
  },
  {
    id: "Start_Date",
    value: "Start Date"
  },
  {
    id: "Contact_Phone",
    value: "Contact Phone"
  },
  {
    id: "Contact_Email",
    value: "Contact Email"
  }];


  constructor(public apiService: ApiService, private toastMsg: ToastrService, private router: Router, private activatedRoute: ActivatedRoute, private formBuilder: UntypedFormBuilder, private pipe: TableFilterPipe,
    private datePipe: DatePipe, private percentPipe: PercentPipe, private currencyPipe: CurrencyPipe, private exportService: ExportService) {

    this.apiService.hideLoader = true;
    localStorage.setItem('href', window.location.href);

    this.infoBox = {
      blueInfo: { name: "", number: 0 },
      greenInfo: { name: "", number: 0 },
      greyInfo: { name: "", number: 0 }
    };
  }

  ngOnInit() {
    if (!this.apiService.checkPermission('ViewSalesAdminDashboard')) {
      this.router.navigate(['/unauthorized']);
    }
    this.getData();
    this.getStaticBoxes();
    this.dataSourceDirect = new MatTableDataSource(this.dataSourceDirect);
    this.dataSourceDirect.sort = this.sort2;
  }

  getStaticBoxes() {
    this.apiService.get('Dashboard/GetActiveDirectCount')
      .subscribe(data => {
        this.infoBox.blueInfo = { name: "Reps with Direct Plans", number: data };
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
    this.apiService.get('Dashboard/GetActiveTraditionalCount')
      .subscribe(data => {
        this.infoBox.greenInfo = { name: "Reps with Traditional Plans", number: data };
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
    this.apiService.get('Dashboard/GetActiveOutreachCount')
      .subscribe(data => {
        this.infoBox.greyInfo = { name: "Reps with Outreach Plans", number: data };
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }
  getData() {
    this.apiService.get('Dashboard/GetDirectStandardPlan')
      .subscribe(data => {
        console.log("direct", data);
        this.getDirect = data.result;
        this.displayedColumnsDirect = this.columnNames.map(x => x.id);
        this.createTable();
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });

    this.apiService.get('Dashboard/GetTraditionalStandardPlan')
      .subscribe(data => {

        this.getTraditional = data.result;
        this.displayedColumns = this.columnNames.map(x => x.id);
        this.createTableTraditional();
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
    this.apiService.get('Dashboard/GetOutreachPlan')
      .subscribe(data => {

        this.getOutreach = data.result;
        this.displayedColumnsOutreach = this.columnNames.map(x => x.id);
        this.createTableOutreach();
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }
  createTable() {
    let tableArr: Element[] = [];
    for (let i: number = 0; i <= this.getDirect.length - 1; i++) {
      let currentRow = this.getDirect[i];
      tableArr.push({
        Contact_Id: currentRow.contact_Id, Contact_Name: currentRow.contact_Name, Plan_Name: currentRow.plan_Name, Plan_Header_Id: currentRow.plan_Header_Id, Sales_Office: currentRow.sales_Office,
        Sales_Division: currentRow.sales_Division, Title: currentRow.title, Start_Date: this.datePipe.transform(currentRow.start_Date), Contact_Phone: currentRow.contact_Phone, Contact_Email: currentRow.contact_Email
      });
    }
    this.dataSourceDirect = new MatTableDataSource(tableArr);
    this.originalDataSourceDirect = tableArr;
    this.dataSourceDirect.sort = this.sort1;
    this.dataSourceDirect.paginator = this.paginator1;

  }
  createTableTraditional() {
    let tableArr: Element[] = [];
    for (let i: number = 0; i <= this.getTraditional.length - 1; i++) {
      let currentRow = this.getTraditional[i];
      tableArr.push({
        Contact_Id: currentRow.contact_Id, Contact_Name: currentRow.contact_Name, Plan_Name: currentRow.plan_Name, Plan_Header_Id: currentRow.plan_Header_Id, Sales_Office: currentRow.sales_Office,
        Sales_Division: currentRow.sales_Division, Title: currentRow.title, Start_Date: this.datePipe.transform(currentRow.start_Date), Contact_Phone: currentRow.contact_Phone, Contact_Email: currentRow.contact_Email
      });
    }
    this.dataSource = new MatTableDataSource(tableArr);
    this.originalDataSource = tableArr;
    this.dataSource.sort = this.sort2;
    this.dataSource.paginator = this.paginator2;

  }
  createTableOutreach() {
    let tableArr: Element[] = [];
    for (let i: number = 0; i <= this.getOutreach.length - 1; i++) {
      let currentRow = this.getOutreach[i];
      tableArr.push({
        Contact_Id: currentRow.contact_Id, Contact_Name: currentRow.contact_Name, Plan_Name: currentRow.plan_Name, Plan_Header_Id: currentRow.plan_Header_Id, Sales_Office: currentRow.sales_Office,
        Sales_Division: currentRow.sales_Division, Title: currentRow.title, Start_Date: this.datePipe.transform(currentRow.start_Date), Contact_Phone: currentRow.contact_Phone, Contact_Email: currentRow.contact_Email
      });
    }
    this.dataSourceOutreach = new MatTableDataSource(tableArr);
    this.originalDataSourceOutreach = tableArr;
    this.dataSourceOutreach.sort = this.sort3;
    this.dataSourceOutreach.paginator = this.paginator3;

  }

  moveToSelectedTab(tabName: string) {
    for (let i = 0; i < document.querySelectorAll('.mat-tab-label-content').length; i++) {
      if ((<HTMLElement>document.querySelectorAll('.mat-tab-label-content')[i]).innerText == tabName) {
        (<HTMLElement>document.querySelectorAll('.mat-tab-label')[i]).click();
      }
    }
  }

  exportTable(tabName: string) {
    var data : any[][];
    var headers: string[];

    switch (tabName) {
      case "Reps with Direct Plans":
        headers = [];
        data = [];
        this.columnNames.forEach(x => {
          headers.push(x.value);

        });
        headers = this.columnNames.map(x => x.value);
        data = this.dataSourceDirect.filteredData.map(x => {
          var keys = this.columnNames.map(y => y.id);
          var values = keys.map(y => {return x[y]});
          return values;
        });

        break;
      
      case "Reps with Traditional Plans":
        headers = this.columnNames.map(x => x.value);
        data = this.dataSource.filteredData.map(x => {
          var keys = this.columnNames.map(y => y.id);
          var values = keys.map(y => {return x[y]});
          return values;
        });


        break;

      case "Reps with Outreach Plans":
        headers = this.columnNames.map(x => x.value);
        data = this.dataSourceOutreach.filteredData.map(x => {
          var keys = this.columnNames.map(y => y.id);
          var values = keys.map(y => {return x[y]});
          return values;
        });
        break;
    }

    // export
    this.exportService.excel(headers, data, tabName);
  }

  rowClick(row: any) {
    
  }
}

export interface Element {
  Contact_Id: string;
  Contact_Name: string,
  Plan_Name: string,
  Plan_Header_Id: string,
  Sales_Office: string,
  Sales_Division: string,
  Title: string,
  Start_Date: string,
  Contact_Phone: string,
  Contact_Email: string
}

export interface ElementAlt {
  Contact_Id: string;
  Contact_Name: string,
  Sales_Office: string,
  Title: string,
  Start_Date: string,
  Contact_Phone: string,
  Contact_Email: string
}