<div class="page-title col-md-12 "><h1> Tiered Overage</h1>
	<div class="breadcrumbs"><a  href="#">Home</a>/<span>Tiered Overage</span>
	</div></div>

 
  <div class="content">
    
    <div class="card">
      <div class="card-header-info">
        <h4 class="card-title no-hover-effect">Tiered Overage</h4>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-12">             
            <div class="input-group float-right table-search">
              <input class="custom-input" type="text" id="searchTextId" [(ngModel)]="searchText" name="searchText" placeholder="Search" (input)="searchForItem()">
              <span class="input-group-icon">
              <i class="fas fa-search"></i> 
          </span>
        </div>
    
      </div>
        </div>
        <mat-table #table [dataSource]="dataSource" matSort>
          <ng-container matColumnDef="{{column.id}}" *ngFor="let column of columnNames">
            <mat-header-cell *matHeaderCellDef mat-sort-header class="table-header"> {{column.value}} </mat-header-cell>
            <mat-cell  [attr.data-td-head]="column.value"   *matCellDef="let element"> {{element[column.id]}} </mat-cell>
          </ng-container>
          <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
          <mat-row *matRowDef="let row; columns: displayedColumns;" class="pointer table-content" (click)= "rowClick(row)"></mat-row>
        </mat-table>
        <mat-paginator [pageSizeOptions]="[5, 10, 20, 50]" showFirstLastButtons></mat-paginator>
        <div *ngIf="apiService.checkPermission('CreateRateTables')">
          <a class="btn  btn-primary float-right"  *ngIf="!addInd"  (click)="addInd = !addInd" ><i class="material-icons pointer">add_circle</i> Add</a>
          <a class="btn  btn-primary float-right" *ngIf="addInd"  (click)="addInd = !addInd"><i class="material-icons pointer">remove_circle</i> Hide</a>
        </div>
      </div>
    </div>
    <div class="card" *ngIf="addInd">
      <div class="card-header-info">
        <h4 class="card-title no-hover-effect"><i class="fas fa-plus"></i> Add Tiered Overage</h4>
      </div>
      <div class="card-body">
        <div>
          <form [formGroup]="tieredOverageBonusRateForm" (ngSubmit)="onSubmit()" class="w-100">
            <div class="row" *ngIf="activeTieredOverageRates">
              <div class="form-group col-md-4"> <div class="row">
                <label class="col-sm-5">Sales Territory</label>
                <div class="col-sm-7">
                <select class="custom-select" name="sales_territory_dropdown" formControlName="salesTerritory"
                  data-style="btn btn-link" id="sales_territory_dropdown">
                  <option *ngFor="let st of dropdowns.salesTerritories" value="{{st.salesTerritoryId}}">
                    {{st.salesTerritory1}}</option>
                </select>
              </div>
            </div>
            </div>
              <div class="form-group col-md-4"> <div class="row">
                <label class="col-sm-5">Utility Company</label>
                <div class="col-sm-7">
                <select class="custom-select" name="utility_company_dropdown" formControlName="utilityCompany"
                  data-style="btn btn-link" id="utility_company_dropdown">
                  <option *ngFor="let uc of dropdowns.utilityCompanies" value="{{uc.utilityCompanyId}}">
                    {{uc.utilityCompany1}}</option>
                </select>
              </div>
            </div>
            </div>
              <div class="form-group col-md-4"> <div class="row">
                <label class="col-sm-5">Finance Partner</label>
                <div class="col-sm-7">
                <select class="custom-select" name="finance_partner_dropdown" formControlName="financePartner"
                  data-style="btn btn-link" id="finance_partner_dropdown">
                  <option *ngFor="let fp of dropdowns.financePartners" value="{{fp.financePartnerId}}">
                    {{fp.financePartner1}}</option>
                </select>
              </div>
            </div>
            </div>
              <div class="form-group col-md-4"> <div class="row">
                <label class="col-sm-5">Purchase Method</label>
                <div class="col-sm-7">
                <select class="custom-select" name="purchase_method_dropdown" formControlName="purchaseMethod"
                  data-style="btn btn-link" id="purchase_method_dropdown">
                  <option *ngFor="let pm of dropdowns.purchaseMethods" value="{{pm.purchaseMethodId}}">
                    {{pm.purchaseMethod1}}</option>
                </select>
              </div>
            </div>
            </div>
            
              <div class="form-group col-md-4"> <div class="row">
                <label class="col-sm-5">Effective Start Date</label>
                <div class="col-sm-7 date-picker">
                <input type="date" #StartDatePicker name="start_date" id="start_date" class="custom-input"
                  formControlName="effectiveStartDate" placeholder="">
                  <span *ngIf="StartDatePicker.value.length > 0" class="mat-icon cal-reset"  (click)="clearDate(StartDatePicker)" style="right:18px;"><i class="far fa-calendar-times"></i></span> 
                  <span *ngIf="StartDatePicker.value.length <= 0" class="mat-icon cal-open" style="right:18px;"><i class="far fa-calendar-alt"></i></span>   
                  
                <div *ngIf="tieredOverageBonusRateForm.errors && tieredOverageBonusRateForm.errors.maxDate">
                  <p style="color: red;">New Effecting Start Date should be greater than any previous start dates</p>
                </div>
              </div>
              </div>
            </div>
            </div>
            <div class="row" *ngFor="let group of tieredOverageGroup; index as i">
              <div class="gray-bg col-md-12 pt-3 mb-1"><div class="row">
              <div class="form-group col-md-4"> <div class="row">
                <label class="col-sm-5">Installation Price (Commissionable PPW)</label>
                <div class="col-sm-7">
                <input currencyMask [options]="{ allowNegative: false, align: 'left', precision: 3 }"
                  name="{{getControlName(group[0])}}" formControlName="{{getControlName(group[0])}}"
                  class="custom-input">
              </div>
              </div>
              </div>
              <div class="form-group col-md-4"><div class="row">
                <label class="col-sm-5">Tiered Overage Percentage</label>
                <div class="col-sm-7">

                  <input currencyMask [options]="{ allowNegative: false, align: 'left', prefix: '', suffix: '%', precision: 2 }"
                         name="{{getControlName(group[1])}}" formControlName="{{getControlName(group[1])}}"
                         class="custom-input">
                  <div *ngIf="tieredOveragePercentage.errors && tieredOveragePercentage.errors.max">
                    <p style="color: red;">Percentage % cannot be higher than 100.00 %</p>
                  </div>
                </div>
            </div>
          </div> 
          <a class="text-info mt-2"><i class="material-icons hover" (click)="removeFormRow(i)">delete</i></a>
              <a class="text-info hover mt-2"><i *ngIf="i == tieredOverageGroup.length - 1" class="material-icons"
                  (click)="addFormRow()">add_box</i></a>
                </div>
              </div>
            </div>
            <div class="row align-button-right">
              <button type="submit" class="btn btn-primary" [disabled]="tieredOverageBonusRateForm.invalid"><i class="fas fa-plus"></i> Add Tiered Overage
                Rate</button>
            </div>
          </form>
        </div>
      </div>
    </div>
    <!--  -->
