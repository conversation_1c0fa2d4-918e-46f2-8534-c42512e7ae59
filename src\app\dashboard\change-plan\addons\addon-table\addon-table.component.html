<table mat-table [dataSource]="dataSource" matSort class="my-table w-100">
    <ng-container matColumnDef="ruleName">
        <th mat-header-cell *matHeaderCellDef mat-sort-header style="width: 75%;"> Rule </th>
        <td data-td-head="Rule" mat-cell *matCellDef="let element"> {{element.ruleName}} </td>
    </ng-container>

    <ng-container matColumnDef="basePayRuleName">
        <th mat-header-cell *matHeaderCellDef mat-sort-header > Base Pay Rule Mapping </th>
        <td data-td-head="basePayRule" mat-cell *matCellDef="let element">
            <select class="custom-select hover" (change)="onSelect($event, element)">
                <option *ngFor="let rule of basePayRules" [value]="rule.ruleId" [selected]="rule.ruleId == element.basePayRuleId">
                    {{rule.ruleName}}</option>
            </select>
        </td>
    </ng-container>

    <ng-container matColumnDef="promptValues">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Prompts </th>
        <td data-td-head="Prompts"  mat-cell *matCellDef="let element">
            <mat-icon class="hover" (click)="onModify(element)" [hidden]="element.ruleTypeName == 'Payment Book'">edit</mat-icon>
        </td>
    </ng-container>

    <ng-container matColumnDef="delete">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Remove </th>
        <td  data-td-head="Remove"   mat-cell *matCellDef="let element">
            <mat-icon class="hover" (click)="removeAddOn(element)">delete</mat-icon>
        </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="columns"></tr>
    <tr mat-row *matRowDef="let row; columns: columns;"></tr>
</table>
<mat-paginator [pageSizeOptions]="pageSizeOptions">
</mat-paginator>