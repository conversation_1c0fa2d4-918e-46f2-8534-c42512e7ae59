import { ComponentFixture, TestBed } from '@angular/core/testing';

import { MonthlyRoofingOverrideSnapshotComponent } from './monthly-roofing-override-snapshot.component';

describe('MonthlyRoofingOverrideSnapshotComponent', () => {
  let component: MonthlyRoofingOverrideSnapshotComponent;
  let fixture: ComponentFixture<MonthlyRoofingOverrideSnapshotComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ MonthlyRoofingOverrideSnapshotComponent ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(MonthlyRoofingOverrideSnapshotComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
