import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DatePipe } from '@angular/common';
import { Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MatLegacyDialog } from '@angular/material/legacy-dialog';
import { MatLegacyPaginator } from '@angular/material/legacy-paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { ToastrService } from 'ngx-toastr';
import { ApiService } from 'src/app/services/api.service';
import { groupBy } from 'src/app/shared/group-by';
import { TieredAmountDialogComponent } from '../tiered-amount-dialog/tiered-amount-dialog.component';

@Component({
  selector: 'app-tiered-amount',
  templateUrl: './tiered-amount.component.html',
  styleUrls: ['./tiered-amount.component.css']
})
export class TieredAmountComponent implements OnInit {
  tierForm: UntypedFormGroup;
  allTieredAmounts:any
  activeTieredAmounts:any;
  tieredAmounts:any;
  tieredData:any;
  stateData:any;
  columnNames = [
    {
      id: "tierName",
      value: "Tier"
    }, 
    {
      id: "stateCode",
      value: "State"
    }, 
    {
      id: "amount",
      value: "Amount"
    }, 
    {
      id: "effectiveStartDate",
      value: "Start Date",
      dataType:'Date'
    }, 
    {
      id: "effectiveEndDate",
      value: "End Date",
      dataType:'Date'
    }, 
  ];
  originalDataSource;
  dataSource;
  displayedColumns = [];
  dateColumns:any;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatLegacyPaginator, { static: true }) paginator: MatLegacyPaginator;
  addInd: boolean = false;
  constructor(public apiService: ApiService, private toastMsg: ToastrService, private formBuilder: UntypedFormBuilder,
    private datePipe: DatePipe,private currencyPipe: CurrencyPipe, private dialog: MatLegacyDialog) {
  }

  ngOnInit(): void {
    if (!this.apiService.checkPermission('ViewRateTables')) {
      this.apiService.goBack();
      this.toastMsg.error("Insufficient Permissions. Please make sure your account can access this information.", "Permissions");
    }
    this.tierForm = this.formBuilder.group({
      tierName: [, [Validators.required]],
      state: [0, [Validators.required]],
      amount: [0, [Validators.required]],
      effectiveStartDate: ['', [Validators.required]],
      effectiveEndDate: [''],
    });
    this.dateColumns = this.columnNames.filter(s => s.dataType =='Date');
    this.displayedColumns = this.columnNames.map(x => x.id);
    this.getAllTieredAmounts();
    this.getActiveTieredAmounts();
    this.getTieredData();
    this.getStateData();
  }
  getAllTieredAmounts() {
    this.apiService.get('BatteryTieredRate/RetrieveAll')
      .subscribe(data => {
        this.allTieredAmounts = data;   
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }
  getActiveTieredAmounts() {
    this.apiService.get('BatteryTieredRate/RetrieveActive')
      .subscribe(data => {
        this.activeTieredAmounts = data;  
        this.createTable(); 
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }
  getTieredData() {
    this.apiService.get('BatteryOverrideTieredRates/dropdowns')
      .subscribe(data => {
        this.tieredData = data;   
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }
  getStateData() {
    this.apiService.get('BatteryCommissionRateMaintenance/dropdowns')
      .subscribe(data => {
        this.stateData = data;   
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }
  createTable() {
    let tableArr: Element[] = [];
    for (let i: number = 0; i <= this.activeTieredAmounts.length - 1; i++) {
      let currentRow = this.activeTieredAmounts[i];
      tableArr.push({
        effectiveEndDate: this.datePipe.transform(currentRow.effectiveEndDate), 
        effectiveStartDate: this.datePipe.transform(currentRow.effectiveStartDate),
        batteryTieredRateId: currentRow.batteryTieredRateId,
        tierId: currentRow.tierId,
        stateCode: currentRow.stateCode,
        stateCodeId: currentRow.stateCodeId,
        tierName: currentRow.tierName,
        amount: this.currencyPipe.transform(currentRow.amount),
      });
    }
    this.dataSource = new MatTableDataSource(tableArr);
    this.originalDataSource = tableArr;
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }
  rowClick(event:any){
    var tieredAmounts = this.allTieredAmounts.filter(x => x.tierId === event.tierId && x.stateCodeId === event.stateCodeId);
    this.tieredAmounts = tieredAmounts;
    tieredAmounts = Object.values(groupBy(tieredAmounts, 'effectiveStartDate'));
    const dialogRef = this.dialog.open(TieredAmountDialogComponent, {
      width: '80%', data: { tieredAmounts }
    });
    if(this.tieredAmounts && this.tieredAmounts.length > 0){
      let effectiveDate = this.transformDate(this.tieredAmounts[0]?.effectiveStartDate);
      let effectiveEndDate = this.transformDate(this.tieredAmounts[0]?.effectiveEndDate);
      this.tierForm.controls['tierName'].setValue(this.tieredAmounts[0]?.tierId);
      this.tierForm.controls['amount'].setValue(this.tieredAmounts[0]?.amount);
      this.tierForm.controls['state'].setValue(this.tieredAmounts[0]?.stateCodeId);
      this.tierForm.controls['effectiveStartDate'].setValue(effectiveDate);
      this.tierForm.controls['effectiveEndDate'].setValue(effectiveEndDate);
    }
    dialogRef.afterClosed().subscribe(result => {
    });
  }
  transformDate(dateStr: string): string | null {
    return this.datePipe.transform(dateStr, 'yyyy-MM-dd');
  }
  onAdd(){
    this.addInd = !this.addInd;
    this.tierForm.reset();
      this.tierForm.clearValidators();
  }
  onSubmit(){
    if (!this.tierForm.invalid) {
      var body = {
        newBatteryTieredRate:{
          batteryTierId: +this.tierForm.controls.tierName.value,
          amount: this.tierForm.controls.amount.value,
          stateCodeId: +this.tierForm.controls.state.value,
          effectiveStartDate: this.tierForm.controls.effectiveStartDate.value,
          effectiveEndDate: this.tierForm.controls.effectiveEndDate.value,
        }
      }
     
      this.apiService.post('BatteryTieredRate', body)
        .subscribe(data => {
          this.toastMsg.success('Tiered Amounts Successfully Added');
          this.getAllTieredAmounts();
          this.getActiveTieredAmounts();
          this.addInd = !this.addInd;
        },
        (err: any) => {
          if (err?.err?.message)
            this.toastMsg.error(err.message, "Server Error!");
          if (typeof err === "string") {
            let isJsonObject = false;            
            let parsedErr: any;
            try {
              parsedErr = JSON.parse(err);
              isJsonObject =parsedErr && typeof parsedErr === "object" &&!Array.isArray(parsedErr);
            } catch (e) {
              isJsonObject = false;
            }
            if (isJsonObject) {
              this.toastMsg.error(parsedErr.result, "Error!");
            } else this.toastMsg.error(err, "Error!");
          }
          if (err?.result) {
            this.toastMsg.error(err.result, "Error!");
          }
        }
      );
    }
  }
  clearDate(date: HTMLInputElement,key:string,event:any) {
    date.value = "";
    if(key =='startDate')
      this.tierForm.controls.effectiveStartDate.setValue('');
    if(key =='endDate')
      this.tierForm.controls.effectiveEndDate.setValue('');
    event.stopPropagation();
  }
}
export interface Element {
  amount:string,
  batteryTieredRateId:number,
  tierId:number,
  effectiveEndDate:string,
  effectiveStartDate:string,
  stateCode:string,
  stateCodeId:number,
  tierName:string
 }
