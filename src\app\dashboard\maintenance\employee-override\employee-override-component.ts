import { Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, FormGroup, Validators } from "@angular/forms";
import { ApiService } from "../../../services/api.service";
import { ToastrService } from 'ngx-toastr';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { DataSource } from '@angular/cdk/table';

import { TableFilterPipe } from '../../../pipe/table-filter.pipe';
import { DatePipe, PercentPipe, CurrencyPipe } from '@angular/common';
import { MatLegacyDialog } from '@angular/material/legacy-dialog';
import { HttpParams, HttpClient } from '@angular/common/http';
import { ApiResponse } from '../../../services/api.response';
import { environment } from '../../../../environments/environment';
import { IEmployeeOverride } from '../../../model/employee-override';
import * as FileSaver from 'file-saver';

@Component({
    selector: 'app-employee-override',
    templateUrl: './employee-override-component.html',
    styleUrls: ['./employee-override-component.css']
})
export class EmployeeOverrideComponent implements OnInit {
    selectedYear: number;
    selectedMonth: number;
    selectedDivision: string;
    salesDivisions: string[] = ["Sales- Direct", "Sales- Traditional", "Sales- Outreach"];
    constructor(public apiService: ApiService, private toastMsg: ToastrService, private formBuilder: UntypedFormBuilder, private pipe: TableFilterPipe, private http: HttpClient,
        private datePipe: DatePipe, private percentPipe: PercentPipe, private currencyPipe: CurrencyPipe, private dialog: MatLegacyDialog) {
    }

    ngOnInit() {
        if (!this.apiService.checkPermission('ViewRateTables')) {
            this.apiService.goBack();
            this.toastMsg.error("Insufficient Permissions. Please make sure your account can access this information.", "Permissions");
        }
    }

    getSalesDivisions() {
        this.apiService.get('GetData/SalesDivisions')
          .subscribe(data => {
            if (data && data.result) {
              this.salesDivisions = data.result.map(div => { return <string>div });
            }
          }, err => {
            this.toastMsg.error(err.message, "Error!");
          });
      }
     

    onSubmit() {

        if (!(this.selectedYear > 2000 && this.selectedYear < 2099)) {
            this.toastMsg.error('Please enter valid year number between 2000 and 2099');
            return;
        }
       if (!(this.selectedMonth > 0 && this.selectedMonth < 13)) {
        this.toastMsg.error('Please enter valid month number between 1 and 12');
        return;
       }
       if (this.selectedDivision == "") {
        this.toastMsg.error('Please select sales division');
        return;
       }
       
        var body = {
            SalesDivision: this.selectedDivision,
            Year: this.selectedYear,
            Month: this.selectedMonth
        }
        this.apiService.post('EmployeeOverride', body)
            .subscribe(data => {
                this.toastMsg.success('Employee override snapshot taken successfully. Please check dynamic report to see the snapshot.');
                }, (err: any) => {
                this.toastMsg.error(err.message, 'Server Error!');
            });
    }
}
