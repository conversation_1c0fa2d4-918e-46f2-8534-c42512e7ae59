import { Component, EventEmitter, OnInit, Output, Input, SimpleChanges } from '@angular/core';
import { ApiService } from '../../../services/api.service';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-starter',
  templateUrl: './starter.component.html',
  styleUrls: ['./starter.component.css']
})
export class StarterComponent implements OnInit {
  jobTypes: any = [];
  jobTypeId: number;
  enabled: boolean = true;
  @Input() jobsStatus = {};
  @Output() submitted = new EventEmitter<boolean>();

  constructor(private apiService: ApiService, private toastMsg: ToastrService) {
    this.getJobTypes();
  }

  ngOnInit() {
    // this.getJobTypes();
  }

  getJobTypes() {
    this.apiService.get('DataIntegration/JobTypes')
      .subscribe(data => {
        this.jobTypes = data;
      }, (err: any) => {
        this.toastMsg.error(err.message, "Server Error!");
      });
  }

  validateButton() {
    let jobType = this.jobTypes.filter(x => x.diJobTypeId == this.jobTypeId)[0];
    if (!jobType) return;
    let status = this.jobsStatus[jobType.diJobTypeName];
    if (status && status.includes("Started")) {
      this.enabled = false;
    } else {
      this.enabled = true;
    }
  }

  onSubmit() {
    if (this.jobTypeId) {
      var body = {
        "DI_Job_Type_Id": this.jobTypeId
      }
      this.apiService.post('DataIntegration', body)
        .subscribe(data => {
          this.toastMsg.success("", "Success!");
          this.submitted.emit(true);
        }, (err: any) => {
          this.toastMsg.error(err.message, "Server Error!");
        });
    } else {
      this.toastMsg.error("Job Type must be selected.", "Missing Data!");
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.jobsStatus) {
      this.validateButton();
    }
  }

  onJobTypeChange() {
    this.validateButton();
  }

}
