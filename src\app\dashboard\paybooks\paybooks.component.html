<div class=" w-100">
  <div class="content">
    <app-go-back></app-go-back>
    <div class="container-fluid">
      <div class="card">
        <div class="card-header-info">
          <div class="col-md">
            <h4 class="card-title">Payment Book</h4>
          </div>
        </div>
        <div class="card-body">
          <div class="card">
            <div class="card-header-info">
              <div class="row">
                <div class="col-md">
                  <h4 class="card-title">
                    Payment Book Balances
                  </h4>
                </div>
              </div>
            </div>
            <div class="card-body">
              <table class="table">
                <tr>
                  <th>Contact</th>
                  <th>Payment Book Type</th>
                  <th>Weekly Pay</th>
                  <th>Overdraw Limit</th>
                  <th>Sales Division</th>
                  <th>Sales Office</th>
                  <th>Balance</th>
                  <th>Date of Last Withdrawal</th>
                </tr>
                <tr *ngFor="let bal of balances | paginate: { itemsPerPage: 10, currentPage: p }">
                  <td><a [routerLink]="['/ui/commissions/paybook', bal.contactId]">{{bal.contactName}}</a></td>
                  <td>{{bal.paymentBookTypeName}}</td>
                  <td>{{bal.weeklyPay | currency}}</td>
                  <td>{{bal.overdrawLimit | currency}}</td>
                  <td>{{bal.salesDivision}}</td>
                  <td>{{bal.salesOffice}}</td>
                  <td>{{bal.balance | currency}}</td>
                  <td>{{bal.lastWithdrawalDate | date}}</td>
                </tr>
              </table>
              <pagination-controls (pageChange)="p = $event"></pagination-controls>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>