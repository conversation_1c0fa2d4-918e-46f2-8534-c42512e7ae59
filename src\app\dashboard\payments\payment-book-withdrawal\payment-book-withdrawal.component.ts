import { Component, OnInit, ViewChild } from '@angular/core';
import { ApiService } from 'src/app/services/api.service';
import { environment } from "src/environments/environment";
import { ToastrService } from 'ngx-toastr';
import { IPaymentBookBalance, IPaymentWithdrawalOverride, PaymentBookBalance, PaymentContact, PaymentPayload } from 'src/app/model/payment.model';
import { HttpClient, HttpParams } from '@angular/common/http';
import { ApiResponse } from 'src/app/services/api.response';
import * as FileSaver from 'file-saver';
import { MatLegacyDialog } from '@angular/material/legacy-dialog';
import { MatLegacyPaginator } from '@angular/material/legacy-paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { UntypedFormControl } from '@angular/forms';
import { ConfirmationDialogComponent } from 'src/app/confirmation-dialog/confirmation-dialog.component';


@Component({
  selector: 'app-payment-book-withdrawal',
  templateUrl: './payment-book-withdrawal.component.html',
  styleUrls: ['./payment-book-withdrawal.component.css']
})
export class PaymentBookWithdrawalComponent implements OnInit {
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatLegacyPaginator, {static: true}) paginator: MatLegacyPaginator;
  paymentBookBalances: MatTableDataSource<IPaymentBookBalance> = new MatTableDataSource([]);
  // pageSizeOptions: number[] = [10, 50, 100, 300, 500];
  pageSizeOptions: number[] = [10, 20, 50];
  pageSize: number = 50;
  paymentColumns: string[] = ["selected", "payBookLink", "contactName", "balance", "paymentBookTypeName", "weeklyPay", "overdrawLimit", "withdrawalAmount", "lastWithdrawalDate", "processDate", "salesDivision", "salesOffice"];
  // paymentBookBalances: PaymentBookBalance[];
  public date: Date;
  searchText: string = "";
  withdrawalPage: number = 1;
  allSelected: boolean = false;
  salesOffices: string[] = [];
  salesDivisions: string[] = [];
  salesOffice: string = null;
  salesDivision: string = null;
  withdrawalName:string = '';
  PaymentApprovalDateRangeStart: Date = null;
  PaymentApprovalDateRangeEnd: Date = null;
  AllowUsersOverride: boolean = false; 
  IsProcess : boolean = false;
  filter: boolean = false;
  showNegativeBooks: boolean = false;
  showInActive: boolean = true;
  positivePaymentBooks: any;
  allPaymentBooks: any;
  allOriginalPaymentBooks: any;
  selectedUsers: any = new UntypedFormControl();
  hiddenUsers: any;
  searchInactive: any = new UntypedFormControl('');
  withdrawalOverrideSingle: any = new UntypedFormControl(0);
  selectedPaymentData:any=[];
  constructor(private apiService: ApiService, private toastMsg: ToastrService, private http: HttpClient,private dialog: MatLegacyDialog) { }

  ngOnInit() {
    if (!this.apiService.checkPermission('ExtractToPaycom')) {
      // this.router.navigate(['/ui/dashboard'])
      this.apiService.goBack();
      this.toastMsg.error("Insufficient Permissions. Please make sure your account can access this information.", "Permissions");
    }

    this.getAllPaymentBookBalances();
    this.getSalesDivisions();
    this.getSalesOffices();
    this.selectedUsers.valueChanges.subscribe(val => {
      console.log(this.hiddenUsers.filter(u => !this.selectedUsers.value.includes(u.contactId)).map(x => x.contactId))
      const toRemove = this.hiddenUsers.filter(u => !this.selectedUsers.value.includes(u.contactId)).map(x => x.contactId)
      this.removeWithdrawalExclusions(toRemove);
    });
    this.searchInactive.valueChanges.subscribe((val:string) => {
      if(val){
        console.log(this.hiddenUsers.filter(x => x.contactName.toLowerCase().includes(val.toLowerCase())))
        this.hiddenUsers = this.hiddenUsers.filter(x => x.contactName.toLowerCase().includes(val.toLowerCase()));
      }
      else {
        this.hiddenUsers = this.allOriginalPaymentBooks.filter(x => x.isHidden);
      }

    });
  }

  editWithdrawalAmount(element: any){
    var alreadyOpen = this.paymentBookBalances.data.find(x => x.hasWithdrawalOverrideValue == true);
    if(alreadyOpen){
      this.toastMsg.warning("Please complete currently open override before starting additional");
    }
    else{
      var row = this.paymentBookBalances.data.find(override => override.contactId === element.contactId);
      row.hasWithdrawalOverrideValue = true;
      if(row.overrideWithdrawalAmount == 0){
        row.overrideWithdrawalAmount = row.withdrawalAmount;
        this.withdrawalOverrideSingle.value = row.withdrawalAmount;
      }
    }

  }

  closeOverrideWithdrawal(element: any){
    var row = this.paymentBookBalances.data.find(override => override.contactId === element.contactId);
    // if(this.withdrawalOverrideSingle.value > row.balance){
    //   this.toastMsg.warning("Withdrawal Override Amount must be less than the available balance amount");
    // }
    if(this.withdrawalOverrideSingle.value == 0 && row.overrideWithdrawalAmount > 0){
        this.toastMsg.warning("Please enter an override amount greater than 0")
    }
    else{
      row.overrideWithdrawalAmount = parseFloat(this.withdrawalOverrideSingle.value);
      row.hasWithdrawalOverrideValue = false;
      if(row.overrideWithdrawalAmount == row.withdrawalAmount){
        row.overrideWithdrawalAmount = 0;
      }
      this.withdrawalOverrideSingle.value = 0;
    }
  }

  resetWithdrawalOverrides() {
    var overrides = this.paymentBookBalances.data.filter(x => x.overrideWithdrawalAmount > 0)
    if(overrides){
      overrides.forEach(element => {
      element.overrideWithdrawalAmount = 0;
      element.hasWithdrawalOverrideValue = false;
    });
    }

  }

  getAllPaymentBookBalances() {      
    // this.http.get<ApiResponse>(`${environment.apiBaseUrl}Payments/ContactPaymentBookBalances${this.salesDivision || this.salesOffice ? '?' : ''}${this.salesDivision != null && this.salesDivision != 'null' ? 'salesDivision=' + this.salesDivision + '&' : ''}${this.salesOffice != null && this.salesOffice != 'null' ? 'salesOffice=' + this.salesOffice + '&' : ''}`)
    let params = new HttpParams();
    params = params.append('withdrawal','false');
    params = params.append('salesDivision', this.salesDivision != null && this.salesDivision != 'null' ? this.salesDivision : '');
    params = params.append('salesOffice', this.salesOffice != null && this.salesOffice != 'null' ? this.salesOffice : '');
    params = params.append('PaymentApprovalDateRangeStart', this.PaymentApprovalDateRangeStart ? this.PaymentApprovalDateRangeStart.toString() : '');
    params = params.append('PaymentApprovalDateRangeEnd', this.PaymentApprovalDateRangeEnd ? this.PaymentApprovalDateRangeEnd.toString() : '');
    params = params.append('IsZeroWithdrawalAmount','false');
    params = params.append('blnShowInActive',this.showInActive ?this.showInActive.toString():'false');
    //console.log(this.selectedUsers.value)
    if(this.selectedUsers.value) {
      this.selectedUsers.value.forEach((user:any) =>{
      params = params.append(`selectedUsers`, user);
    });
  }
    this.http.get<ApiResponse>(`${environment.apiBaseUrl}Payments/ContactPaymentBookBalances`, { params: params })
       .subscribe(data => {
        if (data && data.result) {
          this.allOriginalPaymentBooks = data.result.map((bal: IPaymentBookBalance) => { return bal });
          this.hiddenUsers = this.allOriginalPaymentBooks.filter(x => x.isHidden);
          const hidden = this.hiddenUsers.map(function (user) {return user.contactId});
          this.selectedUsers.value = hidden;
          this.allPaymentBooks = this.allOriginalPaymentBooks.filter(x => !x.isHidden);
          this.positivePaymentBooks = this.allPaymentBooks.filter(x => x.balance > 0);
          this.changeBalancesShown();

          this.applyFilter(this.searchText);

          console.log("Payment Book Balances", this.paymentBookBalances);
        }
      }, (err: any) => {
        this.toastMsg.error(err.message, "Error!");
      })
  }
  changeBalancesShown() {
    if(this.showNegativeBooks){
      this.paymentBookBalances = new MatTableDataSource(this.allPaymentBooks)
      this.paymentBookBalances.paginator = this.paginator;
      this.paymentBookBalances.sort = this.sort;
    }
    else{
      this.paymentBookBalances = new MatTableDataSource(this.positivePaymentBooks)
      this.paymentBookBalances.paginator = this.paginator;
      this.paymentBookBalances.sort = this.sort;
    }
  }

  submitPaymentBookWithdrawals(contactBalances: IPaymentBookBalance[]) {
    let name:string = this.withdrawalName; 
    let contacts: PaymentContact[] = contactBalances.map(bal => { return <PaymentContact>{ contactId: bal.contactId} });
    let withdrawalOverrides: IPaymentWithdrawalOverride[] = contactBalances.map(bal => { return <IPaymentWithdrawalOverride>{ contactId: bal.contactId, overrideWithdrawalAmount: bal.overrideWithdrawalAmount } });
    // let paymentPayload:PaymentPayload = {withdrawalName: name, paymentContact: contacts};
    let paymentPayload:PaymentPayload = {withdrawalName: name, paymentContact: contacts, allowUsersOverride: this.AllowUsersOverride, paymentWithdrawalOverrides: withdrawalOverrides};
    this.http.post(`${environment.apiBaseUrl}Payments/SubmitPaymentBookWithdrawal`, paymentPayload, { responseType: 'blob' })
      .subscribe(data => {
        this.toastMsg.success("Successfully submitted payments.");
        this.downLoadFile(data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;");
        this.getAllPaymentBookBalances();
      }, err => {
        this.toastMsg.error(`${err} - Error processing payment`, "Error!");
      });
  }

  getSalesDivisions() {
    this.apiService.get('GetData/SalesDivisions')
      .subscribe(data => {
        if (data && data.result) {
          this.salesDivisions = data.result.map(div => { return <string>div });
        }
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      });
  }

  getSalesOffices() {
    this.apiService.get('GetData/SalesOffices')
      .subscribe(data => {
        if (data && data.result) {
          this.salesOffices = data.result.map(office => { return <string>office });
        }
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      });
  }

  onBulkSelectionChange() {    
    this.paymentBookBalances.filteredData.map(bal => bal.selected = this.allSelected);
    if(this.allSelected){
      this.openConfirmationPopup();
    }
  }
  openConfirmationPopup(){
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      disableClose:true,
      width: '28%',
      data: {
        message: "Please note that all the records are selected.Do you want to continue?"
      }
    });
    dialogRef.beforeClosed().subscribe((confirmed) => {      
      if (!confirmed) {
        this.allSelected = false;
        if(this.selectedPaymentData.length > 0){
          this.paymentBookBalances.filteredData.filter(obj1 =>
            this.selectedPaymentData.some(obj2 => obj1.contactId === obj2.contactId ? obj1.selected = true:obj1.selected = false) 
          );
        }
        else{
          this.paymentBookBalances.filteredData.map(bal => bal.selected = false);                    
        }
        return false
      }
      this.selectedPaymentData = [];
    });
  }
  onSelectionChange() {
    let balances = this.paymentBookBalances.filteredData.filter(bal => bal.selected);
    this.selectedPaymentData = balances;
    if (balances.length == this.paymentBookBalances.filteredData.length) {
      this.allSelected = true;
    } else {
      this.allSelected = false;
    }
  }

  onChangeFilter() {
    this.getAllPaymentBookBalances();
  }

  bulkWithdrawal() {
    let contactBalances = this.paymentBookBalances.data.filter(bal => bal.selected);

    let contactIds = contactBalances.map(bal => { return { contactId: bal.lastWithdrawalDate, overrideWithdrawalAmount: bal.overrideWithdrawalAmount } });
    let Override = this.AllowUsersOverride
    // alert(Override);
    let IsProcess = true;
    contactIds.forEach(function (item) {
      if (item.contactId != null) {
        let today = new Date();
        let wDate = new Date(item.contactId);
        let timeDiff = Math.abs(today.getTime() - wDate.getTime());
        let diffDays = Math.floor(timeDiff / (1000 * 3600 * 24));
        // alert(diffDays);
        if (diffDays < 7 && Override == false) {
          // alert("There has been a withdrawal for one or more of these selected contacts within the past week. To override and continue with the withdrawal process, please select the Allow Override checkbox");          
          IsProcess = false;
          return false;
        }
        else {          
          IsProcess = true;
        }
      }
    });

    if (IsProcess == true) {      
      console.log(IsProcess)
      if (contactBalances && contactBalances.length > 0) {
        this.submitPaymentBookWithdrawals(contactBalances);
        
        this.AllowUsersOverride = false;
      } else {
        this.toastMsg.warning("No contacts selected to pay");
      }
    }
    else {
      console.log(IsProcess);
      this.toastMsg.error("There has been a withdrawal for one or more of these selected contacts within the past week. To override and continue with the withdrawal process, please select the Allow Override checkbox.", "Error!");
    }
  }

  // METHOD OVERLOAD
  applyFilter(input: string): void;

  applyFilter(input: Event): void;

  applyFilter(input: any): any {
    var filterValue: string;
    if (typeof input === "string") {
      filterValue = input;
    } else {
      filterValue = (input.target as HTMLInputElement).value;
    }
    this.paymentBookBalances.filter = filterValue.trim().toLowerCase();
  }

  withdraw(balance: IPaymentBookBalance) {
    this.submitPaymentBookWithdrawals([balance]);
  }

  checkSelected() {
    return (this.paymentBookBalances && this.paymentBookBalances.data.filter(bal => bal.selected).length > 0 && this.withdrawalName.length > 1);
  }

  getNumberSelected(): number {
    if(this.paymentBookBalances) return this.paymentBookBalances.data.filter(pbb => pbb.selected).length;
  }

  getSelectedSum(): number {
    if (this.paymentBookBalances) return this.paymentBookBalances.data.filter(pbb => pbb.selected).reduce((sum, current) => sum + (current.overrideWithdrawalAmount ? current.overrideWithdrawalAmount : current.withdrawalAmount), 0);
  }

  getWithdrawalsWorksheet() {
    var body = {
      contactIds: this.paymentBookBalances.filteredData.map(x => x.contactId)
    }
    this.http.post(`${environment.apiBaseUrl}Payments/WithdrawalsWorksheet?blnShowInActive=${this.showInActive}`, body,{ responseType: 'blob' })
    .subscribe(data => {
      this.downLoadFile(data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;");
      this.toastMsg.success("Success", "Your data was successfully exported");
    }, err => {
      this.toastMsg.error(err.message, "An error occured while exporting this data.");
    })
  }

  /**
   * Method is use to download file.
   * @param data - Array Buffer data
   * @param type - type of the document.
   */
  downLoadFile(data: any, type: string) {
    let blob = new Blob([data], { type: type });
    let date: Date = new Date();

    FileSaver.saveAs(blob, `Pre_Extracted_${date}.xlsx`);
  }

  openSalesrep(id) {
    const url = `#/ui/commissions/salesrep/${id}`;
    window.open(url, '_blank');
  }

  openPaymentBook(id) {
    const url = `#/ui/commissions/paybook/${id}`;
    window.open(url, '_blank');
  }

  clearSearch(event: any){
    event.stopPropagation();
    this.searchInactive.patchValue('');
  }

  addWithdrawalExclusions(){
    var body = this.paymentBookBalances.data.filter(pbb => pbb.selected).map(x => x.contactId);
    this.http.post(`${environment.apiBaseUrl}Payments/AddWithdrawalExclusions`, body)
    .subscribe(data => {
      this.toastMsg.success("Successfully hid user(s)");
      this.getAllPaymentBookBalances();
    }, err => {
      console.log(err)
      this.toastMsg.error(err.message, "Error!");
    });
  }

  removeWithdrawalExclusions(contactId: any){
    console.log(contactId[0])
    this.http.post(`${environment.apiBaseUrl}Payments/RemoveWithdrawalExclusions`, contactId[0])
    .subscribe(data => {
      this.toastMsg.success("Successfully Re-added Hidden User");
      this.getAllPaymentBookBalances();
    }, err => {
      console.log(err)
      this.toastMsg.error(err.message, "Error!");
    });

  }

  checkHiddenUsers(){
    let disabled = true;
    if(this.paymentBookBalances && this.paymentBookBalances.data.filter(pbb => pbb.selected).length > 0){
      disabled = false;
    }
    return disabled;
  }
}
