/*!
 * sweetalert2 v7.24.1
 * Released under the MIT License.
 */
(function(global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :
    typeof define === 'function' && define.amd ? define(factory) :
    (global.Sweetalert2 = factory());
}(this, (function() {
  'use strict';

  var _typeof = typeof Symbol === "function" && typeof Symbol.iterator === "symbol" ? function(obj) {
    return typeof obj;
  } : function(obj) {
    return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj;
  };











  var classCallCheck = function(instance, Constructor) {
    if (!(instance instanceof Constructor)) {
      throw new TypeError("Cannot call a class as a function");
    }
  };

  var createClass = function() {
    function defineProperties(target, props) {
      for (var i = 0; i < props.length; i++) {
        var descriptor = props[i];
        descriptor.enumerable = descriptor.enumerable || false;
        descriptor.configurable = true;
        if ("value" in descriptor) descriptor.writable = true;
        Object.defineProperty(target, descriptor.key, descriptor);
      }
    }

    return function(Constructor, protoProps, staticProps) {
      if (protoProps) defineProperties(Constructor.prototype, protoProps);
      if (staticProps) defineProperties(Constructor, staticProps);
      return Constructor;
    };
  }();







  var _extends = Object.assign || function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];

      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }

    return target;
  };

  var get = function get(object, property, receiver) {
    if (object === null) object = Function.prototype;
    var desc = Object.getOwnPropertyDescriptor(object, property);

    if (desc === undefined) {
      var parent = Object.getPrototypeOf(object);

      if (parent === null) {
        return undefined;
      } else {
        return get(parent, property, receiver);
      }
    } else if ("value" in desc) {
      return desc.value;
    } else {
      var getter = desc.get;

      if (getter === undefined) {
        return undefined;
      }

      return getter.call(receiver);
    }
  };

  var inherits = function(subClass, superClass) {
    if (typeof superClass !== "function" && superClass !== null) {
      throw new TypeError("Super expression must either be null or a function, not " + typeof superClass);
    }

    subClass.prototype = Object.create(superClass && superClass.prototype, {
      constructor: {
        value: subClass,
        enumerable: false,
        writable: true,
        configurable: true
      }
    });
    if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;
  };











  var possibleConstructorReturn = function(self, call) {
    if (!self) {
      throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
    }

    return call && (typeof call === "object" || typeof call === "function") ? call : self;
  };





  var slicedToArray = function() {
    function sliceIterator(arr, i) {
      var _arr = [];
      var _n = true;
      var _d = false;
      var _e = undefined;

      try {
        for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {
          _arr.push(_s.value);

          if (i && _arr.length === i) break;
        }
      } catch (err) {
        _d = true;
        _e = err;
      } finally {
        try {
          if (!_n && _i["return"]) _i["return"]();
        } finally {
          if (_d) throw _e;
        }
      }

      return _arr;
    }

    return function(arr, i) {
      if (Array.isArray(arr)) {
        return arr;
      } else if (Symbol.iterator in Object(arr)) {
        return sliceIterator(arr, i);
      } else {
        throw new TypeError("Invalid attempt to destructure non-iterable instance");
      }
    };
  }();

  var consolePrefix = 'SweetAlert2:';

  /**
   * Filter the unique values into a new array
   * @param arr
   */
  var uniqueArray = function uniqueArray(arr) {
    var result = [];
    for (var i = 0; i < arr.length; i++) {
      if (result.indexOf(arr[i]) === -1) {
        result.push(arr[i]);
      }
    }
    return result;
  };

  /**
   * Converts `inputOptions` into an array of `[value, label]`s
   * @param inputOptions
   */
  var formatInputOptions = function formatInputOptions(inputOptions) {
    var result = [];
    if (typeof Map !== 'undefined' && inputOptions instanceof Map) {
      inputOptions.forEach(function(value, key) {
        result.push([key, value]);
      });
    } else {
      Object.keys(inputOptions).forEach(function(key) {
        result.push([key, inputOptions[key]]);
      });
    }
    return result;
  };

  /**
   * Standardise console warnings
   * @param message
   */
  var warn = function warn(message) {
    console.warn(consolePrefix + ' ' + message);
  };

  /**
   * Standardise console errors
   * @param message
   */
  var error = function error(message) {
    console.error(consolePrefix + ' ' + message);
  };

  /**
   * Private global state for `warnOnce`
   * @type {Array}
   * @private
   */
  var previousWarnOnceMessages = [];

  /**
   * Show a console warning, but only if it hasn't already been shown
   * @param message
   */
  var warnOnce = function warnOnce(message) {
    if (!(previousWarnOnceMessages.indexOf(message) !== -1)) {
      previousWarnOnceMessages.push(message);
      warn(message);
    }
  };

  /**
   * If `arg` is a function, call it (with no arguments or context) and return the result.
   * Otherwise, just pass the value through
   * @param arg
   */
  var callIfFunction = function callIfFunction(arg) {
    return typeof arg === 'function' ? arg() : arg;
  };

  var isThenable = function isThenable(arg) {
    return (typeof arg === 'undefined' ? 'undefined' : _typeof(arg)) === 'object' && typeof arg.then === 'function';
  };

  var DismissReason = Object.freeze({
    cancel: 'cancel',
    backdrop: 'overlay',
    close: 'close',
    esc: 'esc',
    timer: 'timer'
  });

  var version = "7.24.1";

  var argsToParams = function argsToParams(args) {
    var params = {};
    switch (_typeof(args[0])) {
      case 'string':
        ['title', 'html', 'type'].forEach(function(name, index) {
          switch (_typeof(args[index])) {
            case 'string':
              params[name] = args[index];
              break;
            case 'undefined':
              break;
            default:
              error('Unexpected type of ' + name + '! Expected "string", got ' + _typeof(args[index]));
          }
        });
        break;

      case 'object':
        _extends(params, args[0]);
        break;

      default:
        error('Unexpected type of argument! Expected "string" or "object", got ' + _typeof(args[0]));
        return false;
    }
    return params;
  };

  /**
   * Adapt a legacy inputValidator for use with expectRejections=false
   */
  var adaptInputValidator = function adaptInputValidator(legacyValidator) {
    return function adaptedInputValidator(inputValue, extraParams) {
      return legacyValidator.call(this, inputValue, extraParams).then(function() {
        return undefined;
      }, function(validationError) {
        return validationError;
      });
    };
  };

  var swalPrefix = 'swal2-';

  var prefix = function prefix(items) {
    var result = {};
    for (var i in items) {
      result[items[i]] = swalPrefix + items[i];
    }
    return result;
  };

  var swalClasses = prefix(['container', 'shown', 'height-auto', 'iosfix', 'popup', 'modal', 'no-backdrop', 'toast', 'toast-shown', 'fade', 'show', 'hide', 'noanimation', 'close', 'title', 'header', 'content', 'actions', 'confirm', 'cancel', 'footer', 'icon', 'icon-text', 'image', 'input', 'has-input', 'file', 'range', 'select', 'radio', 'checkbox', 'textarea', 'inputerror', 'validationerror', 'progresssteps', 'activeprogressstep', 'progresscircle', 'progressline', 'loading', 'styled', 'top', 'top-start', 'top-end', 'top-left', 'top-right', 'center', 'center-start', 'center-end', 'center-left', 'center-right', 'bottom', 'bottom-start', 'bottom-end', 'bottom-left', 'bottom-right', 'grow-row', 'grow-column', 'grow-fullscreen']);

  var iconTypes = prefix(['success', 'warning', 'info', 'question', 'error']);

  // Remember state in cases where opening and handling a modal will fiddle with it.
  var states = {
    previousBodyPadding: null
  };

  var hasClass = function hasClass(elem, className) {
    if (elem.classList) {
      return elem.classList.contains(className);
    }
    return false;
  };

  var focusInput = function focusInput(input) {
    input.focus();

    // place cursor at end of text in text input
    if (input.type !== 'file') {
      // http://stackoverflow.com/a/2345915/1331425
      var val = input.value;
      input.value = '';
      input.value = val;
    }
  };

  var addOrRemoveClass = function addOrRemoveClass(target, classList, add) {
    if (!target || !classList) {
      return;
    }
    if (typeof classList === 'string') {
      classList = classList.split(/\s+/).filter(Boolean);
    }
    classList.forEach(function(className) {
      if (target.forEach) {
        target.forEach(function(elem) {
          add ? elem.classList.add(className) : elem.classList.remove(className);
        });
      } else {
        add ? target.classList.add(className) : target.classList.remove(className);
      }
    });
  };

  var addClass = function addClass(target, classList) {
    addOrRemoveClass(target, classList, true);
  };

  var removeClass = function removeClass(target, classList) {
    addOrRemoveClass(target, classList, false);
  };

  var getChildByClass = function getChildByClass(elem, className) {
    for (var i = 0; i < elem.childNodes.length; i++) {
      if (hasClass(elem.childNodes[i], className)) {
        return elem.childNodes[i];
      }
    }
  };

  var show = function show(elem) {
    elem.style.opacity = '';
    elem.style.display = elem.id === swalClasses.content ? 'block' : 'flex';
  };

  var hide = function hide(elem) {
    elem.style.opacity = '';
    elem.style.display = 'none';
  };

  var empty = function empty(elem) {
    while (elem.firstChild) {
      elem.removeChild(elem.firstChild);
    }
  };

  // borrowed from jquery $(elem).is(':visible') implementation
  var isVisible = function isVisible(elem) {
    return elem && (elem.offsetWidth || elem.offsetHeight || elem.getClientRects().length);
  };

  var removeStyleProperty = function removeStyleProperty(elem, property) {
    if (elem.style.removeProperty) {
      elem.style.removeProperty(property);
    } else {
      elem.style.removeAttribute(property);
    }
  };

  var getContainer = function getContainer() {
    return document.body.querySelector('.' + swalClasses.container);
  };

  var elementByClass = function elementByClass(className) {
    var container = getContainer();
    return container ? container.querySelector('.' + className) : null;
  };

  var getPopup = function getPopup() {
    return elementByClass(swalClasses.popup);
  };

  var getIcons = function getIcons() {
    var popup = getPopup();
    return popup.querySelectorAll('.' + swalClasses.icon);
  };

  var getTitle = function getTitle() {
    return elementByClass(swalClasses.title);
  };

  var getContent = function getContent() {
    return elementByClass(swalClasses.content);
  };

  var getImage = function getImage() {
    return elementByClass(swalClasses.image);
  };

  var getProgressSteps = function getProgressSteps() {
    return elementByClass(swalClasses.progresssteps);
  };

  var getValidationError = function getValidationError() {
    return elementByClass(swalClasses.validationerror);
  };

  var getConfirmButton = function getConfirmButton() {
    return elementByClass(swalClasses.confirm);
  };

  var getCancelButton = function getCancelButton() {
    return elementByClass(swalClasses.cancel);
  };

  var getButtonsWrapper = function getButtonsWrapper() {
    warnOnce('swal.getButtonsWrapper() is deprecated and will be removed in the next major release, use swal.getActions() instead');
    return elementByClass(swalClasses.actions);
  };

  var getActions = function getActions() {
    return elementByClass(swalClasses.actions);
  };

  var getFooter = function getFooter() {
    return elementByClass(swalClasses.footer);
  };

  var getCloseButton = function getCloseButton() {
    return elementByClass(swalClasses.close);
  };

  var getFocusableElements = function getFocusableElements() {
    var focusableElementsWithTabindex = Array.prototype.slice.call(getPopup().querySelectorAll('[tabindex]:not([tabindex="-1"]):not([tabindex="0"])'))
      // sort according to tabindex
      .sort(function(a, b) {
        a = parseInt(a.getAttribute('tabindex'));
        b = parseInt(b.getAttribute('tabindex'));
        if (a > b) {
          return 1;
        } else if (a < b) {
          return -1;
        }
        return 0;
      });

    // https://github.com/jkup/focusable/blob/master/index.js
    var otherFocusableElements = Array.prototype.slice.call(getPopup().querySelectorAll('a[href], area[href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), button:not([disabled]), iframe, object, embed, [tabindex="0"], [contenteditable], audio[controls], video[controls]'));

    return uniqueArray(focusableElementsWithTabindex.concat(otherFocusableElements));
  };

  var isModal = function isModal() {
    return !document.body.classList.contains(swalClasses['toast-shown']);
  };

  var isToast = function isToast() {
    return document.body.classList.contains(swalClasses['toast-shown']);
  };

  var isLoading = function isLoading() {
    return getPopup().hasAttribute('data-loading');
  };

  // Detect Node env
  var isNodeEnv = function isNodeEnv() {
    return typeof window === 'undefined' || typeof document === 'undefined';
  };

  var sweetHTML = ('\n <div aria-labelledby="' + swalClasses.title + '" aria-describedby="' + swalClasses.content + '" class="' + swalClasses.popup + '" tabindex="-1">\n   <div class="' + swalClasses.header + '">\n     <ul class="' + swalClasses.progresssteps + '"></ul>\n     <div class="' + swalClasses.icon + ' ' + iconTypes.error + '">\n       <span class="swal2-x-mark"><span class="swal2-x-mark-line-left"></span><span class="swal2-x-mark-line-right"></span></span>\n     </div>\n     <div class="' + swalClasses.icon + ' ' + iconTypes.question + '">\n       <span class="' + swalClasses['icon-text'] + '">?</span>\n      </div>\n     <div class="' + swalClasses.icon + ' ' + iconTypes.warning + '">\n       <span class="' + swalClasses['icon-text'] + '">!</span>\n      </div>\n     <div class="' + swalClasses.icon + ' ' + iconTypes.info + '">\n       <span class="' + swalClasses['icon-text'] + '">i</span>\n      </div>\n     <div class="' + swalClasses.icon + ' ' + iconTypes.success + '">\n       <div class="swal2-success-circular-line-left"></div>\n       <span class="swal2-success-line-tip"></span> <span class="swal2-success-line-long"></span>\n       <div class="swal2-success-ring"></div> <div class="swal2-success-fix"></div>\n       <div class="swal2-success-circular-line-right"></div>\n     </div>\n     <img class="' + swalClasses.image + '" />\n     <h2 class="' + swalClasses.title + '" id="' + swalClasses.title + '"></h2>\n     <button type="button" class="' + swalClasses.close + '">\xD7</button>\n   </div>\n   <div class="' + swalClasses.content + '">\n     <div id="' + swalClasses.content + '"></div>\n     <input class="' + swalClasses.input + '" />\n     <input type="file" class="' + swalClasses.file + '" />\n     <div class="' + swalClasses.range + '">\n       <input type="range" />\n       <output></output>\n     </div>\n     <select class="' + swalClasses.select + '"></select>\n     <div class="' + swalClasses.radio + '"></div>\n     <label for="' + swalClasses.checkbox + '" class="' + swalClasses.checkbox + '">\n       <input type="checkbox" />\n     </label>\n     <textarea class="' + swalClasses.textarea + '"></textarea>\n     <div class="' + swalClasses.validationerror + '" id="' + swalClasses.validationerror + '"></div>\n   </div>\n   <div class="' + swalClasses.actions + '">\n     <button type="button" class="' + swalClasses.confirm + '">OK</button>\n     <button type="button" class="' + swalClasses.cancel + '">Cancel</button>\n   </div>\n   <div class="' + swalClasses.footer + '">\n   </div>\n </div>\n').replace(/(^|\n)\s*/g, '');

  /*
   * Add modal + backdrop to DOM
   */
  var init = function init(params) {
    // Clean up the old popup if it exists
    var c = getContainer();
    if (c) {
      c.parentNode.removeChild(c);
      removeClass([document.documentElement, document.body], [swalClasses['no-backdrop'], swalClasses['has-input'], swalClasses['toast-shown']]);
    }

    if (isNodeEnv()) {
      error('SweetAlert2 requires document to initialize');
      return;
    }

    var container = document.createElement('div');
    container.className = swalClasses.container;
    container.innerHTML = sweetHTML;

    var targetElement = typeof params.target === 'string' ? document.querySelector(params.target) : params.target;
    targetElement.appendChild(container);

    var popup = getPopup();
    var content = getContent();
    var input = getChildByClass(content, swalClasses.input);
    var file = getChildByClass(content, swalClasses.file);
    var range = content.querySelector('.' + swalClasses.range + ' input');
    var rangeOutput = content.querySelector('.' + swalClasses.range + ' output');
    var select = getChildByClass(content, swalClasses.select);
    var checkbox = content.querySelector('.' + swalClasses.checkbox + ' input');
    var textarea = getChildByClass(content, swalClasses.textarea);

    // a11y
    popup.setAttribute('role', params.toast ? 'alert' : 'dialog');
    popup.setAttribute('aria-live', params.toast ? 'polite' : 'assertive');
    if (!params.toast) {
      popup.setAttribute('aria-modal', 'true');
    }

    var oldInputVal = void 0; // IE11 workaround, see #1109 for details
    var resetValidationError = function resetValidationError(e) {
      if (Swal.isVisible() && oldInputVal !== e.target.value) {
        Swal.resetValidationError();
      }
      oldInputVal = e.target.value;
    };

    input.oninput = resetValidationError;
    file.onchange = resetValidationError;
    select.onchange = resetValidationError;
    checkbox.onchange = resetValidationError;
    textarea.oninput = resetValidationError;

    range.oninput = function(e) {
      resetValidationError(e);
      rangeOutput.value = range.value;
    };

    range.onchange = function(e) {
      resetValidationError(e);
      range.nextSibling.value = range.value;
    };

    return popup;
  };

  var parseHtmlToContainer = function parseHtmlToContainer(param, target) {
    if (!param) {
      return hide(target);
    }

    if ((typeof param === 'undefined' ? 'undefined' : _typeof(param)) === 'object') {
      target.innerHTML = '';
      if (0 in param) {
        for (var i = 0; i in param; i++) {
          target.appendChild(param[i].cloneNode(true));
        }
      } else {
        target.appendChild(param.cloneNode(true));
      }
    } else if (param) {
      target.innerHTML = param;
    } else {}
    show(target);
  };

  var animationEndEvent = function() {
    // Prevent run in Node env
    if (isNodeEnv()) {
      return false;
    }

    var testEl = document.createElement('div');
    var transEndEventNames = {
      'WebkitAnimation': 'webkitAnimationEnd',
      'OAnimation': 'oAnimationEnd oanimationend',
      'animation': 'animationend'
    };
    for (var i in transEndEventNames) {
      if (transEndEventNames.hasOwnProperty(i) && typeof testEl.style[i] !== 'undefined') {
        return transEndEventNames[i];
      }
    }

    return false;
  }();

  // Measure width of scrollbar
  // https://github.com/twbs/bootstrap/blob/master/js/modal.js#L279-L286
  var measureScrollbar = function measureScrollbar() {
    var supportsTouch = 'ontouchstart' in window || navigator.msMaxTouchPoints;
    if (supportsTouch) {
      return 0;
    }
    var scrollDiv = document.createElement('div');
    scrollDiv.style.width = '50px';
    scrollDiv.style.height = '50px';
    scrollDiv.style.overflow = 'scroll';
    document.body.appendChild(scrollDiv);
    var scrollbarWidth = scrollDiv.offsetWidth - scrollDiv.clientWidth;
    document.body.removeChild(scrollDiv);
    return scrollbarWidth;
  };

  var fixScrollbar = function fixScrollbar() {
    // for queues, do not do this more than once
    if (states.previousBodyPadding !== null) {
      return;
    }
    // if the body has overflow
    if (document.body.scrollHeight > window.innerHeight) {
      // add padding so the content doesn't shift after removal of scrollbar
      states.previousBodyPadding = parseInt(window.getComputedStyle(document.body).getPropertyValue('padding-right'));
      document.body.style.paddingRight = states.previousBodyPadding + measureScrollbar() + 'px';
    }
  };

  var undoScrollbar = function undoScrollbar() {
    if (states.previousBodyPadding !== null) {
      document.body.style.paddingRight = states.previousBodyPadding;
      states.previousBodyPadding = null;
    }
  };

  // Fix iOS scrolling http://stackoverflow.com/q/39626302/1331425
  var iOSfix = function iOSfix() {
    var iOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
    if (iOS && !hasClass(document.body, swalClasses.iosfix)) {
      var offset = document.body.scrollTop;
      document.body.style.top = offset * -1 + 'px';
      addClass(document.body, swalClasses.iosfix);
    }
  };

  var undoIOSfix = function undoIOSfix() {
    if (hasClass(document.body, swalClasses.iosfix)) {
      var offset = parseInt(document.body.style.top, 10);
      removeClass(document.body, swalClasses.iosfix);
      document.body.style.top = '';
      document.body.scrollTop = offset * -1;
    }
  };

  var globalState = {};

  // Restore previous active (focused) element
  var restoreActiveElement = function restoreActiveElement() {
    var x = window.scrollX;
    var y = window.scrollY;
    globalState.restoreFocusTimeout = setTimeout(function() {
      if (globalState.previousActiveElement && globalState.previousActiveElement.focus) {
        globalState.previousActiveElement.focus();
        globalState.previousActiveElement = null;
      }
    }, 100); // issues/900
    if (typeof x !== 'undefined' && typeof y !== 'undefined') {
      // IE doesn't have scrollX/scrollY support
      window.scrollTo(x, y);
    }
  };

  /*
   * Global function to close sweetAlert
   */
  var close = function close(onClose, onAfterClose) {
    var container = getContainer();
    var popup = getPopup();
    if (!popup) {
      return;
    }

    if (onClose !== null && typeof onClose === 'function') {
      onClose(popup);
    }

    removeClass(popup, swalClasses.show);
    addClass(popup, swalClasses.hide);

    var removePopupAndResetState = function removePopupAndResetState() {
      if (!isToast()) {
        restoreActiveElement();
        globalState.keydownTarget.removeEventListener('keydown', globalState.keydownHandler, {
          capture: globalState.keydownListenerCapture
        });
        globalState.keydownHandlerAdded = false;
      }

      if (container.parentNode) {
        container.parentNode.removeChild(container);
      }
      removeClass([document.documentElement, document.body], [swalClasses.shown, swalClasses['height-auto'], swalClasses['no-backdrop'], swalClasses['has-input'], swalClasses['toast-shown']]);

      if (isModal()) {
        undoScrollbar();
        undoIOSfix();
      }

      if (onAfterClose !== null && typeof onAfterClose === 'function') {
        setTimeout(function() {
          onAfterClose();
        });
      }
    };

    // If animation is supported, animate
    if (animationEndEvent && !hasClass(popup, swalClasses.noanimation)) {
      popup.addEventListener(animationEndEvent, function swalCloseEventFinished() {
        popup.removeEventListener(animationEndEvent, swalCloseEventFinished);
        if (hasClass(popup, swalClasses.hide)) {
          removePopupAndResetState();
        }
      });
    } else {
      // Otherwise, remove immediately
      removePopupAndResetState();
    }
  };

  /*
   * Global function to determine if swal2 popup is shown
   */
  var isVisible$1 = function isVisible() {
    return !!getPopup();
  };

  /*
   * Global function to click 'Confirm' button
   */
  var clickConfirm = function clickConfirm() {
    return getConfirmButton().click();
  };

  /*
   * Global function to click 'Cancel' button
   */
  var clickCancel = function clickCancel() {
    return getCancelButton().click();
  };

  function fire() {
    var Swal = this;

    for (var _len = arguments.length, args = Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }

    return new(Function.prototype.bind.apply(Swal, [null].concat(args)))();
  }

  /**
   * Extends a Swal class making it able to be instantiated without the `new` keyword (and thus without `Swal.fire`)
   * @param ParentSwal
   * @returns {NoNewKeywordSwal}
   */
  function withNoNewKeyword(ParentSwal) {
    var NoNewKeywordSwal = function NoNewKeywordSwal() {
      for (var _len = arguments.length, args = Array(_len), _key = 0; _key < _len; _key++) {
        args[_key] = arguments[_key];
      }

      if (!(this instanceof NoNewKeywordSwal)) {
        return new(Function.prototype.bind.apply(NoNewKeywordSwal, [null].concat(args)))();
      }
      Object.getPrototypeOf(NoNewKeywordSwal).apply(this, args);
    };
    NoNewKeywordSwal.prototype = _extends(Object.create(ParentSwal.prototype), {
      constructor: NoNewKeywordSwal
    });

    if (typeof Object.setPrototypeOf === 'function') {
      Object.setPrototypeOf(NoNewKeywordSwal, ParentSwal);
    } else {
      // Android 4.4
      // eslint-disable-next-line
      NoNewKeywordSwal.__proto__ = ParentSwal;
    }
    return NoNewKeywordSwal;
  }

  var defaultParams = {
    title: '',
    titleText: '',
    text: '',
    html: '',
    footer: '',
    type: null,
    toast: false,
    customClass: '',
    target: 'body',
    backdrop: true,
    animation: true,
    heightAuto: true,
    allowOutsideClick: true,
    allowEscapeKey: true,
    allowEnterKey: true,
    stopKeydownPropagation: true,
    keydownListenerCapture: false,
    showConfirmButton: true,
    showCancelButton: false,
    preConfirm: null,
    confirmButtonText: 'OK',
    confirmButtonAriaLabel: '',
    confirmButtonColor: null,
    confirmButtonClass: null,
    cancelButtonText: 'Cancel',
    cancelButtonAriaLabel: '',
    cancelButtonColor: null,
    cancelButtonClass: null,
    buttonsStyling: true,
    reverseButtons: false,
    focusConfirm: true,
    focusCancel: false,
    showCloseButton: false,
    closeButtonAriaLabel: 'Close this dialog',
    showLoaderOnConfirm: false,
    imageUrl: null,
    imageWidth: null,
    imageHeight: null,
    imageAlt: '',
    imageClass: null,
    timer: null,
    width: null,
    padding: null,
    background: null,
    input: null,
    inputPlaceholder: '',
    inputValue: '',
    inputOptions: {},
    inputAutoTrim: true,
    inputClass: null,
    inputAttributes: {},
    inputValidator: null,
    grow: false,
    position: 'center',
    progressSteps: [],
    currentProgressStep: null,
    progressStepsDistance: null,
    onBeforeOpen: null,
    onAfterClose: null,
    onOpen: null,
    onClose: null,
    useRejections: false,
    expectRejections: false
  };

  var deprecatedParams = ['useRejections', 'expectRejections'];

  /**
   * Is valid parameter
   * @param {String} paramName
   */
  var isValidParameter = function isValidParameter(paramName) {
    return defaultParams.hasOwnProperty(paramName) || paramName === 'extraParams';
  };

  /**
   * Is deprecated parameter
   * @param {String} paramName
   */
  var isDeprecatedParameter = function isDeprecatedParameter(paramName) {
    return deprecatedParams.indexOf(paramName) !== -1;
  };

  /**
   * Show relevant warnings for given params
   *
   * @param params
   */
  var showWarningsForParams = function showWarningsForParams(params) {
    for (var param in params) {
      if (!isValidParameter(param)) {
        warn('Unknown parameter "' + param + '"');
      }
      if (isDeprecatedParameter(param)) {
        warnOnce('The parameter "' + param + '" is deprecated and will be removed in the next major release.');
      }
    }
  };

  var deprecationWarning = '"setDefaults" & "resetDefaults" methods are deprecated in favor of "mixin" method and will be removed in the next major release. For new projects, use "mixin". For past projects already using "setDefaults", support will be provided through an additional package.';
  var defaults$1 = {};

  function withGlobalDefaults(ParentSwal) {
    var SwalWithGlobalDefaults = function(_ParentSwal) {
      inherits(SwalWithGlobalDefaults, _ParentSwal);

      function SwalWithGlobalDefaults() {
        classCallCheck(this, SwalWithGlobalDefaults);
        return possibleConstructorReturn(this, (SwalWithGlobalDefaults.__proto__ || Object.getPrototypeOf(SwalWithGlobalDefaults)).apply(this, arguments));
      }

      createClass(SwalWithGlobalDefaults, [{
        key: '_main',
        value: function _main(params) {
          return get(SwalWithGlobalDefaults.prototype.__proto__ || Object.getPrototypeOf(SwalWithGlobalDefaults.prototype), '_main', this).call(this, _extends({}, defaults$1, params));
        }
      }], [{
        key: 'setDefaults',
        value: function setDefaults(params) {
          warnOnce(deprecationWarning);
          if (!params || (typeof params === 'undefined' ? 'undefined' : _typeof(params)) !== 'object') {
            throw new TypeError('SweetAlert2: The argument for setDefaults() is required and has to be a object');
          }
          showWarningsForParams(params);
          // assign valid params from `params` to `defaults`
          Object.keys(params).forEach(function(param) {
            if (ParentSwal.isValidParameter(param)) {
              defaults$1[param] = params[param];
            }
          });
        }
      }, {
        key: 'resetDefaults',
        value: function resetDefaults() {
          warnOnce(deprecationWarning);
          defaults$1 = {};
        }
      }]);
      return SwalWithGlobalDefaults;
    }(ParentSwal);

    // Set default params if `window._swalDefaults` is an object


    if (typeof window !== 'undefined' && _typeof(window._swalDefaults) === 'object') {
      SwalWithGlobalDefaults.setDefaults(window._swalDefaults);
    }

    return SwalWithGlobalDefaults;
  }

  /**
   * Returns an extended version of `Swal` containing `params` as defaults.
   * Useful for reusing Swal configuration.
   *
   * For example:
   *
   * Before:
   * const textPromptOptions = { input: 'text', showCancelButton: true }
   * const {value: firstName} = await Swal({ ...textPromptOptions, title: 'What is your first name?' })
   * const {value: lastName} = await Swal({ ...textPromptOptions, title: 'What is your last name?' })
   *
   * After:
   * const TextPrompt = Swal.mixin({ input: 'text', showCancelButton: true })
   * const {value: firstName} = await TextPrompt('What is your first name?')
   * const {value: lastName} = await TextPrompt('What is your last name?')
   *
   * @param mixinParams
   */
  function mixin(mixinParams) {
    var Swal = this;
    return withNoNewKeyword(function(_Swal) {
      inherits(MixinSwal, _Swal);

      function MixinSwal() {
        classCallCheck(this, MixinSwal);
        return possibleConstructorReturn(this, (MixinSwal.__proto__ || Object.getPrototypeOf(MixinSwal)).apply(this, arguments));
      }

      createClass(MixinSwal, [{
        key: '_main',
        value: function _main(params) {
          return get(MixinSwal.prototype.__proto__ || Object.getPrototypeOf(MixinSwal.prototype), '_main', this).call(this, _extends({}, mixinParams, params));
        }
      }]);
      return MixinSwal;
    }(Swal));
  }

  // private global state for the queue feature
  var currentSteps = [];

  /*
   * Global function for chaining sweetAlert popups
   */
  var queue = function queue(steps) {
    var swal = this;
    currentSteps = steps;
    var resetQueue = function resetQueue() {
      currentSteps = [];
      document.body.removeAttribute('data-swal2-queue-step');
    };
    var queueResult = [];
    return new Promise(function(resolve, reject) {
      (function step(i, callback) {
        if (i < currentSteps.length) {
          document.body.setAttribute('data-swal2-queue-step', i);

          swal(currentSteps[i]).then(function(result) {
            if (typeof result.value !== 'undefined') {
              queueResult.push(result.value);
              step(i + 1, callback);
            } else {
              resetQueue();
              resolve({
                dismiss: result.dismiss
              });
            }
          });
        } else {
          resetQueue();
          resolve({
            value: queueResult
          });
        }
      })(0);
    });
  };

  /*
   * Global function for getting the index of current popup in queue
   */
  var getQueueStep = function getQueueStep() {
    return document.body.getAttribute('data-swal2-queue-step');
  };

  /*
   * Global function for inserting a popup to the queue
   */
  var insertQueueStep = function insertQueueStep(step, index) {
    if (index && index < currentSteps.length) {
      return currentSteps.splice(index, 0, step);
    }
    return currentSteps.push(step);
  };

  /*
   * Global function for deleting a popup from the queue
   */
  var deleteQueueStep = function deleteQueueStep(index) {
    if (typeof currentSteps[index] !== 'undefined') {
      currentSteps.splice(index, 1);
    }
  };

  /**
   * Show spinner instead of Confirm button and disable Cancel button
   */
  var showLoading = function showLoading() {
    var popup = getPopup();
    if (!popup) {
      Swal('');
    }
    popup = getPopup();
    var actions = getActions();
    var confirmButton = getConfirmButton();
    var cancelButton = getCancelButton();

    show(actions);
    show(confirmButton);
    addClass([popup, actions], swalClasses.loading);
    confirmButton.disabled = true;
    cancelButton.disabled = true;

    popup.setAttribute('data-loading', true);
    popup.setAttribute('aria-busy', true);
    popup.focus();
  };

  /**
   * Show spinner instead of Confirm button and disable Cancel button
   */
  var getTimerLeft = function getTimerLeft() {
    return globalState.timeout && globalState.timeout.getTimerLeft();
  };



  var staticMethods = Object.freeze({
    isValidParameter: isValidParameter,
    isDeprecatedParameter: isDeprecatedParameter,
    argsToParams: argsToParams,
    adaptInputValidator: adaptInputValidator,
    close: close,
    closePopup: close,
    closeModal: close,
    closeToast: close,
    isVisible: isVisible$1,
    clickConfirm: clickConfirm,
    clickCancel: clickCancel,
    getPopup: getPopup,
    getTitle: getTitle,
    getContent: getContent,
    getImage: getImage,
    getButtonsWrapper: getButtonsWrapper,
    getActions: getActions,
    getConfirmButton: getConfirmButton,
    getCancelButton: getCancelButton,
    getFooter: getFooter,
    isLoading: isLoading,
    fire: fire,
    mixin: mixin,
    queue: queue,
    getQueueStep: getQueueStep,
    insertQueueStep: insertQueueStep,
    deleteQueueStep: deleteQueueStep,
    showLoading: showLoading,
    enableLoading: showLoading,
    getTimerLeft: getTimerLeft
  });

  // https://github.com/Riim/symbol-polyfill/blob/master/index.js

  var _Symbol = typeof Symbol === 'function' ? Symbol : function() {
    var idCounter = 0;

    function _Symbol(key) {
      return '__' + key + '_' + Math.floor(Math.random() * 1e9) + '_' + ++idCounter + '__';
    }
    _Symbol.iterator = _Symbol('Symbol.iterator');
    return _Symbol;
  }();

  // WeakMap polyfill, needed for Android 4.4
  // Related issue: https://github.com/sweetalert2/sweetalert2/issues/1071
  // http://webreflection.blogspot.fi/2015/04/a-weakmap-polyfill-in-20-lines-of-code.html

  var WeakMap$1 = typeof WeakMap === 'function' ? WeakMap : function(s, dP, hOP) {
    function WeakMap() {
      dP(this, s, {
        value: _Symbol('WeakMap')
      });
    }
    WeakMap.prototype = {
      'delete': function del(o) {
        delete o[this[s]];
      },
      get: function get(o) {
        return o[this[s]];
      },
      has: function has(o) {
        return hOP.call(o, this[s]);
      },
      set: function set(o, v) {
        dP(o, this[s], {
          configurable: true,
          value: v
        });
      }
    };
    return WeakMap;
  }(_Symbol('WeakMap'), Object.defineProperty, {}.hasOwnProperty);

  /**
   * This module containts `WeakMap`s for each effectively-"private  property" that a `swal` has.
   * For example, to set the private property "foo" of `this` to "bar", you can `privateProps.foo.set(this, 'bar')`
   * This is the approach that Babel will probably take to implement private methods/fields
   *   https://github.com/tc39/proposal-private-methods
   *   https://github.com/babel/babel/pull/7555
   * Once we have the changes from that PR in Babel, and our core class fits reasonable in *one module*
   *   then we can use that language feature.
   */

  var privateProps = {
    promise: new WeakMap$1(),
    innerParams: new WeakMap$1(),
    domCache: new WeakMap$1()
  };

  /**
   * Show spinner instead of Confirm button and disable Cancel button
   */
  function hideLoading() {
    var innerParams = privateProps.innerParams.get(this);
    var domCache = privateProps.domCache.get(this);
    if (!innerParams.showConfirmButton) {
      hide(domCache.confirmButton);
      if (!innerParams.showCancelButton) {
        hide(domCache.actions);
      }
    }
    removeClass([domCache.popup, domCache.actions], swalClasses.loading);
    domCache.popup.removeAttribute('aria-busy');
    domCache.popup.removeAttribute('data-loading');
    domCache.confirmButton.disabled = false;
    domCache.cancelButton.disabled = false;
  }

  // Get input element by specified type or, if type isn't specified, by params.input
  function getInput(inputType) {
    var innerParams = privateProps.innerParams.get(this);
    var domCache = privateProps.domCache.get(this);
    inputType = inputType || innerParams.input;
    if (!inputType) {
      return null;
    }
    switch (inputType) {
      case 'select':
      case 'textarea':
      case 'file':
        return getChildByClass(domCache.content, swalClasses[inputType]);
      case 'checkbox':
        return domCache.popup.querySelector('.' + swalClasses.checkbox + ' input');
      case 'radio':
        return domCache.popup.querySelector('.' + swalClasses.radio + ' input:checked') || domCache.popup.querySelector('.' + swalClasses.radio + ' input:first-child');
      case 'range':
        return domCache.popup.querySelector('.' + swalClasses.range + ' input');
      default:
        return getChildByClass(domCache.content, swalClasses.input);
    }
  }

  function enableButtons() {
    var domCache = privateProps.domCache.get(this);
    domCache.confirmButton.disabled = false;
    domCache.cancelButton.disabled = false;
  }

  function disableButtons() {
    var domCache = privateProps.domCache.get(this);
    domCache.confirmButton.disabled = true;
    domCache.cancelButton.disabled = true;
  }

  function enableConfirmButton() {
    var domCache = privateProps.domCache.get(this);
    domCache.confirmButton.disabled = false;
  }

  function disableConfirmButton() {
    var domCache = privateProps.domCache.get(this);
    domCache.confirmButton.disabled = true;
  }

  function enableInput() {
    var input = this.getInput();
    if (!input) {
      return false;
    }
    if (input.type === 'radio') {
      var radiosContainer = input.parentNode.parentNode;
      var radios = radiosContainer.querySelectorAll('input');
      for (var i = 0; i < radios.length; i++) {
        radios[i].disabled = false;
      }
    } else {
      input.disabled = false;
    }
  }

  function disableInput() {
    var input = this.getInput();
    if (!input) {
      return false;
    }
    if (input && input.type === 'radio') {
      var radiosContainer = input.parentNode.parentNode;
      var radios = radiosContainer.querySelectorAll('input');
      for (var i = 0; i < radios.length; i++) {
        radios[i].disabled = true;
      }
    } else {
      input.disabled = true;
    }
  }

  // Show block with validation error
  function showValidationError(error) {
    var domCache = privateProps.domCache.get(this);
    domCache.validationError.innerHTML = error;
    var popupComputedStyle = window.getComputedStyle(domCache.popup);
    domCache.validationError.style.marginLeft = '-' + popupComputedStyle.getPropertyValue('padding-left');
    domCache.validationError.style.marginRight = '-' + popupComputedStyle.getPropertyValue('padding-right');
    show(domCache.validationError);

    var input = this.getInput();
    if (input) {
      input.setAttribute('aria-invalid', true);
      input.setAttribute('aria-describedBy', swalClasses.validationerror);
      focusInput(input);
      addClass(input, swalClasses.inputerror);
    }
  }

  // Hide block with validation error
  function resetValidationError() {
    var domCache = privateProps.domCache.get(this);
    if (domCache.validationError) {
      hide(domCache.validationError);
    }

    var input = this.getInput();
    if (input) {
      input.removeAttribute('aria-invalid');
      input.removeAttribute('aria-describedBy');
      removeClass(input, swalClasses.inputerror);
    }
  }

  var Timer = function Timer(callback, delay) {
    classCallCheck(this, Timer);

    var id, started, running;
    var remaining = delay;
    this.start = function() {
      running = true;
      started = new Date();
      id = setTimeout(callback, remaining);
    };
    this.stop = function() {
      running = false;
      clearTimeout(id);
      remaining -= new Date() - started;
    };
    this.getTimerLeft = function() {
      if (running) {
        this.stop();
        this.start();
      }
      return remaining;
    };
    this.getStateRunning = function() {
      return running;
    };
    this.start();
  };

  var defaultInputValidators = {
    email: function email(string, extraParams) {
      return (/^[a-zA-Z0-9.+_-]+@[a-zA-Z0-9.-]+\.[a-zA-Z0-9-]{2,24}$/.test(string) ? Promise.resolve() : Promise.reject(extraParams && extraParams.validationMessage ? extraParams.validationMessage : 'Invalid email address'));
    },
    url: function url(string, extraParams) {
      // taken from https://stackoverflow.com/a/3809435/1331425
      return (/^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_+.~#?&//=]*)$/.test(string) ? Promise.resolve() : Promise.reject(extraParams && extraParams.validationMessage ? extraParams.validationMessage : 'Invalid URL'));
    }
  };

  /**
   * Set type, text and actions on popup
   *
   * @param params
   * @returns {boolean}
   */
  function setParameters(params) {
    // Use default `inputValidator` for supported input types if not provided
    if (!params.inputValidator) {
      Object.keys(defaultInputValidators).forEach(function(key) {
        if (params.input === key) {
          params.inputValidator = params.expectRejections ? defaultInputValidators[key] : Swal.adaptInputValidator(defaultInputValidators[key]);
        }
      });
    }

    // Determine if the custom target element is valid
    if (!params.target || typeof params.target === 'string' && !document.querySelector(params.target) || typeof params.target !== 'string' && !params.target.appendChild) {
      warn('Target parameter is not valid, defaulting to "body"');
      params.target = 'body';
    }

    var popup = void 0;
    var oldPopup = getPopup();
    var targetElement = typeof params.target === 'string' ? document.querySelector(params.target) : params.target;
    // If the model target has changed, refresh the popup
    if (oldPopup && targetElement && oldPopup.parentNode !== targetElement.parentNode) {
      popup = init(params);
    } else {
      popup = oldPopup || init(params);
    }

    // Set popup width
    if (params.width) {
      popup.style.width = typeof params.width === 'number' ? params.width + 'px' : params.width;
    }

    // Set popup padding
    if (params.padding) {
      popup.style.padding = typeof params.padding === 'number' ? params.padding + 'px' : params.padding;
    }

    // Set popup background
    if (params.background) {
      popup.style.background = params.background;
    }
    var popupBackgroundColor = window.getComputedStyle(popup).getPropertyValue('background-color');
    var successIconParts = popup.querySelectorAll('[class^=swal2-success-circular-line], .swal2-success-fix');
    for (var i = 0; i < successIconParts.length; i++) {
      successIconParts[i].style.backgroundColor = popupBackgroundColor;
    }

    var container = getContainer();
    var title = getTitle();
    var content = getContent().querySelector('#' + swalClasses.content);
    var actions = getActions();
    var confirmButton = getConfirmButton();
    var cancelButton = getCancelButton();
    var closeButton = getCloseButton();
    var footer = getFooter();

    // Title
    if (params.titleText) {
      title.innerText = params.titleText;
    } else if (params.title) {
      title.innerHTML = params.title.split('\n').join('<br />');
    }

    if (typeof params.backdrop === 'string') {
      getContainer().style.background = params.backdrop;
    } else if (!params.backdrop) {
      addClass([document.documentElement, document.body], swalClasses['no-backdrop']);
    }

    // Content as HTML
    if (params.html) {
      parseHtmlToContainer(params.html, content);

      // Content as plain text
    } else if (params.text) {
      content.textContent = params.text;
      show(content);
    } else {
      hide(content);
    }

    // Position
    if (params.position in swalClasses) {
      addClass(container, swalClasses[params.position]);
    } else {
      warn('The "position" parameter is not valid, defaulting to "center"');
      addClass(container, swalClasses.center);
    }

    // Grow
    if (params.grow && typeof params.grow === 'string') {
      var growClass = 'grow-' + params.grow;
      if (growClass in swalClasses) {
        addClass(container, swalClasses[growClass]);
      }
    }

    // Animation
    if (typeof params.animation === 'function') {
      params.animation = params.animation.call();
    }

    // Close button
    if (params.showCloseButton) {
      closeButton.setAttribute('aria-label', params.closeButtonAriaLabel);
      show(closeButton);
    } else {
      hide(closeButton);
    }

    // Default Class
    popup.className = swalClasses.popup;
    if (params.toast) {
      addClass([document.documentElement, document.body], swalClasses['toast-shown']);
      addClass(popup, swalClasses.toast);
    } else {
      addClass(popup, swalClasses.modal);
    }

    // Custom Class
    if (params.customClass) {
      addClass(popup, params.customClass);
    }

    // Progress steps
    var progressStepsContainer = getProgressSteps();
    var currentProgressStep = parseInt(params.currentProgressStep === null ? Swal.getQueueStep() : params.currentProgressStep, 10);
    if (params.progressSteps && params.progressSteps.length) {
      show(progressStepsContainer);
      empty(progressStepsContainer);
      if (currentProgressStep >= params.progressSteps.length) {
        warn('Invalid currentProgressStep parameter, it should be less than progressSteps.length ' + '(currentProgressStep like JS arrays starts from 0)');
      }
      params.progressSteps.forEach(function(step, index) {
        var circle = document.createElement('li');
        addClass(circle, swalClasses.progresscircle);
        circle.innerHTML = step;
        if (index === currentProgressStep) {
          addClass(circle, swalClasses.activeprogressstep);
        }
        progressStepsContainer.appendChild(circle);
        if (index !== params.progressSteps.length - 1) {
          var line = document.createElement('li');
          addClass(line, swalClasses.progressline);
          if (params.progressStepsDistance) {
            line.style.width = params.progressStepsDistance;
          }
          progressStepsContainer.appendChild(line);
        }
      });
    } else {
      hide(progressStepsContainer);
    }

    // Icon
    var icons = getIcons();
    for (var _i = 0; _i < icons.length; _i++) {
      hide(icons[_i]);
    }
    if (params.type) {
      var validType = false;
      for (var iconType in iconTypes) {
        if (params.type === iconType) {
          validType = true;
          break;
        }
      }
      if (!validType) {
        error('Unknown alert type: ' + params.type);
        return false;
      }
      var icon = popup.querySelector('.' + swalClasses.icon + '.' + iconTypes[params.type]);
      show(icon);

      // Animate icon
      if (params.animation) {
        addClass(icon, 'swal2-animate-' + params.type + '-icon');
      }
    }

    // Custom image
    var image = getImage();
    if (params.imageUrl) {
      image.setAttribute('src', params.imageUrl);
      image.setAttribute('alt', params.imageAlt);
      show(image);

      if (params.imageWidth) {
        image.setAttribute('width', params.imageWidth);
      } else {
        image.removeAttribute('width');
      }

      if (params.imageHeight) {
        image.setAttribute('height', params.imageHeight);
      } else {
        image.removeAttribute('height');
      }

      image.className = swalClasses.image;
      if (params.imageClass) {
        addClass(image, params.imageClass);
      }
    } else {
      hide(image);
    }

    // Cancel button
    if (params.showCancelButton) {
      cancelButton.style.display = 'inline-block';
    } else {
      hide(cancelButton);
    }

    // Confirm button
    if (params.showConfirmButton) {
      removeStyleProperty(confirmButton, 'display');
    } else {
      hide(confirmButton);
    }

    // Actions (buttons) wrapper
    if (!params.showConfirmButton && !params.showCancelButton) {
      hide(actions);
    } else {
      show(actions);
    }

    // Edit text on confirm and cancel buttons
    confirmButton.innerHTML = params.confirmButtonText;
    cancelButton.innerHTML = params.cancelButtonText;

    // ARIA labels for confirm and cancel buttons
    confirmButton.setAttribute('aria-label', params.confirmButtonAriaLabel);
    cancelButton.setAttribute('aria-label', params.cancelButtonAriaLabel);

    // Add buttons custom classes
    confirmButton.className = swalClasses.confirm;
    addClass(confirmButton, params.confirmButtonClass);
    cancelButton.className = swalClasses.cancel;
    addClass(cancelButton, params.cancelButtonClass);

    // Buttons styling
    if (params.buttonsStyling) {
      addClass([confirmButton, cancelButton], swalClasses.styled);

      // Buttons background colors
      if (params.confirmButtonColor) {
        confirmButton.style.backgroundColor = params.confirmButtonColor;
      }
      if (params.cancelButtonColor) {
        cancelButton.style.backgroundColor = params.cancelButtonColor;
      }

      // Loading state
      var confirmButtonBackgroundColor = window.getComputedStyle(confirmButton).getPropertyValue('background-color');
      confirmButton.style.borderLeftColor = confirmButtonBackgroundColor;
      confirmButton.style.borderRightColor = confirmButtonBackgroundColor;
    } else {
      removeClass([confirmButton, cancelButton], swalClasses.styled);

      confirmButton.style.backgroundColor = confirmButton.style.borderLeftColor = confirmButton.style.borderRightColor = '';
      cancelButton.style.backgroundColor = cancelButton.style.borderLeftColor = cancelButton.style.borderRightColor = '';
    }

    // Footer
    parseHtmlToContainer(params.footer, footer);

    // CSS animation
    if (params.animation === true) {
      removeClass(popup, swalClasses.noanimation);
    } else {
      addClass(popup, swalClasses.noanimation);
    }

    // showLoaderOnConfirm && preConfirm
    if (params.showLoaderOnConfirm && !params.preConfirm) {
      warn('showLoaderOnConfirm is set to true, but preConfirm is not defined.\n' + 'showLoaderOnConfirm should be used together with preConfirm, see usage example:\n' + 'https://sweetalert2.github.io/#ajax-request');
    }
  }

  /**
   * Open popup, add necessary classes and styles, fix scrollbar
   *
   * @param {Array} params
   */
  var openPopup = function openPopup(params) {
    var container = getContainer();
    var popup = getPopup();

    if (params.onBeforeOpen !== null && typeof params.onBeforeOpen === 'function') {
      params.onBeforeOpen(popup);
    }

    if (params.animation) {
      addClass(popup, swalClasses.show);
      addClass(container, swalClasses.fade);
      removeClass(popup, swalClasses.hide);
    } else {
      removeClass(popup, swalClasses.fade);
    }
    show(popup);

    // scrolling is 'hidden' until animation is done, after that 'auto'
    container.style.overflowY = 'hidden';
    if (animationEndEvent && !hasClass(popup, swalClasses.noanimation)) {
      popup.addEventListener(animationEndEvent, function swalCloseEventFinished() {
        popup.removeEventListener(animationEndEvent, swalCloseEventFinished);
        container.style.overflowY = 'auto';
      });
    } else {
      container.style.overflowY = 'auto';
    }

    addClass([document.documentElement, document.body, container], swalClasses.shown);
    if (params.heightAuto && params.backdrop && !params.toast) {
      addClass([document.documentElement, document.body], swalClasses['height-auto']);
    }

    if (isModal()) {
      fixScrollbar();
      iOSfix();
    }
    if (!globalState.previousActiveElement) {
      globalState.previousActiveElement = document.activeElement;
    }
    if (params.onOpen !== null && typeof params.onOpen === 'function') {
      setTimeout(function() {
        params.onOpen(popup);
      });
    }
  };

  function _main(userParams) {
    var _this = this;

    showWarningsForParams(userParams);

    var innerParams = _extends({}, defaultParams, userParams);
    setParameters(innerParams);
    Object.freeze(innerParams);
    privateProps.innerParams.set(this, innerParams);

    // clear the previous timer
    if (globalState.timeout) {
      globalState.timeout.stop();
      delete globalState.timeout;
    }

    // clear the restore focus timeout
    clearTimeout(globalState.restoreFocusTimeout);

    var domCache = {
      popup: getPopup(),
      container: getContainer(),
      content: getContent(),
      actions: getActions(),
      confirmButton: getConfirmButton(),
      cancelButton: getCancelButton(),
      closeButton: getCloseButton(),
      validationError: getValidationError(),
      progressSteps: getProgressSteps()
    };
    privateProps.domCache.set(this, domCache);

    var constructor = this.constructor;

    return new Promise(function(resolve, reject) {
      // functions to handle all resolving/rejecting/settling
      var succeedWith = function succeedWith(value) {
        constructor.closePopup(innerParams.onClose, innerParams.onAfterClose); // TODO: make closePopup an *instance* method
        if (innerParams.useRejections) {
          resolve(value);
        } else {
          resolve({
            value: value
          });
        }
      };
      var dismissWith = function dismissWith(dismiss) {
        constructor.closePopup(innerParams.onClose, innerParams.onAfterClose);
        if (innerParams.useRejections) {
          reject(dismiss);
        } else {
          resolve({
            dismiss: dismiss
          });
        }
      };
      var errorWith = function errorWith(error$$1) {
        constructor.closePopup(innerParams.onClose, innerParams.onAfterClose);
        reject(error$$1);
      };

      // Close on timer
      if (innerParams.timer) {
        globalState.timeout = new Timer(function() {
          dismissWith('timer');
          delete globalState.timeout;
        }, innerParams.timer);
      }

      // Get the value of the popup input
      var getInputValue = function getInputValue() {
        var input = _this.getInput();
        if (!input) {
          return null;
        }
        switch (innerParams.input) {
          case 'checkbox':
            return input.checked ? 1 : 0;
          case 'radio':
            return input.checked ? input.value : null;
          case 'file':
            return input.files.length ? input.files[0] : null;
          default:
            return innerParams.inputAutoTrim ? input.value.trim() : input.value;
        }
      };

      // input autofocus
      if (innerParams.input) {
        setTimeout(function() {
          var input = _this.getInput();
          if (input) {
            focusInput(input);
          }
        }, 0);
      }

      var confirm = function confirm(value) {
        if (innerParams.showLoaderOnConfirm) {
          constructor.showLoading(); // TODO: make showLoading an *instance* method
        }

        if (innerParams.preConfirm) {
          _this.resetValidationError();
          var preConfirmPromise = Promise.resolve().then(function() {
            return innerParams.preConfirm(value, innerParams.extraParams);
          });
          if (innerParams.expectRejections) {
            preConfirmPromise.then(function(preConfirmValue) {
              return succeedWith(preConfirmValue || value);
            }, function(validationError) {
              _this.hideLoading();
              if (validationError) {
                _this.showValidationError(validationError);
              }
            });
          } else {
            preConfirmPromise.then(function(preConfirmValue) {
              if (isVisible(domCache.validationError) || preConfirmValue === false) {
                _this.hideLoading();
              } else {
                succeedWith(preConfirmValue || value);
              }
            }, function(error$$1) {
              return errorWith(error$$1);
            });
          }
        } else {
          succeedWith(value);
        }
      };

      // Mouse interactions
      var onButtonEvent = function onButtonEvent(event) {
        var e = event || window.event;
        var target = e.target || e.srcElement;
        var confirmButton = domCache.confirmButton,
          cancelButton = domCache.cancelButton;

        var targetedConfirm = confirmButton && (confirmButton === target || confirmButton.contains(target));
        var targetedCancel = cancelButton && (cancelButton === target || cancelButton.contains(target));

        switch (e.type) {
          case 'click':
            // Clicked 'confirm'
            if (targetedConfirm && constructor.isVisible()) {
              _this.disableButtons();
              if (innerParams.input) {
                var inputValue = getInputValue();

                if (innerParams.inputValidator) {
                  _this.disableInput();
                  var validationPromise = Promise.resolve().then(function() {
                    return innerParams.inputValidator(inputValue, innerParams.extraParams);
                  });
                  if (innerParams.expectRejections) {
                    validationPromise.then(function() {
                      _this.enableButtons();
                      _this.enableInput();
                      confirm(inputValue);
                    }, function(validationError) {
                      _this.enableButtons();
                      _this.enableInput();
                      if (validationError) {
                        _this.showValidationError(validationError);
                      }
                    });
                  } else {
                    validationPromise.then(function(validationError) {
                      _this.enableButtons();
                      _this.enableInput();
                      if (validationError) {
                        _this.showValidationError(validationError);
                      } else {
                        confirm(inputValue);
                      }
                    }, function(error$$1) {
                      return errorWith(error$$1);
                    });
                  }
                } else {
                  confirm(inputValue);
                }
              } else {
                confirm(true);
              }

              // Clicked 'cancel'
            } else if (targetedCancel && constructor.isVisible()) {
              _this.disableButtons();
              dismissWith(constructor.DismissReason.cancel);
            }
            break;
          default:
        }
      };

      var buttons = domCache.popup.querySelectorAll('button');
      for (var i = 0; i < buttons.length; i++) {
        buttons[i].onclick = onButtonEvent;
        buttons[i].onmouseover = onButtonEvent;
        buttons[i].onmouseout = onButtonEvent;
        buttons[i].onmousedown = onButtonEvent;
      }

      // Closing popup by close button
      domCache.closeButton.onclick = function() {
        dismissWith(constructor.DismissReason.close);
      };

      if (innerParams.toast) {
        // Closing popup by internal click
        domCache.popup.onclick = function(e) {
          if (innerParams.showConfirmButton || innerParams.showCancelButton || innerParams.showCloseButton || innerParams.input) {
            return;
          }
          constructor.closePopup(innerParams.onClose, innerParams.onAfterClose);
          dismissWith(constructor.DismissReason.close);
        };
      } else {
        var ignoreOutsideClick = false;

        // Ignore click events that had mousedown on the popup but mouseup on the container
        // This can happen when the user drags a slider
        domCache.popup.onmousedown = function() {
          domCache.container.onmouseup = function(e) {
            domCache.container.onmouseup = undefined;
            // We only check if the mouseup target is the container because usually it doesn't
            // have any other direct children aside of the popup
            if (e.target === domCache.container) {
              ignoreOutsideClick = true;
            }
          };
        };

        // Ignore click events that had mousedown on the container but mouseup on the popup
        domCache.container.onmousedown = function() {
          domCache.popup.onmouseup = function(e) {
            domCache.popup.onmouseup = undefined;
            // We also need to check if the mouseup target is a child of the popup
            if (e.target === domCache.popup || domCache.popup.contains(e.target)) {
              ignoreOutsideClick = true;
            }
          };
        };

        domCache.container.onclick = function(e) {
          if (ignoreOutsideClick) {
            ignoreOutsideClick = false;
            return;
          }
          if (e.target !== domCache.container) {
            return;
          }
          if (callIfFunction(innerParams.allowOutsideClick)) {
            dismissWith(constructor.DismissReason.backdrop);
          }
        };
      }

      // Reverse buttons (Confirm on the right side)
      if (innerParams.reverseButtons) {
        domCache.confirmButton.parentNode.insertBefore(domCache.cancelButton, domCache.confirmButton);
      } else {
        domCache.confirmButton.parentNode.insertBefore(domCache.confirmButton, domCache.cancelButton);
      }

      // Focus handling
      var setFocus = function setFocus(index, increment) {
        var focusableElements = getFocusableElements(innerParams.focusCancel);
        // search for visible elements and select the next possible match
        for (var _i = 0; _i < focusableElements.length; _i++) {
          index = index + increment;

          // rollover to first item
          if (index === focusableElements.length) {
            index = 0;

            // go to last item
          } else if (index === -1) {
            index = focusableElements.length - 1;
          }

          // determine if element is visible
          var el = focusableElements[index];
          if (isVisible(el)) {
            return el.focus();
          }
        }
        // no visible focusable elements, focus the popup
        domCache.popup.focus();
      };

      var keydownHandler = function keydownHandler(e, innerParams) {
        if (innerParams.stopKeydownPropagation) {
          e.stopPropagation();
        }

        var arrowKeys = ['ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown', 'Left', 'Right', 'Up', 'Down' // IE11
        ];

        if (e.key === 'Enter' && !e.isComposing) {
          if (e.target && _this.getInput() && e.target.outerHTML === _this.getInput().outerHTML) {
            if (['textarea', 'file'].indexOf(innerParams.input) !== -1) {
              return; // do not submit
            }

            constructor.clickConfirm();
            e.preventDefault();
          }

          // TAB
        } else if (e.key === 'Tab') {
          var targetElement = e.target || e.srcElement;

          var focusableElements = getFocusableElements(innerParams.focusCancel);
          var btnIndex = -1; // Find the button - note, this is a nodelist, not an array.
          for (var _i2 = 0; _i2 < focusableElements.length; _i2++) {
            if (targetElement === focusableElements[_i2]) {
              btnIndex = _i2;
              break;
            }
          }

          if (!e.shiftKey) {
            // Cycle to the next button
            setFocus(btnIndex, 1);
          } else {
            // Cycle to the prev button
            setFocus(btnIndex, -1);
          }
          e.stopPropagation();
          e.preventDefault();

          // ARROWS - switch focus between buttons
        } else if (arrowKeys.indexOf(e.key) !== -1) {
          // focus Cancel button if Confirm button is currently focused
          if (document.activeElement === domCache.confirmButton && isVisible(domCache.cancelButton)) {
            domCache.cancelButton.focus();
            // and vice versa
          } else if (document.activeElement === domCache.cancelButton && isVisible(domCache.confirmButton)) {
            domCache.confirmButton.focus();
          }

          // ESC
        } else if ((e.key === 'Escape' || e.key === 'Esc') && callIfFunction(innerParams.allowEscapeKey) === true) {
          dismissWith(constructor.DismissReason.esc);
        }
      };

      if (globalState.keydownHandlerAdded) {
        globalState.keydownTarget.removeEventListener('keydown', globalState.keydownHandler, {
          capture: globalState.keydownListenerCapture
        });
        globalState.keydownHandlerAdded = false;
      }

      if (!innerParams.toast) {
        globalState.keydownHandler = function(e) {
          return keydownHandler(e, innerParams);
        };
        globalState.keydownTarget = innerParams.keydownListenerCapture ? window : domCache.popup;
        globalState.keydownListenerCapture = innerParams.keydownListenerCapture;
        globalState.keydownTarget.addEventListener('keydown', globalState.keydownHandler, {
          capture: globalState.keydownListenerCapture
        });
        globalState.keydownHandlerAdded = true;
      }

      _this.enableButtons();
      _this.hideLoading();
      _this.resetValidationError();

      if (innerParams.input) {
        addClass(document.body, swalClasses['has-input']);
      }

      // inputs
      var inputTypes = ['input', 'file', 'range', 'select', 'radio', 'checkbox', 'textarea'];
      var input = void 0;
      for (var _i3 = 0; _i3 < inputTypes.length; _i3++) {
        var inputClass = swalClasses[inputTypes[_i3]];
        var inputContainer = getChildByClass(domCache.content, inputClass);
        input = _this.getInput(inputTypes[_i3]);

        // set attributes
        if (input) {
          for (var j in input.attributes) {
            if (input.attributes.hasOwnProperty(j)) {
              var attrName = input.attributes[j].name;
              if (attrName !== 'type' && attrName !== 'value') {
                input.removeAttribute(attrName);
              }
            }
          }
          for (var attr in innerParams.inputAttributes) {
            input.setAttribute(attr, innerParams.inputAttributes[attr]);
          }
        }

        // set class
        inputContainer.className = inputClass;
        if (innerParams.inputClass) {
          addClass(inputContainer, innerParams.inputClass);
        }

        hide(inputContainer);
      }

      var populateInputOptions = void 0;
      switch (innerParams.input) {
        case 'text':
        case 'email':
        case 'password':
        case 'number':
        case 'tel':
        case 'url':
          input = getChildByClass(domCache.content, swalClasses.input);
          input.value = innerParams.inputValue;
          input.placeholder = innerParams.inputPlaceholder;
          input.type = innerParams.input;
          show(input);
          break;
        case 'file':
          input = getChildByClass(domCache.content, swalClasses.file);
          input.placeholder = innerParams.inputPlaceholder;
          input.type = innerParams.input;
          show(input);
          break;
        case 'range':
          var range = getChildByClass(domCache.content, swalClasses.range);
          var rangeInput = range.querySelector('input');
          var rangeOutput = range.querySelector('output');
          rangeInput.value = innerParams.inputValue;
          rangeInput.type = innerParams.input;
          rangeOutput.value = innerParams.inputValue;
          show(range);
          break;
        case 'select':
          var select = getChildByClass(domCache.content, swalClasses.select);
          select.innerHTML = '';
          if (innerParams.inputPlaceholder) {
            var placeholder = document.createElement('option');
            placeholder.innerHTML = innerParams.inputPlaceholder;
            placeholder.value = '';
            placeholder.disabled = true;
            placeholder.selected = true;
            select.appendChild(placeholder);
          }
          populateInputOptions = function populateInputOptions(inputOptions) {
            inputOptions.forEach(function(_ref) {
              var _ref2 = slicedToArray(_ref, 2),
                optionValue = _ref2[0],
                optionLabel = _ref2[1];

              var option = document.createElement('option');
              option.value = optionValue;
              option.innerHTML = optionLabel;
              if (innerParams.inputValue.toString() === optionValue.toString()) {
                option.selected = true;
              }
              select.appendChild(option);
            });
            show(select);
            select.focus();
          };
          break;
        case 'radio':
          var radio = getChildByClass(domCache.content, swalClasses.radio);
          radio.innerHTML = '';
          populateInputOptions = function populateInputOptions(inputOptions) {
            inputOptions.forEach(function(_ref3) {
              var _ref4 = slicedToArray(_ref3, 2),
                radioValue = _ref4[0],
                radioLabel = _ref4[1];

              var radioInput = document.createElement('input');
              var radioLabelElement = document.createElement('label');
              radioInput.type = 'radio';
              radioInput.name = swalClasses.radio;
              radioInput.value = radioValue;
              if (innerParams.inputValue.toString() === radioValue.toString()) {
                radioInput.checked = true;
              }
              radioLabelElement.innerHTML = radioLabel;
              radioLabelElement.insertBefore(radioInput, radioLabelElement.firstChild);
              radio.appendChild(radioLabelElement);
            });
            show(radio);
            var radios = radio.querySelectorAll('input');
            if (radios.length) {
              radios[0].focus();
            }
          };
          break;
        case 'checkbox':
          var checkbox = getChildByClass(domCache.content, swalClasses.checkbox);
          var checkboxInput = _this.getInput('checkbox');
          checkboxInput.type = 'checkbox';
          checkboxInput.value = 1;
          checkboxInput.id = swalClasses.checkbox;
          checkboxInput.checked = Boolean(innerParams.inputValue);
          var label = checkbox.getElementsByTagName('span');
          if (label.length) {
            checkbox.removeChild(label[0]);
          }
          label = document.createElement('span');
          label.innerHTML = innerParams.inputPlaceholder;
          checkbox.appendChild(label);
          show(checkbox);
          break;
        case 'textarea':
          var textarea = getChildByClass(domCache.content, swalClasses.textarea);
          textarea.value = innerParams.inputValue;
          textarea.placeholder = innerParams.inputPlaceholder;
          show(textarea);
          break;
        case null:
          break;
        default:
          error('Unexpected type of input! Expected "text", "email", "password", "number", "tel", "select", "radio", "checkbox", "textarea", "file" or "url", got "' + innerParams.input + '"');
          break;
      }

      if (innerParams.input === 'select' || innerParams.input === 'radio') {
        var processInputOptions = function processInputOptions(inputOptions) {
          return populateInputOptions(formatInputOptions(inputOptions));
        };
        if (isThenable(innerParams.inputOptions)) {
          constructor.showLoading();
          innerParams.inputOptions.then(function(inputOptions) {
            _this.hideLoading();
            processInputOptions(inputOptions);
          });
        } else if (_typeof(innerParams.inputOptions) === 'object') {
          processInputOptions(innerParams.inputOptions);
        } else {
          error('Unexpected type of inputOptions! Expected object, Map or Promise, got ' + _typeof(innerParams.inputOptions));
        }
      } else if (['text', 'email', 'number', 'tel', 'textarea'].indexOf(innerParams.input) !== -1 && isThenable(innerParams.inputValue)) {
        constructor.showLoading();
        hide(input);
        innerParams.inputValue.then(function(inputValue) {
          input.value = innerParams.input === 'number' ? parseFloat(inputValue) || 0 : inputValue + '';
          show(input);
          _this.hideLoading();
        }).catch(function(err) {
          error('Error in inputValue promise: ' + err);
          input.value = '';
          show(input);
          _this.hideLoading();
        });
      }

      openPopup(innerParams);

      if (!innerParams.toast) {
        if (!callIfFunction(innerParams.allowEnterKey)) {
          if (document.activeElement) {
            document.activeElement.blur();
          }
        } else if (innerParams.focusCancel && isVisible(domCache.cancelButton)) {
          domCache.cancelButton.focus();
        } else if (innerParams.focusConfirm && isVisible(domCache.confirmButton)) {
          domCache.confirmButton.focus();
        } else {
          setFocus(-1, 1);
        }
      }

      // fix scroll
      domCache.container.scrollTop = 0;
    });
  }



  var instanceMethods = Object.freeze({
    hideLoading: hideLoading,
    disableLoading: hideLoading,
    getInput: getInput,
    enableButtons: enableButtons,
    disableButtons: disableButtons,
    enableConfirmButton: enableConfirmButton,
    disableConfirmButton: disableConfirmButton,
    enableInput: enableInput,
    disableInput: disableInput,
    showValidationError: showValidationError,
    resetValidationError: resetValidationError,
    _main: _main
  });

  var currentInstance = void 0;

  // SweetAlert constructor
  function SweetAlert() {
    // Prevent run in Node env
    if (typeof window === 'undefined') {
      return;
    }

    // Check for the existence of Promise
    if (typeof Promise === 'undefined') {
      error('This package requires a Promise library, please include a shim to enable it in this browser (See: https://github.com/sweetalert2/sweetalert2/wiki/Migration-from-SweetAlert-to-SweetAlert2#1-ie-support)');
    }

    for (var _len = arguments.length, args = Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }

    if (typeof args[0] === 'undefined') {
      error('SweetAlert2 expects at least 1 attribute!');
      return false;
    }

    currentInstance = this;

    var outerParams = Object.freeze(this.constructor.argsToParams(args));

    Object.defineProperties(this, {
      params: {
        value: outerParams,
        writable: false,
        enumerable: true
      }
    });

    var promise = this._main(this.params);
    privateProps.promise.set(this, promise);
  }

  // `catch` cannot be the name of a module export, so we define our thenable methods here instead
  SweetAlert.prototype.then = function(onFulfilled, onRejected) {
    var promise = privateProps.promise.get(this);
    return promise.then(onFulfilled, onRejected);
  };
  SweetAlert.prototype.catch = function(onRejected) {
    var promise = privateProps.promise.get(this);
    return promise.catch(onRejected);
  };
  SweetAlert.prototype.finally = function(onFinally) {
    var promise = privateProps.promise.get(this);
    return promise.finally(onFinally);
  };

  // Assign instance methods from src/instanceMethods/*.js to prototype
  _extends(SweetAlert.prototype, instanceMethods);

  // Assign static methods from src/staticMethods/*.js to constructor
  _extends(SweetAlert, staticMethods);

  // Proxy to instance methods to constructor, for now, for backwards compatibility
  Object.keys(instanceMethods).forEach(function(key) {
    SweetAlert[key] = function() {
      if (currentInstance) {
        var _currentInstance;

        return (_currentInstance = currentInstance)[key].apply(_currentInstance, arguments);
      }
    };
  });

  SweetAlert.DismissReason = DismissReason;

  SweetAlert.noop = function() {};

  SweetAlert.version = version;

  var Swal = withNoNewKeyword(withGlobalDefaults(SweetAlert));
  Swal.default = Swal;

  return Swal;

})));
if (typeof window !== 'undefined' && window.Sweetalert2) {
  window.swal = window.sweetAlert = window.Swal = window.SweetAlert = window.Sweetalert2
}

"undefined" != typeof document && function(e, t) {
  var n = e.createElement("style");
  if (e.getElementsByTagName("head")[0].appendChild(n), n.styleSheet) n.styleSheet.disabled || (n.styleSheet.cssText = t);
  else try {
    n.innerHTML = t
  } catch (e) {
    n.innerText = t
  }
}(document, "@-webkit-keyframes swal2-show {\n" +
  "  0% {\n" +
  "    -webkit-transform: scale(0.7);\n" +
  "            transform: scale(0.7); }\n" +
  "  45% {\n" +
  "    -webkit-transform: scale(1.05);\n" +
  "            transform: scale(1.05); }\n" +
  "  80% {\n" +
  "    -webkit-transform: scale(0.95);\n" +
  "            transform: scale(0.95); }\n" +
  "  100% {\n" +
  "    -webkit-transform: scale(1);\n" +
  "            transform: scale(1); } }\n" +
  "\n" +
  "@keyframes swal2-show {\n" +
  "  0% {\n" +
  "    -webkit-transform: scale(0.7);\n" +
  "            transform: scale(0.7); }\n" +
  "  45% {\n" +
  "    -webkit-transform: scale(1.05);\n" +
  "            transform: scale(1.05); }\n" +
  "  80% {\n" +
  "    -webkit-transform: scale(0.95);\n" +
  "            transform: scale(0.95); }\n" +
  "  100% {\n" +
  "    -webkit-transform: scale(1);\n" +
  "            transform: scale(1); } }\n" +
  "\n" +
  "@-webkit-keyframes swal2-hide {\n" +
  "  0% {\n" +
  "    -webkit-transform: scale(1);\n" +
  "            transform: scale(1);\n" +
  "    opacity: 1; }\n" +
  "  100% {\n" +
  "    -webkit-transform: scale(0.5);\n" +
  "            transform: scale(0.5);\n" +
  "    opacity: 0; } }\n" +
  "\n" +
  "@keyframes swal2-hide {\n" +
  "  0% {\n" +
  "    -webkit-transform: scale(1);\n" +
  "            transform: scale(1);\n" +
  "    opacity: 1; }\n" +
  "  100% {\n" +
  "    -webkit-transform: scale(0.5);\n" +
  "            transform: scale(0.5);\n" +
  "    opacity: 0; } }\n" +
  "\n" +
  "@-webkit-keyframes swal2-animate-success-line-tip {\n" +
  "  0% {\n" +
  "    top: 1.1875em;\n" +
  "    left: .0625em;\n" +
  "    width: 0; }\n" +
  "  54% {\n" +
  "    top: 1.0625em;\n" +
  "    left: .125em;\n" +
  "    width: 0; }\n" +
  "  70% {\n" +
  "    top: 2.1875em;\n" +
  "    left: -.375em;\n" +
  "    width: 3.125em; }\n" +
  "  84% {\n" +
  "    top: 3em;\n" +
  "    left: 1.3125em;\n" +
  "    width: 1.0625em; }\n" +
  "  100% {\n" +
  "    top: 2.8125em;\n" +
  "    left: .875em;\n" +
  "    width: 1.5625em; } }\n" +
  "\n" +
  "@keyframes swal2-animate-success-line-tip {\n" +
  "  0% {\n" +
  "    top: 1.1875em;\n" +
  "    left: .0625em;\n" +
  "    width: 0; }\n" +
  "  54% {\n" +
  "    top: 1.0625em;\n" +
  "    left: .125em;\n" +
  "    width: 0; }\n" +
  "  70% {\n" +
  "    top: 2.1875em;\n" +
  "    left: -.375em;\n" +
  "    width: 3.125em; }\n" +
  "  84% {\n" +
  "    top: 3em;\n" +
  "    left: 1.3125em;\n" +
  "    width: 1.0625em; }\n" +
  "  100% {\n" +
  "    top: 2.8125em;\n" +
  "    left: .875em;\n" +
  "    width: 1.5625em; } }\n" +
  "\n" +
  "@-webkit-keyframes swal2-animate-success-line-long {\n" +
  "  0% {\n" +
  "    top: 3.375em;\n" +
  "    right: 2.875em;\n" +
  "    width: 0; }\n" +
  "  65% {\n" +
  "    top: 3.375em;\n" +
  "    right: 2.875em;\n" +
  "    width: 0; }\n" +
  "  84% {\n" +
  "    top: 2.1875em;\n" +
  "    right: 0;\n" +
  "    width: 3.4375em; }\n" +
  "  100% {\n" +
  "    top: 2.375em;\n" +
  "    right: .5em;\n" +
  "    width: 2.9375em; } }\n" +
  "\n" +
  "@keyframes swal2-animate-success-line-long {\n" +
  "  0% {\n" +
  "    top: 3.375em;\n" +
  "    right: 2.875em;\n" +
  "    width: 0; }\n" +
  "  65% {\n" +
  "    top: 3.375em;\n" +
  "    right: 2.875em;\n" +
  "    width: 0; }\n" +
  "  84% {\n" +
  "    top: 2.1875em;\n" +
  "    right: 0;\n" +
  "    width: 3.4375em; }\n" +
  "  100% {\n" +
  "    top: 2.375em;\n" +
  "    right: .5em;\n" +
  "    width: 2.9375em; } }\n" +
  "\n" +
  "@-webkit-keyframes swal2-rotate-success-circular-line {\n" +
  "  0% {\n" +
  "    -webkit-transform: rotate(-45deg);\n" +
  "            transform: rotate(-45deg); }\n" +
  "  5% {\n" +
  "    -webkit-transform: rotate(-45deg);\n" +
  "            transform: rotate(-45deg); }\n" +
  "  12% {\n" +
  "    -webkit-transform: rotate(-405deg);\n" +
  "            transform: rotate(-405deg); }\n" +
  "  100% {\n" +
  "    -webkit-transform: rotate(-405deg);\n" +
  "            transform: rotate(-405deg); } }\n" +
  "\n" +
  "@keyframes swal2-rotate-success-circular-line {\n" +
  "  0% {\n" +
  "    -webkit-transform: rotate(-45deg);\n" +
  "            transform: rotate(-45deg); }\n" +
  "  5% {\n" +
  "    -webkit-transform: rotate(-45deg);\n" +
  "            transform: rotate(-45deg); }\n" +
  "  12% {\n" +
  "    -webkit-transform: rotate(-405deg);\n" +
  "            transform: rotate(-405deg); }\n" +
  "  100% {\n" +
  "    -webkit-transform: rotate(-405deg);\n" +
  "            transform: rotate(-405deg); } }\n" +
  "\n" +
  "@-webkit-keyframes swal2-animate-error-x-mark {\n" +
  "  0% {\n" +
  "    margin-top: 1.625em;\n" +
  "    -webkit-transform: scale(0.4);\n" +
  "            transform: scale(0.4);\n" +
  "    opacity: 0; }\n" +
  "  50% {\n" +
  "    margin-top: 1.625em;\n" +
  "    -webkit-transform: scale(0.4);\n" +
  "            transform: scale(0.4);\n" +
  "    opacity: 0; }\n" +
  "  80% {\n" +
  "    margin-top: -.375em;\n" +
  "    -webkit-transform: scale(1.15);\n" +
  "            transform: scale(1.15); }\n" +
  "  100% {\n" +
  "    margin-top: 0;\n" +
  "    -webkit-transform: scale(1);\n" +
  "            transform: scale(1);\n" +
  "    opacity: 1; } }\n" +
  "\n" +
  "@keyframes swal2-animate-error-x-mark {\n" +
  "  0% {\n" +
  "    margin-top: 1.625em;\n" +
  "    -webkit-transform: scale(0.4);\n" +
  "            transform: scale(0.4);\n" +
  "    opacity: 0; }\n" +
  "  50% {\n" +
  "    margin-top: 1.625em;\n" +
  "    -webkit-transform: scale(0.4);\n" +
  "            transform: scale(0.4);\n" +
  "    opacity: 0; }\n" +
  "  80% {\n" +
  "    margin-top: -.375em;\n" +
  "    -webkit-transform: scale(1.15);\n" +
  "            transform: scale(1.15); }\n" +
  "  100% {\n" +
  "    margin-top: 0;\n" +
  "    -webkit-transform: scale(1);\n" +
  "            transform: scale(1);\n" +
  "    opacity: 1; } }\n" +
  "\n" +
  "@-webkit-keyframes swal2-animate-error-icon {\n" +
  "  0% {\n" +
  "    -webkit-transform: rotateX(100deg);\n" +
  "            transform: rotateX(100deg);\n" +
  "    opacity: 0; }\n" +
  "  100% {\n" +
  "    -webkit-transform: rotateX(0deg);\n" +
  "            transform: rotateX(0deg);\n" +
  "    opacity: 1; } }\n" +
  "\n" +
  "@keyframes swal2-animate-error-icon {\n" +
  "  0% {\n" +
  "    -webkit-transform: rotateX(100deg);\n" +
  "            transform: rotateX(100deg);\n" +
  "    opacity: 0; }\n" +
  "  100% {\n" +
  "    -webkit-transform: rotateX(0deg);\n" +
  "            transform: rotateX(0deg);\n" +
  "    opacity: 1; } }\n" +
  "\n" +
  "body.swal2-toast-shown.swal2-has-input > .swal2-container > .swal2-toast {\n" +
  "  flex-direction: column;\n" +
  "  align-items: stretch; }\n" +
  "  body.swal2-toast-shown.swal2-has-input > .swal2-container > .swal2-toast .swal2-actions {\n" +
  "    flex: 1;\n" +
  "    align-self: stretch;\n" +
  "    justify-content: flex-end;\n" +
  "    height: 2.2em; }\n" +
  "  body.swal2-toast-shown.swal2-has-input > .swal2-container > .swal2-toast .swal2-loading {\n" +
  "    justify-content: center; }\n" +
  "  body.swal2-toast-shown.swal2-has-input > .swal2-container > .swal2-toast .swal2-input {\n" +
  "    height: 2em;\n" +
  "    margin: .3125em auto;\n" +
  "    font-size: 1em; }\n" +
  "  body.swal2-toast-shown.swal2-has-input > .swal2-container > .swal2-toast .swal2-validationerror {\n" +
  "    font-size: 1em; }\n" +
  "\n" +
  "body.swal2-toast-shown > .swal2-container {\n" +
  "  position: fixed;\n" +
  "  background-color: transparent; }\n" +
  "  body.swal2-toast-shown > .swal2-container.swal2-shown {\n" +
  "    background-color: transparent; }\n" +
  "  body.swal2-toast-shown > .swal2-container.swal2-top {\n" +
  "    top: 0;\n" +
  "    right: auto;\n" +
  "    bottom: auto;\n" +
  "    left: 50%;\n" +
  "    -webkit-transform: translateX(-50%);\n" +
  "            transform: translateX(-50%); }\n" +
  "  body.swal2-toast-shown > .swal2-container.swal2-top-end, body.swal2-toast-shown > .swal2-container.swal2-top-right {\n" +
  "    top: 0;\n" +
  "    right: 0;\n" +
  "    bottom: auto;\n" +
  "    left: auto; }\n" +
  "  body.swal2-toast-shown > .swal2-container.swal2-top-start, body.swal2-toast-shown > .swal2-container.swal2-top-left {\n" +
  "    top: 0;\n" +
  "    right: auto;\n" +
  "    bottom: auto;\n" +
  "    left: 0; }\n" +
  "  body.swal2-toast-shown > .swal2-container.swal2-center-start, body.swal2-toast-shown > .swal2-container.swal2-center-left {\n" +
  "    top: 50%;\n" +
  "    right: auto;\n" +
  "    bottom: auto;\n" +
  "    left: 0;\n" +
  "    -webkit-transform: translateY(-50%);\n" +
  "            transform: translateY(-50%); }\n" +
  "  body.swal2-toast-shown > .swal2-container.swal2-center {\n" +
  "    top: 50%;\n" +
  "    right: auto;\n" +
  "    bottom: auto;\n" +
  "    left: 50%;\n" +
  "    -webkit-transform: translate(-50%, -50%);\n" +
  "            transform: translate(-50%, -50%); }\n" +
  "  body.swal2-toast-shown > .swal2-container.swal2-center-end, body.swal2-toast-shown > .swal2-container.swal2-center-right {\n" +
  "    top: 50%;\n" +
  "    right: 0;\n" +
  "    bottom: auto;\n" +
  "    left: auto;\n" +
  "    -webkit-transform: translateY(-50%);\n" +
  "            transform: translateY(-50%); }\n" +
  "  body.swal2-toast-shown > .swal2-container.swal2-bottom-start, body.swal2-toast-shown > .swal2-container.swal2-bottom-left {\n" +
  "    top: auto;\n" +
  "    right: auto;\n" +
  "    bottom: 0;\n" +
  "    left: 0; }\n" +
  "  body.swal2-toast-shown > .swal2-container.swal2-bottom {\n" +
  "    top: auto;\n" +
  "    right: auto;\n" +
  "    bottom: 0;\n" +
  "    left: 50%;\n" +
  "    -webkit-transform: translateX(-50%);\n" +
  "            transform: translateX(-50%); }\n" +
  "  body.swal2-toast-shown > .swal2-container.swal2-bottom-end, body.swal2-toast-shown > .swal2-container.swal2-bottom-right {\n" +
  "    top: auto;\n" +
  "    right: 0;\n" +
  "    bottom: 0;\n" +
  "    left: auto; }\n" +
  "\n" +
  ".swal2-popup.swal2-toast {\n" +
  "  flex-direction: row;\n" +
  "  align-items: center;\n" +
  "  width: auto;\n" +
  "  padding: 0.625em;\n" +
  "  box-shadow: 0 0 0.625em #d9d9d9;\n" +
  "  overflow-y: hidden; }\n" +
  "  .swal2-popup.swal2-toast .swal2-header {\n" +
  "    flex-direction: row; }\n" +
  "  .swal2-popup.swal2-toast .swal2-title {\n" +
  "    justify-content: flex-start;\n" +
  "    margin: 0 .6em;\n" +
  "    font-size: 1em; }\n" +
  "  .swal2-popup.swal2-toast .swal2-close {\n" +
  "    position: initial; }\n" +
  "  .swal2-popup.swal2-toast .swal2-content {\n" +
  "    justify-content: flex-start;\n" +
  "    font-size: 1em; }\n" +
  "  .swal2-popup.swal2-toast .swal2-icon {\n" +
  "    width: 2em;\n" +
  "    min-width: 2em;\n" +
  "    height: 2em;\n" +
  "    margin: 0; }\n" +
  "    .swal2-popup.swal2-toast .swal2-icon-text {\n" +
  "      font-size: 2em;\n" +
  "      font-weight: bold;\n" +
  "      line-height: 1em; }\n" +
  "    .swal2-popup.swal2-toast .swal2-icon.swal2-success .swal2-success-ring {\n" +
  "      width: 2em;\n" +
  "      height: 2em; }\n" +
  "    .swal2-popup.swal2-toast .swal2-icon.swal2-error [class^='swal2-x-mark-line'] {\n" +
  "      top: .875em;\n" +
  "      width: 1.375em; }\n" +
  "      .swal2-popup.swal2-toast .swal2-icon.swal2-error [class^='swal2-x-mark-line'][class$='left'] {\n" +
  "        left: .3125em; }\n" +
  "      .swal2-popup.swal2-toast .swal2-icon.swal2-error [class^='swal2-x-mark-line'][class$='right'] {\n" +
  "        right: .3125em; }\n" +
  "  .swal2-popup.swal2-toast .swal2-actions {\n" +
  "    height: auto;\n" +
  "    margin: 0 .3125em; }\n" +
  "  .swal2-popup.swal2-toast .swal2-styled {\n" +
  "    margin: 0 .3125em;\n" +
  "    padding: .3125em .625em;\n" +
  "    font-size: 1em; }\n" +
  "    .swal2-popup.swal2-toast .swal2-styled:focus {\n" +
  "      box-shadow: 0 0 0 0.0625em #fff, 0 0 0 0.125em rgba(50, 100, 150, 0.4); }\n" +
  "  .swal2-popup.swal2-toast .swal2-success {\n" +
  "    border-color: #a5dc86; }\n" +
  "    .swal2-popup.swal2-toast .swal2-success [class^='swal2-success-circular-line'] {\n" +
  "      position: absolute;\n" +
  "      width: 2em;\n" +
  "      height: 2.8125em;\n" +
  "      -webkit-transform: rotate(45deg);\n" +
  "              transform: rotate(45deg);\n" +
  "      border-radius: 50%; }\n" +
  "      .swal2-popup.swal2-toast .swal2-success [class^='swal2-success-circular-line'][class$='left'] {\n" +
  "        top: -.25em;\n" +
  "        left: -.9375em;\n" +
  "        -webkit-transform: rotate(-45deg);\n" +
  "                transform: rotate(-45deg);\n" +
  "        -webkit-transform-origin: 2em 2em;\n" +
  "                transform-origin: 2em 2em;\n" +
  "        border-radius: 4em 0 0 4em; }\n" +
  "      .swal2-popup.swal2-toast .swal2-success [class^='swal2-success-circular-line'][class$='right'] {\n" +
  "        top: -.25em;\n" +
  "        left: .9375em;\n" +
  "        -webkit-transform-origin: 0 2em;\n" +
  "                transform-origin: 0 2em;\n" +
  "        border-radius: 0 4em 4em 0; }\n" +
  "    .swal2-popup.swal2-toast .swal2-success .swal2-success-ring {\n" +
  "      width: 2em;\n" +
  "      height: 2em; }\n" +
  "    .swal2-popup.swal2-toast .swal2-success .swal2-success-fix {\n" +
  "      top: 0;\n" +
  "      left: .4375em;\n" +
  "      width: .4375em;\n" +
  "      height: 2.6875em; }\n" +
  "    .swal2-popup.swal2-toast .swal2-success [class^='swal2-success-line'] {\n" +
  "      height: .3125em; }\n" +
  "      .swal2-popup.swal2-toast .swal2-success [class^='swal2-success-line'][class$='tip'] {\n" +
  "        top: 1.125em;\n" +
  "        left: .1875em;\n" +
  "        width: .75em; }\n" +
  "      .swal2-popup.swal2-toast .swal2-success [class^='swal2-success-line'][class$='long'] {\n" +
  "        top: .9375em;\n" +
  "        right: .1875em;\n" +
  "        width: 1.375em; }\n" +
  "  .swal2-popup.swal2-toast.swal2-show {\n" +
  "    -webkit-animation: showSweetToast .5s;\n" +
  "            animation: showSweetToast .5s; }\n" +
  "  .swal2-popup.swal2-toast.swal2-hide {\n" +
  "    -webkit-animation: hideSweetToast .2s forwards;\n" +
  "            animation: hideSweetToast .2s forwards; }\n" +
  "  .swal2-popup.swal2-toast .swal2-animate-success-icon .swal2-success-line-tip {\n" +
  "    -webkit-animation: animate-toast-success-tip .75s;\n" +
  "            animation: animate-toast-success-tip .75s; }\n" +
  "  .swal2-popup.swal2-toast .swal2-animate-success-icon .swal2-success-line-long {\n" +
  "    -webkit-animation: animate-toast-success-long .75s;\n" +
  "            animation: animate-toast-success-long .75s; }\n" +
  "\n" +
  "@-webkit-keyframes showSweetToast {\n" +
  "  0% {\n" +
  "    -webkit-transform: translateY(-0.625em) rotateZ(2deg);\n" +
  "            transform: translateY(-0.625em) rotateZ(2deg);\n" +
  "    opacity: 0; }\n" +
  "  33% {\n" +
  "    -webkit-transform: translateY(0) rotateZ(-2deg);\n" +
  "            transform: translateY(0) rotateZ(-2deg);\n" +
  "    opacity: .5; }\n" +
  "  66% {\n" +
  "    -webkit-transform: translateY(0.3125em) rotateZ(2deg);\n" +
  "            transform: translateY(0.3125em) rotateZ(2deg);\n" +
  "    opacity: .7; }\n" +
  "  100% {\n" +
  "    -webkit-transform: translateY(0) rotateZ(0);\n" +
  "            transform: translateY(0) rotateZ(0);\n" +
  "    opacity: 1; } }\n" +
  "\n" +
  "@keyframes showSweetToast {\n" +
  "  0% {\n" +
  "    -webkit-transform: translateY(-0.625em) rotateZ(2deg);\n" +
  "            transform: translateY(-0.625em) rotateZ(2deg);\n" +
  "    opacity: 0; }\n" +
  "  33% {\n" +
  "    -webkit-transform: translateY(0) rotateZ(-2deg);\n" +
  "            transform: translateY(0) rotateZ(-2deg);\n" +
  "    opacity: .5; }\n" +
  "  66% {\n" +
  "    -webkit-transform: translateY(0.3125em) rotateZ(2deg);\n" +
  "            transform: translateY(0.3125em) rotateZ(2deg);\n" +
  "    opacity: .7; }\n" +
  "  100% {\n" +
  "    -webkit-transform: translateY(0) rotateZ(0);\n" +
  "            transform: translateY(0) rotateZ(0);\n" +
  "    opacity: 1; } }\n" +
  "\n" +
  "@-webkit-keyframes hideSweetToast {\n" +
  "  0% {\n" +
  "    opacity: 1; }\n" +
  "  33% {\n" +
  "    opacity: .5; }\n" +
  "  100% {\n" +
  "    -webkit-transform: rotateZ(1deg);\n" +
  "            transform: rotateZ(1deg);\n" +
  "    opacity: 0; } }\n" +
  "\n" +
  "@keyframes hideSweetToast {\n" +
  "  0% {\n" +
  "    opacity: 1; }\n" +
  "  33% {\n" +
  "    opacity: .5; }\n" +
  "  100% {\n" +
  "    -webkit-transform: rotateZ(1deg);\n" +
  "            transform: rotateZ(1deg);\n" +
  "    opacity: 0; } }\n" +
  "\n" +
  "@-webkit-keyframes animate-toast-success-tip {\n" +
  "  0% {\n" +
  "    top: .5625em;\n" +
  "    left: .0625em;\n" +
  "    width: 0; }\n" +
  "  54% {\n" +
  "    top: .125em;\n" +
  "    left: .125em;\n" +
  "    width: 0; }\n" +
  "  70% {\n" +
  "    top: .625em;\n" +
  "    left: -.25em;\n" +
  "    width: 1.625em; }\n" +
  "  84% {\n" +
  "    top: 1.0625em;\n" +
  "    left: .75em;\n" +
  "    width: .5em; }\n" +
  "  100% {\n" +
  "    top: 1.125em;\n" +
  "    left: .1875em;\n" +
  "    width: .75em; } }\n" +
  "\n" +
  "@keyframes animate-toast-success-tip {\n" +
  "  0% {\n" +
  "    top: .5625em;\n" +
  "    left: .0625em;\n" +
  "    width: 0; }\n" +
  "  54% {\n" +
  "    top: .125em;\n" +
  "    left: .125em;\n" +
  "    width: 0; }\n" +
  "  70% {\n" +
  "    top: .625em;\n" +
  "    left: -.25em;\n" +
  "    width: 1.625em; }\n" +
  "  84% {\n" +
  "    top: 1.0625em;\n" +
  "    left: .75em;\n" +
  "    width: .5em; }\n" +
  "  100% {\n" +
  "    top: 1.125em;\n" +
  "    left: .1875em;\n" +
  "    width: .75em; } }\n" +
  "\n" +
  "@-webkit-keyframes animate-toast-success-long {\n" +
  "  0% {\n" +
  "    top: 1.625em;\n" +
  "    right: 1.375em;\n" +
  "    width: 0; }\n" +
  "  65% {\n" +
  "    top: 1.25em;\n" +
  "    right: .9375em;\n" +
  "    width: 0; }\n" +
  "  84% {\n" +
  "    top: .9375em;\n" +
  "    right: 0;\n" +
  "    width: 1.125em; }\n" +
  "  100% {\n" +
  "    top: .9375em;\n" +
  "    right: .1875em;\n" +
  "    width: 1.375em; } }\n" +
  "\n" +
  "@keyframes animate-toast-success-long {\n" +
  "  0% {\n" +
  "    top: 1.625em;\n" +
  "    right: 1.375em;\n" +
  "    width: 0; }\n" +
  "  65% {\n" +
  "    top: 1.25em;\n" +
  "    right: .9375em;\n" +
  "    width: 0; }\n" +
  "  84% {\n" +
  "    top: .9375em;\n" +
  "    right: 0;\n" +
  "    width: 1.125em; }\n" +
  "  100% {\n" +
  "    top: .9375em;\n" +
  "    right: .1875em;\n" +
  "    width: 1.375em; } }\n" +
  "\n" +
  "body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown) {\n" +
  "  overflow-y: hidden; }\n" +
  "\n" +
  "body.swal2-height-auto {\n" +
  "  height: auto !important; }\n" +
  "\n" +
  "body.swal2-no-backdrop .swal2-shown {\n" +
  "  top: auto;\n" +
  "  right: auto;\n" +
  "  bottom: auto;\n" +
  "  left: auto;\n" +
  "  background-color: transparent; }\n" +
  "  body.swal2-no-backdrop .swal2-shown > .swal2-modal {\n" +
  "    box-shadow: 0 0 10px rgba(0, 0, 0, 0.4); }\n" +
  "  body.swal2-no-backdrop .swal2-shown.swal2-top {\n" +
  "    top: 0;\n" +
  "    left: 50%;\n" +
  "    -webkit-transform: translateX(-50%);\n" +
  "            transform: translateX(-50%); }\n" +
  "  body.swal2-no-backdrop .swal2-shown.swal2-top-start, body.swal2-no-backdrop .swal2-shown.swal2-top-left {\n" +
  "    top: 0;\n" +
  "    left: 0; }\n" +
  "  body.swal2-no-backdrop .swal2-shown.swal2-top-end, body.swal2-no-backdrop .swal2-shown.swal2-top-right {\n" +
  "    top: 0;\n" +
  "    right: 0; }\n" +
  "  body.swal2-no-backdrop .swal2-shown.swal2-center {\n" +
  "    top: 50%;\n" +
  "    left: 50%;\n" +
  "    -webkit-transform: translate(-50%, -50%);\n" +
  "            transform: translate(-50%, -50%); }\n" +
  "  body.swal2-no-backdrop .swal2-shown.swal2-center-start, body.swal2-no-backdrop .swal2-shown.swal2-center-left {\n" +
  "    top: 50%;\n" +
  "    left: 0;\n" +
  "    -webkit-transform: translateY(-50%);\n" +
  "            transform: translateY(-50%); }\n" +
  "  body.swal2-no-backdrop .swal2-shown.swal2-center-end, body.swal2-no-backdrop .swal2-shown.swal2-center-right {\n" +
  "    top: 50%;\n" +
  "    right: 0;\n" +
  "    -webkit-transform: translateY(-50%);\n" +
  "            transform: translateY(-50%); }\n" +
  "  body.swal2-no-backdrop .swal2-shown.swal2-bottom {\n" +
  "    bottom: 0;\n" +
  "    left: 50%;\n" +
  "    -webkit-transform: translateX(-50%);\n" +
  "            transform: translateX(-50%); }\n" +
  "  body.swal2-no-backdrop .swal2-shown.swal2-bottom-start, body.swal2-no-backdrop .swal2-shown.swal2-bottom-left {\n" +
  "    bottom: 0;\n" +
  "    left: 0; }\n" +
  "  body.swal2-no-backdrop .swal2-shown.swal2-bottom-end, body.swal2-no-backdrop .swal2-shown.swal2-bottom-right {\n" +
  "    right: 0;\n" +
  "    bottom: 0; }\n" +
  "\n" +
  ".swal2-container {\n" +
  "  display: flex;\n" +
  "  position: fixed;\n" +
  "  top: 0;\n" +
  "  right: 0;\n" +
  "  bottom: 0;\n" +
  "  left: 0;\n" +
  "  flex-direction: row;\n" +
  "  align-items: center;\n" +
  "  justify-content: center;\n" +
  "  padding: 10px;\n" +
  "  background-color: transparent;\n" +
  "  z-index: 1060;\n" +
  "  overflow-x: hidden;\n" +
  "  -webkit-overflow-scrolling: touch; }\n" +
  "  .swal2-container.swal2-top {\n" +
  "    align-items: flex-start; }\n" +
  "  .swal2-container.swal2-top-start, .swal2-container.swal2-top-left {\n" +
  "    align-items: flex-start;\n" +
  "    justify-content: flex-start; }\n" +
  "  .swal2-container.swal2-top-end, .swal2-container.swal2-top-right {\n" +
  "    align-items: flex-start;\n" +
  "    justify-content: flex-end; }\n" +
  "  .swal2-container.swal2-center {\n" +
  "    align-items: center; }\n" +
  "  .swal2-container.swal2-center-start, .swal2-container.swal2-center-left {\n" +
  "    align-items: center;\n" +
  "    justify-content: flex-start; }\n" +
  "  .swal2-container.swal2-center-end, .swal2-container.swal2-center-right {\n" +
  "    align-items: center;\n" +
  "    justify-content: flex-end; }\n" +
  "  .swal2-container.swal2-bottom {\n" +
  "    align-items: flex-end; }\n" +
  "  .swal2-container.swal2-bottom-start, .swal2-container.swal2-bottom-left {\n" +
  "    align-items: flex-end;\n" +
  "    justify-content: flex-start; }\n" +
  "  .swal2-container.swal2-bottom-end, .swal2-container.swal2-bottom-right {\n" +
  "    align-items: flex-end;\n" +
  "    justify-content: flex-end; }\n" +
  "  .swal2-container.swal2-grow-fullscreen > .swal2-modal {\n" +
  "    display: flex !important;\n" +
  "    flex: 1;\n" +
  "    align-self: stretch;\n" +
  "    justify-content: center; }\n" +
  "  .swal2-container.swal2-grow-row > .swal2-modal {\n" +
  "    display: flex !important;\n" +
  "    flex: 1;\n" +
  "    align-content: center;\n" +
  "    justify-content: center; }\n" +
  "  .swal2-container.swal2-grow-column {\n" +
  "    flex: 1;\n" +
  "    flex-direction: column; }\n" +
  "    .swal2-container.swal2-grow-column.swal2-top, .swal2-container.swal2-grow-column.swal2-center, .swal2-container.swal2-grow-column.swal2-bottom {\n" +
  "      align-items: center; }\n" +
  "    .swal2-container.swal2-grow-column.swal2-top-start, .swal2-container.swal2-grow-column.swal2-center-start, .swal2-container.swal2-grow-column.swal2-bottom-start, .swal2-container.swal2-grow-column.swal2-top-left, .swal2-container.swal2-grow-column.swal2-center-left, .swal2-container.swal2-grow-column.swal2-bottom-left {\n" +
  "      align-items: flex-start; }\n" +
  "    .swal2-container.swal2-grow-column.swal2-top-end, .swal2-container.swal2-grow-column.swal2-center-end, .swal2-container.swal2-grow-column.swal2-bottom-end, .swal2-container.swal2-grow-column.swal2-top-right, .swal2-container.swal2-grow-column.swal2-center-right, .swal2-container.swal2-grow-column.swal2-bottom-right {\n" +
  "      align-items: flex-end; }\n" +
  "    .swal2-container.swal2-grow-column > .swal2-modal {\n" +
  "      display: flex !important;\n" +
  "      flex: 1;\n" +
  "      align-content: center;\n" +
  "      justify-content: center; }\n" +
  "  .swal2-container:not(.swal2-top):not(.swal2-top-start):not(.swal2-top-end):not(.swal2-top-left):not(.swal2-top-right):not(.swal2-center-start):not(.swal2-center-end):not(.swal2-center-left):not(.swal2-center-right):not(.swal2-bottom):not(.swal2-bottom-start):not(.swal2-bottom-end):not(.swal2-bottom-left):not(.swal2-bottom-right) > .swal2-modal {\n" +
  "    margin: auto; }\n" +
  "  @media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {\n" +
  "    .swal2-container .swal2-modal {\n" +
  "      margin: 0 !important; } }\n" +
  "  .swal2-container.swal2-fade {\n" +
  "    transition: background-color .1s; }\n" +
  "  .swal2-container.swal2-shown {\n" +
  "    background-color: rgba(0, 0, 0, 0.4); }\n" +
  "\n" +
  ".swal2-popup {\n" +
  "  display: none;\n" +
  "  position: relative;\n" +
  "  flex-direction: column;\n" +
  "  justify-content: center;\n" +
  "  width: 32em;\n" +
  "  max-width: 100%;\n" +
  "  padding: 1.25em;\n" +
  "  border-radius: 0.3125em;\n" +
  "  background: #fff;\n" +
  "  font-family: inherit;\n" +
  "  font-size: 1rem;\n" +
  "  box-sizing: border-box; }\n" +
  "  .swal2-popup:focus {\n" +
  "    outline: none; }\n" +
  "  .swal2-popup.swal2-loading {\n" +
  "    overflow-y: hidden; }\n" +
  "  .swal2-popup .swal2-header {\n" +
  "    display: flex;\n" +
  "    flex-direction: column;\n" +
  "    align-items: center; }\n" +
  "  .swal2-popup .swal2-title {\n" +
  "    display: block;\n" +
  "    position: relative;\n" +
  "    max-width: 100%;\n" +
  "    margin: 0 0 0.4em;\n" +
  "    padding: 0;\n" +
  "    color: #595959;\n" +
  "    font-size: 1.875em;\n" +
  "    font-weight: 600;\n" +
  "    text-align: center;\n" +
  "    text-transform: none;\n" +
  "    word-wrap: break-word; }\n" +
  "  .swal2-popup .swal2-actions {\n" +
  "    align-items: center;\n" +
  "    justify-content: center;\n" +
  "    margin: 1.25em auto 0; }\n" +
  "    .swal2-popup .swal2-actions:not(.swal2-loading) .swal2-styled[disabled] {\n" +
  "      opacity: .4; }\n" +
  "    .swal2-popup .swal2-actions:not(.swal2-loading) .swal2-styled:hover {\n" +
  "      background-image: linear-gradient(rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.1)); }\n" +
  "    .swal2-popup .swal2-actions:not(.swal2-loading) .swal2-styled:active {\n" +
  "      background-image: linear-gradient(rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)); }\n" +
  "    .swal2-popup .swal2-actions.swal2-loading .swal2-styled.swal2-confirm {\n" +
  "      width: 2.5em;\n" +
  "      height: 2.5em;\n" +
  "      margin: .46875em;\n" +
  "      padding: 0;\n" +
  "      border: .25em solid transparent;\n" +
  "      border-radius: 100%;\n" +
  "      border-color: transparent;\n" +
  "      background-color: transparent !important;\n" +
  "      color: transparent;\n" +
  "      cursor: default;\n" +
  "      box-sizing: border-box;\n" +
  "      -webkit-animation: swal2-rotate-loading 1.5s linear 0s infinite normal;\n" +
  "              animation: swal2-rotate-loading 1.5s linear 0s infinite normal;\n" +
  "      -webkit-user-select: none;\n" +
  "         -moz-user-select: none;\n" +
  "          -ms-user-select: none;\n" +
  "              user-select: none; }\n" +
  "    .swal2-popup .swal2-actions.swal2-loading .swal2-styled.swal2-cancel {\n" +
  "      margin-right: 30px;\n" +
  "      margin-left: 30px; }\n" +
  "    .swal2-popup .swal2-actions.swal2-loading :not(.swal2-styled).swal2-confirm::after {\n" +
  "      display: inline-block;\n" +
  "      width: 15px;\n" +
  "      height: 15px;\n" +
  "      margin-left: 5px;\n" +
  "      border: 3px solid #999999;\n" +
  "      border-radius: 50%;\n" +
  "      border-right-color: transparent;\n" +
  "      box-shadow: 1px 1px 1px #fff;\n" +
  "      content: '';\n" +
  "      -webkit-animation: swal2-rotate-loading 1.5s linear 0s infinite normal;\n" +
  "              animation: swal2-rotate-loading 1.5s linear 0s infinite normal; }\n" +
  "  .swal2-popup .swal2-styled {\n" +
  "    margin: 0 .3125em;\n" +
  "    padding: .625em 2em;\n" +
  "    font-weight: 500;\n" +
  "    box-shadow: none; }\n" +
  "    .swal2-popup .swal2-styled:not([disabled]) {\n" +
  "      cursor: pointer; }\n" +
  "    .swal2-popup .swal2-styled.swal2-confirm {\n" +
  "      border: 0;\n" +
  "      border-radius: 0.25em;\n" +
  "      background: initial;\n" +
  "      background-color: #3085d6;\n" +
  "      color: #fff;\n" +
  "      font-size: 1.0625em; }\n" +
  "    .swal2-popup .swal2-styled.swal2-cancel {\n" +
  "      border: 0;\n" +
  "      border-radius: 0.25em;\n" +
  "      background: initial;\n" +
  "      background-color: #aaa;\n" +
  "      color: #fff;\n" +
  "      font-size: 1.0625em; }\n" +
  "    .swal2-popup .swal2-styled:focus {\n" +
  "      outline: none;\n" +
  "      box-shadow: 0 0 0 2px #fff, 0 0 0 4px rgba(50, 100, 150, 0.4); }\n" +
  "    .swal2-popup .swal2-styled::-moz-focus-inner {\n" +
  "      border: 0; }\n" +
  "  .swal2-popup .swal2-footer {\n" +
  "    justify-content: center;\n" +
  "    margin: 1.25em 0 0;\n" +
  "    padding-top: 1em;\n" +
  "    border-top: 1px solid #eee;\n" +
  "    color: #545454;\n" +
  "    font-size: 1em; }\n" +
  "  .swal2-popup .swal2-image {\n" +
  "    max-width: 100%;\n" +
  "    margin: 1.25em auto; }\n" +
  "  .swal2-popup .swal2-close {\n" +
  "    position: absolute;\n" +
  "    top: 0;\n" +
  "    right: 0;\n" +
  "    justify-content: center;\n" +
  "    width: 1.2em;\n" +
  "    height: 1.2em;\n" +
  "    padding: 0;\n" +
  "    transition: color 0.1s ease-out;\n" +
  "    border: none;\n" +
  "    border-radius: 0;\n" +
  "    background: transparent;\n" +
  "    color: #cccccc;\n" +
  "    font-family: serif;\n" +
  "    font-size: 2.5em;\n" +
  "    line-height: 1.2;\n" +
  "    cursor: pointer;\n" +
  "    overflow: hidden; }\n" +
  "    .swal2-popup .swal2-close:hover {\n" +
  "      -webkit-transform: none;\n" +
  "              transform: none;\n" +
  "      color: #f27474; }\n" +
  "  .swal2-popup > .swal2-input,\n" +
  "  .swal2-popup > .swal2-file,\n" +
  "  .swal2-popup > .swal2-textarea,\n" +
  "  .swal2-popup > .swal2-select,\n" +
  "  .swal2-popup > .swal2-radio,\n" +
  "  .swal2-popup > .swal2-checkbox {\n" +
  "    display: none; }\n" +
  "  .swal2-popup .swal2-content {\n" +
  "    justify-content: center;\n" +
  "    margin: 0;\n" +
  "    padding: 0;\n" +
  "    color: #545454;\n" +
  "    font-size: 1.125em;\n" +
  "    font-weight: 300;\n" +
  "    line-height: normal;\n" +
  "    word-wrap: break-word; }\n" +
  "  .swal2-popup #swal2-content {\n" +
  "    text-align: center; }\n" +
  "  .swal2-popup .swal2-input,\n" +
  "  .swal2-popup .swal2-file,\n" +
  "  .swal2-popup .swal2-textarea,\n" +
  "  .swal2-popup .swal2-select,\n" +
  "  .swal2-popup .swal2-radio,\n" +
  "  .swal2-popup .swal2-checkbox {\n" +
  "    margin: 1em auto; }\n" +
  "  .swal2-popup .swal2-input,\n" +
  "  .swal2-popup .swal2-file,\n" +
  "  .swal2-popup .swal2-textarea {\n" +
  "    width: 100%;\n" +
  "    transition: border-color .3s, box-shadow .3s;\n" +
  "    border: 1px solid #d9d9d9;\n" +
  "    border-radius: 0.1875em;\n" +
  "    font-size: 1.125em;\n" +
  "    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.06);\n" +
  "    box-sizing: border-box; }\n" +
  "    .swal2-popup .swal2-input.swal2-inputerror,\n" +
  "    .swal2-popup .swal2-file.swal2-inputerror,\n" +
  "    .swal2-popup .swal2-textarea.swal2-inputerror {\n" +
  "      border-color: #f27474 !important;\n" +
  "      box-shadow: 0 0 2px #f27474 !important; }\n" +
  "    .swal2-popup .swal2-input:focus,\n" +
  "    .swal2-popup .swal2-file:focus,\n" +
  "    .swal2-popup .swal2-textarea:focus {\n" +
  "      border: 1px solid #b4dbed;\n" +
  "      outline: none;\n" +
  "      box-shadow: 0 0 3px #c4e6f5; }\n" +
  "    .swal2-popup .swal2-input::-webkit-input-placeholder,\n" +
  "    .swal2-popup .swal2-file::-webkit-input-placeholder,\n" +
  "    .swal2-popup .swal2-textarea::-webkit-input-placeholder {\n" +
  "      color: #cccccc; }\n" +
  "    .swal2-popup .swal2-input:-ms-input-placeholder,\n" +
  "    .swal2-popup .swal2-file:-ms-input-placeholder,\n" +
  "    .swal2-popup .swal2-textarea:-ms-input-placeholder {\n" +
  "      color: #cccccc; }\n" +
  "    .swal2-popup .swal2-input::-ms-input-placeholder,\n" +
  "    .swal2-popup .swal2-file::-ms-input-placeholder,\n" +
  "    .swal2-popup .swal2-textarea::-ms-input-placeholder {\n" +
  "      color: #cccccc; }\n" +
  "    .swal2-popup .swal2-input::placeholder,\n" +
  "    .swal2-popup .swal2-file::placeholder,\n" +
  "    .swal2-popup .swal2-textarea::placeholder {\n" +
  "      color: #cccccc; }\n" +
  "  .swal2-popup .swal2-range input {\n" +
  "    width: 80%; }\n" +
  "  .swal2-popup .swal2-range output {\n" +
  "    width: 20%;\n" +
  "    font-weight: 600;\n" +
  "    text-align: center; }\n" +
  "  .swal2-popup .swal2-range input,\n" +
  "  .swal2-popup .swal2-range output {\n" +
  "    height: 2.625em;\n" +
  "    margin: 1em auto;\n" +
  "    padding: 0;\n" +
  "    font-size: 1.125em;\n" +
  "    line-height: 2.625em; }\n" +
  "  .swal2-popup .swal2-input {\n" +
  "    height: 2.625em;\n" +
  "    padding: 0.75em; }\n" +
  "    .swal2-popup .swal2-input[type='number'] {\n" +
  "      max-width: 10em; }\n" +
  "  .swal2-popup .swal2-file {\n" +
  "    font-size: 1.125em; }\n" +
  "  .swal2-popup .swal2-textarea {\n" +
  "    height: 6.75em;\n" +
  "    padding: 0.75em; }\n" +
  "  .swal2-popup .swal2-select {\n" +
  "    min-width: 50%;\n" +
  "    max-width: 100%;\n" +
  "    padding: .375em .625em;\n" +
  "    color: #545454;\n" +
  "    font-size: 1.125em; }\n" +
  "  .swal2-popup .swal2-radio,\n" +
  "  .swal2-popup .swal2-checkbox {\n" +
  "    align-items: center;\n" +
  "    justify-content: center; }\n" +
  "    .swal2-popup .swal2-radio label,\n" +
  "    .swal2-popup .swal2-checkbox label {\n" +
  "      margin: 0 .6em;\n" +
  "      font-size: 1.125em; }\n" +
  "    .swal2-popup .swal2-radio input,\n" +
  "    .swal2-popup .swal2-checkbox input {\n" +
  "      margin: 0 .4em; }\n" +
  "  .swal2-popup .swal2-validationerror {\n" +
  "    display: none;\n" +
  "    align-items: center;\n" +
  "    justify-content: center;\n" +
  "    padding: 0.625em;\n" +
  "    background: #f0f0f0;\n" +
  "    color: #666666;\n" +
  "    font-size: 1em;\n" +
  "    font-weight: 300;\n" +
  "    overflow: hidden; }\n" +
  "    .swal2-popup .swal2-validationerror::before {\n" +
  "      display: inline-block;\n" +
  "      width: 1.5em;\n" +
  "      min-width: 1.5em;\n" +
  "      height: 1.5em;\n" +
  "      margin: 0 .625em;\n" +
  "      border-radius: 50%;\n" +
  "      background-color: #f27474;\n" +
  "      color: #fff;\n" +
  "      font-weight: 600;\n" +
  "      line-height: 1.5em;\n" +
  "      text-align: center;\n" +
  "      content: '!';\n" +
  "      zoom: normal; }\n" +
  "\n" +
  "@supports (-ms-accelerator: true) {\n" +
  "  .swal2-range input {\n" +
  "    width: 100% !important; }\n" +
  "  .swal2-range output {\n" +
  "    display: none; } }\n" +
  "\n" +
  "@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {\n" +
  "  .swal2-range input {\n" +
  "    width: 100% !important; }\n" +
  "  .swal2-range output {\n" +
  "    display: none; } }\n" +
  "\n" +
  "@-moz-document url-prefix() {\n" +
  "  .swal2-close:focus {\n" +
  "    outline: 2px solid rgba(50, 100, 150, 0.4); } }\n" +
  "\n" +
  ".swal2-icon {\n" +
  "  position: relative;\n" +
  "  justify-content: center;\n" +
  "  width: 5em;\n" +
  "  height: 5em;\n" +
  "  margin: 1.25em auto 1.875em;\n" +
  "  border: .25em solid transparent;\n" +
  "  border-radius: 50%;\n" +
  "  line-height: 5em;\n" +
  "  cursor: default;\n" +
  "  box-sizing: content-box;\n" +
  "  -webkit-user-select: none;\n" +
  "     -moz-user-select: none;\n" +
  "      -ms-user-select: none;\n" +
  "          user-select: none;\n" +
  "  zoom: normal; }\n" +
  "  .swal2-icon-text {\n" +
  "    font-size: 3.75em; }\n" +
  "  .swal2-icon.swal2-error {\n" +
  "    border-color: #f27474; }\n" +
  "    .swal2-icon.swal2-error .swal2-x-mark {\n" +
  "      position: relative;\n" +
  "      flex-grow: 1; }\n" +
  "    .swal2-icon.swal2-error [class^='swal2-x-mark-line'] {\n" +
  "      display: block;\n" +
  "      position: absolute;\n" +
  "      top: 2.3125em;\n" +
  "      width: 2.9375em;\n" +
  "      height: .3125em;\n" +
  "      border-radius: .125em;\n" +
  "      background-color: #f27474; }\n" +
  "      .swal2-icon.swal2-error [class^='swal2-x-mark-line'][class$='left'] {\n" +
  "        left: 1.0625em;\n" +
  "        -webkit-transform: rotate(45deg);\n" +
  "                transform: rotate(45deg); }\n" +
  "      .swal2-icon.swal2-error [class^='swal2-x-mark-line'][class$='right'] {\n" +
  "        right: 1em;\n" +
  "        -webkit-transform: rotate(-45deg);\n" +
  "                transform: rotate(-45deg); }\n" +
  "  .swal2-icon.swal2-warning {\n" +
  "    border-color: #facea8;\n" +
  "    color: #f8bb86; }\n" +
  "  .swal2-icon.swal2-info {\n" +
  "    border-color: #9de0f6;\n" +
  "    color: #3fc3ee; }\n" +
  "  .swal2-icon.swal2-question {\n" +
  "    border-color: #c9dae1;\n" +
  "    color: #87adbd; }\n" +
  "  .swal2-icon.swal2-success {\n" +
  "    border-color: #a5dc86; }\n" +
  "    .swal2-icon.swal2-success [class^='swal2-success-circular-line'] {\n" +
  "      position: absolute;\n" +
  "      width: 3.75em;\n" +
  "      height: 7.5em;\n" +
  "      -webkit-transform: rotate(45deg);\n" +
  "              transform: rotate(45deg);\n" +
  "      border-radius: 50%; }\n" +
  "      .swal2-icon.swal2-success [class^='swal2-success-circular-line'][class$='left'] {\n" +
  "        top: -.4375em;\n" +
  "        left: -2.0635em;\n" +
  "        -webkit-transform: rotate(-45deg);\n" +
  "                transform: rotate(-45deg);\n" +
  "        -webkit-transform-origin: 3.75em 3.75em;\n" +
  "                transform-origin: 3.75em 3.75em;\n" +
  "        border-radius: 7.5em 0 0 7.5em; }\n" +
  "      .swal2-icon.swal2-success [class^='swal2-success-circular-line'][class$='right'] {\n" +
  "        top: -.6875em;\n" +
  "        left: 1.875em;\n" +
  "        -webkit-transform: rotate(-45deg);\n" +
  "                transform: rotate(-45deg);\n" +
  "        -webkit-transform-origin: 0 3.75em;\n" +
  "                transform-origin: 0 3.75em;\n" +
  "        border-radius: 0 7.5em 7.5em 0; }\n" +
  "    .swal2-icon.swal2-success .swal2-success-ring {\n" +
  "      position: absolute;\n" +
  "      top: -.25em;\n" +
  "      left: -.25em;\n" +
  "      width: 100%;\n" +
  "      height: 100%;\n" +
  "      border: 0.25em solid rgba(165, 220, 134, 0.3);\n" +
  "      border-radius: 50%;\n" +
  "      z-index: 2;\n" +
  "      box-sizing: content-box; }\n" +
  "    .swal2-icon.swal2-success .swal2-success-fix {\n" +
  "      position: absolute;\n" +
  "      top: .5em;\n" +
  "      left: 1.625em;\n" +
  "      width: .4375em;\n" +
  "      height: 5.625em;\n" +
  "      -webkit-transform: rotate(-45deg);\n" +
  "              transform: rotate(-45deg);\n" +
  "      z-index: 1; }\n" +
  "    .swal2-icon.swal2-success [class^='swal2-success-line'] {\n" +
  "      display: block;\n" +
  "      position: absolute;\n" +
  "      height: .3125em;\n" +
  "      border-radius: .125em;\n" +
  "      background-color: #a5dc86;\n" +
  "      z-index: 2; }\n" +
  "      .swal2-icon.swal2-success [class^='swal2-success-line'][class$='tip'] {\n" +
  "        top: 2.875em;\n" +
  "        left: .875em;\n" +
  "        width: 1.5625em;\n" +
  "        -webkit-transform: rotate(45deg);\n" +
  "                transform: rotate(45deg); }\n" +
  "      .swal2-icon.swal2-success [class^='swal2-success-line'][class$='long'] {\n" +
  "        top: 2.375em;\n" +
  "        right: .5em;\n" +
  "        width: 2.9375em;\n" +
  "        -webkit-transform: rotate(-45deg);\n" +
  "                transform: rotate(-45deg); }\n" +
  "\n" +
  ".swal2-progresssteps {\n" +
  "  align-items: center;\n" +
  "  margin: 0 0 1.25em;\n" +
  "  padding: 0;\n" +
  "  font-weight: 600; }\n" +
  "  .swal2-progresssteps li {\n" +
  "    display: inline-block;\n" +
  "    position: relative; }\n" +
  "  .swal2-progresssteps .swal2-progresscircle {\n" +
  "    width: 2em;\n" +
  "    height: 2em;\n" +
  "    border-radius: 2em;\n" +
  "    background: #3085d6;\n" +
  "    color: #fff;\n" +
  "    line-height: 2em;\n" +
  "    text-align: center;\n" +
  "    z-index: 20; }\n" +
  "    .swal2-progresssteps .swal2-progresscircle:first-child {\n" +
  "      margin-left: 0; }\n" +
  "    .swal2-progresssteps .swal2-progresscircle:last-child {\n" +
  "      margin-right: 0; }\n" +
  "    .swal2-progresssteps .swal2-progresscircle.swal2-activeprogressstep {\n" +
  "      background: #3085d6; }\n" +
  "      .swal2-progresssteps .swal2-progresscircle.swal2-activeprogressstep ~ .swal2-progresscircle {\n" +
  "        background: #add8e6; }\n" +
  "      .swal2-progresssteps .swal2-progresscircle.swal2-activeprogressstep ~ .swal2-progressline {\n" +
  "        background: #add8e6; }\n" +
  "  .swal2-progresssteps .swal2-progressline {\n" +
  "    width: 2.5em;\n" +
  "    height: .4em;\n" +
  "    margin: 0 -1px;\n" +
  "    background: #3085d6;\n" +
  "    z-index: 10; }\n" +
  "\n" +
  "[class^='swal2'] {\n" +
  "  -webkit-tap-highlight-color: transparent; }\n" +
  "\n" +
  ".swal2-show {\n" +
  "  -webkit-animation: swal2-show 0.3s;\n" +
  "          animation: swal2-show 0.3s; }\n" +
  "  .swal2-show.swal2-noanimation {\n" +
  "    -webkit-animation: none;\n" +
  "            animation: none; }\n" +
  "\n" +
  ".swal2-hide {\n" +
  "  -webkit-animation: swal2-hide 0.15s forwards;\n" +
  "          animation: swal2-hide 0.15s forwards; }\n" +
  "  .swal2-hide.swal2-noanimation {\n" +
  "    -webkit-animation: none;\n" +
  "            animation: none; }\n" +
  "\n" +
  "[dir='rtl'] .swal2-close {\n" +
  "  right: auto;\n" +
  "  left: 0; }\n" +
  "\n" +
  ".swal2-animate-success-icon .swal2-success-line-tip {\n" +
  "  -webkit-animation: swal2-animate-success-line-tip 0.75s;\n" +
  "          animation: swal2-animate-success-line-tip 0.75s; }\n" +
  "\n" +
  ".swal2-animate-success-icon .swal2-success-line-long {\n" +
  "  -webkit-animation: swal2-animate-success-line-long 0.75s;\n" +
  "          animation: swal2-animate-success-line-long 0.75s; }\n" +
  "\n" +
  ".swal2-animate-success-icon .swal2-success-circular-line-right {\n" +
  "  -webkit-animation: swal2-rotate-success-circular-line 4.25s ease-in;\n" +
  "          animation: swal2-rotate-success-circular-line 4.25s ease-in; }\n" +
  "\n" +
  ".swal2-animate-error-icon {\n" +
  "  -webkit-animation: swal2-animate-error-icon 0.5s;\n" +
  "          animation: swal2-animate-error-icon 0.5s; }\n" +
  "  .swal2-animate-error-icon .swal2-x-mark {\n" +
  "    -webkit-animation: swal2-animate-error-x-mark 0.5s;\n" +
  "            animation: swal2-animate-error-x-mark 0.5s; }\n" +
  "\n" +
  "@-webkit-keyframes swal2-rotate-loading {\n" +
  "  0% {\n" +
  "    -webkit-transform: rotate(0deg);\n" +
  "            transform: rotate(0deg); }\n" +
  "  100% {\n" +
  "    -webkit-transform: rotate(360deg);\n" +
  "            transform: rotate(360deg); } }\n" +
  "\n" +
  "@keyframes swal2-rotate-loading {\n" +
  "  0% {\n" +
  "    -webkit-transform: rotate(0deg);\n" +
  "            transform: rotate(0deg); }\n" +
  "  100% {\n" +
  "    -webkit-transform: rotate(360deg);\n" +
  "            transform: rotate(360deg); } }");