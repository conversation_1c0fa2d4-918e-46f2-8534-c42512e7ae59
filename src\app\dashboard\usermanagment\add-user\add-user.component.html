<div class="page-title col-md-12 ">
  <h1>User Management</h1>
  <div class="breadcrumbs"><a href="#">Home</a>/<a href="#/ui/usermanagement">User Management</a>/<span>{{editUser.type == "Edit" ? "Edit" : "Create"}} User</span>
  </div>
</div>

 
  <div class="content">
     <div class="card">
      <div class="card-header-info">
        <h4 class="card-title"><i class="fas fa-user-plus"></i> {{submit_btn}}</h4>
      </div>
      <div class="card-body">
       
          <form [formGroup]="userForm" (ngSubmit)="onSubmit()">
            <div class="row">
              <div class="form-group col-md-6"> <div class="row">
                <label class="col-sm-5">Name</label>
                <div class="col-sm-7">
                <input type="text" name="name" id="name" class="custom-input" formControlName="name" placeholder="">
                <div class="error"
                  *ngIf="userForm.controls['name'].hasError('required') && userForm.controls['name'].touched">Name is
                  required.</div>
              </div>
            </div>
          </div>
              <div class="form-group col-md-6">  <div class="row"> 
                <label class="col-sm-5">Email</label>
                <div class="col-sm-7">
                <input name="text" formControlName="email" class="custom-input input-email" style="width: 70%;" placeholder="Enter Email Address...">
                <span class="input-group-addon ">@trinity-solar.com</span>
                <div class="error"
                  *ngIf="userForm.controls['email'].hasError('required') && userForm.controls['email'].touched">Email
                  is required.</div>
              </div> 
            </div>
            </div> </div>
            <div class="row" *ngIf="!this.editUser.type">
            </div>
            <div class="row">
              <div class="form-group col-md-6"><div class="row">
                <label class="col-sm-5">Role</label>
                <div class="col-sm-7">
                <select class="custom-select" name="role" formControlName="role" data-style="btn btn-link" id="role">
                  <option value="">Select</option>
                  <option *ngFor="let roleItem of rolesList" value="{{roleItem.roleID}}">{{roleItem.roleName}}</option>
                </select>
                <div class="error"
                  *ngIf="userForm.controls['role'].hasError('required') && userForm.controls['role'].touched">Role is
                  required.</div>
              </div>
            </div>
            </div>
              <div class="form-group col-md-6"> <div class="row">
                <label class="col-sm-5">Status</label>
                <div class="col-sm-7">
                <select class="custom-select" name="status" formControlName="status" data-style="btn btn-link"
                  id="status">
                  <option value="">Select</option>
                  <option value="1">Active</option>
                  <option value="0">Inactive</option>
                </select>
                <div class="error"
                  *ngIf="userForm.controls['status'].hasError('required') && userForm.controls['status'].touched">Status
                  is required.</div>
              </div>
            </div>
            </div>
            <div class="form-group col-md-6" style="display:none">
              <div class="row">
                <label class="col-sm-5">Recruiter</label>
                <div class="col-sm-7">
                  <select class="custom-select" name="recruiter" formControlName="recruiter" data-style="btn btn-link" id="recruiter">
                    <option value="0">Select</option>
                    <option *ngFor="let userItem of usersList" value="{{userItem.empId}}">{{userItem.userName}}</option>
                  </select>
                </div>
              </div>
            </div>
            </div>
            <div class="row">
            <div class="col-md-12">
              <button type="submit" class="btn btn-primary float-right" [disabled]="userForm.invalid"><i class="fas fa-user-plus"></i>  {{submit_btn}}</button>
            </div>
          </div>
          </form>
   
      </div>
    </div>
  </div>
 