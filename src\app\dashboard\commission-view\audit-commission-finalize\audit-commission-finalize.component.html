<div class="slide-toggle-btn tab-light-bg mb-3">
    <button [className]="showCommissionAudit ? 'tab-toggle tab-expanded' : 'tab-toggle tab-collapsed'"   mat-flat-button color="primary"  (click)="showCommissionAudit = !showCommissionAudit"><i class="fas fa-history"></i> Commission
        Finalization Audit<i
            class="material-icons tab-icons"> {{showCommissionAudit ? 'remove_circle_outline' : 'add_circle_outline'}} </i></button>
    <div [hidden]="!showCommissionAudit"  class="toggle-container">
        <table mat-table [dataSource]="commissionAuditElements" matSort class="my-table w-100">
            <ng-container matColumnDef="commissionId">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> Commission Id </th>
                <td mat-cell data-td-head="Commission Id" *matCellDef="let element">{{element.commissionId}}</td>
            </ng-container>

            <ng-container matColumnDef="finalized">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> Finalized </th>
                <td data-td-head="Finalized"  mat-cell *matCellDef="let element">{{element.finalized}}</td>
            </ng-container>

            <ng-container matColumnDef="reasonForChange">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> Reason for Change </th>
                <td data-td-head="Reason For Change"  mat-cell *matCellDef="let element">{{element.reasonForChange}}</td>
            </ng-container>

            <ng-container matColumnDef="userCreatedTimestamp">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> User Created Timestamp </th>
                <td data-td-head="User Created Timestamp"  mat-cell *matCellDef="let element">{{element.userCreatedTimestamp | datezone}}</td>
            </ng-container>

            <ng-container matColumnDef="userCreatedId">
                <th class="mat-column-25" mat-header-cell *matHeaderCellDef mat-sort-header> User Created Id </th>
                <td data-td-head="User Created Id"  mat-cell *matCellDef="let element">{{element.userCreatedId}}</td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="commissionAuditColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: commissionAuditColumns;"></tr>
        </table>
        <mat-paginator [pageSizeOptions]="pageSizeOptions">
        </mat-paginator>
    </div>