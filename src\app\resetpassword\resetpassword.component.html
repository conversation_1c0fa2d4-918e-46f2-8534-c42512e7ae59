<div class=" w-100">
	<div class="content">
		<div class="container">
			<div class="row justify-content-center">
				<div class="col-md-5 offset-md-0" style="padding-top:25px;">
					<div class="text-center">
						<a [routerLink]="['/']"><img src="/assets/images/Trinity.png" width="150px" alt=""></a>
					</div>
					<div class="card"
						style="box-shadow: 0 8px 8px 0 rgba(0, 0, 0, 0.2), 0 8px 20px 0 rgba(0, 0, 0, 0.19);">
						<div class="card-header">
							<div class="text-center">
								<h3 style="font-weight: 400;"> RESET PASSWORD </h3>
							</div>
						</div>

						<div class="card-body">
							<form [formGroup]="resetForm" (ngSubmit)="onSubmit()">
								<div class="row">
									<div class="col-md-12">
										<div class="form-group">
											<label class="bmd-label-floating">New Password</label>
											<input type="password" formControlName="new_pass" name="new_pass"
												class="form-control" autocomplete="off" placeholder="New Password">
											<div class="error"
												*ngIf="resetForm.controls['new_pass'].hasError('required') && resetForm.controls['new_pass'].touched">
												New Password is required</div>
											<div class="error"
												*ngIf="resetForm.controls['new_pass'].hasError('minlength') && resetForm.controls['new_pass'].touched">
												Your new password must be at least 6 characters long.</div>
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-md-12">
										<div class="form-group">
											<label class="bmd-label-floating">Confirm Password</label>
											<input type="password" formControlName="confirm_pass" name="confirm_pass"
												class="form-control" autocomplete="off" placeholder="Confirm Password">
											<div class="error"
												*ngIf="resetForm.controls['confirm_pass'].hasError('required') && resetForm.controls['confirm_pass'].touched">
												Confirm Password is required</div>
											<div class="error"
												*ngIf="resetForm.controls['confirm_pass'].hasError('minlength') && resetForm.controls['confirm_pass'].touched">
												Your confirm password must be at least 6 characters long.</div>
										</div>
									</div>
								</div>
								<button type="submit" [disabled]="resetForm.invalid" class="btn col-md-12">RESET
									PASSWORD</button>
								<div class="clearfix"></div>
							</form>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>