{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"trinity-commissions-ui": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"aot": true, "optimization": false, "sourceMap": true, "namedChunks": true, "extractLicenses": false, "vendorChunk": false, "buildOptimizer": false, "outputPath": "dist", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets", "src/web.config"], "styles": ["src/styles.css", "node_modules/ngx-toastr/toastr.css"], "scripts": []}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb"}]}, "staging": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.staging.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb"}]}, "test": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.test.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb"}]}, "local": {"budgets": [{"type": "anyComponentStyle", "maximumWarning": "6kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.local.ts"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "trinity-commissions-ui:build"}, "configurations": {"production": {"browserTarget": "trinity-commissions-ui:build:production"}, "local": {"browserTarget": "trinity-commissions-ui:build:local"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "trinity-commissions-ui:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.css", "node_modules/font-awesome/css/font-awesome.css"], "scripts": []}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["tsconfig.app.json", "tsconfig.spec.json", "e2e/tsconfig.json"], "exclude": ["**/node_modules/**"]}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "trinity-commissions-ui:serve"}, "configurations": {"production": {"devServerTarget": "trinity-commissions-ui:serve:production"}}}}}}}