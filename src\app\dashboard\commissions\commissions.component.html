<!-- <app-commissions-sidebar></app-commissions-sidebar> -->
<div class="">
  <div class="content">
    <h3 class="text-center" style="line-height: 0.4em; font-weight:500;">Commissions Dashboard</h3>
    <div class="container-fluid">
      <div class="row">
        <div class="col-lg-3 col-md-6 col-sm-6">
          <div class="card card-stats">
            <div class="card-header card-header-info card-header-icon">
              <div class="card-icon">
                <i class="material-icons">menu_book</i>
              </div>
              <p class="card-category">Contact Payment Book Balances</p>
              <!-- <h3 class="card-title">0
                <small>GB</small>
              </h3> -->
            </div> 
            <div class="card-footer">
              <div class="stats">
                <i class="material-icons">menu_book</i>
                <div *ngIf="apiService.checkPermission('PaymentBookReports')">
                  <a [routerLink]="['/ui/commissions/paybooks']">Payment Book Dashboard</a>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-3 col-md-6 col-sm-6">
          <div class="card card-stats">
            <div class="card-header card-header-info card-header-icon">
              <div class="card-icon">
                <i class="material-icons">assessment</i>
              </div>
              <p class="card-category">Commission Link 2</p>
              <h3 class="card-title">0</h3>
            </div>
            <div class="card-footer">
              <div class="stats">
                <i class="material-icons">date_range</i> Last 24 Hours
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-3 col-md-6 col-sm-6">
          <div class="card card-stats">
            <div class="card-header card-header-info card-header-icon">
              <div class="card-icon">
                <i class="material-icons">supervised_user_circle</i>
              </div>
              <p class="card-category">Commission Link 3</p>
              <h3 class="card-title">0</h3>
            </div>
            <div class="card-footer">
              <div class="stats">
                <i class="material-icons">person_pin</i> Employees List
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-3 col-md-6 col-sm-6">
          <div class="card card-stats">
            <div class="card-header card-header-info card-header-icon">
              <div class="card-icon">
                <i class="fa fa-twitter"></i>
              </div>
              <p class="card-category">Commission Link 4</p>
              <h3 class="card-title">+0</h3>
            </div>
            <div class="card-footer">
              <div class="stats">
                <i class="material-icons">update</i> Just Updated
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md">
          
            <form>
              <div class="card">
                <div class="card-header-info">
                  <h4 class="card-title">Search</h4>
                </div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-6">
                      <div *ngIf="apiService.checkPermission('ViewOpportunityDetail')">
                        <div class="form-group">
                          <label class="bmd-label-floating" for="opname">Opportunity Name :</label>
                          <input type="text" class="form-control" id="usr" name="opname">
                          <button type="" class="btn"><a
                              [routerLink]="['/ui/commissions/opportunitieslist']">Search Opportunities</a></button>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div *ngIf="apiService.checkPermission('ViewSalesRepDetail')">
                        <div class="form-group">
                          <label class="bmd-label-floating" for="opname">Sales Rep Name :</label>
                          <input type="text" class="form-control" id="usr" name="opname">
                          <button type="" class="btn"><a
                              [routerLink]="['/ui/commissions/salescontactslist']">Search Sales Rep</a></button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </form>
          
        </div>
      </div>
      <div class="row">
        <div class="col-md-6">
          <div class="card ">
            <div class="card-body h-50">
              <h4 class="card-title text-center">Comming Soon</h4>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="card ">
            <a [routerLink]="['/ui/commissions/data-integration']">
              <div class="card-body h-50">
                <h4 class="card-title text-center">Comming Soon</h4>
              </div>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>