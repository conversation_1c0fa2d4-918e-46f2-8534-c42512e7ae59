import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { DashboardComponent } from '../dashboard/dashboard.component';
import { CommissionsComponent } from './commissions/commissions.component';
import { LeadsourceconfigurationComponent } from '../dashboard/maintenance/lead-source-commissions-modifier/lead-source-commissions-modifier-component';
import { LeadSourceComponent } from '../dashboard/maintenance/lead-source/lead-source-component';
import { EmployeeOverrideComponent } from '../dashboard/maintenance/employee-override/employee-override-component';
import { EmployeeOverrideRoofingComponent } from '../dashboard/maintenance/employee-override-roofing/employee-override-roofing-component';
import { CreatePlanComponent } from './create-plan/create-plan.component';

import { ChangePlanComponent } from './change-plan/change-plan.component';
import { OpportunityDetailsComponent } from './opportunity-details/opportunity-details.component';
import { PaymentsComponent } from './payments/payments.component';
import { PaymentBookWithdrawalComponent } from './payments/payment-book-withdrawal/payment-book-withdrawal.component';
// import { OutreachOpportunitiesComponent } from './reports/outreach-opportunities/outreach-opportunities.component';
import { EmpHistoryComponent } from './emp-history/emp-history.component';
import { DynamicReportComponent } from './reports/dynamic-report/dynamic-report.component';
import { EmpHistoryDetailsComponent } from './emp-history-details/emp-history-details.component';
import { UserDashboardComponent } from './user-dashboard/user-dashboard.component';
import { PaybooksComponent } from './paybooks/paybooks.component';
import { ReportComponent } from './report/report.component';
import { SalesRepComponent } from './sales-rep/sales-rep.component';
import { SalesRepConfigurationComponent } from './sales-rep-configuration/sales-rep-configuration.component';
import { OpportunitiesListComponent } from './opportunities-list/opportunities-list.component';
import { SalesRepListComponent } from './sales-rep-list/sales-rep-list.component';
import { SearchComponent } from './search/search.component';
import { DataIntegrationComponent } from './data-integration/data-integration.component';
import { UsersListComponent } from './usermanagment/users-list/users-list.component'
import { AddUserComponent } from './usermanagment/add-user/add-user.component'
import { EditUserComponent } from './usermanagment/edit-user/edit-user.component'
import { AuthGuard } from '../guards/auth.guard';
import { AdminAccessGuard } from '../guards/admin-access.guard';
import { SalesrepDashboardComponent } from './salesrep-dashboard/salesrep-dashboard.component';
import { PaymentBookDashboardComponent } from './payment-book-dashboard/payment-book-dashboard.component';
import { CommissionViewComponent } from './commission-view/commission-view.component';
import { InnerDashboardComponent } from './inner-dashboard/inner-dashboard.component';
import { FinanceAdminDashboardComponent } from './financeAdmin-dashboard/financeAdmin-dashboard.component';
import { SalesAdminDashboardComponent } from './salesadmin-dashboard/salesAdmin-dashboard.component';
import { LegalAdminDashboardComponent } from './legalAdmin-dashboard/legalAdmin-dashboard.component';
import { SearchResultComponent } from './search-result/search-result.component';
import { PlanExecutionComponent } from './plan-execution/plan-execution.component';
import { CustomReportComponent } from './custom-report/custom-report.component';
import { HomeDepotEmployeeOverrideComponent } from './maintenance/home-depot-employee-override/home-depot-employee-override/home-depot-employee-override.component';
import { MonthlyRoofingOverrideSnapshotComponent } from './maintenance/monthly-roofing-override-snapshot/monthly-roofing-override-snapshot.component';
import { BatteryQuarterlyOverrideComponent } from './maintenance/battery-quarterly-override/battery-quarterly-override.component';
import { QuaterlyRoofingOverrideComponent } from './maintenance/quaterly-roofing-override/quaterly-roofing-override.component';
import { CommissionableDivisionContactInclusionComponent } from './maintenance/commissionable-division-contact-inclusion/commissionable-division-contact-inclusion.component';
// import { EvaluateCommissionComponent } from './opportunity-details/evaluate-commission/evaluate-commission.component';
import { EvaluateCommissionViewComponent } from './evaluate-commission-view/evaluate-commission-view.component';
import { PrepaidPaymentsDashboardComponent } from './payments/prepaid-payments/prepaid-payments-dashboard/prepaid-payments-dashboard.component';
// import { RoleGuard } from '../guards/role.guard';
// import { MsalGuard } from '@azure/msal-angular';

const routes: Routes = [
  { path: '', redirectTo: 'dashboard', pathMatch: 'full' },
  { path: 'dashboard', component: DashboardComponent, canActivate: [AuthGuard] },
  { path: 'dashboard/inner', component: InnerDashboardComponent },
  { path: 'dashboard/finance', component: FinanceAdminDashboardComponent, canActivate: [AuthGuard] },
  { path: 'dashboard/sales', component: SalesAdminDashboardComponent },
  { path: 'dashboard/legal', component: LegalAdminDashboardComponent },
  { path: 'usermanagement', component: UsersListComponent, canActivate: [AuthGuard] },
  { path: 'commissions', component: CommissionsComponent, canActivate: [AuthGuard] },
  { path: 'maintenance/leadsourcecommissionsmodifier', component: LeadsourceconfigurationComponent, canActivate: [AuthGuard] },
  { path: 'maintenance/leadsource', component: LeadSourceComponent, canActivate: [AuthGuard] },
  { path: 'maintenance/employeeoverride', component: EmployeeOverrideComponent, canActivate: [AuthGuard] },
  { path: 'maintenance/homedepotoutreachsnapshot', component: HomeDepotEmployeeOverrideComponent, canActivate: [AuthGuard] },
  { path: 'maintenance/employeeoverrideroofing', component: EmployeeOverrideRoofingComponent, canActivate: [AuthGuard] },
  { path: 'maintenance/monthlyroofingoverridesnapshot', component: MonthlyRoofingOverrideSnapshotComponent, canActivate: [AuthGuard] },
  { path: 'maintenance/batteryquarterlyoverridesnapshot', component: BatteryQuarterlyOverrideComponent, canActivate: [AuthGuard] },
  { path: 'maintenance/quarterlyroofingoverridesnapshot', component: QuaterlyRoofingOverrideComponent, canActivate: [AuthGuard] },
  { path: 'maintenance/commissionabledivisioncontactinclusion', component: CommissionableDivisionContactInclusionComponent, canActivate: [AuthGuard] },
  
  { path: 'commissions/opportunitydetails/:opportunity_id', component: OpportunityDetailsComponent, canActivate: [AuthGuard] },
  { path: 'commissions/opportunitieslist', component: OpportunitiesListComponent, canActivate: [AuthGuard] },
  { path: 'commissions/payments', component: PaymentsComponent, canActivate: [AuthGuard] },
  { path: 'commissions/paymentwithdrawals', component: PaymentBookWithdrawalComponent, canActivate: [AuthGuard] },
  { path: 'commissions/prepaidpayments', component: PrepaidPaymentsDashboardComponent, canActivate: [AuthGuard] },
  // { path: 'commissions/outreachopportunities', component: OutreachOpportunitiesComponent, canActivate: [AuthGuard] },
  { path: 'commissions/dynamicreports', component: DynamicReportComponent, canActivate: [AuthGuard] },

  { path: 'commissions/createplan', component: CreatePlanComponent, canActivate: [AuthGuard] },
  { path: 'commissions/commission/:commission_id', component: CommissionViewComponent, canActivate: [AuthGuard] },
  { path: 'commissions/evaluatecommission/:commission_id', component: EvaluateCommissionViewComponent, canActivate: [AuthGuard] },
  { path: 'commissions/rule', loadChildren: () => import('src/app/dashboard/create-rule/create-rule.module').then(m => m.CreateRuleModule), canActivate: [AuthGuard] },
  // // { path: 'commissions/rule', loadChildren: '../dashboard/create-rule/create-rule.module#CreateRuleModule', canActivate: [AuthGuard] },
  { path: 'commissions/ratetables', loadChildren: () => import('src/app/dashboard/rate-tables/rate-tables.module').then(m => m.RateTablesModule), canActivate: [AuthGuard] },
  { path: 'settings', loadChildren: () => import('src/app/dashboard/settings/setting.module').then(m => m.SettingModule), canActivate: [AuthGuard] },
  { path: 'commissions/emphistory', component: EmpHistoryComponent, canActivate: [AuthGuard] },
  { path: 'commissions/emphistorydetails', component: EmpHistoryDetailsComponent, canActivate: [AuthGuard] },
  { path: 'commissions/userdashboard', component: UserDashboardComponent, canActivate: [AuthGuard] },
  { path: 'commissions/paybooks', component: PaybooksComponent, canActivate: [AuthGuard] },
  { path: 'commissions/paybook/:contact_id', component: PaymentBookDashboardComponent, canActivate: [AuthGuard] },
  { path: 'commissions/report', component: ReportComponent, canActivate: [AuthGuard] },
  { path: 'commissions/salesrep/:contact_id', component: SalesRepComponent, canActivate: [AuthGuard] },
  { path: 'commissions/salescontactslist', component: SalesRepListComponent, canActivate: [AuthGuard] },
  { path: 'commissions/salesrepconfiguration/:contact_id/:contact_plan_id/:plan_id', component: SalesRepConfigurationComponent, canActivate: [AuthGuard] },
  { path: 'commissions/salesrepconfiguration/:contact_id', component: SalesRepConfigurationComponent, canActivate: [AuthGuard] },
  { path: 'commissions/planassign/:contact_id', component: ChangePlanComponent, canActivate: [AuthGuard] },
  { path: 'commissions/search', component: SearchComponent, canActivate: [AuthGuard] },
  { path: 'commissions/data-integration', component: DataIntegrationComponent, canActivate: [AuthGuard] },
  { path: 'commissions/plan-execution', component: PlanExecutionComponent, canActivate: [AuthGuard] },
  // /** User Managment */
  { path: 'commissions/usermanagement/users', component: UsersListComponent, canActivate: [AuthGuard] },
  { path: 'commissions/usermanagement/users/create', component: AddUserComponent, canActivate: [AuthGuard] },
  { path: 'commissions/usermanagement/users/edit', component: EditUserComponent, canActivate: [AuthGuard] }, //, canActivate:[AdminAccessGuard]
  { path: 'commissions/salesrepdashboard/:contactId', component: SalesrepDashboardComponent, canActivate: [AuthGuard] },
  { path: 'commissions/search/:keyword', component: SearchResultComponent, canActivate: [AuthGuard] },
  { path: 'reports/custom-report', component: CustomReportComponent, canActivate: [AuthGuard] },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class DashboardRoutingModule { }
