import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { EmployeeOverrideRoofingComponent } from './employee-override-roofing-component';

describe('EmployeeOverrideRoofingComponent', () => {
    let component: EmployeeOverrideRoofingComponent;
    let fixture: ComponentFixture<EmployeeOverrideRoofingComponent>;

    beforeEach(async(() => {
        TestBed.configureTestingModule({
            declarations: [EmployeeOverrideRoofingComponent]
        })
            .compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(EmployeeOverrideRoofingComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
