<div class="dailog-title-bg">
    <div class="dailog-title"><i class="fas fa-history"></i> History<button class="dailog-close"
        [mat-dialog-close]><span>X</span></button>
    </div>
  </div>
  <div class="row" *ngIf="splitCommissionGroup">
   
    <div class="col-md-6">
      <div class="row">
        <label class="col-sm-5">Sales Division</label>
        <span class="col-sm-5">{{splitCommissionGroup[0][0]?.salesDivision}}</span>
      </div>
    </div>
    <div class="col-md-6">
      <div class="row">
        <label class="col-sm-5">State</label>
        <span class="col-sm-7">{{splitCommissionGroup[0][0]?.stateCode}}</span>
      </div>
    </div>
    <div class="col-md-6">
      <div class="row">
        <label class="col-sm-5">Team</label>
        <span class="col-sm-7">{{splitCommissionGroup[0][0]?.team}}</span>
      </div>
    </div>
    <div class="col-md-6">
      <div class="row">
        <label class="col-sm-5">Product Type</label>
        <span class="col-sm-7">{{splitCommissionGroup[0][0]?.productType}}</span>
      </div>
    </div>
  
    <div class="col-md-12">
      <table class="my-table mat-table w-100 mt-3">
        <thead>
          <tr class="mat-header-row">
            <th class="mat-header-cell" scope="col">Effective Start Date</th>
            <th class="mat-header-cell" scope="col">Effective End Date</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let tr of splitCommissionGroup">
            <tr class="mat-row" (click)="groupClick(tr)">
              <td data-td-head="Effective Start Date" class="mat-cell">{{tr[0].effectiveStartDate | date}}</td>
              <td data-td-head="Effective End Date" class="mat-cell">{{tr[0].effectiveEndDate | date}}</td>
            </tr>
            <td colspan="2" style="background-color: #FFF;"
              *ngIf="splitCommissionSelectedGroup && tr[0].effectiveStartDate == splitCommissionSelectedGroup[0].effectiveStartDate">
  
              <table class="my-table mat-table w-100 mt-3">
                <thead>
                  <tr class="mat-header-row">
                    
                    <th class="mat-header-cell" scope="col">Sales Division</th>
                    <th class="mat-header-cell" scope="col">State</th>
                    <th class="mat-header-cell" scope="col">Team</th>
                    <th class="mat-header-cell" scope="col">Product Type</th>
                    <th class="mat-header-cell" scope="col">Split Type</th>
                    <th class="mat-header-cell" scope="col">Primary Split Value</th>
                    <th class="mat-header-cell" scope="col">Secondary Split Value</th>
                    <th class="mat-header-cell" scope="col">Split Description</th>
                  </tr>
                </thead>
                <tbody>
                  <tr class="mat-row" *ngFor="let tr of splitCommissionSelectedGroup">
                    <td data-td-head="Sales Division" class="mat-cell"> {{tr.salesDivision }}</td>
                    <td data-td-head="State Code" class="mat-cell"> {{tr.stateCode }}</td>
                    <td data-td-head="Team" class="mat-cell"> {{tr.team}}</td>
                    <td data-td-head="Product Type" class="mat-cell">{{tr.productType}} </td>
                    <td data-td-head="Split Type" class="mat-cell"> {{tr.splitType}}</td>
                    <td data-td-head="Primary Split Value" class="mat-cell"> {{tr.splitTypePrimaryValue }}</td>
                    <td data-td-head="Secondary Split Value" class="mat-cell"> {{tr.splitTypeSecondaryValue }}</td>
                    <td data-td-head="Split Description" class="mat-cell"> {{tr.splitDescription}}</td>
                  </tr>
                </tbody>
              </table>
            </td>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>