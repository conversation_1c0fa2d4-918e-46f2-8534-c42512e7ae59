<div class="dailog-title-bg">
    <div class="dailog-title"><i class="fas fa-history"></i> History<button class="dailog-close"
        [mat-dialog-close]><span>X</span></button>
    </div>
  </div>
  <div class="row" *ngIf="roofCommissionRateGroup">
  
  
    <div class="col-md-6">
      <div class="row">
        <label class="col-sm-5">Pricing Type</label>
        <span class="col-sm-7">{{roofCommissionRateGroup[0][0].pricingType}}</span>
      </div>
    </div>
    <div class="col-md-6">
      <div class="row">
        <label class="col-sm-5">Sales Rep Type</label>
        <span class="col-sm-5">{{roofCommissionRateGroup[0][0].salesRepType}}</span>
      </div>
    </div>
    <div class="col-md-6">
      <div class="row">
        <label class="col-sm-5">Floor Rate</label>
        <span class="col-sm-7">{{roofCommissionRateGroup[0][0].floorRate  | currency}}</span>
      </div>
    </div>
    <div class="col-md-6">
      <div class="row">
        <label class="col-sm-5">Base Rate</label>
        <span class="col-sm-7">{{roofCommissionRateGroup[0][0].baseRate  | currency}}</span>
      </div>
    </div>
    <div class="col-md-6">
      <div class="row">
        <label class="col-sm-5">Floor Rate Low Slope</label>
        <span class="col-sm-7">{{roofCommissionRateGroup[0][0].floorRateLowSlope  | currency}}</span>
      </div>
    </div>
    <div class="col-md-6">
      <div class="row">
        <label class="col-sm-5">Lead Fee</label>
        <span class="col-sm-7">{{roofCommissionRateGroup[0][0].leadFee  | currency}}</span>
      </div>
    </div>
    <div class="col-md-12">
      <table class="my-table mat-table w-100 mt-3">
        <thead>
          <tr class="mat-header-row">
            <th class="mat-header-cell" scope="col">Effective Start Date</th>
            <th class="mat-header-cell" scope="col">Effective End Date</th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let tr of roofCommissionRateGroup">
            <tr class="mat-row" (click)="groupClick(tr)">
              <td data-td-head="Effective Start Date" class="mat-cell">{{tr[0].effectiveStartDate | date}}</td>
              <td data-td-head="Effective End Date" class="mat-cell">{{tr[0].effectiveEndDate | date}}</td>
            </tr>
            <td colspan="2" style="background-color: #FFF;"
              *ngIf="roofCommissionSelectedGroup && tr[0].effectiveStartDate == roofCommissionSelectedGroup[0].effectiveStartDate">
  
              <table class="my-table mat-table w-100 mt-3">
                <thead>
                  <tr class="mat-header-row">
                    <th class="mat-header-cell" scope="col">Sales Metric</th>
                    <th class="mat-header-cell" scope="col">Low Slope Sales Metric</th>
                    <th class="mat-header-cell" scope="col">Commission Amount</th>
                    <th class="mat-header-cell" scope="col">Self Gen Commission Amount</th>
                  </tr>
                </thead>
                <tbody>
                  <tr class="mat-row" *ngFor="let tr of roofCommissionSelectedGroup">
                    <td data-td-head="PPA Rate" class="mat-cell"> {{tr.salesMetric | currency:'USD':true:'1.2-3'}}</td>
                    <td data-td-head="Price per kw" class="mat-cell"> {{tr.lowSlopeSalesMetric | currency}}</td>
                    <td data-td-head="Price per kw" class="mat-cell"> {{tr.commissionAmount | currency}}</td>
                    <td data-td-head="Price per kw" class="mat-cell"> {{tr.selfGenCommissionAmount | currency}}</td>
                  </tr>
                </tbody>
              </table>
            </td>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>