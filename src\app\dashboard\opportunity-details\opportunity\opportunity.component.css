#calc-button-container {
 
}

.opportunity-table {
    max-width: 100%;
}

.opportunity-row {
    display: inline-flex;
    width: 100%;
    height: 100%;
    justify-content: flex-start;
}

.opportunity-list-row {
    width: 100%;
}

.opportunity-row-item {
    width: 50%;
    font-size: 12px;
    border-bottom: 1px solid #aaaaaa;
}

.opportunity-row-title {
    font-weight: bold;
}

.opportunity-grid-tile {
}

.opportunity-grid-list {

}

.opportunity-list-header {
    margin-bottom: 2%;
    font-weight: 900;
}

.opportunity-list-container {
    display: inline-flex;
    flex-direction: column;
    width: 49%;
    margin-right: 1%;
}

p.opportunity-row-item.opportunity-row-item {
    margin: 0;
}